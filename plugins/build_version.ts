import { IApi } from 'umi'
import dayjs from 'dayjs'
import fs from 'fs'
import path from 'path'
export default (api: IApi) => {
  const udpateTime = dayjs()
  api.onBuildComplete(({ err, stats }) => {
    if (err) {
      console.log('err', err)
      return
    }
    fs.writeFileSync(
      path.resolve(api.paths.absOutputPath?.split('\\').join('/') || '', 'update_version.json'),
      JSON.stringify({
        version: udpateTime.unix(),
        application: api.pkg.name,
        update_time: udpateTime.format('YYYY-MM-DD HH:mm:ss')
      })
    )
  })
  api.modifyHTML(($) => {
    $('body').prepend(
      `<div class="update-version ${
        api.pkg.name
      }" style="display: none" data-version="${udpateTime.unix()}"></div>`
    )
    return $
  })
}
