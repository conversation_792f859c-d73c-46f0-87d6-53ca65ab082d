import { defineConfig } from '@umijs/max';
// @ts-ignore
export default defineConfig({
  publicPath: 'https://hxl-web-prod.oss-cn-hangzhou.aliyuncs.com/erp_web/',
  define: {
    'process.env.BASE_URL': '',
    'process.env.XLB_ENV': 'production',
    'process.env.XLB_TMS_TOKEN_PRE': 'production',
    'process.env.ERP_URL': 'https://gdp.xlbsoft.com',
    'process.env.FETCH_VERSION_URL':
      'https://hxl-web-prod.oss-cn-hangzhou.aliyuncs.com/erp_web/update_version.json'
  },
});
