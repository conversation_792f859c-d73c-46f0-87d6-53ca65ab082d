{"timestamp": "2025-08-11T18:16:25.519Z", "summary": {"totalFiles": 180, "changedFiles": 180, "totalChanges": 635}, "details": [{"file": "src/pages/archives/cargoOwnerCenter/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.export", "to": "/erp/hxl.erp.delivery.cargo.owner.conf.export", "count": 1}, {"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import", "to": "/erp/hxl.erp.delivery.cargo.owner.conf.import", "count": 2}, {"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download", "to": "/erp/hxl.erp.delivery.cargo.owner.conf.template.download", "count": 2}, {"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.page", "to": "/erp/hxl.erp.delivery.cargo.owner.conf.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.update.import", "to": "/erp/hxl.erp.item.update.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.itemsupdatetemplate.download", "to": "/erp/hxl.erp.itemsupdatetemplate.download", "count": 1}], "success": true}, {"file": "src/pages/archives/storeArea/header/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storearea.store.import", "to": "/erp/hxl.erp.storearea.store.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storeareatemplate.download", "to": "/erp/hxl.erp.storeareatemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.export", "to": "/erp/hxl.erp.storearea.export", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.read", "to": "/erp/hxl.erp.storearea.read", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.update", "to": "/erp/hxl.erp.storearea.update", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.save", "to": "/erp/hxl.erp.storearea.save", "count": 2}, {"from": "/erp-mdm/hxl.erp.storearea.delete", "to": "/erp/hxl.erp.storearea.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.find", "to": "/erp/hxl.erp.storearea.find", "count": 1}], "success": true}, {"file": "src/pages/archives/storeArea/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.storearea.find", "to": "/erp/hxl.erp.storearea.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.save", "to": "/erp/hxl.erp.storearea.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.delete", "to": "/erp/hxl.erp.storearea.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.batch.export", "to": "/erp/hxl.erp.storearea.batch.export", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.update", "to": "/erp/hxl.erp.storearea.update", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.batchupdate", "to": "/erp/hxl.erp.storearea.batchupdate", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.read", "to": "/erp/hxl.erp.storearea.read", "count": 1}, {"from": "/erp-mdm/hxl.erp.storegroup.find", "to": "/erp/hxl.erp.storegroup.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.storeareacategory.delete", "to": "/erp/hxl.erp.storeareacategory.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.storeareacategory.find", "to": "/erp/hxl.erp.storeareacategory.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.storeareacategory.save", "to": "/erp/hxl.erp.storeareacategory.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.storeareacategory.update", "to": "/erp/hxl.erp.storeareacategory.update", "count": 1}], "success": true}, {"file": "src/pages/archives/skuCeilingManagement/header/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.skulimit.page", "to": "/erp/hxl.erp.org.skulimit.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.skulimittemplate.download", "to": "/erp/hxl.erp.org.skulimittemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.skulimit.update.import", "to": "/erp/hxl.erp.org.skulimit.update.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.short.page", "to": "/erp/hxl.erp.item.short.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.skulimit.excludeitemtemplate.download", "to": "/erp/hxl.erp.org.skulimit.excludeitemtemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.skulimit.excludeitem.import", "to": "/erp/hxl.erp.org.skulimit.excludeitem.import", "count": 1}], "success": true}, {"file": "src/pages/archives/companyHeader/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.companyinvoice.page", "to": "/erp/hxl.erp.companyinvoice.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}, {"from": "/erp-mdm/hxl.erp.companyinvoice.delete", "to": "/erp/hxl.erp.companyinvoice.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.companyinvoice.save", "to": "/erp/hxl.erp.companyinvoice.save", "count": 2}, {"from": "/erp-mdm/hxl.erp.companyinvoice.export", "to": "/erp/hxl.erp.companyinvoice.export", "count": 1}, {"from": "/erp-mdm/hxl.erp.companyinvoice.read", "to": "/erp/hxl.erp.companyinvoice.read", "count": 1}], "success": true}, {"file": "src/pages/archives/contractMangement/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.contract.read", "to": "/erp/hxl.erp.contract.read", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.view", "to": "/erp/hxl.erp.contract.view", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.download", "to": "/erp/hxl.erp.contract.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.enum", "to": "/erp/hxl.erp.contract.enum", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.template.page", "to": "/erp/hxl.erp.contract.template.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.create", "to": "/erp/hxl.erp.contract.create", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.whitelist.page", "to": "/erp/hxl.erp.contract.whitelist.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.whitelist.create", "to": "/erp/hxl.erp.contract.whitelist.create", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.whitelist.export", "to": "/erp/hxl.erp.contract.whitelist.export", "count": 1}], "success": true}, {"file": "src/pages/archives/goodsOrder/component/batchChange.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storename.import", "to": "/erp/hxl.erp.storename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storecodetemplate.download", "to": "/erp/hxl.erp.storecodetemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.items.batchimport", "to": "/erp/hxl.erp.items.batchimport", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.shorttemplate.download", "to": "/erp/hxl.erp.item.shorttemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/archives/goodsOrder/component/batchChangeSpecial.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storename.import", "to": "/erp/hxl.erp.storename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storecodetemplate.download", "to": "/erp/hxl.erp.storecodetemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.items.batchimport", "to": "/erp/hxl.erp.items.batchimport", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.shorttemplate.download", "to": "/erp/hxl.erp.item.shorttemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/archives/organizeManage/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 3}, {"from": "/erp-mdm/hxl.erp.org.page", "to": "/erp/hxl.erp.org.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.read", "to": "/erp/hxl.erp.org.read", "count": 1}, {"from": "/erp-mdm/hxl.erp.server.org.check.default", "to": "/erp/hxl.erp.server.org.check.default", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.save", "to": "/erp/hxl.erp.org.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.delete", "to": "/erp/hxl.erp.org.delete", "count": 1}], "success": true}, {"file": "src/pages/archives/devicesBrand/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.storehardware.brand.delete", "to": "/erp/hxl.erp.storehardware.brand.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehardware.brand.find", "to": "/erp/hxl.erp.storehardware.brand.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehardware.category.find", "to": "/erp/hxl.erp.storehardware.category.find", "count": 2}, {"from": "/erp-mdm/hxl.erp.storehardware.brand.save", "to": "/erp/hxl.erp.storehardware.brand.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehardware.brand.update", "to": "/erp/hxl.erp.storehardware.brand.update", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehardware.category.delete", "to": "/erp/hxl.erp.storehardware.category.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehardware.category.update", "to": "/erp/hxl.erp.storehardware.category.update", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehardware.category.save", "to": "/erp/hxl.erp.storehardware.category.save", "count": 1}], "success": true}, {"file": "src/pages/archives/orgBusinessArea/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.business.scope.item.batchdelete", "to": "/erp/hxl.erp.org.business.scope.item.batchdelete", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.business.scope.item.export", "to": "/erp/hxl.erp.org.business.scope.item.export", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.business.scope.item.import", "to": "/erp/hxl.erp.org.business.scope.item.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.business.scope.item.template.download", "to": "/erp/hxl.erp.org.business.scope.item.template.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.business.scope.item.page", "to": "/erp/hxl.erp.org.business.scope.item.page", "count": 1}], "success": true}, {"file": "src/pages/archives/contractTemplateSetup/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.contract.enum", "to": "/erp/hxl.erp.contract.enum", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.template.acquire", "to": "/erp/hxl.erp.contract.template.acquire", "count": 2}, {"from": "/erp-mdm/hxl.erp.contract.template.update", "to": "/erp/hxl.erp.contract.template.update", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.template.save", "to": "/erp/hxl.erp.contract.template.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.template.delete", "to": "/erp/hxl.erp.contract.template.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.template.read", "to": "/erp/hxl.erp.contract.template.read", "count": 1}, {"from": "/erp-mdm/hxl.erp.contract.template.check", "to": "/erp/hxl.erp.contract.template.check", "count": 1}], "success": true}, {"file": "src/pages/archives/cargoOwner/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.cargo.owner.export", "to": "/erp/hxl.erp.cargo.owner.export", "count": 1}, {"from": "/erp-mdm/hxl.erp.cargo.owner.import", "to": "/erp/hxl.erp.cargo.owner.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.cargo.owner.template.download", "to": "/erp/hxl.erp.cargo.owner.template.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.cargo.owner.page", "to": "/erp/hxl.erp.cargo.owner.page", "count": 1}], "success": true}, {"file": "src/pages/archives/supplierMainBody/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.suppliermainbody.find", "to": "/erp/hxl.erp.suppliermainbody.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.suppliermainbody.delete", "to": "/erp/hxl.erp.suppliermainbody.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.suppliermainbody.save", "to": "/erp/hxl.erp.suppliermainbody.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.suppliermainbody.update", "to": "/erp/hxl.erp.suppliermainbody.update", "count": 1}], "success": true}, {"file": "src/pages/archives/userBranch/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.userdept.find", "to": "/erp/hxl.erp.userdept.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.userdept.delete", "to": "/erp/hxl.erp.userdept.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.userdept.save", "to": "/erp/hxl.erp.userdept.save", "count": 2}, {"from": "/erp-mdm/hxl.erp.userdept.update", "to": "/erp/hxl.erp.userdept.update", "count": 1}], "success": true}, {"file": "src/pages/archives/devicesBrand/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehardware.brand.update", "to": "/erp/hxl.erp.storehardware.brand.update", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehardware.brand.save", "to": "/erp/hxl.erp.storehardware.brand.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehardware.brand.delete", "to": "/erp/hxl.erp.storehardware.brand.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehardware.brand.find", "to": "/erp/hxl.erp.storehardware.brand.find", "count": 1}], "success": true}, {"file": "src/pages/archives/goodsBranch/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.dept.find", "to": "/erp/hxl.erp.dept.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.dept.delete", "to": "/erp/hxl.erp.dept.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.dept.save", "to": "/erp/hxl.erp.dept.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.dept.update", "to": "/erp/hxl.erp.dept.update", "count": 1}], "success": true}, {"file": "src/pages/archives/goodsBrand/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.brand.find", "to": "/erp/hxl.erp.brand.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.brand.delete", "to": "/erp/hxl.erp.brand.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.brand.save", "to": "/erp/hxl.erp.brand.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.brand.update", "to": "/erp/hxl.erp.brand.update", "count": 1}], "success": true}, {"file": "src/pages/archives/goodsUnits/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.itemunit.find", "to": "/erp/hxl.erp.itemunit.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.itemunit.delete", "to": "/erp/hxl.erp.itemunit.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.itemunit.save", "to": "/erp/hxl.erp.itemunit.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.itemunit.update", "to": "/erp/hxl.erp.itemunit.update", "count": 1}], "success": true}, {"file": "src/pages/archives/cargoOwnerCenter/server.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.save", "to": "/erp/hxl.erp.delivery.cargo.owner.conf.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.update", "to": "/erp/hxl.erp.delivery.cargo.owner.conf.update", "count": 1}, {"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.batchdelete", "to": "/erp/hxl.erp.delivery.cargo.owner.conf.batchdelete", "count": 1}, {"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.copy", "to": "/erp/hxl.erp.delivery.cargo.owner.conf.copy", "count": 1}, {"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.sync", "to": "/erp/hxl.erp.delivery.cargo.owner.conf.sync", "count": 1}], "success": true}, {"file": "src/pages/archives/contractMangement/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.contract.enum", "to": "/erp/hxl.erp.contract.enum", "count": 4}, {"from": "/erp-mdm/hxl.erp.contract.template.page", "to": "/erp/hxl.erp.contract.template.page", "count": 1}], "success": true}, {"file": "src/pages/archives/priceTemplate/header/component/batchChange.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.items.batchimport", "to": "/erp/hxl.erp.items.batchimport", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.shorttemplate.download", "to": "/erp/hxl.erp.item.shorttemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/archives/skuCeilingManagement/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.org.skulimit.update", "to": "/erp/hxl.erp.org.skulimit.update", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.skulimit.export", "to": "/erp/hxl.erp.org.skulimit.export", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.skulimit.excludeitem.find", "to": "/erp/hxl.erp.org.skulimit.excludeitem.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.skulimit.excludeitem.export", "to": "/erp/hxl.erp.org.skulimit.excludeitem.export", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.skulimit.excludeitem.save", "to": "/erp/hxl.erp.org.skulimit.excludeitem.save", "count": 1}], "success": true}, {"file": "src/pages/archives/cargoOwner/server.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.cargo.owner.save", "to": "/erp/hxl.erp.cargo.owner.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.cargo.owner.batchdelete", "to": "/erp/hxl.erp.cargo.owner.batchdelete", "count": 1}, {"from": "/erp-mdm/hxl.erp.cargo.owner.disabled", "to": "/erp/hxl.erp.cargo.owner.disabled", "count": 1}, {"from": "/erp-mdm/hxl.erp.cargo.owner.enabled", "to": "/erp/hxl.erp.cargo.owner.enabled", "count": 1}], "success": true}, {"file": "src/pages/archives/goodsBranch/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.dept.find", "to": "/erp/hxl.erp.dept.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.dept.save", "to": "/erp/hxl.erp.dept.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.dept.delete", "to": "/erp/hxl.erp.dept.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.dept.update", "to": "/erp/hxl.erp.dept.update", "count": 1}], "success": true}, {"file": "src/pages/archives/organizeManage/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.server.org.check.default", "to": "/erp/hxl.erp.server.org.check.default", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.update", "to": "/erp/hxl.erp.org.update", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.save", "to": "/erp/hxl.erp.org.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/archives/storeArea/header/component/batchChange.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storearea.store.import", "to": "/erp/hxl.erp.storearea.store.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storecodetemplate.download", "to": "/erp/hxl.erp.storecodetemplate.download", "count": 1}], "success": true}, {"file": "src/pages/archives/supplierMainBody/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.suppliermainbody.find", "to": "/erp/hxl.erp.suppliermainbody.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.suppliermainbody.save", "to": "/erp/hxl.erp.suppliermainbody.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.suppliermainbody.update", "to": "/erp/hxl.erp.suppliermainbody.update", "count": 1}, {"from": "/erp-mdm/hxl.erp.suppliermainbody.delete", "to": "/erp/hxl.erp.suppliermainbody.delete", "count": 1}], "success": true}, {"file": "src/pages/archives/userBranch/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.userdept.find", "to": "/erp/hxl.erp.userdept.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.userdept.save", "to": "/erp/hxl.erp.userdept.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.userdept.delete", "to": "/erp/hxl.erp.userdept.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.userdept.update", "to": "/erp/hxl.erp.userdept.update", "count": 1}], "success": true}, {"file": "src/pages/archives/administrativeRegion/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.store.area.find.all", "to": "/erp/hxl.erp.store.area.find.all", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.area.detail.export", "to": "/erp/hxl.erp.store.area.detail.export", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.area.detail.page", "to": "/erp/hxl.erp.store.area.detail.page", "count": 1}], "success": true}, {"file": "src/pages/archives/companyHeader/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.companyinvoice.save", "to": "/erp/hxl.erp.companyinvoice.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.companyinvoice.delete", "to": "/erp/hxl.erp.companyinvoice.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.companyinvoice.export", "to": "/erp/hxl.erp.companyinvoice.export", "count": 1}], "success": true}, {"file": "src/pages/archives/contractMangement/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.contract.page", "to": "/erp/hxl.erp.contract.page", "count": 2}, {"from": "/erp-mdm/hxl.erp.contract.whitelist.page", "to": "/erp/hxl.erp.contract.whitelist.page", "count": 2}], "success": true}, {"file": "src/pages/archives/skuCeilingManagement/components/RecordModal.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.skulimit.log.find", "to": "/erp/hxl.erp.org.skulimit.log.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.skulimit.excludeitem.log.find", "to": "/erp/hxl.erp.org.skulimit.excludeitem.log.find", "count": 1}], "success": true}, {"file": "src/pages/archives/cargoOwner/components/modifyRecord/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.cargo.owner.log.page", "to": "/erp/hxl.erp.cargo.owner.log.page", "count": 1}], "success": true}, {"file": "src/pages/archives/cargoOwnerCenter/components/modifyRecord/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.log.page", "to": "/erp/hxl.erp.delivery.cargo.owner.conf.log.page", "count": 1}], "success": true}, {"file": "src/pages/archives/administrativeRegion/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.area.detail.export", "to": "/erp/hxl.erp.store.area.detail.export", "count": 1}], "success": true}, {"file": "src/pages/archives/cargoOwner/components/addItem/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/archives/contractTemplateSetup/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.contract.template.page", "to": "/erp/hxl.erp.contract.template.page", "count": 1}], "success": true}, {"file": "src/pages/archives/OBSPaymentEntity/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/archives/orgBusinessArea/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.org.business.scope.item.add", "to": "/erp/hxl.erp.org.business.scope.item.add", "count": 1}], "success": true}, {"file": "src/pages/archives/priceTemplate/item/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/archives/skuCeilingManagement/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/archives/skuCeilingManagement/item/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.skulimit.read", "to": "/erp/hxl.erp.org.skulimit.read", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storename.import", "to": "/erp/hxl.erp.storename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storecodetemplate.download", "to": "/erp/hxl.erp.storecodetemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.items.batchimport", "to": "/erp/hxl.erp.items.batchimport", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.shorttemplate.download", "to": "/erp/hxl.erp.item.shorttemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.page", "to": "/erp/hxl.erp.org.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliveryanalysis/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 2}, {"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 2}, {"from": "/erp-mdm/hxl.erp.store.center.find", "to": "/erp/hxl.erp.store.center.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.all.shortfind", "to": "/erp/hxl.erp.store.all.shortfind", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeDeliveryPrice/component/batchChange/batchChange.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storename.import", "to": "/erp/hxl.erp.storename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storecodetemplate.download", "to": "/erp/hxl.erp.storecodetemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.items.batchimport", "to": "/erp/hxl.erp.items.batchimport", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.shorttemplate.download", "to": "/erp/hxl.erp.item.shorttemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeOrders/item/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.center.find", "to": "/erp/hxl.erp.store.center.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.all.shortfind", "to": "/erp/hxl.erp.store.all.shortfind", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.allcenter.find", "to": "/erp/hxl.erp.store.allcenter.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.balance.read", "to": "/erp/hxl.erp.store.balance.read", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.read", "to": "/erp/hxl.erp.item.read", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliverySpecialPrice/item/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.org.find", "to": "/erp/hxl.erp.delivery.cargo.owner.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.commonstorename.import", "to": "/erp/hxl.erp.commonstorename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storenametemplate.download", "to": "/erp/hxl.erp.storenametemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.area.find", "to": "/erp/hxl.erp.store.area.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/distributionGross/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 2}, {"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.suppliermainbody.find", "to": "/erp/hxl.erp.suppliermainbody.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.settlementcategory.center.find", "to": "/erp/hxl.erp.settlementcategory.center.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/stockForecasts/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.page", "to": "/erp/hxl.erp.store.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.storegroup.find", "to": "/erp/hxl.erp.storegroup.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.storegroup.delete", "to": "/erp/hxl.erp.storegroup.delete", "count": 1}, {"from": "/erp-mdm/hxl.erp.storegroup.update", "to": "/erp/hxl.erp.storegroup.update", "count": 1}, {"from": "/erp-mdm/hxl.erp.storegroup.save", "to": "/erp/hxl.erp.storegroup.save", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliveryCenterStore/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.commonstorename.import", "to": "/erp/hxl.erp.commonstorename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.orgdeliverycenter.find", "to": "/erp/hxl.erp.store.orgdeliverycenter.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.sharedeliverycenter.find", "to": "/erp/hxl.erp.store.sharedeliverycenter.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.all.shortfind", "to": "/erp/hxl.erp.store.all.shortfind", "count": 1}], "success": true}, {"file": "src/pages/delivery/goodsway/data.ts", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 2}, {"from": "/erp-mdm/hxl.erp.store.all.shortfind", "to": "/erp/hxl.erp.store.all.shortfind", "count": 2}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/stockForecasts/component/batchChange.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storename.import", "to": "/erp/hxl.erp.storename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storecodetemplate.download", "to": "/erp/hxl.erp.storecodetemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliveryCenterStore/component/batchChange.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storename.import", "to": "/erp/hxl.erp.storename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storecodetemplate.download", "to": "/erp/hxl.erp.storecodetemplate.download", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliveryCenterStore/modal.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.commonstorename.import", "to": "/erp/hxl.erp.commonstorename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storenametemplate.download", "to": "/erp/hxl.erp.storenametemplate.download", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliverySpecialPrice/api.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.center.find", "to": "/erp/hxl.erp.store.center.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 2}], "success": true}, {"file": "src/pages/delivery/distributionGross/sever.ts", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.itemlabel.find", "to": "/erp/hxl.erp.itemlabel.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.settlementcategory.center.find", "to": "/erp/hxl.erp.settlementcategory.center.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/replenishGoodsAnalysis/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.area.find.all", "to": "/erp/hxl.erp.store.area.find.all", "count": 2}], "success": true}, {"file": "src/pages/delivery/saleParam/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 2}, {"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/delivery/strongDataAnalysis/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/transferDocument/item/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.store.center.find", "to": "/erp/hxl.erp.store.center.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}], "success": true}, {"file": "src/pages/delivery/basketStat/data.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/collectDocument/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/collectDocument/item.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.cargo.owner.pageforinner", "to": "/erp/hxl.erp.cargo.owner.pageforinner", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliverySpecialPriceAnalysis/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 2}], "success": true}, {"file": "src/pages/delivery/directSupplyPoint/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 2}], "success": true}, {"file": "src/pages/delivery/marketingCampaign/item.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.center.find", "to": "/erp/hxl.erp.store.center.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/receivingApplication/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/receivingApplication/item.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.cargo.owner.pageforinner", "to": "/erp/hxl.erp.cargo.owner.pageforinner", "count": 1}], "success": true}, {"file": "src/pages/delivery/replenishTemplate/item/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.item.short.page", "to": "/erp/hxl.erp.item.short.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/stockForecasts/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storegroup.find", "to": "/erp/hxl.erp.storegroup.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.page", "to": "/erp/hxl.erp.store.page", "count": 1}], "success": true}, {"file": "src/pages/delivery/stockTypeSetting/item/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.file.upload", "to": "/erp/hxl.erp.file.upload", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeDeliveryDay/item.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storecodetemplate.download", "to": "/erp/hxl.erp.storecodetemplate.download", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeOrderingDate/item/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storecodetemplate.download", "to": "/erp/hxl.erp.storecodetemplate.download", "count": 1}], "success": true}, {"file": "src/pages/delivery/supplyAnalyze/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.itemlabel.find", "to": "/erp/hxl.erp.itemlabel.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/advancePosition/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.center.find", "to": "/erp/hxl.erp.store.center.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliveryanalysis/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.itemlabel.find", "to": "/erp/hxl.erp.itemlabel.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliveryCenterStore/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliveryDetails/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliveryDetails/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliveryPriceMange/header/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliveryPriceMange/item/components/xlbBaseGoods/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.center.find", "to": "/erp/hxl.erp.store.center.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/deliveryPriceMange/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.center.find", "to": "/erp/hxl.erp.store.center.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/forceDeliveryRule/components/addPCRules/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/forceDeliveryRule/components/addRules/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/goodsway/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/marketingCampaign/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/delivery/marketingCampaign/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.center.find", "to": "/erp/hxl.erp.store.center.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/stockPrediction/data.ts", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeDeliveryDay/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeDeliveryPrice/component/copy/copy.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeOrderingDate/header/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeOrders/components/batchOrder/batchOrder.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeOrders/components/deliveryOrder/deliveryOrder.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeOrders/components/uploadPhotoGroup.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.file.delete", "to": "/erp/hxl.erp.file.delete", "count": 1}], "success": true}, {"file": "src/pages/delivery/storeOrders/item/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}], "success": true}, {"file": "src/pages/delivery/transferDocument/components/batchOrder/batchOrder.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}], "success": true}, {"file": "src/pages/delivery/transferDocument/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/transferDocument/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.center.find", "to": "/erp/hxl.erp.store.center.find", "count": 1}], "success": true}, {"file": "src/pages/delivery/transferWay/data.ts", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/constants/baseDataConfig.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 11}, {"from": "/erp-mdm/hxl.erp.storearea.find", "to": "/erp/hxl.erp.storearea.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.userdept.find", "to": "/erp/hxl.erp.userdept.find", "count": 3}, {"from": "/erp-mdm/hxl.erp.storelabel.find", "to": "/erp/hxl.erp.storelabel.find", "count": 9}, {"from": "/erp-mdm/hxl.erp.itemlabel.find", "to": "/erp/hxl.erp.itemlabel.find", "count": 6}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 16}, {"from": "/erp-mdm/hxl.erp.storeareacategory.find", "to": "/erp/hxl.erp.storeareacategory.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.businessarea.detail.find", "to": "/erp/hxl.erp.businessarea.detail.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.businessarea.find", "to": "/erp/hxl.erp.businessarea.find", "count": 8}, {"from": "/erp-mdm/hxl.erp.role.page", "to": "/erp/hxl.erp.role.page", "count": 3}, {"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.storegroup.find", "to": "/erp/hxl.erp.storegroup.find", "count": 8}, {"from": "/erp-mdm/hxl.erp.store.ids.find", "to": "/erp/hxl.erp.store.ids.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.baseparam.read", "to": "/erp/hxl.erp.baseparam.read", "count": 4}, {"from": "/erp-mdm/hxl.erp.store.area.find.all", "to": "/erp/hxl.erp.store.area.find.all", "count": 6}, {"from": "/erp-mdm/hxl.erp.item.short.page", "to": "/erp/hxl.erp.item.short.page", "count": 3}, {"from": "/erp-mdm/hxl.erp.dept.find", "to": "/erp/hxl.erp.dept.find", "count": 11}, {"from": "/erp-mdm/hxl.erp.brand.find", "to": "/erp/hxl.erp.brand.find", "count": 11}, {"from": "/erp-mdm/hxl.erp.suppliercategory.findwithsupplier", "to": "/erp/hxl.erp.suppliercategory.findwithsupplier", "count": 12}, {"from": "/erp-mdm/hxl.erp.supplier.short.page", "to": "/erp/hxl.erp.supplier.short.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.suppliercategory.find", "to": "/erp/hxl.erp.suppliercategory.find", "count": 6}, {"from": "/erp-mdm/hxl.erp.customattribute.show.find", "to": "/erp/hxl.erp.customattribute.show.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.suppliermainbody.find", "to": "/erp/hxl.erp.suppliermainbody.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.labour.page", "to": "/erp/hxl.erp.labour.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.user.page", "to": "/erp/hxl.erp.user.page", "count": 3}, {"from": "/erp-mdm/hxl.erp.storehouse.page", "to": "/erp/hxl.erp.storehouse.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.client.page", "to": "/erp/hxl.erp.client.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.clientcategory.find", "to": "/erp/hxl.erp.clientcategory.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.businessscope.short.find", "to": "/erp/hxl.erp.businessscope.short.find", "count": 3}, {"from": "/erp-mdm/hxl.erp.businessscope.find", "to": "/erp/hxl.erp.businessscope.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.cargo.owner.pageforinner", "to": "/erp/hxl.erp.cargo.owner.pageforinner", "count": 1}, {"from": "/erp-mdm/hxl.erp.delivery.cargo.owner.conf.readbystoreIds", "to": "/erp/hxl.erp.delivery.cargo.owner.conf.readbystoreIds", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.short.ids.find", "to": "/erp/hxl.erp.item.short.ids.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.item.page", "to": "/erp/hxl.erp.org.item.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.cargo.owner.page", "to": "/erp/hxl.erp.cargo.owner.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.supplier.producer.find", "to": "/erp/hxl.erp.supplier.producer.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find", "to": "/erp/hxl.erp.supplier.producerandexecutivestandard.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldListConfig.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.authority.search", "to": "/erp/hxl.erp.authority.search", "count": 8}, {"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 7}, {"from": "/erp-mdm/hxl.erp.printtemplate.menus", "to": "/erp/hxl.erp.printtemplate.menus", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 2}, {"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 6}, {"from": "/erp-mdm/hxl.erp.store.area.find.all", "to": "/erp/hxl.erp.store.area.find.all", "count": 3}, {"from": "/erp-mdm/hxl.erp.store.center.find", "to": "/erp/hxl.erp.store.center.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.baseparam.read", "to": "/erp/hxl.erp.baseparam.read", "count": 1}, {"from": "/erp-mdm/hxl.erp.hardwarecategory.find", "to": "/erp/hxl.erp.hardwarecategory.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 2}], "success": true}, {"file": "src/data/common/fieldModule/userManage.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.role.page", "to": "/erp/hxl.erp.role.page", "count": 5}, {"from": "/erp-mdm/hxl.erp.userdept.find", "to": "/erp/hxl.erp.userdept.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.storearea.find", "to": "/erp/hxl.erp.storearea.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/goodsFiles.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.dept.find", "to": "/erp/hxl.erp.dept.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.brand.find", "to": "/erp/hxl.erp.brand.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.settlementcategory.center.find", "to": "/erp/hxl.erp.settlementcategory.center.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/newYearGoodsPlanDetails.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 2}], "success": true}, {"file": "src/data/common/fieldModule/deliveryCenterStore.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.sharedeliverycenter.find", "to": "/erp/hxl.erp.store.sharedeliverycenter.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.all.shortfind", "to": "/erp/hxl.erp.store.all.shortfind", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/interWarehouseTransfer.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 2}, {"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/returnRateStatistics.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.brand.find", "to": "/erp/hxl.erp.brand.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/storeOrder.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 2}], "success": true}, {"file": "src/data/common/fieldModule/devicesBrand.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehardware.category.find", "to": "/erp/hxl.erp.storehardware.category.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/payMode.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/storeDeviceManage.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehardware.category.find", "to": "/erp/hxl.erp.storehardware.category.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/storeManage.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.store.area.find.all", "to": "/erp/hxl.erp.store.area.find.all", "count": 1}, {"from": "/erp-mdm/hxl.erp.baseparam.read", "to": "/erp/hxl.erp.baseparam.read", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/supplier.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.suppliermainbody.find", "to": "/erp/hxl.erp.suppliermainbody.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/tiktokcouponrefund.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/businessRange.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.businessscopecategory.find", "to": "/erp/hxl.erp.businessscopecategory.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/purchaseBusinessRange.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.businessscopecategory.find", "to": "/erp/hxl.erp.businessscopecategory.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/purchaseOrdering.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/stockCheckOrder.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/storeArea.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storegroup.find", "to": "/erp/hxl.erp.storegroup.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/storeDeilveryPrice.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/data/common/fieldModule/wholesaleCustomer.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholesalePrice/components/batchChange/batchChange.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storename.import", "to": "/erp/hxl.erp.storename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storecodetemplate.download", "to": "/erp/hxl.erp.storecodetemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.items.batchimport", "to": "/erp/hxl.erp.items.batchimport", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.shorttemplate.download", "to": "/erp/hxl.erp.item.shorttemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/wholesale/customerGoodsAttributes/component/batchChange/batchChange.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.clientname.import", "to": "/erp/hxl.erp.clientname.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.clientnametemplate.download", "to": "/erp/hxl.erp.clientnametemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.items.batchimport", "to": "/erp/hxl.erp.items.batchimport", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.shorttemplate.download", "to": "/erp/hxl.erp.item.shorttemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholesaleOrgSetting/modal.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.commonstorename.import", "to": "/erp/hxl.erp.commonstorename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storenametemplate.download", "to": "/erp/hxl.erp.storenametemplate.download", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholeSaleAnalyze/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholeSaleAnalyze/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.itemlabel.find", "to": "/erp/hxl.erp.itemlabel.find", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholesalePrice/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/wholesale/buyParam/header/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholeSaleAnalyze/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholeSaleDetail/data.ts", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholeSaleDetail/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholeSaleDetail/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholesaleOrgSetting/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholesalePrice/components/copy/copy.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholesalePriceAdjustment/data.ts", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/wholesale/wholesalePriceAdjustment/header/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/procurement/supplierRelationshipManagement/header/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 2}, {"from": "/erp-mdm/hxl.erp.storename.import", "to": "/erp/hxl.erp.storename.import", "count": 1}, {"from": "/erp-mdm/hxl.erp.storecodetemplate.download", "to": "/erp/hxl.erp.storecodetemplate.download", "count": 1}, {"from": "/erp-mdm/hxl.erp.items.batchimport", "to": "/erp/hxl.erp.items.batchimport", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.shorttemplate.download", "to": "/erp/hxl.erp.item.shorttemplate.download", "count": 1}], "success": true}, {"file": "src/pages/procurement/purchaseReport/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.area.find.all", "to": "/erp/hxl.erp.store.area.find.all", "count": 1}], "success": true}, {"file": "src/pages/procurement/supplierRelationshipManagement/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find", "to": "/erp/hxl.erp.supplier.producerandexecutivestandard.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.baseparam.read", "to": "/erp/hxl.erp.baseparam.read", "count": 2}], "success": true}, {"file": "src/pages/procurement/purchaseLatestPrice/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/procurement/purchaseReport/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.settlementcategory.center.find", "to": "/erp/hxl.erp.settlementcategory.center.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}], "success": true}, {"file": "src/pages/procurement/purchaseShare/header/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}, {"from": "/erp-mdm/hxl.erp.store.all.find", "to": "/erp/hxl.erp.store.all.find", "count": 1}], "success": true}, {"file": "src/pages/procurement/stockPlan/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/procurement/orderParamsConfig/data.ts", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/procurement/orderValidDay/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/procurement/orderValidDay/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/procurement/purchasePrice/data.ts", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/procurement/purchasePrice/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.brand.find", "to": "/erp/hxl.erp.brand.find", "count": 1}], "success": true}, {"file": "src/pages/procurement/purchaseShare/header/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/procurement/stockPlan/data.ts", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/procurement/supplierRelationshipManagement/header/components/Additem/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/pages/stock/stockLog/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.settlementcategory.center.find", "to": "/erp/hxl.erp.settlementcategory.center.find", "count": 1}], "success": true}, {"file": "src/pages/stock/stockSearch/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.suppliermainbody.find", "to": "/erp/hxl.erp.suppliermainbody.find", "count": 1}], "success": true}, {"file": "src/pages/stock/stockSearch/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.suppliermainbody.find", "to": "/erp/hxl.erp.suppliermainbody.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.maxlevel.read", "to": "/erp/hxl.erp.category.maxlevel.read", "count": 1}], "success": true}, {"file": "src/pages/stock/unsalableItem/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/stock/stockCollaborativeSharingr/components/addModel/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.store.cargoownerdelivery.short.page", "to": "/erp/hxl.erp.store.cargoownerdelivery.short.page", "count": 1}], "success": true}, {"file": "src/pages/stock/stockCollaborativeSharingr/data.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.cargoownerdelivery.short.page", "to": "/erp/hxl.erp.store.cargoownerdelivery.short.page", "count": 1}], "success": true}, {"file": "src/pages/stock/stockDyingPeriod/data.ts", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/stock/stockDyingPeriod/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.page", "to": "/erp/hxl.erp.storehouse.page", "count": 1}], "success": true}, {"file": "src/pages/stock/stockLog/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.settlementcategory.center.find", "to": "/erp/hxl.erp.settlementcategory.center.find", "count": 1}], "success": true}, {"file": "src/pages/stock/unsalableItem/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}], "success": true}, {"file": "src/pages/purchase/purchaseReplenishAnalysis/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 2}], "success": true}, {"file": "src/pages/purchase/orderWatch/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}, {"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/purchase/mustSellGoodsDetail/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/purchase/mustSellGoodsManagement/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/purchase/newItemPurchasePlan/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}], "success": true}, {"file": "src/pages/purchasement/itemSku/server.ts", "changes": [{"from": "/erp-mdm/hxl.erp.item.summary", "to": "/erp/hxl.erp.item.summary", "count": 4}, {"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 2}], "success": true}, {"file": "src/pages/purchasement/itemSku/header/index.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.org.find", "to": "/erp/hxl.erp.org.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.item.summary", "to": "/erp/hxl.erp.item.summary", "count": 1}], "success": true}, {"file": "src/pages/dataAnalysis/heightLowInventoryGoods/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 1}, {"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/pages/dataAnalysis/profitLossStatistic/server.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.storehouse.store.find", "to": "/erp/hxl.erp.storehouse.store.find", "count": 2}], "success": true}, {"file": "src/pages/dataAnalysis/profitLossStatistic/data.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.category.find", "to": "/erp/hxl.erp.category.find", "count": 1}], "success": true}, {"file": "src/components/relateStoreCreate.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.businessarea.store.find", "to": "/erp/hxl.erp.businessarea.store.find", "count": 1}], "success": true}, {"file": "src/components/common/xlbOrgids/selectOrg.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.org.tree", "to": "/erp/hxl.erp.org.tree", "count": 1}], "success": true}, {"file": "src/provider.tsx", "changes": [{"from": "/erp-mdm/hxl.erp.usercolumn.get", "to": "/erp/hxl.erp.usercolumn.get", "count": 1}, {"from": "/erp-mdm/hxl.erp.usercolumn.update", "to": "/erp/hxl.erp.usercolumn.update", "count": 1}], "success": true}, {"file": "src/utils/purchaseUrl.ts", "changes": [{"from": "/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find", "to": "/erp/hxl.erp.supplier.producerandexecutivestandard.find", "count": 1}], "success": true}, {"file": "src/utils/utils.ts", "changes": [{"from": "/erp-mdm/hxl.erp.usercolumn.get", "to": "/erp/hxl.erp.usercolumn.get", "count": 1}], "success": true}, {"file": "src/api/common.ts", "changes": [{"from": "/erp-mdm/hxl.erp.store.short.page", "to": "/erp/hxl.erp.store.short.page", "count": 1}], "success": true}, {"file": "src/hooks/useBaseParams.ts", "changes": [{"from": "/erp-mdm/hxl.erp.baseparam.read", "to": "/erp/hxl.erp.baseparam.read", "count": 1}], "success": true}, {"file": "src/services/system/index.ts", "changes": [{"from": "/erp-mdm/hxl.erp.user.account.login", "to": "/erp/hxl.erp.user.account.login", "count": 1}], "success": true}]}