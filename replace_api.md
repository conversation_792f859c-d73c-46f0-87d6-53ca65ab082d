# xlb_erp_web API 路径替换详细记录

## 概览信息

- **总替换次数**: 742
- **涉及文件数**: 180
- **涉及业务模块数**: 16

## 按业务模块分类的替换详情

### 业务页面 - archives
**总替换次数**: 241
**涉及文件数**: 43

#### 📁 src\pages\archives\cargoOwnerCenter\index.tsx
**业务路径**: archives/cargoOwnerCenter/index.tsx
**替换次数**: 15

1. **第186行**: `/erp/hxl.erp.delivery.cargo.owner.conf.export` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.export`
2. **第186行**: `/erp/hxl.erp.delivery.cargo.owner.conf.export` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.export`
3. **第217行**: `/erp/hxl.erp.delivery.cargo.owner.conf.import` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import`
4. **第217行**: `/erp/hxl.erp.delivery.cargo.owner.conf.import` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import`
5. **第218行**: `/erp/hxl.erp.delivery.cargo.owner.conf.template.download` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download`
6. **第218行**: `/erp/hxl.erp.delivery.cargo.owner.conf.template.download` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download`
7. **第268行**: `/erp/hxl.erp.delivery.cargo.owner.conf.page` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.page`
8. **第328行**: `/erp/hxl.erp.delivery.cargo.owner.conf.import` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import`
9. **第328行**: `/erp/hxl.erp.delivery.cargo.owner.conf.import` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import`
10. **第329行**: `/erp/hxl.erp.delivery.cargo.owner.conf.template.download` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download`
11. **第329行**: `/erp/hxl.erp.delivery.cargo.owner.conf.template.download` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download`
12. **第336行**: `/erp/hxl.erp.item.update.import` → `/erp-mdm/hxl.erp.item.update.import`
13. **第336行**: `/erp/hxl.erp.item.update.import` → `/erp-mdm/hxl.erp.item.update.import`
14. **第337行**: `/erp/hxl.erp.itemsupdatetemplate.download` → `/erp-mdm/hxl.erp.itemsupdatetemplate.download`
15. **第337行**: `/erp/hxl.erp.itemsupdatetemplate.download` → `/erp-mdm/hxl.erp.itemsupdatetemplate.download`

#### 📁 src\pages\archives\storeArea\header\index.tsx
**业务路径**: archives/storeArea/header/index.tsx
**替换次数**: 14

1. **第42行**: `/erp/hxl.erp.storearea.store.import` → `/erp-mdm/hxl.erp.storearea.store.import`
2. **第42行**: `/erp/hxl.erp.storearea.store.import` → `/erp-mdm/hxl.erp.storearea.store.import`
3. **第43行**: `/erp/hxl.erp.storeareatemplate.download` → `/erp-mdm/hxl.erp.storeareatemplate.download`
4. **第43行**: `/erp/hxl.erp.storeareatemplate.download` → `/erp-mdm/hxl.erp.storeareatemplate.download`
5. **第72行**: `/erp/hxl.erp.storearea.export` → `/erp-mdm/hxl.erp.storearea.export`
6. **第335行**: `/erp/hxl.erp.storearea.read` → `/erp-mdm/hxl.erp.storearea.read`
7. **第350行**: `/erp/hxl.erp.storearea.update` → `/erp-mdm/hxl.erp.storearea.update`
8. **第350行**: `/erp/hxl.erp.storearea.update` → `/erp-mdm/hxl.erp.storearea.update`
9. **第360行**: `/erp/hxl.erp.storearea.save` → `/erp-mdm/hxl.erp.storearea.save`
10. **第372行**: `/erp/hxl.erp.storearea.save` → `/erp-mdm/hxl.erp.storearea.save`
11. **第372行**: `/erp/hxl.erp.storearea.save` → `/erp-mdm/hxl.erp.storearea.save`
12. **第377行**: `/erp/hxl.erp.storearea.delete` → `/erp-mdm/hxl.erp.storearea.delete`
13. **第377行**: `/erp/hxl.erp.storearea.delete` → `/erp-mdm/hxl.erp.storearea.delete`
14. **第419行**: `/erp/hxl.erp.storearea.find` → `/erp-mdm/hxl.erp.storearea.find`

#### 📁 src\pages\archives\storeArea\server.ts
**业务路径**: archives/storeArea/server.ts
**替换次数**: 12

1. **第7行**: `/erp/hxl.erp.storearea.find` → `/erp-mdm/hxl.erp.storearea.find`
2. **第12行**: `/erp/hxl.erp.storearea.save` → `/erp-mdm/hxl.erp.storearea.save`
3. **第17行**: `/erp/hxl.erp.storearea.delete` → `/erp-mdm/hxl.erp.storearea.delete`
4. **第22行**: `/erp/hxl.erp.storearea.batch.export` → `/erp-mdm/hxl.erp.storearea.batch.export`
5. **第27行**: `/erp/hxl.erp.storearea.update` → `/erp-mdm/hxl.erp.storearea.update`
6. **第32行**: `/erp/hxl.erp.storearea.batchupdate` → `/erp-mdm/hxl.erp.storearea.batchupdate`
7. **第37行**: `/erp/hxl.erp.storearea.read` → `/erp-mdm/hxl.erp.storearea.read`
8. **第42行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
9. **第47行**: `/erp/hxl.erp.storeareacategory.delete` → `/erp-mdm/hxl.erp.storeareacategory.delete`
10. **第52行**: `/erp/hxl.erp.storeareacategory.find` → `/erp-mdm/hxl.erp.storeareacategory.find`
11. **第57行**: `/erp/hxl.erp.storeareacategory.save` → `/erp-mdm/hxl.erp.storeareacategory.save`
12. **第62行**: `/erp/hxl.erp.storeareacategory.update` → `/erp-mdm/hxl.erp.storeareacategory.update`

#### 📁 src\pages\archives\skuCeilingManagement\header\index.tsx
**业务路径**: archives/skuCeilingManagement/header/index.tsx
**替换次数**: 11

1. **第460行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第484行**: `/erp/hxl.erp.org.skulimit.page` → `/erp-mdm/hxl.erp.org.skulimit.page`
3. **第534行**: `/erp/hxl.erp.org.skulimittemplate.download` → `/erp-mdm/hxl.erp.org.skulimittemplate.download`
4. **第534行**: `/erp/hxl.erp.org.skulimittemplate.download` → `/erp-mdm/hxl.erp.org.skulimittemplate.download`
5. **第535行**: `/erp/hxl.erp.org.skulimit.update.import` → `/erp-mdm/hxl.erp.org.skulimit.update.import`
6. **第535行**: `/erp/hxl.erp.org.skulimit.update.import` → `/erp-mdm/hxl.erp.org.skulimit.update.import`
7. **第604行**: `/erp/hxl.erp.item.short.page` → `/erp-mdm/hxl.erp.item.short.page`
8. **第644行**: `/erp/hxl.erp.org.skulimit.excludeitemtemplate.download` → `/erp-mdm/hxl.erp.org.skulimit.excludeitemtemplate.download`
9. **第644行**: `/erp/hxl.erp.org.skulimit.excludeitemtemplate.download` → `/erp-mdm/hxl.erp.org.skulimit.excludeitemtemplate.download`
10. **第645行**: `/erp/hxl.erp.org.skulimit.excludeitem.import` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.import`
11. **第645行**: `/erp/hxl.erp.org.skulimit.excludeitem.import` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.import`

#### 📁 src\pages\archives\companyHeader\index.tsx
**业务路径**: archives/companyHeader/index.tsx
**替换次数**: 10

1. **第44行**: `/erp/hxl.erp.companyinvoice.page` → `/erp-mdm/hxl.erp.companyinvoice.page`
2. **第60行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
3. **第70行**: `/erp/hxl.erp.companyinvoice.delete` → `/erp-mdm/hxl.erp.companyinvoice.delete`
4. **第70行**: `/erp/hxl.erp.companyinvoice.delete` → `/erp-mdm/hxl.erp.companyinvoice.delete`
5. **第76行**: `/erp/hxl.erp.companyinvoice.save` → `/erp-mdm/hxl.erp.companyinvoice.save`
6. **第76行**: `/erp/hxl.erp.companyinvoice.save` → `/erp-mdm/hxl.erp.companyinvoice.save`
7. **第96行**: `/erp/hxl.erp.companyinvoice.export` → `/erp-mdm/hxl.erp.companyinvoice.export`
8. **第96行**: `/erp/hxl.erp.companyinvoice.export` → `/erp-mdm/hxl.erp.companyinvoice.export`
9. **第102行**: `/erp/hxl.erp.companyinvoice.read` → `/erp-mdm/hxl.erp.companyinvoice.read`
10. **第224行**: `/erp/hxl.erp.companyinvoice.save` → `/erp-mdm/hxl.erp.companyinvoice.save`

#### 📁 src\pages\archives\contractMangement\server.ts
**业务路径**: archives/contractMangement/server.ts
**替换次数**: 10

1. **第8行**: `/erp/hxl.erp.contract.read` → `/erp-mdm/hxl.erp.contract.read`
2. **第13行**: `/erp/hxl.erp.contract.view` → `/erp-mdm/hxl.erp.contract.view`
3. **第18行**: `/erp/hxl.erp.contract.download` → `/erp-mdm/hxl.erp.contract.download`
4. **第23行**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`
5. **第26行**: `/erp/hxl.erp.contract.template.page` → `/erp-mdm/hxl.erp.contract.template.page`
6. **第29行**: `/erp/hxl.erp.contract.create` → `/erp-mdm/hxl.erp.contract.create`
7. **第32行**: `/erp/hxl.erp.contract.whitelist.page` → `/erp-mdm/hxl.erp.contract.whitelist.page`
8. **第35行**: `/erp/hxl.erp.contract.whitelist.create` → `/erp-mdm/hxl.erp.contract.whitelist.create`
9. **第58行**: `/erp/hxl.erp.contract.whitelist.export` → `/erp-mdm/hxl.erp.contract.whitelist.export`
10. **第58行**: `/erp/hxl.erp.contract.whitelist.export` → `/erp-mdm/hxl.erp.contract.whitelist.export`

#### 📁 src\pages\archives\goodsOrder\component\batchChange.tsx
**业务路径**: archives/goodsOrder/component/batchChange.tsx
**替换次数**: 10

1. **第31行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
2. **第31行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
3. **第32行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
4. **第32行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
5. **第51行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
6. **第51行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
7. **第52行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
8. **第52行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
9. **第263行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
10. **第331行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx
**业务路径**: archives/goodsOrder/component/batchChangeSpecial.tsx
**替换次数**: 10

1. **第133行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
2. **第133行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
3. **第134行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
4. **第134行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
5. **第153行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
6. **第153行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
7. **第154行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
8. **第154行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
9. **第205行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
10. **第278行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\archives\organizeManage\index.tsx
**业务路径**: archives/organizeManage/index.tsx
**替换次数**: 10

1. **第23行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
2. **第40行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
3. **第46行**: `/erp/hxl.erp.org.page` → `/erp-mdm/hxl.erp.org.page`
4. **第114行**: `/erp/hxl.erp.org.read` → `/erp-mdm/hxl.erp.org.read`
5. **第139行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
6. **第373行**: `/erp/hxl.erp.server.org.check.default` → `/erp-mdm/hxl.erp.server.org.check.default`
7. **第432行**: `/erp/hxl.erp.org.save` → `/erp-mdm/hxl.erp.org.save`
8. **第432行**: `/erp/hxl.erp.org.save` → `/erp-mdm/hxl.erp.org.save`
9. **第441行**: `/erp/hxl.erp.org.delete` → `/erp-mdm/hxl.erp.org.delete`
10. **第441行**: `/erp/hxl.erp.org.delete` → `/erp-mdm/hxl.erp.org.delete`

#### 📁 src\pages\archives\devicesBrand\server.ts
**业务路径**: archives/devicesBrand/server.ts
**替换次数**: 9

1. **第5行**: `/erp/hxl.erp.storehardware.brand.delete` → `/erp-mdm/hxl.erp.storehardware.brand.delete`
2. **第8行**: `/erp/hxl.erp.storehardware.brand.find` → `/erp-mdm/hxl.erp.storehardware.brand.find`
3. **第14行**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`
4. **第16行**: `/erp/hxl.erp.storehardware.brand.save` → `/erp-mdm/hxl.erp.storehardware.brand.save`
5. **第18行**: `/erp/hxl.erp.storehardware.brand.update` → `/erp-mdm/hxl.erp.storehardware.brand.update`
6. **第21行**: `/erp/hxl.erp.storehardware.category.delete` → `/erp-mdm/hxl.erp.storehardware.category.delete`
7. **第28行**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`
8. **第34行**: `/erp/hxl.erp.storehardware.category.update` → `/erp-mdm/hxl.erp.storehardware.category.update`
9. **第40行**: `/erp/hxl.erp.storehardware.category.save` → `/erp-mdm/hxl.erp.storehardware.category.save`

#### 📁 src\pages\archives\orgBusinessArea\index.tsx
**业务路径**: archives/orgBusinessArea/index.tsx
**替换次数**: 9

1. **第63行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第119行**: `/erp/hxl.erp.org.business.scope.item.batchdelete` → `/erp-mdm/hxl.erp.org.business.scope.item.batchdelete`
3. **第119行**: `/erp/hxl.erp.org.business.scope.item.batchdelete` → `/erp-mdm/hxl.erp.org.business.scope.item.batchdelete`
4. **第129行**: `/erp/hxl.erp.org.business.scope.item.export` → `/erp-mdm/hxl.erp.org.business.scope.item.export`
5. **第129行**: `/erp/hxl.erp.org.business.scope.item.export` → `/erp-mdm/hxl.erp.org.business.scope.item.export`
6. **第136行**: `/erp/hxl.erp.org.business.scope.item.import` → `/erp-mdm/hxl.erp.org.business.scope.item.import`
7. **第136行**: `/erp/hxl.erp.org.business.scope.item.import` → `/erp-mdm/hxl.erp.org.business.scope.item.import`
8. **第138行**: `/erp/hxl.erp.org.business.scope.item.template.download` → `/erp-mdm/hxl.erp.org.business.scope.item.template.download`
9. **第142行**: `/erp/hxl.erp.org.business.scope.item.page` → `/erp-mdm/hxl.erp.org.business.scope.item.page`

#### 📁 src\pages\archives\contractTemplateSetup\server.ts
**业务路径**: archives/contractTemplateSetup/server.ts
**替换次数**: 8

1. **第5行**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`
2. **第9行**: `/erp/hxl.erp.contract.template.acquire` → `/erp-mdm/hxl.erp.contract.template.acquire`
3. **第12行**: `/erp/hxl.erp.contract.template.update` → `/erp-mdm/hxl.erp.contract.template.update`
4. **第12行**: `/erp/hxl.erp.contract.template.save` → `/erp-mdm/hxl.erp.contract.template.save`
5. **第12行**: `/erp/hxl.erp.contract.template.update` → `/erp-mdm/hxl.erp.contract.template.update`
6. **第17行**: `/erp/hxl.erp.contract.template.delete` → `/erp-mdm/hxl.erp.contract.template.delete`
7. **第21行**: `/erp/hxl.erp.contract.template.read` → `/erp-mdm/hxl.erp.contract.template.read`
8. **第27行**: `/erp/hxl.erp.contract.template.check` → `/erp-mdm/hxl.erp.contract.template.check`

#### 📁 src\pages\archives\cargoOwner\index.tsx
**业务路径**: archives/cargoOwner/index.tsx
**替换次数**: 7

1. **第60行**: `/erp/hxl.erp.cargo.owner.export` → `/erp-mdm/hxl.erp.cargo.owner.export`
2. **第60行**: `/erp/hxl.erp.cargo.owner.export` → `/erp-mdm/hxl.erp.cargo.owner.export`
3. **第74行**: `/erp/hxl.erp.cargo.owner.import` → `/erp-mdm/hxl.erp.cargo.owner.import`
4. **第74行**: `/erp/hxl.erp.cargo.owner.import` → `/erp-mdm/hxl.erp.cargo.owner.import`
5. **第75行**: `/erp/hxl.erp.cargo.owner.template.download` → `/erp-mdm/hxl.erp.cargo.owner.template.download`
6. **第75行**: `/erp/hxl.erp.cargo.owner.template.download` → `/erp-mdm/hxl.erp.cargo.owner.template.download`
7. **第127行**: `/erp/hxl.erp.cargo.owner.page` → `/erp-mdm/hxl.erp.cargo.owner.page`

#### 📁 src\pages\archives\supplierMainBody\index.tsx
**业务路径**: archives/supplierMainBody/index.tsx
**替换次数**: 7

1. **第13行**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
2. **第20行**: `/erp/hxl.erp.suppliermainbody.delete` → `/erp-mdm/hxl.erp.suppliermainbody.delete`
3. **第20行**: `/erp/hxl.erp.suppliermainbody.delete` → `/erp-mdm/hxl.erp.suppliermainbody.delete`
4. **第24行**: `/erp/hxl.erp.suppliermainbody.save` → `/erp-mdm/hxl.erp.suppliermainbody.save`
5. **第24行**: `/erp/hxl.erp.suppliermainbody.save` → `/erp-mdm/hxl.erp.suppliermainbody.save`
6. **第70行**: `/erp/hxl.erp.suppliermainbody.update` → `/erp-mdm/hxl.erp.suppliermainbody.update`
7. **第70行**: `/erp/hxl.erp.suppliermainbody.update` → `/erp-mdm/hxl.erp.suppliermainbody.update`

#### 📁 src\pages\archives\userBranch\index.tsx
**业务路径**: archives/userBranch/index.tsx
**替换次数**: 7

1. **第14行**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
2. **第23行**: `/erp/hxl.erp.userdept.delete` → `/erp-mdm/hxl.erp.userdept.delete`
3. **第23行**: `/erp/hxl.erp.userdept.delete` → `/erp-mdm/hxl.erp.userdept.delete`
4. **第27行**: `/erp/hxl.erp.userdept.save` → `/erp-mdm/hxl.erp.userdept.save`
5. **第27行**: `/erp/hxl.erp.userdept.save` → `/erp-mdm/hxl.erp.userdept.save`
6. **第85行**: `/erp/hxl.erp.userdept.save` → `/erp-mdm/hxl.erp.userdept.save`
7. **第94行**: `/erp/hxl.erp.userdept.update` → `/erp-mdm/hxl.erp.userdept.update`

#### 📁 src\pages\archives\devicesBrand\index.tsx
**业务路径**: archives/devicesBrand/index.tsx
**替换次数**: 6

1. **第131行**: `/erp/hxl.erp.storehardware.brand.update` → `/erp-mdm/hxl.erp.storehardware.brand.update`
2. **第170行**: `/erp/hxl.erp.storehardware.brand.save` → `/erp-mdm/hxl.erp.storehardware.brand.save`
3. **第170行**: `/erp/hxl.erp.storehardware.brand.save` → `/erp-mdm/hxl.erp.storehardware.brand.save`
4. **第181行**: `/erp/hxl.erp.storehardware.brand.delete` → `/erp-mdm/hxl.erp.storehardware.brand.delete`
5. **第181行**: `/erp/hxl.erp.storehardware.brand.delete` → `/erp-mdm/hxl.erp.storehardware.brand.delete`
6. **第185行**: `/erp/hxl.erp.storehardware.brand.find` → `/erp-mdm/hxl.erp.storehardware.brand.find`

#### 📁 src\pages\archives\goodsBranch\index.tsx
**业务路径**: archives/goodsBranch/index.tsx
**替换次数**: 6

1. **第14行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
2. **第24行**: `/erp/hxl.erp.dept.delete` → `/erp-mdm/hxl.erp.dept.delete`
3. **第24行**: `/erp/hxl.erp.dept.delete` → `/erp-mdm/hxl.erp.dept.delete`
4. **第28行**: `/erp/hxl.erp.dept.save` → `/erp-mdm/hxl.erp.dept.save`
5. **第28行**: `/erp/hxl.erp.dept.save` → `/erp-mdm/hxl.erp.dept.save`
6. **第57行**: `/erp/hxl.erp.dept.update` → `/erp-mdm/hxl.erp.dept.update`

#### 📁 src\pages\archives\goodsBrand\index.tsx
**业务路径**: archives/goodsBrand/index.tsx
**替换次数**: 6

1. **第17行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
2. **第27行**: `/erp/hxl.erp.brand.delete` → `/erp-mdm/hxl.erp.brand.delete`
3. **第27行**: `/erp/hxl.erp.brand.delete` → `/erp-mdm/hxl.erp.brand.delete`
4. **第31行**: `/erp/hxl.erp.brand.save` → `/erp-mdm/hxl.erp.brand.save`
5. **第31行**: `/erp/hxl.erp.brand.save` → `/erp-mdm/hxl.erp.brand.save`
6. **第66行**: `/erp/hxl.erp.brand.update` → `/erp-mdm/hxl.erp.brand.update`

#### 📁 src\pages\archives\goodsUnits\index.tsx
**业务路径**: archives/goodsUnits/index.tsx
**替换次数**: 6

1. **第14行**: `/erp/hxl.erp.itemunit.find` → `/erp-mdm/hxl.erp.itemunit.find`
2. **第24行**: `/erp/hxl.erp.itemunit.delete` → `/erp-mdm/hxl.erp.itemunit.delete`
3. **第24行**: `/erp/hxl.erp.itemunit.delete` → `/erp-mdm/hxl.erp.itemunit.delete`
4. **第28行**: `/erp/hxl.erp.itemunit.save` → `/erp-mdm/hxl.erp.itemunit.save`
5. **第28行**: `/erp/hxl.erp.itemunit.save` → `/erp-mdm/hxl.erp.itemunit.save`
6. **第56行**: `/erp/hxl.erp.itemunit.update` → `/erp-mdm/hxl.erp.itemunit.update`

#### 📁 src\pages\archives\cargoOwnerCenter\server.tsx
**业务路径**: archives/cargoOwnerCenter/server.tsx
**替换次数**: 5

1. **第7行**: `/erp/hxl.erp.delivery.cargo.owner.conf.save` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.save`
2. **第14行**: `/erp/hxl.erp.delivery.cargo.owner.conf.update` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.update`
3. **第21行**: `/erp/hxl.erp.delivery.cargo.owner.conf.batchdelete` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.batchdelete`
4. **第28行**: `/erp/hxl.erp.delivery.cargo.owner.conf.copy` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.copy`
5. **第37行**: `/erp/hxl.erp.delivery.cargo.owner.conf.sync` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.sync`

#### 📁 src\pages\archives\contractMangement\data.tsx
**业务路径**: archives/contractMangement/data.tsx
**替换次数**: 5

1. **第92行**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`
2. **第115行**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`
3. **第153行**: `/erp/hxl.erp.contract.template.page` → `/erp-mdm/hxl.erp.contract.template.page`
4. **第187行**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`
5. **第211行**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`

#### 📁 src\pages\archives\priceTemplate\header\component\batchChange.tsx
**业务路径**: archives/priceTemplate/header/component/batchChange.tsx
**替换次数**: 5

1. **第117行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
2. **第117行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
3. **第118行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
4. **第118行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
5. **第189行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\archives\skuCeilingManagement\server.ts
**业务路径**: archives/skuCeilingManagement/server.ts
**替换次数**: 5

1. **第9行**: `/erp/hxl.erp.org.skulimit.update` → `/erp-mdm/hxl.erp.org.skulimit.update`
2. **第15行**: `/erp/hxl.erp.org.skulimit.export` → `/erp-mdm/hxl.erp.org.skulimit.export`
3. **第20行**: `/erp/hxl.erp.org.skulimit.excludeitem.find` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.find`
4. **第25行**: `/erp/hxl.erp.org.skulimit.excludeitem.export` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.export`
5. **第30行**: `/erp/hxl.erp.org.skulimit.excludeitem.save` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.save`

#### 📁 src\pages\archives\cargoOwner\server.tsx
**业务路径**: archives/cargoOwner/server.tsx
**替换次数**: 4

1. **第6行**: `/erp/hxl.erp.cargo.owner.save` → `/erp-mdm/hxl.erp.cargo.owner.save`
2. **第10行**: `/erp/hxl.erp.cargo.owner.batchdelete` → `/erp-mdm/hxl.erp.cargo.owner.batchdelete`
3. **第14行**: `/erp/hxl.erp.cargo.owner.disabled` → `/erp-mdm/hxl.erp.cargo.owner.disabled`
4. **第18行**: `/erp/hxl.erp.cargo.owner.enabled` → `/erp-mdm/hxl.erp.cargo.owner.enabled`

#### 📁 src\pages\archives\goodsBranch\server.ts
**业务路径**: archives/goodsBranch/server.ts
**替换次数**: 4

1. **第8行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
2. **第14行**: `/erp/hxl.erp.dept.save` → `/erp-mdm/hxl.erp.dept.save`
3. **第20行**: `/erp/hxl.erp.dept.delete` → `/erp-mdm/hxl.erp.dept.delete`
4. **第26行**: `/erp/hxl.erp.dept.update` → `/erp-mdm/hxl.erp.dept.update`

#### 📁 src\pages\archives\organizeManage\server.ts
**业务路径**: archives/organizeManage/server.ts
**替换次数**: 4

1. **第8行**: `/erp/hxl.erp.server.org.check.default` → `/erp-mdm/hxl.erp.server.org.check.default`
2. **第11行**: `/erp/hxl.erp.org.update` → `/erp-mdm/hxl.erp.org.update`
3. **第14行**: `/erp/hxl.erp.org.save` → `/erp-mdm/hxl.erp.org.save`
4. **第17行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\archives\storeArea\header\component\batchChange.tsx
**业务路径**: archives/storeArea/header/component/batchChange.tsx
**替换次数**: 4

1. **第50行**: `/erp/hxl.erp.storearea.store.import` → `/erp-mdm/hxl.erp.storearea.store.import`
2. **第50行**: `/erp/hxl.erp.storearea.store.import` → `/erp-mdm/hxl.erp.storearea.store.import`
3. **第51行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
4. **第51行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`

#### 📁 src\pages\archives\supplierMainBody\server.ts
**业务路径**: archives/supplierMainBody/server.ts
**替换次数**: 4

1. **第7行**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
2. **第12行**: `/erp/hxl.erp.suppliermainbody.save` → `/erp-mdm/hxl.erp.suppliermainbody.save`
3. **第16行**: `/erp/hxl.erp.suppliermainbody.update` → `/erp-mdm/hxl.erp.suppliermainbody.update`
4. **第20行**: `/erp/hxl.erp.suppliermainbody.delete` → `/erp-mdm/hxl.erp.suppliermainbody.delete`

#### 📁 src\pages\archives\userBranch\server.ts
**业务路径**: archives/userBranch/server.ts
**替换次数**: 4

1. **第7行**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
2. **第11行**: `/erp/hxl.erp.userdept.save` → `/erp-mdm/hxl.erp.userdept.save`
3. **第16行**: `/erp/hxl.erp.userdept.delete` → `/erp-mdm/hxl.erp.userdept.delete`
4. **第21行**: `/erp/hxl.erp.userdept.update` → `/erp-mdm/hxl.erp.userdept.update`

#### 📁 src\pages\archives\administrativeRegion\index.tsx
**业务路径**: archives/administrativeRegion/index.tsx
**替换次数**: 3

1. **第15行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
2. **第26行**: `/erp/hxl.erp.store.area.detail.export` → `/erp-mdm/hxl.erp.store.area.detail.export`
3. **第30行**: `/erp/hxl.erp.store.area.detail.page` → `/erp-mdm/hxl.erp.store.area.detail.page`

#### 📁 src\pages\archives\companyHeader\server.ts
**业务路径**: archives/companyHeader/server.ts
**替换次数**: 3

1. **第8行**: `/erp/hxl.erp.companyinvoice.save` → `/erp-mdm/hxl.erp.companyinvoice.save`
2. **第13行**: `/erp/hxl.erp.companyinvoice.delete` → `/erp-mdm/hxl.erp.companyinvoice.delete`
3. **第18行**: `/erp/hxl.erp.companyinvoice.export` → `/erp-mdm/hxl.erp.companyinvoice.export`

#### 📁 src\pages\archives\contractMangement\index.tsx
**业务路径**: archives/contractMangement/index.tsx
**替换次数**: 3

1. **第42行**: `/erp/hxl.erp.contract.page` → `/erp-mdm/hxl.erp.contract.page`
2. **第58行**: `/erp/hxl.erp.contract.page` → `/erp-mdm/hxl.erp.contract.page`
3. **第63行**: `/erp/hxl.erp.contract.whitelist.page` → `/erp-mdm/hxl.erp.contract.whitelist.page`

#### 📁 src\pages\archives\skuCeilingManagement\components\RecordModal.tsx
**业务路径**: archives/skuCeilingManagement/components/RecordModal.tsx
**替换次数**: 3

1. **第56行**: `/erp/hxl.erp.org.skulimit.log.find` → `/erp-mdm/hxl.erp.org.skulimit.log.find`
2. **第56行**: `/erp/hxl.erp.org.skulimit.log.find` → `/erp-mdm/hxl.erp.org.skulimit.log.find`
3. **第57行**: `/erp/hxl.erp.org.skulimit.excludeitem.log.find` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.log.find`

#### 📁 src\pages\archives\cargoOwner\components\modifyRecord\index.tsx
**业务路径**: archives/cargoOwner/components/modifyRecord/index.tsx
**替换次数**: 2

1. **第15行**: `/erp/hxl.erp.cargo.owner.log.page` → `/erp-mdm/hxl.erp.cargo.owner.log.page`
2. **第15行**: `/erp/hxl.erp.cargo.owner.log.page` → `/erp-mdm/hxl.erp.cargo.owner.log.page`

#### 📁 src\pages\archives\cargoOwnerCenter\components\modifyRecord\index.tsx
**业务路径**: archives/cargoOwnerCenter/components/modifyRecord/index.tsx
**替换次数**: 2

1. **第15行**: `/erp/hxl.erp.delivery.cargo.owner.conf.log.page` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.log.page`
2. **第15行**: `/erp/hxl.erp.delivery.cargo.owner.conf.log.page` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.log.page`

#### 📁 src\pages\archives\administrativeRegion\server.ts
**业务路径**: archives/administrativeRegion/server.ts
**替换次数**: 1

1. **第4行**: `/erp/hxl.erp.store.area.detail.export` → `/erp-mdm/hxl.erp.store.area.detail.export`

#### 📁 src\pages\archives\cargoOwner\components\addItem\index.tsx
**业务路径**: archives/cargoOwner/components/addItem/index.tsx
**替换次数**: 1

1. **第108行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\archives\contractTemplateSetup\index.tsx
**业务路径**: archives/contractTemplateSetup/index.tsx
**替换次数**: 1

1. **第111行**: `/erp/hxl.erp.contract.template.page` → `/erp-mdm/hxl.erp.contract.template.page`

#### 📁 src\pages\archives\OBSPaymentEntity\index.tsx
**业务路径**: archives/OBSPaymentEntity/index.tsx
**替换次数**: 1

1. **第146行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\pages\archives\orgBusinessArea\server.ts
**业务路径**: archives/orgBusinessArea/server.ts
**替换次数**: 1

1. **第6行**: `/erp/hxl.erp.org.business.scope.item.add` → `/erp-mdm/hxl.erp.org.business.scope.item.add`

#### 📁 src\pages\archives\priceTemplate\item\index.tsx
**业务路径**: archives/priceTemplate/item/index.tsx
**替换次数**: 1

1. **第510行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\archives\skuCeilingManagement\data.tsx
**业务路径**: archives/skuCeilingManagement/data.tsx
**替换次数**: 1

1. **第36行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\archives\skuCeilingManagement\item\index.tsx
**业务路径**: archives/skuCeilingManagement/item/index.tsx
**替换次数**: 1

1. **第103行**: `/erp/hxl.erp.org.skulimit.read` → `/erp-mdm/hxl.erp.org.skulimit.read`

### 业务页面 - delivery
**总替换次数**: 150
**涉及文件数**: 57

#### 📁 src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx
**业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx
**替换次数**: 11

1. **第133行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
2. **第133行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
3. **第134行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
4. **第134行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
5. **第151行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
6. **第151行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
7. **第152行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
8. **第152行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
9. **第167行**: `/erp/hxl.erp.org.page` → `/erp-mdm/hxl.erp.org.page`
10. **第219行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
11. **第288行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\deliveryanalysis\data.tsx
**业务路径**: delivery/deliveryanalysis/data.tsx
**替换次数**: 10

1. **第37行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第62行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
3. **第62行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
4. **第63行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
5. **第102行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
6. **第138行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
7. **第164行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
8. **第164行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
9. **第165行**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`
10. **第195行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\storeDeliveryPrice\component\batchChange\batchChange.tsx
**业务路径**: delivery/storeDeliveryPrice/component/batchChange/batchChange.tsx
**替换次数**: 9

1. **第116行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
2. **第116行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
3. **第117行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
4. **第117行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
5. **第143行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
6. **第143行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
7. **第144行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
8. **第144行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
9. **第358行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\storeOrders\item\server.ts
**业务路径**: delivery/storeOrders/item/server.ts
**替换次数**: 7

1. **第87行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第95行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
3. **第99行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
4. **第106行**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`
5. **第110行**: `/erp/hxl.erp.store.allcenter.find` → `/erp-mdm/hxl.erp.store.allcenter.find`
6. **第114行**: `/erp/hxl.erp.store.balance.read` → `/erp-mdm/hxl.erp.store.balance.read`
7. **第118行**: `/erp/hxl.erp.item.read` → `/erp-mdm/hxl.erp.item.read`

#### 📁 src\pages\delivery\deliverySpecialPrice\item\index.tsx
**业务路径**: delivery/deliverySpecialPrice/item/index.tsx
**替换次数**: 6

1. **第77行**: `/erp/hxl.erp.delivery.cargo.owner.org.find` → `/erp-mdm/hxl.erp.delivery.cargo.owner.org.find`
2. **第744行**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
3. **第744行**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
4. **第745行**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`
5. **第745行**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`
6. **第1117行**: `/erp/hxl.erp.store.area.find` → `/erp-mdm/hxl.erp.store.area.find`

#### 📁 src\pages\delivery\distributionGross\index.tsx
**业务路径**: delivery/distributionGross/index.tsx
**替换次数**: 6

1. **第81行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第135行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
3. **第162行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
4. **第212行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
5. **第272行**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
6. **第394行**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`

#### 📁 src\pages\delivery\stockForecasts\server.ts
**业务路径**: delivery/stockForecasts/server.ts
**替换次数**: 6

1. **第6行**: `/erp/hxl.erp.store.page` → `/erp-mdm/hxl.erp.store.page`
2. **第35行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
3. **第40行**: `/erp/hxl.erp.storegroup.delete` → `/erp-mdm/hxl.erp.storegroup.delete`
4. **第45行**: `/erp/hxl.erp.storegroup.update` → `/erp-mdm/hxl.erp.storegroup.update`
5. **第49行**: `/erp/hxl.erp.storegroup.save` → `/erp-mdm/hxl.erp.storegroup.save`
6. **第61行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\deliveryCenterStore\server.ts
**业务路径**: delivery/deliveryCenterStore/server.ts
**替换次数**: 5

1. **第25行**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
2. **第27行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
3. **第33行**: `/erp/hxl.erp.store.orgdeliverycenter.find` → `/erp-mdm/hxl.erp.store.orgdeliverycenter.find`
4. **第37行**: `/erp/hxl.erp.store.sharedeliverycenter.find` → `/erp-mdm/hxl.erp.store.sharedeliverycenter.find`
5. **第42行**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`

#### 📁 src\pages\delivery\goodsway\data.ts
**业务路径**: delivery/goodsway/data.ts
**替换次数**: 5

1. **第25行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第49行**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`
3. **第72行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
4. **第97行**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`
5. **第126行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\stockForecasts\component\batchChange.tsx
**业务路径**: delivery/stockForecasts/component/batchChange.tsx
**替换次数**: 5

1. **第78行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
2. **第78行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
3. **第79行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
4. **第79行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
5. **第162行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\deliveryCenterStore\component\batchChange.tsx
**业务路径**: delivery/deliveryCenterStore/component/batchChange.tsx
**替换次数**: 4

1. **第33行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
2. **第33行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
3. **第34行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
4. **第34行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`

#### 📁 src\pages\delivery\deliveryCenterStore\modal.tsx
**业务路径**: delivery/deliveryCenterStore/modal.tsx
**替换次数**: 4

1. **第72行**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
2. **第72行**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
3. **第73行**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`
4. **第73行**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`

#### 📁 src\pages\delivery\deliverySpecialPrice\api.ts
**业务路径**: delivery/deliverySpecialPrice/api.ts
**替换次数**: 4

1. **第74行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
2. **第78行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
3. **第82行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
4. **第86行**: `/erp/hxl.erp.org.findbylevel` → `/erp-mdm/hxl.erp.org.findbylevel`

#### 📁 src\pages\delivery\distributionGross\sever.ts
**业务路径**: delivery/distributionGross/sever.ts
**替换次数**: 3

1. **第55行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第60行**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
3. **第65行**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`

#### 📁 src\pages\delivery\replenishGoodsAnalysis\data.tsx
**业务路径**: delivery/replenishGoodsAnalysis/data.tsx
**替换次数**: 3

1. **第70行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第125行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
3. **第143行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`

#### 📁 src\pages\delivery\saleParam\index.tsx
**业务路径**: delivery/saleParam/index.tsx
**替换次数**: 3

1. **第401行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第1225行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
3. **第1297行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\pages\delivery\strongDataAnalysis\index.tsx
**业务路径**: delivery/strongDataAnalysis/index.tsx
**替换次数**: 3

1. **第42行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第86行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
3. **第265行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

#### 📁 src\pages\delivery\transferDocument\item\index.tsx
**业务路径**: delivery/transferDocument/item/index.tsx
**替换次数**: 3

1. **第625行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
2. **第625行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
3. **第626行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`

#### 📁 src\pages\delivery\basketStat\data.ts
**业务路径**: delivery/basketStat/data.ts
**替换次数**: 2

1. **第59行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
2. **第80行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\delivery\collectDocument\data.tsx
**业务路径**: delivery/collectDocument/data.tsx
**替换次数**: 2

1. **第147行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
2. **第190行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

#### 📁 src\pages\delivery\collectDocument\item.tsx
**业务路径**: delivery/collectDocument/item.tsx
**替换次数**: 2

1. **第75行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第101行**: `/erp/hxl.erp.cargo.owner.pageforinner` → `/erp-mdm/hxl.erp.cargo.owner.pageforinner`

#### 📁 src\pages\delivery\deliverySpecialPriceAnalysis\index.tsx
**业务路径**: delivery/deliverySpecialPriceAnalysis/index.tsx
**替换次数**: 2

1. **第45行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第82行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\delivery\directSupplyPoint\index.tsx
**业务路径**: delivery/directSupplyPoint/index.tsx
**替换次数**: 2

1. **第52行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第355行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

#### 📁 src\pages\delivery\marketingCampaign\item.tsx
**业务路径**: delivery/marketingCampaign/item.tsx
**替换次数**: 2

1. **第53行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第79行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`

#### 📁 src\pages\delivery\receivingApplication\data.tsx
**业务路径**: delivery/receivingApplication/data.tsx
**替换次数**: 2

1. **第193行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第251行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

#### 📁 src\pages\delivery\receivingApplication\item.tsx
**业务路径**: delivery/receivingApplication/item.tsx
**替换次数**: 2

1. **第78行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第104行**: `/erp/hxl.erp.cargo.owner.pageforinner` → `/erp-mdm/hxl.erp.cargo.owner.pageforinner`

#### 📁 src\pages\delivery\replenishTemplate\item\index.tsx
**业务路径**: delivery/replenishTemplate/item/index.tsx
**替换次数**: 2

1. **第149行**: `/erp/hxl.erp.item.short.page` → `/erp-mdm/hxl.erp.item.short.page`
2. **第619行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\stockForecasts\index.tsx
**业务路径**: delivery/stockForecasts/index.tsx
**替换次数**: 2

1. **第199行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
2. **第233行**: `/erp/hxl.erp.store.page` → `/erp-mdm/hxl.erp.store.page`

#### 📁 src\pages\delivery\stockTypeSetting\item\index.tsx
**业务路径**: delivery/stockTypeSetting/item/index.tsx
**替换次数**: 2

1. **第159行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第464行**: `/erp/hxl.erp.file.upload` → `/erp-mdm/hxl.erp.file.upload`

#### 📁 src\pages\delivery\storeDeliveryDay\item.tsx
**业务路径**: delivery/storeDeliveryDay/item.tsx
**替换次数**: 2

1. **第191行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
2. **第191行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`

#### 📁 src\pages\delivery\storeOrderingDate\item\index.tsx
**业务路径**: delivery/storeOrderingDate/item/index.tsx
**替换次数**: 2

1. **第150行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
2. **第150行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`

#### 📁 src\pages\delivery\supplyAnalyze\server.ts
**业务路径**: delivery/supplyAnalyze/server.ts
**替换次数**: 2

1. **第13行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第18行**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`

#### 📁 src\pages\delivery\advancePosition\server.ts
**业务路径**: delivery/advancePosition/server.ts
**替换次数**: 1

1. **第6行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`

#### 📁 src\pages\delivery\deliveryanalysis\server.ts
**业务路径**: delivery/deliveryanalysis/server.ts
**替换次数**: 1

1. **第20行**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`

#### 📁 src\pages\delivery\deliveryCenterStore\index.tsx
**业务路径**: delivery/deliveryCenterStore/index.tsx
**替换次数**: 1

1. **第593行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\pages\delivery\deliveryDetails\data.tsx
**业务路径**: delivery/deliveryDetails/data.tsx
**替换次数**: 1

1. **第351行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\deliveryDetails\index.tsx
**业务路径**: delivery/deliveryDetails/index.tsx
**替换次数**: 1

1. **第98行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\delivery\deliveryPriceMange\header\index.tsx
**业务路径**: delivery/deliveryPriceMange/header/index.tsx
**替换次数**: 1

1. **第87行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\pages\delivery\deliveryPriceMange\item\components\xlbBaseGoods\server.ts
**业务路径**: delivery/deliveryPriceMange/item/components/xlbBaseGoods/server.ts
**替换次数**: 1

1. **第45行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`

#### 📁 src\pages\delivery\deliveryPriceMange\server.ts
**业务路径**: delivery/deliveryPriceMange/server.ts
**替换次数**: 1

1. **第66行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`

#### 📁 src\pages\delivery\forceDeliveryRule\components\addPCRules\index.tsx
**业务路径**: delivery/forceDeliveryRule/components/addPCRules/index.tsx
**替换次数**: 1

1. **第45行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\forceDeliveryRule\components\addRules\index.tsx
**业务路径**: delivery/forceDeliveryRule/components/addRules/index.tsx
**替换次数**: 1

1. **第52行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\goodsway\index.tsx
**业务路径**: delivery/goodsway/index.tsx
**替换次数**: 1

1. **第160行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\delivery\marketingCampaign\index.tsx
**业务路径**: delivery/marketingCampaign/index.tsx
**替换次数**: 1

1. **第85行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\pages\delivery\marketingCampaign\server.ts
**业务路径**: delivery/marketingCampaign/server.ts
**替换次数**: 1

1. **第59行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`

#### 📁 src\pages\delivery\stockPrediction\data.ts
**业务路径**: delivery/stockPrediction/data.ts
**替换次数**: 1

1. **第125行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\storeDeliveryDay\index.tsx
**业务路径**: delivery/storeDeliveryDay/index.tsx
**替换次数**: 1

1. **第55行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\pages\delivery\storeDeliveryPrice\component\copy\copy.tsx
**业务路径**: delivery/storeDeliveryPrice/component/copy/copy.tsx
**替换次数**: 1

1. **第145行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\delivery\storeOrderingDate\header\index.tsx
**业务路径**: delivery/storeOrderingDate/header/index.tsx
**替换次数**: 1

1. **第52行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\pages\delivery\storeOrders\components\batchOrder\batchOrder.tsx
**业务路径**: delivery/storeOrders/components/batchOrder/batchOrder.tsx
**替换次数**: 1

1. **第402行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`

#### 📁 src\pages\delivery\storeOrders\components\deliveryOrder\deliveryOrder.tsx
**业务路径**: delivery/storeOrders/components/deliveryOrder/deliveryOrder.tsx
**替换次数**: 1

1. **第276行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`

#### 📁 src\pages\delivery\storeOrders\components\uploadPhotoGroup.tsx
**业务路径**: delivery/storeOrders/components/uploadPhotoGroup.tsx
**替换次数**: 1

1. **第86行**: `/erp/hxl.erp.file.delete` → `/erp-mdm/hxl.erp.file.delete`

#### 📁 src\pages\delivery\storeOrders\item\index.tsx
**业务路径**: delivery/storeOrders/item/index.tsx
**替换次数**: 1

1. **第425行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`

#### 📁 src\pages\delivery\transferDocument\components\batchOrder\batchOrder.tsx
**业务路径**: delivery/transferDocument/components/batchOrder/batchOrder.tsx
**替换次数**: 1

1. **第453行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`

#### 📁 src\pages\delivery\transferDocument\index.tsx
**业务路径**: delivery/transferDocument/index.tsx
**替换次数**: 1

1. **第115行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\delivery\transferDocument\server.ts
**业务路径**: delivery/transferDocument/server.ts
**替换次数**: 1

1. **第58行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`

#### 📁 src\pages\delivery\transferWay\data.ts
**业务路径**: delivery/transferWay/data.ts
**替换次数**: 1

1. **第68行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

### 配置常量 - constants
**总替换次数**: 141
**涉及文件数**: 1

#### 📁 src\constants\baseDataConfig.tsx
**业务路径**: constants/baseDataConfig.tsx
**替换次数**: 141

1. **第17行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第81行**: `/erp/hxl.erp.storearea.find` → `/erp-mdm/hxl.erp.storearea.find`
3. **第105行**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
4. **第130行**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
5. **第170行**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
6. **第210行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
7. **第233行**: `/erp/hxl.erp.storeareacategory.find` → `/erp-mdm/hxl.erp.storeareacategory.find`
8. **第253行**: `/erp/hxl.erp.businessarea.detail.find` → `/erp-mdm/hxl.erp.businessarea.detail.find`
9. **第254行**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
10. **第262行**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
11. **第273行**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
12. **第290行**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
13. **第391行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
14. **第406行**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
15. **第424行**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
16. **第437行**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
17. **第480行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
18. **第481行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
19. **第482行**: `/erp/hxl.erp.store.ids.find` → `/erp-mdm/hxl.erp.store.ids.find`
20. **第552行**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
21. **第569行**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
22. **第611行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
23. **第745行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
24. **第750行**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
25. **第760行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
26. **第779行**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
27. **第785行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
28. **第794行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
29. **第798行**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
30. **第802行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
31. **第807行**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
32. **第812行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
33. **第852行**: `/erp/hxl.erp.item.short.page` → `/erp-mdm/hxl.erp.item.short.page`
34. **第853行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
35. **第920行**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
36. **第953行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
37. **第958行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
38. **第963行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
39. **第968行**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
40. **第973行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
41. **第978行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
42. **第982行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
43. **第986行**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
44. **第1000行**: `/erp/hxl.erp.supplier.short.page` → `/erp-mdm/hxl.erp.supplier.short.page`
45. **第1001行**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
46. **第1060行**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
47. **第1061行**: `/erp/hxl.erp.customattribute.show.find` → `/erp-mdm/hxl.erp.customattribute.show.find`
48. **第1128行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
49. **第1150行**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
50. **第1248行**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
51. **第1258行**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
52. **第1366行**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
53. **第1376行**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
54. **第1398行**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
55. **第1421行**: `/erp/hxl.erp.labour.page` → `/erp-mdm/hxl.erp.labour.page`
56. **第1447行**: `/erp/hxl.erp.user.page` → `/erp-mdm/hxl.erp.user.page`
57. **第1529行**: `/erp/hxl.erp.storehouse.page` → `/erp-mdm/hxl.erp.storehouse.page`
58. **第1530行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
59. **第1677行**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
60. **第1694行**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
61. **第1732行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
62. **第1867行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
63. **第1872行**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
64. **第1881行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
65. **第1900行**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
66. **第1906行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
67. **第1915行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
68. **第1919行**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
69. **第1923行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
70. **第1928行**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
71. **第1933行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
72. **第1969行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
73. **第1993行**: `/erp/hxl.erp.client.page` → `/erp-mdm/hxl.erp.client.page`
74. **第1994行**: `/erp/hxl.erp.clientcategory.find` → `/erp-mdm/hxl.erp.clientcategory.find`
75. **第2033行**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
76. **第2132行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
77. **第2360行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
78. **第2450行**: `/erp/hxl.erp.item.short.page` → `/erp-mdm/hxl.erp.item.short.page`
79. **第2451行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
80. **第2515行**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
81. **第2626行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
82. **第2631行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
83. **第2636行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
84. **第2641行**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
85. **第2646行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
86. **第2651行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
87. **第2655行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
88. **第2659行**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
89. **第2675行**: `/erp/hxl.erp.businessscope.short.find` → `/erp-mdm/hxl.erp.businessscope.short.find`
90. **第2731行**: `/erp/hxl.erp.businessscope.short.find` → `/erp-mdm/hxl.erp.businessscope.short.find`
91. **第2732行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
92. **第2781行**: `/erp/hxl.erp.businessscope.find` → `/erp-mdm/hxl.erp.businessscope.find`
93. **第2809行**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
94. **第2824行**: `/erp/hxl.erp.businessscope.short.find` → `/erp-mdm/hxl.erp.businessscope.short.find`
95. **第2905行**: `/erp/hxl.erp.user.page` → `/erp-mdm/hxl.erp.user.page`
96. **第2926行**: `/erp/hxl.erp.user.page` → `/erp-mdm/hxl.erp.user.page`
97. **第2960行**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
98. **第2986行**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
99. **第3022行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
100. **第3096行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
101. **第3185行**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
102. **第3202行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
103. **第3239行**: `/erp/hxl.erp.cargo.owner.pageforinner` → `/erp-mdm/hxl.erp.cargo.owner.pageforinner`
104. **第3320行**: `/erp/hxl.erp.delivery.cargo.owner.conf.readbystoreIds` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.readbystoreIds`
105. **第3411行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
106. **第3477行**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
107. **第3513行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
108. **第3518行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
109. **第3523行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
110. **第3528行**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
111. **第3533行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
112. **第3538行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
113. **第3542行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
114. **第3546行**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
115. **第3584行**: `/erp/hxl.erp.item.short.page` → `/erp-mdm/hxl.erp.item.short.page`
116. **第3585行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
117. **第3586行**: `/erp/hxl.erp.item.short.ids.find` → `/erp-mdm/hxl.erp.item.short.ids.find`
118. **第3673行**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
119. **第3706行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
120. **第3711行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
121. **第3716行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
122. **第3721行**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
123. **第3726行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
124. **第3731行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
125. **第3735行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
126. **第3739行**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
127. **第3757行**: `/erp/hxl.erp.org.item.page` → `/erp-mdm/hxl.erp.org.item.page`
128. **第3758行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
129. **第3829行**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
130. **第3857行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
131. **第3893行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
132. **第3898行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
133. **第3903行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
134. **第3908行**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
135. **第3913行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
136. **第3918行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
137. **第3922行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
138. **第3926行**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
139. **第3940行**: `/erp/hxl.erp.cargo.owner.page` → `/erp-mdm/hxl.erp.cargo.owner.page`
140. **第4005行**: `/erp/hxl.erp.supplier.producer.find` → `/erp-mdm/hxl.erp.supplier.producer.find`
141. **第4077行**: `/erp/hxl.erp.supplier.producerandexecutivestandard.find` → `/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find`

### 数据配置 - data
**总替换次数**: 86
**涉及文件数**: 21

#### 📁 src\data\common\fieldListConfig.tsx
**业务路径**: data/common/fieldListConfig.tsx
**替换次数**: 38

1. **第393行**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
2. **第415行**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
3. **第440行**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
4. **第462行**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
5. **第487行**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
6. **第509行**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
7. **第534行**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
8. **第556行**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
9. **第710行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
10. **第733行**: `/erp/hxl.erp.org.tree.invoice` → `/erp-mdm/hxl.erp.org.tree.invoice`
11. **第763行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
12. **第785行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
13. **第807行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
14. **第830行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
15. **第1249行**: `/erp/hxl.erp.printtemplate.menus` → `/erp-mdm/hxl.erp.printtemplate.menus`
16. **第1350行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
17. **第1501行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
18. **第1501行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
19. **第1575行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
20. **第1575行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
21. **第1630行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
22. **第1630行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
23. **第1658行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
24. **第1658行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
25. **第1852行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
26. **第1852行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
27. **第1994行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
28. **第2164行**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
29. **第2387行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
30. **第2416行**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
31. **第2445行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
32. **第2445行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
33. **第2472行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
34. **第2555行**: `/erp/hxl.erp.hardwarecategory.find` → `/erp-mdm/hxl.erp.hardwarecategory.find`
35. **第2555行**: `/erp/hxl.erp.hardwarecategory.find` → `/erp-mdm/hxl.erp.hardwarecategory.find`
36. **第2595行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
37. **第2789行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
38. **第2816行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`

#### 📁 src\data\common\fieldModule\userManage.tsx
**业务路径**: data/common/fieldModule/userManage.tsx
**替换次数**: 7

1. **第34行**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
2. **第54行**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
3. **第68行**: `/erp/hxl.erp.storearea.find` → `/erp-mdm/hxl.erp.storearea.find`
4. **第134行**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
5. **第154行**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
6. **第173行**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
7. **第192行**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`

#### 📁 src\data\common\fieldModule\goodsFiles.tsx
**业务路径**: data/common/fieldModule/goodsFiles.tsx
**替换次数**: 6

1. **第18行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
2. **第18行**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
3. **第41行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
4. **第41行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
5. **第64行**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`
6. **第64行**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`

#### 📁 src\data\common\fieldModule\newYearGoodsPlanDetails.tsx
**业务路径**: data/common/fieldModule/newYearGoodsPlanDetails.tsx
**替换次数**: 4

1. **第47行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第47行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
3. **第194行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
4. **第194行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\data\common\fieldModule\deliveryCenterStore.tsx
**业务路径**: data/common/fieldModule/deliveryCenterStore.tsx
**替换次数**: 3

1. **第17行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
2. **第37行**: `/erp/hxl.erp.store.sharedeliverycenter.find` → `/erp-mdm/hxl.erp.store.sharedeliverycenter.find`
3. **第58行**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`

#### 📁 src\data\common\fieldModule\interWarehouseTransfer.tsx
**业务路径**: data/common/fieldModule/interWarehouseTransfer.tsx
**替换次数**: 3

1. **第49行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第104行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
3. **第169行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

#### 📁 src\data\common\fieldModule\returnRateStatistics.tsx
**业务路径**: data/common/fieldModule/returnRateStatistics.tsx
**替换次数**: 3

1. **第38行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第38行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
3. **第188行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`

#### 📁 src\data\common\fieldModule\storeOrder.tsx
**业务路径**: data/common/fieldModule/storeOrder.tsx
**替换次数**: 3

1. **第60行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
2. **第93行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
3. **第144行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

#### 📁 src\data\common\fieldModule\devicesBrand.tsx
**业务路径**: data/common/fieldModule/devicesBrand.tsx
**替换次数**: 2

1. **第16行**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`
2. **第16行**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`

#### 📁 src\data\common\fieldModule\payMode.tsx
**业务路径**: data/common/fieldModule/payMode.tsx
**替换次数**: 2

1. **第98行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第98行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\data\common\fieldModule\storeDeviceManage.tsx
**业务路径**: data/common/fieldModule/storeDeviceManage.tsx
**替换次数**: 2

1. **第18行**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`
2. **第18行**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`

#### 📁 src\data\common\fieldModule\storeManage.tsx
**业务路径**: data/common/fieldModule/storeManage.tsx
**替换次数**: 2

1. **第210行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
2. **第238行**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`

#### 📁 src\data\common\fieldModule\supplier.tsx
**业务路径**: data/common/fieldModule/supplier.tsx
**替换次数**: 2

1. **第41行**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
2. **第63行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\data\common\fieldModule\tiktokcouponrefund.tsx
**业务路径**: data/common/fieldModule/tiktokcouponrefund.tsx
**替换次数**: 2

1. **第26行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第26行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\data\common\fieldModule\businessRange.tsx
**业务路径**: data/common/fieldModule/businessRange.tsx
**替换次数**: 1

1. **第16行**: `/erp/hxl.erp.businessscopecategory.find` → `/erp-mdm/hxl.erp.businessscopecategory.find`

#### 📁 src\data\common\fieldModule\purchaseBusinessRange.tsx
**业务路径**: data/common/fieldModule/purchaseBusinessRange.tsx
**替换次数**: 1

1. **第15行**: `/erp/hxl.erp.businessscopecategory.find` → `/erp-mdm/hxl.erp.businessscopecategory.find`

#### 📁 src\data\common\fieldModule\purchaseOrdering.tsx
**业务路径**: data/common/fieldModule/purchaseOrdering.tsx
**替换次数**: 1

1. **第70行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

#### 📁 src\data\common\fieldModule\stockCheckOrder.tsx
**业务路径**: data/common/fieldModule/stockCheckOrder.tsx
**替换次数**: 1

1. **第57行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

#### 📁 src\data\common\fieldModule\storeArea.tsx
**业务路径**: data/common/fieldModule/storeArea.tsx
**替换次数**: 1

1. **第32行**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`

#### 📁 src\data\common\fieldModule\storeDeilveryPrice.tsx
**业务路径**: data/common/fieldModule/storeDeilveryPrice.tsx
**替换次数**: 1

1. **第69行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\data\common\fieldModule\wholesaleCustomer.tsx
**业务路径**: data/common/fieldModule/wholesaleCustomer.tsx
**替换次数**: 1

1. **第15行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

### 业务页面 - wholesale
**总替换次数**: 38
**涉及文件数**: 15

#### 📁 src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx
**业务路径**: wholesale/wholesalePrice/components/batchChange/batchChange.tsx
**替换次数**: 10

1. **第132行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
2. **第132行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
3. **第133行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
4. **第133行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
5. **第149行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
6. **第149行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
7. **第150行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
8. **第150行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
9. **第166行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
10. **第373行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\wholesale\customerGoodsAttributes\component\batchChange\batchChange.tsx
**业务路径**: wholesale/customerGoodsAttributes/component/batchChange/batchChange.tsx
**替换次数**: 9

1. **第98行**: `/erp/hxl.erp.clientname.import` → `/erp-mdm/hxl.erp.clientname.import`
2. **第98行**: `/erp/hxl.erp.clientname.import` → `/erp-mdm/hxl.erp.clientname.import`
3. **第99行**: `/erp/hxl.erp.clientnametemplate.download` → `/erp-mdm/hxl.erp.clientnametemplate.download`
4. **第99行**: `/erp/hxl.erp.clientnametemplate.download` → `/erp-mdm/hxl.erp.clientnametemplate.download`
5. **第115行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
6. **第115行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
7. **第116行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
8. **第116行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
9. **第222行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\wholesale\wholesaleOrgSetting\modal.tsx
**业务路径**: wholesale/wholesaleOrgSetting/modal.tsx
**替换次数**: 4

1. **第62行**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
2. **第62行**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
3. **第63行**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`
4. **第63行**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`

#### 📁 src\pages\wholesale\wholeSaleAnalyze\data.tsx
**业务路径**: wholesale/wholeSaleAnalyze/data.tsx
**替换次数**: 2

1. **第138行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第191行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\wholesale\wholeSaleAnalyze\server.ts
**业务路径**: wholesale/wholeSaleAnalyze/server.ts
**替换次数**: 2

1. **第13行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第18行**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`

#### 📁 src\pages\wholesale\wholesalePrice\data.tsx
**业务路径**: wholesale/wholesalePrice/data.tsx
**替换次数**: 2

1. **第103行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
2. **第183行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\wholesale\buyParam\header\index.tsx
**业务路径**: wholesale/buyParam/header/index.tsx
**替换次数**: 1

1. **第157行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\wholesale\wholeSaleAnalyze\index.tsx
**业务路径**: wholesale/wholeSaleAnalyze/index.tsx
**替换次数**: 1

1. **第380行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\wholesale\wholeSaleDetail\data.ts
**业务路径**: wholesale/wholeSaleDetail/data.ts
**替换次数**: 1

1. **第154行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\wholesale\wholeSaleDetail\index.tsx
**业务路径**: wholesale/wholeSaleDetail/index.tsx
**替换次数**: 1

1. **第156行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\wholesale\wholeSaleDetail\server.ts
**业务路径**: wholesale/wholeSaleDetail/server.ts
**替换次数**: 1

1. **第11行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

#### 📁 src\pages\wholesale\wholesaleOrgSetting\index.tsx
**业务路径**: wholesale/wholesaleOrgSetting/index.tsx
**替换次数**: 1

1. **第185行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\pages\wholesale\wholesalePrice\components\copy\copy.tsx
**业务路径**: wholesale/wholesalePrice/components/copy/copy.tsx
**替换次数**: 1

1. **第410行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\wholesale\wholesalePriceAdjustment\data.ts
**业务路径**: wholesale/wholesalePriceAdjustment/data.ts
**替换次数**: 1

1. **第83行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\pages\wholesale\wholesalePriceAdjustment\header\index.tsx
**业务路径**: wholesale/wholesalePriceAdjustment/header/index.tsx
**替换次数**: 1

1. **第321行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

### 业务页面 - procurement
**总替换次数**: 33
**涉及文件数**: 15

#### 📁 src\pages\procurement\supplierRelationshipManagement\header\index.tsx
**业务路径**: procurement/supplierRelationshipManagement/header/index.tsx
**替换次数**: 10

1. **第62行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
2. **第962行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
3. **第962行**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
4. **第963行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
5. **第963行**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
6. **第980行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
7. **第980行**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
8. **第981行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
9. **第981行**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
10. **第1100行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\pages\procurement\purchaseReport\data.tsx
**业务路径**: procurement/purchaseReport/data.tsx
**替换次数**: 4

1. **第157行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第207行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
3. **第284行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
4. **第438行**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`

#### 📁 src\pages\procurement\supplierRelationshipManagement\server.ts
**业务路径**: procurement/supplierRelationshipManagement/server.ts
**替换次数**: 3

1. **第9行**: `/erp/hxl.erp.supplier.producerandexecutivestandard.find` → `/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find`
2. **第37行**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
3. **第41行**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`

#### 📁 src\pages\procurement\purchaseLatestPrice\index.tsx
**业务路径**: procurement/purchaseLatestPrice/index.tsx
**替换次数**: 2

1. **第163行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第250行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\procurement\purchaseReport\server.ts
**业务路径**: procurement/purchaseReport/server.ts
**替换次数**: 2

1. **第73行**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`
2. **第79行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

#### 📁 src\pages\procurement\purchaseShare\header\server.ts
**业务路径**: procurement/purchaseShare/header/server.ts
**替换次数**: 2

1. **第44行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
2. **第49行**: `/erp/hxl.erp.store.all.find` → `/erp-mdm/hxl.erp.store.all.find`

#### 📁 src\pages\procurement\stockPlan\index.tsx
**业务路径**: procurement/stockPlan/index.tsx
**替换次数**: 2

1. **第206行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第206行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\procurement\orderParamsConfig\data.ts
**业务路径**: procurement/orderParamsConfig/data.ts
**替换次数**: 1

1. **第20行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\procurement\orderValidDay\data.tsx
**业务路径**: procurement/orderValidDay/data.tsx
**替换次数**: 1

1. **第13行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\procurement\orderValidDay\index.tsx
**业务路径**: procurement/orderValidDay/index.tsx
**替换次数**: 1

1. **第126行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

#### 📁 src\pages\procurement\purchasePrice\data.ts
**业务路径**: procurement/purchasePrice/data.ts
**替换次数**: 1

1. **第72行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\procurement\purchasePrice\server.ts
**业务路径**: procurement/purchasePrice/server.ts
**替换次数**: 1

1. **第13行**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`

#### 📁 src\pages\procurement\purchaseShare\header\index.tsx
**业务路径**: procurement/purchaseShare/header/index.tsx
**替换次数**: 1

1. **第184行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

#### 📁 src\pages\procurement\stockPlan\data.ts
**业务路径**: procurement/stockPlan/data.ts
**替换次数**: 1

1. **第46行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\procurement\supplierRelationshipManagement\header\components\Additem\index.tsx
**业务路径**: procurement/supplierRelationshipManagement/header/components/Additem/index.tsx
**替换次数**: 1

1. **第383行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

### 业务页面 - stock
**总替换次数**: 17
**涉及文件数**: 10

#### 📁 src\pages\stock\stockLog\data.tsx
**业务路径**: stock/stockLog/data.tsx
**替换次数**: 3

1. **第189行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第257行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
3. **第317行**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`

#### 📁 src\pages\stock\stockSearch\data.tsx
**业务路径**: stock/stockSearch/data.tsx
**替换次数**: 3

1. **第140行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第203行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
3. **第290行**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`

#### 📁 src\pages\stock\stockSearch\server.ts
**业务路径**: stock/stockSearch/server.ts
**替换次数**: 3

1. **第26行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第30行**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
3. **第34行**: `/erp/hxl.erp.category.maxlevel.read` → `/erp-mdm/hxl.erp.category.maxlevel.read`

#### 📁 src\pages\stock\unsalableItem\data.tsx
**业务路径**: stock/unsalableItem/data.tsx
**替换次数**: 2

1. **第83行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第109行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\stock\stockCollaborativeSharingr\components\addModel\index.tsx
**业务路径**: stock/stockCollaborativeSharingr/components/addModel/index.tsx
**替换次数**: 1

1. **第159行**: `/erp/hxl.erp.store.cargoownerdelivery.short.page` → `/erp-mdm/hxl.erp.store.cargoownerdelivery.short.page`

#### 📁 src\pages\stock\stockCollaborativeSharingr\data.ts
**业务路径**: stock/stockCollaborativeSharingr/data.ts
**替换次数**: 1

1. **第57行**: `/erp/hxl.erp.store.cargoownerdelivery.short.page` → `/erp-mdm/hxl.erp.store.cargoownerdelivery.short.page`

#### 📁 src\pages\stock\stockDyingPeriod\data.ts
**业务路径**: stock/stockDyingPeriod/data.ts
**替换次数**: 1

1. **第285行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\stock\stockDyingPeriod\server.ts
**业务路径**: stock/stockDyingPeriod/server.ts
**替换次数**: 1

1. **第5行**: `/erp/hxl.erp.storehouse.page` → `/erp-mdm/hxl.erp.storehouse.page`

#### 📁 src\pages\stock\stockLog\server.ts
**业务路径**: stock/stockLog/server.ts
**替换次数**: 1

1. **第21行**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`

#### 📁 src\pages\stock\unsalableItem\server.ts
**业务路径**: stock/unsalableItem/server.ts
**替换次数**: 1

1. **第9行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

### 业务页面 - purchase
**总替换次数**: 12
**涉及文件数**: 5

#### 📁 src\pages\purchase\purchaseReplenishAnalysis\data.tsx
**业务路径**: purchase/purchaseReplenishAnalysis/data.tsx
**替换次数**: 4

1. **第84行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第149行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
3. **第200行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
4. **第260行**: `/erp/hxl.erp.category.findmaxlevel` → `/erp-mdm/hxl.erp.category.findmaxlevel`

#### 📁 src\pages\purchase\orderWatch\data.tsx
**业务路径**: purchase/orderWatch/data.tsx
**替换次数**: 3

1. **第94行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
2. **第171行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
3. **第240行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\purchase\mustSellGoodsDetail\data.tsx
**业务路径**: purchase/mustSellGoodsDetail/data.tsx
**替换次数**: 2

1. **第21行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第78行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\purchase\mustSellGoodsManagement\data.tsx
**业务路径**: purchase/mustSellGoodsManagement/data.tsx
**替换次数**: 2

1. **第52行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
2. **第117行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\purchase\newItemPurchasePlan\data.tsx
**业务路径**: purchase/newItemPurchasePlan/data.tsx
**替换次数**: 1

1. **第244行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`

### 业务页面 - purchasement
**总替换次数**: 9
**涉及文件数**: 2

#### 📁 src\pages\purchasement\itemSku\server.ts
**业务路径**: purchasement/itemSku/server.ts
**替换次数**: 6

1. **第4行**: `/erp/hxl.erp.item.summary` → `/erp-mdm/hxl.erp.item.summary`
2. **第8行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
3. **第12行**: `/erp/hxl.erp.item.summary.read` → `/erp-mdm/hxl.erp.item.summary.read`
4. **第16行**: `/erp/hxl.erp.item.summary.initial.save` → `/erp-mdm/hxl.erp.item.summary.initial.save`
5. **第20行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
6. **第23行**: `/erp/hxl.erp.item.summary.read` → `/erp-mdm/hxl.erp.item.summary.read`

#### 📁 src\pages\purchasement\itemSku\header\index.tsx
**业务路径**: purchasement/itemSku/header/index.tsx
**替换次数**: 3

1. **第123行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
2. **第361行**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
3. **第385行**: `/erp/hxl.erp.item.summary` → `/erp-mdm/hxl.erp.item.summary`

### 业务页面 - dataAnalysis
**总替换次数**: 5
**涉及文件数**: 3

#### 📁 src\pages\dataAnalysis\heightLowInventoryGoods\data.tsx
**业务路径**: dataAnalysis/heightLowInventoryGoods/data.tsx
**替换次数**: 2

1. **第100行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第123行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

#### 📁 src\pages\dataAnalysis\profitLossStatistic\server.tsx
**业务路径**: dataAnalysis/profitLossStatistic/server.tsx
**替换次数**: 2

1. **第5行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
2. **第10行**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`

#### 📁 src\pages\dataAnalysis\profitLossStatistic\data.tsx
**业务路径**: dataAnalysis/profitLossStatistic/data.tsx
**替换次数**: 1

1. **第166行**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`

### 公共组件 - components
**总替换次数**: 3
**涉及文件数**: 2

#### 📁 src\components\relateStoreCreate.tsx
**业务路径**: components/relateStoreCreate.tsx
**替换次数**: 2

1. **第15行**: `/erp/hxl.erp.businessarea.store.find` → `/erp-mdm/hxl.erp.businessarea.store.find`
2. **第15行**: `/erp/hxl.erp.businessarea.store.find` → `/erp-mdm/hxl.erp.businessarea.store.find`

#### 📁 src\components\common\xlbOrgids\selectOrg.tsx
**业务路径**: components/common/xlbOrgids/selectOrg.tsx
**替换次数**: 1

1. **第30行**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`

### 其他 - src
**总替换次数**: 2
**涉及文件数**: 1

#### 📁 src\provider.tsx
**业务路径**: src\provider.tsx
**替换次数**: 2

1. **第42行**: `/erp/hxl.erp.usercolumn.get` → `/erp-mdm/hxl.erp.usercolumn.get`
2. **第43行**: `/erp/hxl.erp.usercolumn.update` → `/erp-mdm/hxl.erp.usercolumn.update`

### 工具函数 - utils
**总替换次数**: 2
**涉及文件数**: 2

#### 📁 src\utils\purchaseUrl.ts
**业务路径**: utils/purchaseUrl.ts
**替换次数**: 1

1. **第14行**: `/erp/hxl.erp.supplier.producerandexecutivestandard.find` → `/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find`

#### 📁 src\utils\utils.ts
**业务路径**: utils/utils.ts
**替换次数**: 1

1. **第18行**: `/erp/hxl.erp.usercolumn.get` → `/erp-mdm/hxl.erp.usercolumn.get`

### 其他 - api
**总替换次数**: 1
**涉及文件数**: 1

#### 📁 src\api\common.ts
**业务路径**: src\api\common.ts
**替换次数**: 1

1. **第14行**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`

### 其他 - hooks
**总替换次数**: 1
**涉及文件数**: 1

#### 📁 src\hooks\useBaseParams.ts
**业务路径**: src\hooks\useBaseParams.ts
**替换次数**: 1

1. **第32行**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`

### 服务接口 - services
**总替换次数**: 1
**涉及文件数**: 1

#### 📁 src\services\system\index.ts
**业务路径**: services/system/index.ts
**替换次数**: 1

1. **第10行**: `/erp/hxl.erp.user.account.login` → `/erp-mdm/hxl.erp.user.account.login`


## 按文件统计替换次数

1. **src\constants\baseDataConfig.tsx**: 141次替换
2. **src\data\common\fieldListConfig.tsx**: 38次替换
3. **src\pages\archives\cargoOwnerCenter\index.tsx**: 15次替换
4. **src\pages\archives\storeArea\header\index.tsx**: 14次替换
5. **src\pages\archives\storeArea\server.ts**: 12次替换
6. **src\pages\archives\skuCeilingManagement\header\index.tsx**: 11次替换
7. **src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx**: 11次替换
8. **src\pages\archives\companyHeader\index.tsx**: 10次替换
9. **src\pages\archives\contractMangement\server.ts**: 10次替换
10. **src\pages\archives\goodsOrder\component\batchChange.tsx**: 10次替换
11. **src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx**: 10次替换
12. **src\pages\archives\organizeManage\index.tsx**: 10次替换
13. **src\pages\delivery\deliveryanalysis\data.tsx**: 10次替换
14. **src\pages\procurement\supplierRelationshipManagement\header\index.tsx**: 10次替换
15. **src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx**: 10次替换
16. **src\pages\archives\devicesBrand\server.ts**: 9次替换
17. **src\pages\archives\orgBusinessArea\index.tsx**: 9次替换
18. **src\pages\delivery\storeDeliveryPrice\component\batchChange\batchChange.tsx**: 9次替换
19. **src\pages\wholesale\customerGoodsAttributes\component\batchChange\batchChange.tsx**: 9次替换
20. **src\pages\archives\contractTemplateSetup\server.ts**: 8次替换
21. **src\data\common\fieldModule\userManage.tsx**: 7次替换
22. **src\pages\archives\cargoOwner\index.tsx**: 7次替换
23. **src\pages\archives\supplierMainBody\index.tsx**: 7次替换
24. **src\pages\archives\userBranch\index.tsx**: 7次替换
25. **src\pages\delivery\storeOrders\item\server.ts**: 7次替换
26. **src\data\common\fieldModule\goodsFiles.tsx**: 6次替换
27. **src\pages\archives\devicesBrand\index.tsx**: 6次替换
28. **src\pages\archives\goodsBranch\index.tsx**: 6次替换
29. **src\pages\archives\goodsBrand\index.tsx**: 6次替换
30. **src\pages\archives\goodsUnits\index.tsx**: 6次替换
31. **src\pages\delivery\deliverySpecialPrice\item\index.tsx**: 6次替换
32. **src\pages\delivery\distributionGross\index.tsx**: 6次替换
33. **src\pages\delivery\stockForecasts\server.ts**: 6次替换
34. **src\pages\purchasement\itemSku\server.ts**: 6次替换
35. **src\pages\archives\cargoOwnerCenter\server.tsx**: 5次替换
36. **src\pages\archives\contractMangement\data.tsx**: 5次替换
37. **src\pages\archives\priceTemplate\header\component\batchChange.tsx**: 5次替换
38. **src\pages\archives\skuCeilingManagement\server.ts**: 5次替换
39. **src\pages\delivery\deliveryCenterStore\server.ts**: 5次替换
40. **src\pages\delivery\goodsway\data.ts**: 5次替换
41. **src\pages\delivery\stockForecasts\component\batchChange.tsx**: 5次替换
42. **src\data\common\fieldModule\newYearGoodsPlanDetails.tsx**: 4次替换
43. **src\pages\archives\cargoOwner\server.tsx**: 4次替换
44. **src\pages\archives\goodsBranch\server.ts**: 4次替换
45. **src\pages\archives\organizeManage\server.ts**: 4次替换
46. **src\pages\archives\storeArea\header\component\batchChange.tsx**: 4次替换
47. **src\pages\archives\supplierMainBody\server.ts**: 4次替换
48. **src\pages\archives\userBranch\server.ts**: 4次替换
49. **src\pages\delivery\deliveryCenterStore\component\batchChange.tsx**: 4次替换
50. **src\pages\delivery\deliveryCenterStore\modal.tsx**: 4次替换
51. **src\pages\delivery\deliverySpecialPrice\api.ts**: 4次替换
52. **src\pages\procurement\purchaseReport\data.tsx**: 4次替换
53. **src\pages\purchase\purchaseReplenishAnalysis\data.tsx**: 4次替换
54. **src\pages\wholesale\wholesaleOrgSetting\modal.tsx**: 4次替换
55. **src\data\common\fieldModule\deliveryCenterStore.tsx**: 3次替换
56. **src\data\common\fieldModule\interWarehouseTransfer.tsx**: 3次替换
57. **src\data\common\fieldModule\returnRateStatistics.tsx**: 3次替换
58. **src\data\common\fieldModule\storeOrder.tsx**: 3次替换
59. **src\pages\archives\administrativeRegion\index.tsx**: 3次替换
60. **src\pages\archives\companyHeader\server.ts**: 3次替换
61. **src\pages\archives\contractMangement\index.tsx**: 3次替换
62. **src\pages\archives\skuCeilingManagement\components\RecordModal.tsx**: 3次替换
63. **src\pages\delivery\distributionGross\sever.ts**: 3次替换
64. **src\pages\delivery\replenishGoodsAnalysis\data.tsx**: 3次替换
65. **src\pages\delivery\saleParam\index.tsx**: 3次替换
66. **src\pages\delivery\strongDataAnalysis\index.tsx**: 3次替换
67. **src\pages\delivery\transferDocument\item\index.tsx**: 3次替换
68. **src\pages\procurement\supplierRelationshipManagement\server.ts**: 3次替换
69. **src\pages\purchase\orderWatch\data.tsx**: 3次替换
70. **src\pages\purchasement\itemSku\header\index.tsx**: 3次替换
71. **src\pages\stock\stockLog\data.tsx**: 3次替换
72. **src\pages\stock\stockSearch\data.tsx**: 3次替换
73. **src\pages\stock\stockSearch\server.ts**: 3次替换
74. **src\components\relateStoreCreate.tsx**: 2次替换
75. **src\data\common\fieldModule\devicesBrand.tsx**: 2次替换
76. **src\data\common\fieldModule\payMode.tsx**: 2次替换
77. **src\data\common\fieldModule\storeDeviceManage.tsx**: 2次替换
78. **src\data\common\fieldModule\storeManage.tsx**: 2次替换
79. **src\data\common\fieldModule\supplier.tsx**: 2次替换
80. **src\data\common\fieldModule\tiktokcouponrefund.tsx**: 2次替换
81. **src\pages\archives\cargoOwner\components\modifyRecord\index.tsx**: 2次替换
82. **src\pages\archives\cargoOwnerCenter\components\modifyRecord\index.tsx**: 2次替换
83. **src\pages\dataAnalysis\heightLowInventoryGoods\data.tsx**: 2次替换
84. **src\pages\dataAnalysis\profitLossStatistic\server.tsx**: 2次替换
85. **src\pages\delivery\basketStat\data.ts**: 2次替换
86. **src\pages\delivery\collectDocument\data.tsx**: 2次替换
87. **src\pages\delivery\collectDocument\item.tsx**: 2次替换
88. **src\pages\delivery\deliverySpecialPriceAnalysis\index.tsx**: 2次替换
89. **src\pages\delivery\directSupplyPoint\index.tsx**: 2次替换
90. **src\pages\delivery\marketingCampaign\item.tsx**: 2次替换
91. **src\pages\delivery\receivingApplication\data.tsx**: 2次替换
92. **src\pages\delivery\receivingApplication\item.tsx**: 2次替换
93. **src\pages\delivery\replenishTemplate\item\index.tsx**: 2次替换
94. **src\pages\delivery\stockForecasts\index.tsx**: 2次替换
95. **src\pages\delivery\stockTypeSetting\item\index.tsx**: 2次替换
96. **src\pages\delivery\storeDeliveryDay\item.tsx**: 2次替换
97. **src\pages\delivery\storeOrderingDate\item\index.tsx**: 2次替换
98. **src\pages\delivery\supplyAnalyze\server.ts**: 2次替换
99. **src\pages\procurement\purchaseLatestPrice\index.tsx**: 2次替换
100. **src\pages\procurement\purchaseReport\server.ts**: 2次替换
101. **src\pages\procurement\purchaseShare\header\server.ts**: 2次替换
102. **src\pages\procurement\stockPlan\index.tsx**: 2次替换
103. **src\pages\purchase\mustSellGoodsDetail\data.tsx**: 2次替换
104. **src\pages\purchase\mustSellGoodsManagement\data.tsx**: 2次替换
105. **src\pages\stock\unsalableItem\data.tsx**: 2次替换
106. **src\pages\wholesale\wholeSaleAnalyze\data.tsx**: 2次替换
107. **src\pages\wholesale\wholeSaleAnalyze\server.ts**: 2次替换
108. **src\pages\wholesale\wholesalePrice\data.tsx**: 2次替换
109. **src\provider.tsx**: 2次替换
110. **src\api\common.ts**: 1次替换
111. **src\components\common\xlbOrgids\selectOrg.tsx**: 1次替换
112. **src\data\common\fieldModule\businessRange.tsx**: 1次替换
113. **src\data\common\fieldModule\purchaseBusinessRange.tsx**: 1次替换
114. **src\data\common\fieldModule\purchaseOrdering.tsx**: 1次替换
115. **src\data\common\fieldModule\stockCheckOrder.tsx**: 1次替换
116. **src\data\common\fieldModule\storeArea.tsx**: 1次替换
117. **src\data\common\fieldModule\storeDeilveryPrice.tsx**: 1次替换
118. **src\data\common\fieldModule\wholesaleCustomer.tsx**: 1次替换
119. **src\hooks\useBaseParams.ts**: 1次替换
120. **src\pages\archives\administrativeRegion\server.ts**: 1次替换
121. **src\pages\archives\cargoOwner\components\addItem\index.tsx**: 1次替换
122. **src\pages\archives\contractTemplateSetup\index.tsx**: 1次替换
123. **src\pages\archives\OBSPaymentEntity\index.tsx**: 1次替换
124. **src\pages\archives\orgBusinessArea\server.ts**: 1次替换
125. **src\pages\archives\priceTemplate\item\index.tsx**: 1次替换
126. **src\pages\archives\skuCeilingManagement\data.tsx**: 1次替换
127. **src\pages\archives\skuCeilingManagement\item\index.tsx**: 1次替换
128. **src\pages\dataAnalysis\profitLossStatistic\data.tsx**: 1次替换
129. **src\pages\delivery\advancePosition\server.ts**: 1次替换
130. **src\pages\delivery\deliveryanalysis\server.ts**: 1次替换
131. **src\pages\delivery\deliveryCenterStore\index.tsx**: 1次替换
132. **src\pages\delivery\deliveryDetails\data.tsx**: 1次替换
133. **src\pages\delivery\deliveryDetails\index.tsx**: 1次替换
134. **src\pages\delivery\deliveryPriceMange\header\index.tsx**: 1次替换
135. **src\pages\delivery\deliveryPriceMange\item\components\xlbBaseGoods\server.ts**: 1次替换
136. **src\pages\delivery\deliveryPriceMange\server.ts**: 1次替换
137. **src\pages\delivery\forceDeliveryRule\components\addPCRules\index.tsx**: 1次替换
138. **src\pages\delivery\forceDeliveryRule\components\addRules\index.tsx**: 1次替换
139. **src\pages\delivery\goodsway\index.tsx**: 1次替换
140. **src\pages\delivery\marketingCampaign\index.tsx**: 1次替换
141. **src\pages\delivery\marketingCampaign\server.ts**: 1次替换
142. **src\pages\delivery\stockPrediction\data.ts**: 1次替换
143. **src\pages\delivery\storeDeliveryDay\index.tsx**: 1次替换
144. **src\pages\delivery\storeDeliveryPrice\component\copy\copy.tsx**: 1次替换
145. **src\pages\delivery\storeOrderingDate\header\index.tsx**: 1次替换
146. **src\pages\delivery\storeOrders\components\batchOrder\batchOrder.tsx**: 1次替换
147. **src\pages\delivery\storeOrders\components\deliveryOrder\deliveryOrder.tsx**: 1次替换
148. **src\pages\delivery\storeOrders\components\uploadPhotoGroup.tsx**: 1次替换
149. **src\pages\delivery\storeOrders\item\index.tsx**: 1次替换
150. **src\pages\delivery\transferDocument\components\batchOrder\batchOrder.tsx**: 1次替换
151. **src\pages\delivery\transferDocument\index.tsx**: 1次替换
152. **src\pages\delivery\transferDocument\server.ts**: 1次替换
153. **src\pages\delivery\transferWay\data.ts**: 1次替换
154. **src\pages\procurement\orderParamsConfig\data.ts**: 1次替换
155. **src\pages\procurement\orderValidDay\data.tsx**: 1次替换
156. **src\pages\procurement\orderValidDay\index.tsx**: 1次替换
157. **src\pages\procurement\purchasePrice\data.ts**: 1次替换
158. **src\pages\procurement\purchasePrice\server.ts**: 1次替换
159. **src\pages\procurement\purchaseShare\header\index.tsx**: 1次替换
160. **src\pages\procurement\stockPlan\data.ts**: 1次替换
161. **src\pages\procurement\supplierRelationshipManagement\header\components\Additem\index.tsx**: 1次替换
162. **src\pages\purchase\newItemPurchasePlan\data.tsx**: 1次替换
163. **src\pages\stock\stockCollaborativeSharingr\components\addModel\index.tsx**: 1次替换
164. **src\pages\stock\stockCollaborativeSharingr\data.ts**: 1次替换
165. **src\pages\stock\stockDyingPeriod\data.ts**: 1次替换
166. **src\pages\stock\stockDyingPeriod\server.ts**: 1次替换
167. **src\pages\stock\stockLog\server.ts**: 1次替换
168. **src\pages\stock\unsalableItem\server.ts**: 1次替换
169. **src\pages\wholesale\buyParam\header\index.tsx**: 1次替换
170. **src\pages\wholesale\wholeSaleAnalyze\index.tsx**: 1次替换
171. **src\pages\wholesale\wholeSaleDetail\data.ts**: 1次替换
172. **src\pages\wholesale\wholeSaleDetail\index.tsx**: 1次替换
173. **src\pages\wholesale\wholeSaleDetail\server.ts**: 1次替换
174. **src\pages\wholesale\wholesaleOrgSetting\index.tsx**: 1次替换
175. **src\pages\wholesale\wholesalePrice\components\copy\copy.tsx**: 1次替换
176. **src\pages\wholesale\wholesalePriceAdjustment\data.ts**: 1次替换
177. **src\pages\wholesale\wholesalePriceAdjustment\header\index.tsx**: 1次替换
178. **src\services\system\index.ts**: 1次替换
179. **src\utils\purchaseUrl.ts**: 1次替换
180. **src\utils\utils.ts**: 1次替换

## 按API接口统计替换次数

1. **/erp-mdm/hxl.erp.org.find**: 81次
2. **/erp-mdm/hxl.erp.category.find**: 58次
3. **/erp-mdm/hxl.erp.storehouse.store.find**: 34次
4. **/erp-mdm/hxl.erp.org.tree**: 30次
5. **/erp-mdm/hxl.erp.storecodetemplate.download**: 22次
6. **/erp-mdm/hxl.erp.store.short.page**: 20次
7. **/erp-mdm/hxl.erp.brand.find**: 16次
8. **/erp-mdm/hxl.erp.storename.import**: 16次
9. **/erp-mdm/hxl.erp.items.batchimport**: 16次
10. **/erp-mdm/hxl.erp.item.shorttemplate.download**: 16次
11. **/erp-mdm/hxl.erp.dept.find**: 15次
12. **/erp-mdm/hxl.erp.storegroup.find**: 12次
13. **/erp-mdm/hxl.erp.store.area.find.all**: 12次
14. **/erp-mdm/hxl.erp.store.center.find**: 12次
15. **/erp-mdm/hxl.erp.itemlabel.find**: 10次
16. **/erp-mdm/hxl.erp.suppliercategory.findwithsupplier**: 10次
17. **/erp-mdm/hxl.erp.storelabel.find**: 9次
18. **/erp-mdm/hxl.erp.baseparam.read**: 9次
19. **/erp-mdm/hxl.erp.businessarea.find**: 8次
20. **/erp-mdm/hxl.erp.role.page**: 8次
21. **/erp-mdm/hxl.erp.authority.search**: 8次
22. **/erp-mdm/hxl.erp.suppliermainbody.find**: 7次
23. **/erp-mdm/hxl.erp.settlementcategory.center.find**: 7次
24. **/erp-mdm/hxl.erp.commonstorename.import**: 7次
25. **/erp-mdm/hxl.erp.userdept.find**: 6次
26. **/erp-mdm/hxl.erp.suppliercategory.find**: 6次
27. **/erp-mdm/hxl.erp.store.all.shortfind**: 6次
28. **/erp-mdm/hxl.erp.storehardware.category.find**: 6次
29. **/erp-mdm/hxl.erp.contract.enum**: 6次
30. **/erp-mdm/hxl.erp.storenametemplate.download**: 6次
31. **/erp-mdm/hxl.erp.item.short.page**: 5次
32. **/erp-mdm/hxl.erp.storearea.find**: 4次
33. **/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import**: 4次
34. **/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download**: 4次
35. **/erp-mdm/hxl.erp.companyinvoice.save**: 4次
36. **/erp-mdm/hxl.erp.storearea.store.import**: 4次
37. **/erp-mdm/hxl.erp.storearea.save**: 4次
38. **/erp-mdm/hxl.erp.userdept.save**: 4次
39. **/erp-mdm/hxl.erp.user.page**: 3次
40. **/erp-mdm/hxl.erp.businessscope.short.find**: 3次
41. **/erp-mdm/hxl.erp.cargo.owner.pageforinner**: 3次
42. **/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find**: 3次
43. **/erp-mdm/hxl.erp.companyinvoice.delete**: 3次
44. **/erp-mdm/hxl.erp.companyinvoice.export**: 3次
45. **/erp-mdm/hxl.erp.contract.template.page**: 3次
46. **/erp-mdm/hxl.erp.storehardware.brand.save**: 3次
47. **/erp-mdm/hxl.erp.storehardware.brand.delete**: 3次
48. **/erp-mdm/hxl.erp.dept.delete**: 3次
49. **/erp-mdm/hxl.erp.dept.save**: 3次
50. **/erp-mdm/hxl.erp.org.save**: 3次
51. **/erp-mdm/hxl.erp.storearea.update**: 3次
52. **/erp-mdm/hxl.erp.storearea.delete**: 3次
53. **/erp-mdm/hxl.erp.suppliermainbody.delete**: 3次
54. **/erp-mdm/hxl.erp.suppliermainbody.save**: 3次
55. **/erp-mdm/hxl.erp.suppliermainbody.update**: 3次
56. **/erp-mdm/hxl.erp.userdept.delete**: 3次
57. **/erp-mdm/hxl.erp.businessarea.store.find**: 2次
58. **/erp-mdm/hxl.erp.storeareacategory.find**: 2次
59. **/erp-mdm/hxl.erp.storehouse.page**: 2次
60. **/erp-mdm/hxl.erp.cargo.owner.page**: 2次
61. **/erp-mdm/hxl.erp.hardwarecategory.find**: 2次
62. **/erp-mdm/hxl.erp.businessscopecategory.find**: 2次
63. **/erp-mdm/hxl.erp.store.sharedeliverycenter.find**: 2次
64. **/erp-mdm/hxl.erp.store.area.detail.export**: 2次
65. **/erp-mdm/hxl.erp.cargo.owner.log.page**: 2次
66. **/erp-mdm/hxl.erp.cargo.owner.export**: 2次
67. **/erp-mdm/hxl.erp.cargo.owner.import**: 2次
68. **/erp-mdm/hxl.erp.cargo.owner.template.download**: 2次
69. **/erp-mdm/hxl.erp.delivery.cargo.owner.conf.log.page**: 2次
70. **/erp-mdm/hxl.erp.delivery.cargo.owner.conf.export**: 2次
71. **/erp-mdm/hxl.erp.item.update.import**: 2次
72. **/erp-mdm/hxl.erp.itemsupdatetemplate.download**: 2次
73. **/erp-mdm/hxl.erp.contract.page**: 2次
74. **/erp-mdm/hxl.erp.contract.whitelist.page**: 2次
75. **/erp-mdm/hxl.erp.contract.whitelist.export**: 2次
76. **/erp-mdm/hxl.erp.contract.template.update**: 2次
77. **/erp-mdm/hxl.erp.storehardware.brand.update**: 2次
78. **/erp-mdm/hxl.erp.storehardware.brand.find**: 2次
79. **/erp-mdm/hxl.erp.dept.update**: 2次
80. **/erp-mdm/hxl.erp.brand.delete**: 2次
81. **/erp-mdm/hxl.erp.brand.save**: 2次
82. **/erp-mdm/hxl.erp.itemunit.delete**: 2次
83. **/erp-mdm/hxl.erp.itemunit.save**: 2次
84. **/erp-mdm/hxl.erp.org.page**: 2次
85. **/erp-mdm/hxl.erp.server.org.check.default**: 2次
86. **/erp-mdm/hxl.erp.org.delete**: 2次
87. **/erp-mdm/hxl.erp.org.business.scope.item.batchdelete**: 2次
88. **/erp-mdm/hxl.erp.org.business.scope.item.export**: 2次
89. **/erp-mdm/hxl.erp.org.business.scope.item.import**: 2次
90. **/erp-mdm/hxl.erp.org.skulimit.log.find**: 2次
91. **/erp-mdm/hxl.erp.org.skulimittemplate.download**: 2次
92. **/erp-mdm/hxl.erp.org.skulimit.update.import**: 2次
93. **/erp-mdm/hxl.erp.org.skulimit.excludeitemtemplate.download**: 2次
94. **/erp-mdm/hxl.erp.org.skulimit.excludeitem.import**: 2次
95. **/erp-mdm/hxl.erp.storeareatemplate.download**: 2次
96. **/erp-mdm/hxl.erp.storearea.read**: 2次
97. **/erp-mdm/hxl.erp.userdept.update**: 2次
98. **/erp-mdm/hxl.erp.store.page**: 2次
99. **/erp-mdm/hxl.erp.item.summary**: 2次
100. **/erp-mdm/hxl.erp.item.summary.read**: 2次
101. **/erp-mdm/hxl.erp.store.cargoownerdelivery.short.page**: 2次
102. **/erp-mdm/hxl.erp.clientname.import**: 2次
103. **/erp-mdm/hxl.erp.clientnametemplate.download**: 2次
104. **/erp-mdm/hxl.erp.usercolumn.get**: 2次
105. **/erp-mdm/hxl.erp.businessarea.detail.find**: 1次
106. **/erp-mdm/hxl.erp.store.ids.find**: 1次
107. **/erp-mdm/hxl.erp.supplier.short.page**: 1次
108. **/erp-mdm/hxl.erp.customattribute.show.find**: 1次
109. **/erp-mdm/hxl.erp.labour.page**: 1次
110. **/erp-mdm/hxl.erp.client.page**: 1次
111. **/erp-mdm/hxl.erp.clientcategory.find**: 1次
112. **/erp-mdm/hxl.erp.businessscope.find**: 1次
113. **/erp-mdm/hxl.erp.delivery.cargo.owner.conf.readbystoreIds**: 1次
114. **/erp-mdm/hxl.erp.item.short.ids.find**: 1次
115. **/erp-mdm/hxl.erp.org.item.page**: 1次
116. **/erp-mdm/hxl.erp.supplier.producer.find**: 1次
117. **/erp-mdm/hxl.erp.org.tree.invoice**: 1次
118. **/erp-mdm/hxl.erp.printtemplate.menus**: 1次
119. **/erp-mdm/hxl.erp.store.area.detail.page**: 1次
120. **/erp-mdm/hxl.erp.cargo.owner.save**: 1次
121. **/erp-mdm/hxl.erp.cargo.owner.batchdelete**: 1次
122. **/erp-mdm/hxl.erp.cargo.owner.disabled**: 1次
123. **/erp-mdm/hxl.erp.cargo.owner.enabled**: 1次
124. **/erp-mdm/hxl.erp.delivery.cargo.owner.conf.page**: 1次
125. **/erp-mdm/hxl.erp.delivery.cargo.owner.conf.save**: 1次
126. **/erp-mdm/hxl.erp.delivery.cargo.owner.conf.update**: 1次
127. **/erp-mdm/hxl.erp.delivery.cargo.owner.conf.batchdelete**: 1次
128. **/erp-mdm/hxl.erp.delivery.cargo.owner.conf.copy**: 1次
129. **/erp-mdm/hxl.erp.delivery.cargo.owner.conf.sync**: 1次
130. **/erp-mdm/hxl.erp.companyinvoice.page**: 1次
131. **/erp-mdm/hxl.erp.companyinvoice.read**: 1次
132. **/erp-mdm/hxl.erp.contract.read**: 1次
133. **/erp-mdm/hxl.erp.contract.view**: 1次
134. **/erp-mdm/hxl.erp.contract.download**: 1次
135. **/erp-mdm/hxl.erp.contract.create**: 1次
136. **/erp-mdm/hxl.erp.contract.whitelist.create**: 1次
137. **/erp-mdm/hxl.erp.contract.template.acquire**: 1次
138. **/erp-mdm/hxl.erp.contract.template.save**: 1次
139. **/erp-mdm/hxl.erp.contract.template.delete**: 1次
140. **/erp-mdm/hxl.erp.contract.template.read**: 1次
141. **/erp-mdm/hxl.erp.contract.template.check**: 1次
142. **/erp-mdm/hxl.erp.storehardware.category.delete**: 1次
143. **/erp-mdm/hxl.erp.storehardware.category.update**: 1次
144. **/erp-mdm/hxl.erp.storehardware.category.save**: 1次
145. **/erp-mdm/hxl.erp.brand.update**: 1次
146. **/erp-mdm/hxl.erp.itemunit.find**: 1次
147. **/erp-mdm/hxl.erp.itemunit.update**: 1次
148. **/erp-mdm/hxl.erp.org.read**: 1次
149. **/erp-mdm/hxl.erp.org.update**: 1次
150. **/erp-mdm/hxl.erp.org.business.scope.item.template.download**: 1次
151. **/erp-mdm/hxl.erp.org.business.scope.item.page**: 1次
152. **/erp-mdm/hxl.erp.org.business.scope.item.add**: 1次
153. **/erp-mdm/hxl.erp.org.skulimit.excludeitem.log.find**: 1次
154. **/erp-mdm/hxl.erp.org.skulimit.page**: 1次
155. **/erp-mdm/hxl.erp.org.skulimit.read**: 1次
156. **/erp-mdm/hxl.erp.org.skulimit.update**: 1次
157. **/erp-mdm/hxl.erp.org.skulimit.export**: 1次
158. **/erp-mdm/hxl.erp.org.skulimit.excludeitem.find**: 1次
159. **/erp-mdm/hxl.erp.org.skulimit.excludeitem.export**: 1次
160. **/erp-mdm/hxl.erp.org.skulimit.excludeitem.save**: 1次
161. **/erp-mdm/hxl.erp.storearea.export**: 1次
162. **/erp-mdm/hxl.erp.storearea.batch.export**: 1次
163. **/erp-mdm/hxl.erp.storearea.batchupdate**: 1次
164. **/erp-mdm/hxl.erp.storeareacategory.delete**: 1次
165. **/erp-mdm/hxl.erp.storeareacategory.save**: 1次
166. **/erp-mdm/hxl.erp.storeareacategory.update**: 1次
167. **/erp-mdm/hxl.erp.store.orgdeliverycenter.find**: 1次
168. **/erp-mdm/hxl.erp.org.findbylevel**: 1次
169. **/erp-mdm/hxl.erp.delivery.cargo.owner.org.find**: 1次
170. **/erp-mdm/hxl.erp.store.area.find**: 1次
171. **/erp-mdm/hxl.erp.storegroup.delete**: 1次
172. **/erp-mdm/hxl.erp.storegroup.update**: 1次
173. **/erp-mdm/hxl.erp.storegroup.save**: 1次
174. **/erp-mdm/hxl.erp.file.upload**: 1次
175. **/erp-mdm/hxl.erp.file.delete**: 1次
176. **/erp-mdm/hxl.erp.store.allcenter.find**: 1次
177. **/erp-mdm/hxl.erp.store.balance.read**: 1次
178. **/erp-mdm/hxl.erp.item.read**: 1次
179. **/erp-mdm/hxl.erp.store.all.find**: 1次
180. **/erp-mdm/hxl.erp.category.findmaxlevel**: 1次
181. **/erp-mdm/hxl.erp.item.summary.initial.save**: 1次
182. **/erp-mdm/hxl.erp.category.maxlevel.read**: 1次
183. **/erp-mdm/hxl.erp.usercolumn.update**: 1次
184. **/erp-mdm/hxl.erp.user.account.login**: 1次

## 详细替换记录

### 1. src\api\common.ts:14
- **业务模块**: 其他 > api
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: src\api\common.ts

### 2. src\components\common\xlbOrgids\selectOrg.tsx:30
- **业务模块**: 公共组件 > components > common
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: components/common/xlbOrgids/selectOrg.tsx

### 3. src\components\relateStoreCreate.tsx:15
- **业务模块**: 公共组件 > components > relateStoreCreate.tsx
- **替换内容**: `/erp/hxl.erp.businessarea.store.find` → `/erp-mdm/hxl.erp.businessarea.store.find`
- **业务路径**: components/relateStoreCreate.tsx

### 4. src\components\relateStoreCreate.tsx:15
- **业务模块**: 公共组件 > components > relateStoreCreate.tsx
- **替换内容**: `/erp/hxl.erp.businessarea.store.find` → `/erp-mdm/hxl.erp.businessarea.store.find`
- **业务路径**: components/relateStoreCreate.tsx

### 5. src\constants\baseDataConfig.tsx:17
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: constants/baseDataConfig.tsx

### 6. src\constants\baseDataConfig.tsx:81
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storearea.find` → `/erp-mdm/hxl.erp.storearea.find`
- **业务路径**: constants/baseDataConfig.tsx

### 7. src\constants\baseDataConfig.tsx:105
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 8. src\constants\baseDataConfig.tsx:130
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 9. src\constants\baseDataConfig.tsx:170
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 10. src\constants\baseDataConfig.tsx:210
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 11. src\constants\baseDataConfig.tsx:233
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storeareacategory.find` → `/erp-mdm/hxl.erp.storeareacategory.find`
- **业务路径**: constants/baseDataConfig.tsx

### 12. src\constants\baseDataConfig.tsx:253
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessarea.detail.find` → `/erp-mdm/hxl.erp.businessarea.detail.find`
- **业务路径**: constants/baseDataConfig.tsx

### 13. src\constants\baseDataConfig.tsx:254
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
- **业务路径**: constants/baseDataConfig.tsx

### 14. src\constants\baseDataConfig.tsx:262
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
- **业务路径**: constants/baseDataConfig.tsx

### 15. src\constants\baseDataConfig.tsx:273
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
- **业务路径**: constants/baseDataConfig.tsx

### 16. src\constants\baseDataConfig.tsx:290
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
- **业务路径**: constants/baseDataConfig.tsx

### 17. src\constants\baseDataConfig.tsx:391
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: constants/baseDataConfig.tsx

### 18. src\constants\baseDataConfig.tsx:406
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
- **业务路径**: constants/baseDataConfig.tsx

### 19. src\constants\baseDataConfig.tsx:424
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
- **业务路径**: constants/baseDataConfig.tsx

### 20. src\constants\baseDataConfig.tsx:437
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
- **业务路径**: constants/baseDataConfig.tsx

### 21. src\constants\baseDataConfig.tsx:480
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: constants/baseDataConfig.tsx

### 22. src\constants\baseDataConfig.tsx:481
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: constants/baseDataConfig.tsx

### 23. src\constants\baseDataConfig.tsx:482
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.store.ids.find` → `/erp-mdm/hxl.erp.store.ids.find`
- **业务路径**: constants/baseDataConfig.tsx

### 24. src\constants\baseDataConfig.tsx:552
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
- **业务路径**: constants/baseDataConfig.tsx

### 25. src\constants\baseDataConfig.tsx:569
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 26. src\constants\baseDataConfig.tsx:611
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: constants/baseDataConfig.tsx

### 27. src\constants\baseDataConfig.tsx:745
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: constants/baseDataConfig.tsx

### 28. src\constants\baseDataConfig.tsx:750
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
- **业务路径**: constants/baseDataConfig.tsx

### 29. src\constants\baseDataConfig.tsx:760
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: constants/baseDataConfig.tsx

### 30. src\constants\baseDataConfig.tsx:779
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 31. src\constants\baseDataConfig.tsx:785
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: constants/baseDataConfig.tsx

### 32. src\constants\baseDataConfig.tsx:794
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: constants/baseDataConfig.tsx

### 33. src\constants\baseDataConfig.tsx:798
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
- **业务路径**: constants/baseDataConfig.tsx

### 34. src\constants\baseDataConfig.tsx:802
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: constants/baseDataConfig.tsx

### 35. src\constants\baseDataConfig.tsx:807
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 36. src\constants\baseDataConfig.tsx:812
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: constants/baseDataConfig.tsx

### 37. src\constants\baseDataConfig.tsx:852
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.item.short.page` → `/erp-mdm/hxl.erp.item.short.page`
- **业务路径**: constants/baseDataConfig.tsx

### 38. src\constants\baseDataConfig.tsx:853
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 39. src\constants\baseDataConfig.tsx:920
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 40. src\constants\baseDataConfig.tsx:953
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 41. src\constants\baseDataConfig.tsx:958
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 42. src\constants\baseDataConfig.tsx:963
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: constants/baseDataConfig.tsx

### 43. src\constants\baseDataConfig.tsx:968
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
- **业务路径**: constants/baseDataConfig.tsx

### 44. src\constants\baseDataConfig.tsx:973
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 45. src\constants\baseDataConfig.tsx:978
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 46. src\constants\baseDataConfig.tsx:982
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: constants/baseDataConfig.tsx

### 47. src\constants\baseDataConfig.tsx:986
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
- **业务路径**: constants/baseDataConfig.tsx

### 48. src\constants\baseDataConfig.tsx:1000
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.supplier.short.page` → `/erp-mdm/hxl.erp.supplier.short.page`
- **业务路径**: constants/baseDataConfig.tsx

### 49. src\constants\baseDataConfig.tsx:1001
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
- **业务路径**: constants/baseDataConfig.tsx

### 50. src\constants\baseDataConfig.tsx:1060
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
- **业务路径**: constants/baseDataConfig.tsx

### 51. src\constants\baseDataConfig.tsx:1061
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.customattribute.show.find` → `/erp-mdm/hxl.erp.customattribute.show.find`
- **业务路径**: constants/baseDataConfig.tsx

### 52. src\constants\baseDataConfig.tsx:1128
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: constants/baseDataConfig.tsx

### 53. src\constants\baseDataConfig.tsx:1150
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
- **业务路径**: constants/baseDataConfig.tsx

### 54. src\constants\baseDataConfig.tsx:1248
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
- **业务路径**: constants/baseDataConfig.tsx

### 55. src\constants\baseDataConfig.tsx:1258
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
- **业务路径**: constants/baseDataConfig.tsx

### 56. src\constants\baseDataConfig.tsx:1366
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
- **业务路径**: constants/baseDataConfig.tsx

### 57. src\constants\baseDataConfig.tsx:1376
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
- **业务路径**: constants/baseDataConfig.tsx

### 58. src\constants\baseDataConfig.tsx:1398
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.find` → `/erp-mdm/hxl.erp.suppliercategory.find`
- **业务路径**: constants/baseDataConfig.tsx

### 59. src\constants\baseDataConfig.tsx:1421
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.labour.page` → `/erp-mdm/hxl.erp.labour.page`
- **业务路径**: constants/baseDataConfig.tsx

### 60. src\constants\baseDataConfig.tsx:1447
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.user.page` → `/erp-mdm/hxl.erp.user.page`
- **业务路径**: constants/baseDataConfig.tsx

### 61. src\constants\baseDataConfig.tsx:1529
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storehouse.page` → `/erp-mdm/hxl.erp.storehouse.page`
- **业务路径**: constants/baseDataConfig.tsx

### 62. src\constants\baseDataConfig.tsx:1530
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: constants/baseDataConfig.tsx

### 63. src\constants\baseDataConfig.tsx:1677
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
- **业务路径**: constants/baseDataConfig.tsx

### 64. src\constants\baseDataConfig.tsx:1694
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 65. src\constants\baseDataConfig.tsx:1732
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: constants/baseDataConfig.tsx

### 66. src\constants\baseDataConfig.tsx:1867
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: constants/baseDataConfig.tsx

### 67. src\constants\baseDataConfig.tsx:1872
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
- **业务路径**: constants/baseDataConfig.tsx

### 68. src\constants\baseDataConfig.tsx:1881
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: constants/baseDataConfig.tsx

### 69. src\constants\baseDataConfig.tsx:1900
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 70. src\constants\baseDataConfig.tsx:1906
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: constants/baseDataConfig.tsx

### 71. src\constants\baseDataConfig.tsx:1915
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: constants/baseDataConfig.tsx

### 72. src\constants\baseDataConfig.tsx:1919
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessarea.find` → `/erp-mdm/hxl.erp.businessarea.find`
- **业务路径**: constants/baseDataConfig.tsx

### 73. src\constants\baseDataConfig.tsx:1923
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: constants/baseDataConfig.tsx

### 74. src\constants\baseDataConfig.tsx:1928
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 75. src\constants\baseDataConfig.tsx:1933
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: constants/baseDataConfig.tsx

### 76. src\constants\baseDataConfig.tsx:1969
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 77. src\constants\baseDataConfig.tsx:1993
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.client.page` → `/erp-mdm/hxl.erp.client.page`
- **业务路径**: constants/baseDataConfig.tsx

### 78. src\constants\baseDataConfig.tsx:1994
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.clientcategory.find` → `/erp-mdm/hxl.erp.clientcategory.find`
- **业务路径**: constants/baseDataConfig.tsx

### 79. src\constants\baseDataConfig.tsx:2033
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 80. src\constants\baseDataConfig.tsx:2132
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: constants/baseDataConfig.tsx

### 81. src\constants\baseDataConfig.tsx:2360
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: constants/baseDataConfig.tsx

### 82. src\constants\baseDataConfig.tsx:2450
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.item.short.page` → `/erp-mdm/hxl.erp.item.short.page`
- **业务路径**: constants/baseDataConfig.tsx

### 83. src\constants\baseDataConfig.tsx:2451
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 84. src\constants\baseDataConfig.tsx:2515
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 85. src\constants\baseDataConfig.tsx:2626
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 86. src\constants\baseDataConfig.tsx:2631
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 87. src\constants\baseDataConfig.tsx:2636
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: constants/baseDataConfig.tsx

### 88. src\constants\baseDataConfig.tsx:2641
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
- **业务路径**: constants/baseDataConfig.tsx

### 89. src\constants\baseDataConfig.tsx:2646
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 90. src\constants\baseDataConfig.tsx:2651
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 91. src\constants\baseDataConfig.tsx:2655
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: constants/baseDataConfig.tsx

### 92. src\constants\baseDataConfig.tsx:2659
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
- **业务路径**: constants/baseDataConfig.tsx

### 93. src\constants\baseDataConfig.tsx:2675
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessscope.short.find` → `/erp-mdm/hxl.erp.businessscope.short.find`
- **业务路径**: constants/baseDataConfig.tsx

### 94. src\constants\baseDataConfig.tsx:2731
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessscope.short.find` → `/erp-mdm/hxl.erp.businessscope.short.find`
- **业务路径**: constants/baseDataConfig.tsx

### 95. src\constants\baseDataConfig.tsx:2732
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: constants/baseDataConfig.tsx

### 96. src\constants\baseDataConfig.tsx:2781
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessscope.find` → `/erp-mdm/hxl.erp.businessscope.find`
- **业务路径**: constants/baseDataConfig.tsx

### 97. src\constants\baseDataConfig.tsx:2809
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
- **业务路径**: constants/baseDataConfig.tsx

### 98. src\constants\baseDataConfig.tsx:2824
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.businessscope.short.find` → `/erp-mdm/hxl.erp.businessscope.short.find`
- **业务路径**: constants/baseDataConfig.tsx

### 99. src\constants\baseDataConfig.tsx:2905
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.user.page` → `/erp-mdm/hxl.erp.user.page`
- **业务路径**: constants/baseDataConfig.tsx

### 100. src\constants\baseDataConfig.tsx:2926
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.user.page` → `/erp-mdm/hxl.erp.user.page`
- **业务路径**: constants/baseDataConfig.tsx

### 101. src\constants\baseDataConfig.tsx:2960
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.storelabel.find` → `/erp-mdm/hxl.erp.storelabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 102. src\constants\baseDataConfig.tsx:2986
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 103. src\constants\baseDataConfig.tsx:3022
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: constants/baseDataConfig.tsx

### 104. src\constants\baseDataConfig.tsx:3096
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: constants/baseDataConfig.tsx

### 105. src\constants\baseDataConfig.tsx:3185
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 106. src\constants\baseDataConfig.tsx:3202
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: constants/baseDataConfig.tsx

### 107. src\constants\baseDataConfig.tsx:3239
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.cargo.owner.pageforinner` → `/erp-mdm/hxl.erp.cargo.owner.pageforinner`
- **业务路径**: constants/baseDataConfig.tsx

### 108. src\constants\baseDataConfig.tsx:3320
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.readbystoreIds` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.readbystoreIds`
- **业务路径**: constants/baseDataConfig.tsx

### 109. src\constants\baseDataConfig.tsx:3411
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 110. src\constants\baseDataConfig.tsx:3477
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 111. src\constants\baseDataConfig.tsx:3513
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 112. src\constants\baseDataConfig.tsx:3518
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 113. src\constants\baseDataConfig.tsx:3523
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: constants/baseDataConfig.tsx

### 114. src\constants\baseDataConfig.tsx:3528
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
- **业务路径**: constants/baseDataConfig.tsx

### 115. src\constants\baseDataConfig.tsx:3533
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 116. src\constants\baseDataConfig.tsx:3538
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 117. src\constants\baseDataConfig.tsx:3542
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: constants/baseDataConfig.tsx

### 118. src\constants\baseDataConfig.tsx:3546
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
- **业务路径**: constants/baseDataConfig.tsx

### 119. src\constants\baseDataConfig.tsx:3584
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.item.short.page` → `/erp-mdm/hxl.erp.item.short.page`
- **业务路径**: constants/baseDataConfig.tsx

### 120. src\constants\baseDataConfig.tsx:3585
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 121. src\constants\baseDataConfig.tsx:3586
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.item.short.ids.find` → `/erp-mdm/hxl.erp.item.short.ids.find`
- **业务路径**: constants/baseDataConfig.tsx

### 122. src\constants\baseDataConfig.tsx:3673
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 123. src\constants\baseDataConfig.tsx:3706
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 124. src\constants\baseDataConfig.tsx:3711
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 125. src\constants\baseDataConfig.tsx:3716
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: constants/baseDataConfig.tsx

### 126. src\constants\baseDataConfig.tsx:3721
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
- **业务路径**: constants/baseDataConfig.tsx

### 127. src\constants\baseDataConfig.tsx:3726
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 128. src\constants\baseDataConfig.tsx:3731
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 129. src\constants\baseDataConfig.tsx:3735
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: constants/baseDataConfig.tsx

### 130. src\constants\baseDataConfig.tsx:3739
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
- **业务路径**: constants/baseDataConfig.tsx

### 131. src\constants\baseDataConfig.tsx:3757
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.item.page` → `/erp-mdm/hxl.erp.org.item.page`
- **业务路径**: constants/baseDataConfig.tsx

### 132. src\constants\baseDataConfig.tsx:3758
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 133. src\constants\baseDataConfig.tsx:3829
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
- **业务路径**: constants/baseDataConfig.tsx

### 134. src\constants\baseDataConfig.tsx:3857
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: constants/baseDataConfig.tsx

### 135. src\constants\baseDataConfig.tsx:3893
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 136. src\constants\baseDataConfig.tsx:3898
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 137. src\constants\baseDataConfig.tsx:3903
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: constants/baseDataConfig.tsx

### 138. src\constants\baseDataConfig.tsx:3908
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
- **业务路径**: constants/baseDataConfig.tsx

### 139. src\constants\baseDataConfig.tsx:3913
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: constants/baseDataConfig.tsx

### 140. src\constants\baseDataConfig.tsx:3918
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: constants/baseDataConfig.tsx

### 141. src\constants\baseDataConfig.tsx:3922
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: constants/baseDataConfig.tsx

### 142. src\constants\baseDataConfig.tsx:3926
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.suppliercategory.findwithsupplier` → `/erp-mdm/hxl.erp.suppliercategory.findwithsupplier`
- **业务路径**: constants/baseDataConfig.tsx

### 143. src\constants\baseDataConfig.tsx:3940
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.cargo.owner.page` → `/erp-mdm/hxl.erp.cargo.owner.page`
- **业务路径**: constants/baseDataConfig.tsx

### 144. src\constants\baseDataConfig.tsx:4005
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.supplier.producer.find` → `/erp-mdm/hxl.erp.supplier.producer.find`
- **业务路径**: constants/baseDataConfig.tsx

### 145. src\constants\baseDataConfig.tsx:4077
- **业务模块**: 配置常量 > constants
- **替换内容**: `/erp/hxl.erp.supplier.producerandexecutivestandard.find` → `/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find`
- **业务路径**: constants/baseDataConfig.tsx

### 146. src\data\common\fieldListConfig.tsx:393
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
- **业务路径**: data/common/fieldListConfig.tsx

### 147. src\data\common\fieldListConfig.tsx:415
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
- **业务路径**: data/common/fieldListConfig.tsx

### 148. src\data\common\fieldListConfig.tsx:440
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
- **业务路径**: data/common/fieldListConfig.tsx

### 149. src\data\common\fieldListConfig.tsx:462
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
- **业务路径**: data/common/fieldListConfig.tsx

### 150. src\data\common\fieldListConfig.tsx:487
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
- **业务路径**: data/common/fieldListConfig.tsx

### 151. src\data\common\fieldListConfig.tsx:509
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
- **业务路径**: data/common/fieldListConfig.tsx

### 152. src\data\common\fieldListConfig.tsx:534
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
- **业务路径**: data/common/fieldListConfig.tsx

### 153. src\data\common\fieldListConfig.tsx:556
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.authority.search` → `/erp-mdm/hxl.erp.authority.search`
- **业务路径**: data/common/fieldListConfig.tsx

### 154. src\data\common\fieldListConfig.tsx:710
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: data/common/fieldListConfig.tsx

### 155. src\data\common\fieldListConfig.tsx:733
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.tree.invoice` → `/erp-mdm/hxl.erp.org.tree.invoice`
- **业务路径**: data/common/fieldListConfig.tsx

### 156. src\data\common\fieldListConfig.tsx:763
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: data/common/fieldListConfig.tsx

### 157. src\data\common\fieldListConfig.tsx:785
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: data/common/fieldListConfig.tsx

### 158. src\data\common\fieldListConfig.tsx:807
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: data/common/fieldListConfig.tsx

### 159. src\data\common\fieldListConfig.tsx:830
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: data/common/fieldListConfig.tsx

### 160. src\data\common\fieldListConfig.tsx:1249
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.printtemplate.menus` → `/erp-mdm/hxl.erp.printtemplate.menus`
- **业务路径**: data/common/fieldListConfig.tsx

### 161. src\data\common\fieldListConfig.tsx:1350
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 162. src\data\common\fieldListConfig.tsx:1501
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 163. src\data\common\fieldListConfig.tsx:1501
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 164. src\data\common\fieldListConfig.tsx:1575
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 165. src\data\common\fieldListConfig.tsx:1575
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 166. src\data\common\fieldListConfig.tsx:1630
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 167. src\data\common\fieldListConfig.tsx:1630
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 168. src\data\common\fieldListConfig.tsx:1658
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 169. src\data\common\fieldListConfig.tsx:1658
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 170. src\data\common\fieldListConfig.tsx:1852
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 171. src\data\common\fieldListConfig.tsx:1852
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 172. src\data\common\fieldListConfig.tsx:1994
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: data/common/fieldListConfig.tsx

### 173. src\data\common\fieldListConfig.tsx:2164
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 174. src\data\common\fieldListConfig.tsx:2387
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: data/common/fieldListConfig.tsx

### 175. src\data\common\fieldListConfig.tsx:2416
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
- **业务路径**: data/common/fieldListConfig.tsx

### 176. src\data\common\fieldListConfig.tsx:2445
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 177. src\data\common\fieldListConfig.tsx:2445
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 178. src\data\common\fieldListConfig.tsx:2472
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 179. src\data\common\fieldListConfig.tsx:2555
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.hardwarecategory.find` → `/erp-mdm/hxl.erp.hardwarecategory.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 180. src\data\common\fieldListConfig.tsx:2555
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.hardwarecategory.find` → `/erp-mdm/hxl.erp.hardwarecategory.find`
- **业务路径**: data/common/fieldListConfig.tsx

### 181. src\data\common\fieldListConfig.tsx:2595
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: data/common/fieldListConfig.tsx

### 182. src\data\common\fieldListConfig.tsx:2789
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: data/common/fieldListConfig.tsx

### 183. src\data\common\fieldListConfig.tsx:2816
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: data/common/fieldListConfig.tsx

### 184. src\data\common\fieldModule\businessRange.tsx:16
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.businessscopecategory.find` → `/erp-mdm/hxl.erp.businessscopecategory.find`
- **业务路径**: data/common/fieldModule/businessRange.tsx

### 185. src\data\common\fieldModule\deliveryCenterStore.tsx:17
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: data/common/fieldModule/deliveryCenterStore.tsx

### 186. src\data\common\fieldModule\deliveryCenterStore.tsx:37
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.store.sharedeliverycenter.find` → `/erp-mdm/hxl.erp.store.sharedeliverycenter.find`
- **业务路径**: data/common/fieldModule/deliveryCenterStore.tsx

### 187. src\data\common\fieldModule\deliveryCenterStore.tsx:58
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`
- **业务路径**: data/common/fieldModule/deliveryCenterStore.tsx

### 188. src\data\common\fieldModule\devicesBrand.tsx:16
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`
- **业务路径**: data/common/fieldModule/devicesBrand.tsx

### 189. src\data\common\fieldModule\devicesBrand.tsx:16
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`
- **业务路径**: data/common/fieldModule/devicesBrand.tsx

### 190. src\data\common\fieldModule\goodsFiles.tsx:18
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: data/common/fieldModule/goodsFiles.tsx

### 191. src\data\common\fieldModule\goodsFiles.tsx:18
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: data/common/fieldModule/goodsFiles.tsx

### 192. src\data\common\fieldModule\goodsFiles.tsx:41
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: data/common/fieldModule/goodsFiles.tsx

### 193. src\data\common\fieldModule\goodsFiles.tsx:41
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: data/common/fieldModule/goodsFiles.tsx

### 194. src\data\common\fieldModule\goodsFiles.tsx:64
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`
- **业务路径**: data/common/fieldModule/goodsFiles.tsx

### 195. src\data\common\fieldModule\goodsFiles.tsx:64
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`
- **业务路径**: data/common/fieldModule/goodsFiles.tsx

### 196. src\data\common\fieldModule\interWarehouseTransfer.tsx:49
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/interWarehouseTransfer.tsx

### 197. src\data\common\fieldModule\interWarehouseTransfer.tsx:104
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/interWarehouseTransfer.tsx

### 198. src\data\common\fieldModule\interWarehouseTransfer.tsx:169
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: data/common/fieldModule/interWarehouseTransfer.tsx

### 199. src\data\common\fieldModule\newYearGoodsPlanDetails.tsx:47
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/newYearGoodsPlanDetails.tsx

### 200. src\data\common\fieldModule\newYearGoodsPlanDetails.tsx:47
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/newYearGoodsPlanDetails.tsx

### 201. src\data\common\fieldModule\newYearGoodsPlanDetails.tsx:194
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/newYearGoodsPlanDetails.tsx

### 202. src\data\common\fieldModule\newYearGoodsPlanDetails.tsx:194
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/newYearGoodsPlanDetails.tsx

### 203. src\data\common\fieldModule\payMode.tsx:98
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/payMode.tsx

### 204. src\data\common\fieldModule\payMode.tsx:98
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/payMode.tsx

### 205. src\data\common\fieldModule\purchaseBusinessRange.tsx:15
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.businessscopecategory.find` → `/erp-mdm/hxl.erp.businessscopecategory.find`
- **业务路径**: data/common/fieldModule/purchaseBusinessRange.tsx

### 206. src\data\common\fieldModule\purchaseOrdering.tsx:70
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: data/common/fieldModule/purchaseOrdering.tsx

### 207. src\data\common\fieldModule\returnRateStatistics.tsx:38
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/returnRateStatistics.tsx

### 208. src\data\common\fieldModule\returnRateStatistics.tsx:38
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/returnRateStatistics.tsx

### 209. src\data\common\fieldModule\returnRateStatistics.tsx:188
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: data/common/fieldModule/returnRateStatistics.tsx

### 210. src\data\common\fieldModule\stockCheckOrder.tsx:57
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: data/common/fieldModule/stockCheckOrder.tsx

### 211. src\data\common\fieldModule\storeArea.tsx:32
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: data/common/fieldModule/storeArea.tsx

### 212. src\data\common\fieldModule\storeDeilveryPrice.tsx:69
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: data/common/fieldModule/storeDeilveryPrice.tsx

### 213. src\data\common\fieldModule\storeDeviceManage.tsx:18
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`
- **业务路径**: data/common/fieldModule/storeDeviceManage.tsx

### 214. src\data\common\fieldModule\storeDeviceManage.tsx:18
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`
- **业务路径**: data/common/fieldModule/storeDeviceManage.tsx

### 215. src\data\common\fieldModule\storeManage.tsx:210
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: data/common/fieldModule/storeManage.tsx

### 216. src\data\common\fieldModule\storeManage.tsx:238
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
- **业务路径**: data/common/fieldModule/storeManage.tsx

### 217. src\data\common\fieldModule\storeOrder.tsx:60
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: data/common/fieldModule/storeOrder.tsx

### 218. src\data\common\fieldModule\storeOrder.tsx:93
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: data/common/fieldModule/storeOrder.tsx

### 219. src\data\common\fieldModule\storeOrder.tsx:144
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: data/common/fieldModule/storeOrder.tsx

### 220. src\data\common\fieldModule\supplier.tsx:41
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
- **业务路径**: data/common/fieldModule/supplier.tsx

### 221. src\data\common\fieldModule\supplier.tsx:63
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: data/common/fieldModule/supplier.tsx

### 222. src\data\common\fieldModule\tiktokcouponrefund.tsx:26
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/tiktokcouponrefund.tsx

### 223. src\data\common\fieldModule\tiktokcouponrefund.tsx:26
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: data/common/fieldModule/tiktokcouponrefund.tsx

### 224. src\data\common\fieldModule\userManage.tsx:34
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
- **业务路径**: data/common/fieldModule/userManage.tsx

### 225. src\data\common\fieldModule\userManage.tsx:54
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
- **业务路径**: data/common/fieldModule/userManage.tsx

### 226. src\data\common\fieldModule\userManage.tsx:68
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.storearea.find` → `/erp-mdm/hxl.erp.storearea.find`
- **业务路径**: data/common/fieldModule/userManage.tsx

### 227. src\data\common\fieldModule\userManage.tsx:134
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
- **业务路径**: data/common/fieldModule/userManage.tsx

### 228. src\data\common\fieldModule\userManage.tsx:154
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
- **业务路径**: data/common/fieldModule/userManage.tsx

### 229. src\data\common\fieldModule\userManage.tsx:173
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
- **业务路径**: data/common/fieldModule/userManage.tsx

### 230. src\data\common\fieldModule\userManage.tsx:192
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.role.page` → `/erp-mdm/hxl.erp.role.page`
- **业务路径**: data/common/fieldModule/userManage.tsx

### 231. src\data\common\fieldModule\wholesaleCustomer.tsx:15
- **业务模块**: 数据配置 > data > common
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: data/common/fieldModule/wholesaleCustomer.tsx

### 232. src\hooks\useBaseParams.ts:32
- **业务模块**: 其他 > hooks
- **替换内容**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
- **业务路径**: src\hooks\useBaseParams.ts

### 233. src\pages\archives\administrativeRegion\index.tsx:15
- **业务模块**: 业务页面 > archives > administrativeRegion
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: archives/administrativeRegion/index.tsx

### 234. src\pages\archives\administrativeRegion\index.tsx:26
- **业务模块**: 业务页面 > archives > administrativeRegion
- **替换内容**: `/erp/hxl.erp.store.area.detail.export` → `/erp-mdm/hxl.erp.store.area.detail.export`
- **业务路径**: archives/administrativeRegion/index.tsx

### 235. src\pages\archives\administrativeRegion\index.tsx:30
- **业务模块**: 业务页面 > archives > administrativeRegion
- **替换内容**: `/erp/hxl.erp.store.area.detail.page` → `/erp-mdm/hxl.erp.store.area.detail.page`
- **业务路径**: archives/administrativeRegion/index.tsx

### 236. src\pages\archives\administrativeRegion\server.ts:4
- **业务模块**: 业务页面 > archives > administrativeRegion
- **替换内容**: `/erp/hxl.erp.store.area.detail.export` → `/erp-mdm/hxl.erp.store.area.detail.export`
- **业务路径**: archives/administrativeRegion/server.ts

### 237. src\pages\archives\cargoOwner\components\addItem\index.tsx:108
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: archives/cargoOwner/components/addItem/index.tsx

### 238. src\pages\archives\cargoOwner\components\modifyRecord\index.tsx:15
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.log.page` → `/erp-mdm/hxl.erp.cargo.owner.log.page`
- **业务路径**: archives/cargoOwner/components/modifyRecord/index.tsx

### 239. src\pages\archives\cargoOwner\components\modifyRecord\index.tsx:15
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.log.page` → `/erp-mdm/hxl.erp.cargo.owner.log.page`
- **业务路径**: archives/cargoOwner/components/modifyRecord/index.tsx

### 240. src\pages\archives\cargoOwner\index.tsx:60
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.export` → `/erp-mdm/hxl.erp.cargo.owner.export`
- **业务路径**: archives/cargoOwner/index.tsx

### 241. src\pages\archives\cargoOwner\index.tsx:60
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.export` → `/erp-mdm/hxl.erp.cargo.owner.export`
- **业务路径**: archives/cargoOwner/index.tsx

### 242. src\pages\archives\cargoOwner\index.tsx:74
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.import` → `/erp-mdm/hxl.erp.cargo.owner.import`
- **业务路径**: archives/cargoOwner/index.tsx

### 243. src\pages\archives\cargoOwner\index.tsx:74
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.import` → `/erp-mdm/hxl.erp.cargo.owner.import`
- **业务路径**: archives/cargoOwner/index.tsx

### 244. src\pages\archives\cargoOwner\index.tsx:75
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.template.download` → `/erp-mdm/hxl.erp.cargo.owner.template.download`
- **业务路径**: archives/cargoOwner/index.tsx

### 245. src\pages\archives\cargoOwner\index.tsx:75
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.template.download` → `/erp-mdm/hxl.erp.cargo.owner.template.download`
- **业务路径**: archives/cargoOwner/index.tsx

### 246. src\pages\archives\cargoOwner\index.tsx:127
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.page` → `/erp-mdm/hxl.erp.cargo.owner.page`
- **业务路径**: archives/cargoOwner/index.tsx

### 247. src\pages\archives\cargoOwner\server.tsx:6
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.save` → `/erp-mdm/hxl.erp.cargo.owner.save`
- **业务路径**: archives/cargoOwner/server.tsx

### 248. src\pages\archives\cargoOwner\server.tsx:10
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.batchdelete` → `/erp-mdm/hxl.erp.cargo.owner.batchdelete`
- **业务路径**: archives/cargoOwner/server.tsx

### 249. src\pages\archives\cargoOwner\server.tsx:14
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.disabled` → `/erp-mdm/hxl.erp.cargo.owner.disabled`
- **业务路径**: archives/cargoOwner/server.tsx

### 250. src\pages\archives\cargoOwner\server.tsx:18
- **业务模块**: 业务页面 > archives > cargoOwner
- **替换内容**: `/erp/hxl.erp.cargo.owner.enabled` → `/erp-mdm/hxl.erp.cargo.owner.enabled`
- **业务路径**: archives/cargoOwner/server.tsx

### 251. src\pages\archives\cargoOwnerCenter\components\modifyRecord\index.tsx:15
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.log.page` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.log.page`
- **业务路径**: archives/cargoOwnerCenter/components/modifyRecord/index.tsx

### 252. src\pages\archives\cargoOwnerCenter\components\modifyRecord\index.tsx:15
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.log.page` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.log.page`
- **业务路径**: archives/cargoOwnerCenter/components/modifyRecord/index.tsx

### 253. src\pages\archives\cargoOwnerCenter\index.tsx:186
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.export` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.export`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 254. src\pages\archives\cargoOwnerCenter\index.tsx:186
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.export` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.export`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 255. src\pages\archives\cargoOwnerCenter\index.tsx:217
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.import` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 256. src\pages\archives\cargoOwnerCenter\index.tsx:217
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.import` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 257. src\pages\archives\cargoOwnerCenter\index.tsx:218
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.template.download` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 258. src\pages\archives\cargoOwnerCenter\index.tsx:218
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.template.download` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 259. src\pages\archives\cargoOwnerCenter\index.tsx:268
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.page` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.page`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 260. src\pages\archives\cargoOwnerCenter\index.tsx:328
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.import` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 261. src\pages\archives\cargoOwnerCenter\index.tsx:328
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.import` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 262. src\pages\archives\cargoOwnerCenter\index.tsx:329
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.template.download` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 263. src\pages\archives\cargoOwnerCenter\index.tsx:329
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.template.download` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 264. src\pages\archives\cargoOwnerCenter\index.tsx:336
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.item.update.import` → `/erp-mdm/hxl.erp.item.update.import`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 265. src\pages\archives\cargoOwnerCenter\index.tsx:336
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.item.update.import` → `/erp-mdm/hxl.erp.item.update.import`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 266. src\pages\archives\cargoOwnerCenter\index.tsx:337
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.itemsupdatetemplate.download` → `/erp-mdm/hxl.erp.itemsupdatetemplate.download`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 267. src\pages\archives\cargoOwnerCenter\index.tsx:337
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.itemsupdatetemplate.download` → `/erp-mdm/hxl.erp.itemsupdatetemplate.download`
- **业务路径**: archives/cargoOwnerCenter/index.tsx

### 268. src\pages\archives\cargoOwnerCenter\server.tsx:7
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.save` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.save`
- **业务路径**: archives/cargoOwnerCenter/server.tsx

### 269. src\pages\archives\cargoOwnerCenter\server.tsx:14
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.update` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.update`
- **业务路径**: archives/cargoOwnerCenter/server.tsx

### 270. src\pages\archives\cargoOwnerCenter\server.tsx:21
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.batchdelete` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.batchdelete`
- **业务路径**: archives/cargoOwnerCenter/server.tsx

### 271. src\pages\archives\cargoOwnerCenter\server.tsx:28
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.copy` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.copy`
- **业务路径**: archives/cargoOwnerCenter/server.tsx

### 272. src\pages\archives\cargoOwnerCenter\server.tsx:37
- **业务模块**: 业务页面 > archives > cargoOwnerCenter
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.conf.sync` → `/erp-mdm/hxl.erp.delivery.cargo.owner.conf.sync`
- **业务路径**: archives/cargoOwnerCenter/server.tsx

### 273. src\pages\archives\companyHeader\index.tsx:44
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.page` → `/erp-mdm/hxl.erp.companyinvoice.page`
- **业务路径**: archives/companyHeader/index.tsx

### 274. src\pages\archives\companyHeader\index.tsx:60
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: archives/companyHeader/index.tsx

### 275. src\pages\archives\companyHeader\index.tsx:70
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.delete` → `/erp-mdm/hxl.erp.companyinvoice.delete`
- **业务路径**: archives/companyHeader/index.tsx

### 276. src\pages\archives\companyHeader\index.tsx:70
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.delete` → `/erp-mdm/hxl.erp.companyinvoice.delete`
- **业务路径**: archives/companyHeader/index.tsx

### 277. src\pages\archives\companyHeader\index.tsx:76
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.save` → `/erp-mdm/hxl.erp.companyinvoice.save`
- **业务路径**: archives/companyHeader/index.tsx

### 278. src\pages\archives\companyHeader\index.tsx:76
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.save` → `/erp-mdm/hxl.erp.companyinvoice.save`
- **业务路径**: archives/companyHeader/index.tsx

### 279. src\pages\archives\companyHeader\index.tsx:96
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.export` → `/erp-mdm/hxl.erp.companyinvoice.export`
- **业务路径**: archives/companyHeader/index.tsx

### 280. src\pages\archives\companyHeader\index.tsx:96
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.export` → `/erp-mdm/hxl.erp.companyinvoice.export`
- **业务路径**: archives/companyHeader/index.tsx

### 281. src\pages\archives\companyHeader\index.tsx:102
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.read` → `/erp-mdm/hxl.erp.companyinvoice.read`
- **业务路径**: archives/companyHeader/index.tsx

### 282. src\pages\archives\companyHeader\index.tsx:224
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.save` → `/erp-mdm/hxl.erp.companyinvoice.save`
- **业务路径**: archives/companyHeader/index.tsx

### 283. src\pages\archives\companyHeader\server.ts:8
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.save` → `/erp-mdm/hxl.erp.companyinvoice.save`
- **业务路径**: archives/companyHeader/server.ts

### 284. src\pages\archives\companyHeader\server.ts:13
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.delete` → `/erp-mdm/hxl.erp.companyinvoice.delete`
- **业务路径**: archives/companyHeader/server.ts

### 285. src\pages\archives\companyHeader\server.ts:18
- **业务模块**: 业务页面 > archives > companyHeader
- **替换内容**: `/erp/hxl.erp.companyinvoice.export` → `/erp-mdm/hxl.erp.companyinvoice.export`
- **业务路径**: archives/companyHeader/server.ts

### 286. src\pages\archives\contractMangement\data.tsx:92
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`
- **业务路径**: archives/contractMangement/data.tsx

### 287. src\pages\archives\contractMangement\data.tsx:115
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`
- **业务路径**: archives/contractMangement/data.tsx

### 288. src\pages\archives\contractMangement\data.tsx:153
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.template.page` → `/erp-mdm/hxl.erp.contract.template.page`
- **业务路径**: archives/contractMangement/data.tsx

### 289. src\pages\archives\contractMangement\data.tsx:187
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`
- **业务路径**: archives/contractMangement/data.tsx

### 290. src\pages\archives\contractMangement\data.tsx:211
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`
- **业务路径**: archives/contractMangement/data.tsx

### 291. src\pages\archives\contractMangement\index.tsx:42
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.page` → `/erp-mdm/hxl.erp.contract.page`
- **业务路径**: archives/contractMangement/index.tsx

### 292. src\pages\archives\contractMangement\index.tsx:58
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.page` → `/erp-mdm/hxl.erp.contract.page`
- **业务路径**: archives/contractMangement/index.tsx

### 293. src\pages\archives\contractMangement\index.tsx:63
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.whitelist.page` → `/erp-mdm/hxl.erp.contract.whitelist.page`
- **业务路径**: archives/contractMangement/index.tsx

### 294. src\pages\archives\contractMangement\server.ts:8
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.read` → `/erp-mdm/hxl.erp.contract.read`
- **业务路径**: archives/contractMangement/server.ts

### 295. src\pages\archives\contractMangement\server.ts:13
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.view` → `/erp-mdm/hxl.erp.contract.view`
- **业务路径**: archives/contractMangement/server.ts

### 296. src\pages\archives\contractMangement\server.ts:18
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.download` → `/erp-mdm/hxl.erp.contract.download`
- **业务路径**: archives/contractMangement/server.ts

### 297. src\pages\archives\contractMangement\server.ts:23
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`
- **业务路径**: archives/contractMangement/server.ts

### 298. src\pages\archives\contractMangement\server.ts:26
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.template.page` → `/erp-mdm/hxl.erp.contract.template.page`
- **业务路径**: archives/contractMangement/server.ts

### 299. src\pages\archives\contractMangement\server.ts:29
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.create` → `/erp-mdm/hxl.erp.contract.create`
- **业务路径**: archives/contractMangement/server.ts

### 300. src\pages\archives\contractMangement\server.ts:32
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.whitelist.page` → `/erp-mdm/hxl.erp.contract.whitelist.page`
- **业务路径**: archives/contractMangement/server.ts

### 301. src\pages\archives\contractMangement\server.ts:35
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.whitelist.create` → `/erp-mdm/hxl.erp.contract.whitelist.create`
- **业务路径**: archives/contractMangement/server.ts

### 302. src\pages\archives\contractMangement\server.ts:58
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.whitelist.export` → `/erp-mdm/hxl.erp.contract.whitelist.export`
- **业务路径**: archives/contractMangement/server.ts

### 303. src\pages\archives\contractMangement\server.ts:58
- **业务模块**: 业务页面 > archives > contractMangement
- **替换内容**: `/erp/hxl.erp.contract.whitelist.export` → `/erp-mdm/hxl.erp.contract.whitelist.export`
- **业务路径**: archives/contractMangement/server.ts

### 304. src\pages\archives\contractTemplateSetup\index.tsx:111
- **业务模块**: 业务页面 > archives > contractTemplateSetup
- **替换内容**: `/erp/hxl.erp.contract.template.page` → `/erp-mdm/hxl.erp.contract.template.page`
- **业务路径**: archives/contractTemplateSetup/index.tsx

### 305. src\pages\archives\contractTemplateSetup\server.ts:5
- **业务模块**: 业务页面 > archives > contractTemplateSetup
- **替换内容**: `/erp/hxl.erp.contract.enum` → `/erp-mdm/hxl.erp.contract.enum`
- **业务路径**: archives/contractTemplateSetup/server.ts

### 306. src\pages\archives\contractTemplateSetup\server.ts:9
- **业务模块**: 业务页面 > archives > contractTemplateSetup
- **替换内容**: `/erp/hxl.erp.contract.template.acquire` → `/erp-mdm/hxl.erp.contract.template.acquire`
- **业务路径**: archives/contractTemplateSetup/server.ts

### 307. src\pages\archives\contractTemplateSetup\server.ts:12
- **业务模块**: 业务页面 > archives > contractTemplateSetup
- **替换内容**: `/erp/hxl.erp.contract.template.update` → `/erp-mdm/hxl.erp.contract.template.update`
- **业务路径**: archives/contractTemplateSetup/server.ts

### 308. src\pages\archives\contractTemplateSetup\server.ts:12
- **业务模块**: 业务页面 > archives > contractTemplateSetup
- **替换内容**: `/erp/hxl.erp.contract.template.save` → `/erp-mdm/hxl.erp.contract.template.save`
- **业务路径**: archives/contractTemplateSetup/server.ts

### 309. src\pages\archives\contractTemplateSetup\server.ts:12
- **业务模块**: 业务页面 > archives > contractTemplateSetup
- **替换内容**: `/erp/hxl.erp.contract.template.update` → `/erp-mdm/hxl.erp.contract.template.update`
- **业务路径**: archives/contractTemplateSetup/server.ts

### 310. src\pages\archives\contractTemplateSetup\server.ts:17
- **业务模块**: 业务页面 > archives > contractTemplateSetup
- **替换内容**: `/erp/hxl.erp.contract.template.delete` → `/erp-mdm/hxl.erp.contract.template.delete`
- **业务路径**: archives/contractTemplateSetup/server.ts

### 311. src\pages\archives\contractTemplateSetup\server.ts:21
- **业务模块**: 业务页面 > archives > contractTemplateSetup
- **替换内容**: `/erp/hxl.erp.contract.template.read` → `/erp-mdm/hxl.erp.contract.template.read`
- **业务路径**: archives/contractTemplateSetup/server.ts

### 312. src\pages\archives\contractTemplateSetup\server.ts:27
- **业务模块**: 业务页面 > archives > contractTemplateSetup
- **替换内容**: `/erp/hxl.erp.contract.template.check` → `/erp-mdm/hxl.erp.contract.template.check`
- **业务路径**: archives/contractTemplateSetup/server.ts

### 313. src\pages\archives\devicesBrand\index.tsx:131
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.brand.update` → `/erp-mdm/hxl.erp.storehardware.brand.update`
- **业务路径**: archives/devicesBrand/index.tsx

### 314. src\pages\archives\devicesBrand\index.tsx:170
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.brand.save` → `/erp-mdm/hxl.erp.storehardware.brand.save`
- **业务路径**: archives/devicesBrand/index.tsx

### 315. src\pages\archives\devicesBrand\index.tsx:170
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.brand.save` → `/erp-mdm/hxl.erp.storehardware.brand.save`
- **业务路径**: archives/devicesBrand/index.tsx

### 316. src\pages\archives\devicesBrand\index.tsx:181
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.brand.delete` → `/erp-mdm/hxl.erp.storehardware.brand.delete`
- **业务路径**: archives/devicesBrand/index.tsx

### 317. src\pages\archives\devicesBrand\index.tsx:181
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.brand.delete` → `/erp-mdm/hxl.erp.storehardware.brand.delete`
- **业务路径**: archives/devicesBrand/index.tsx

### 318. src\pages\archives\devicesBrand\index.tsx:185
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.brand.find` → `/erp-mdm/hxl.erp.storehardware.brand.find`
- **业务路径**: archives/devicesBrand/index.tsx

### 319. src\pages\archives\devicesBrand\server.ts:5
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.brand.delete` → `/erp-mdm/hxl.erp.storehardware.brand.delete`
- **业务路径**: archives/devicesBrand/server.ts

### 320. src\pages\archives\devicesBrand\server.ts:8
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.brand.find` → `/erp-mdm/hxl.erp.storehardware.brand.find`
- **业务路径**: archives/devicesBrand/server.ts

### 321. src\pages\archives\devicesBrand\server.ts:14
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`
- **业务路径**: archives/devicesBrand/server.ts

### 322. src\pages\archives\devicesBrand\server.ts:16
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.brand.save` → `/erp-mdm/hxl.erp.storehardware.brand.save`
- **业务路径**: archives/devicesBrand/server.ts

### 323. src\pages\archives\devicesBrand\server.ts:18
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.brand.update` → `/erp-mdm/hxl.erp.storehardware.brand.update`
- **业务路径**: archives/devicesBrand/server.ts

### 324. src\pages\archives\devicesBrand\server.ts:21
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.category.delete` → `/erp-mdm/hxl.erp.storehardware.category.delete`
- **业务路径**: archives/devicesBrand/server.ts

### 325. src\pages\archives\devicesBrand\server.ts:28
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.category.find` → `/erp-mdm/hxl.erp.storehardware.category.find`
- **业务路径**: archives/devicesBrand/server.ts

### 326. src\pages\archives\devicesBrand\server.ts:34
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.category.update` → `/erp-mdm/hxl.erp.storehardware.category.update`
- **业务路径**: archives/devicesBrand/server.ts

### 327. src\pages\archives\devicesBrand\server.ts:40
- **业务模块**: 业务页面 > archives > devicesBrand
- **替换内容**: `/erp/hxl.erp.storehardware.category.save` → `/erp-mdm/hxl.erp.storehardware.category.save`
- **业务路径**: archives/devicesBrand/server.ts

### 328. src\pages\archives\goodsBranch\index.tsx:14
- **业务模块**: 业务页面 > archives > goodsBranch
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: archives/goodsBranch/index.tsx

### 329. src\pages\archives\goodsBranch\index.tsx:24
- **业务模块**: 业务页面 > archives > goodsBranch
- **替换内容**: `/erp/hxl.erp.dept.delete` → `/erp-mdm/hxl.erp.dept.delete`
- **业务路径**: archives/goodsBranch/index.tsx

### 330. src\pages\archives\goodsBranch\index.tsx:24
- **业务模块**: 业务页面 > archives > goodsBranch
- **替换内容**: `/erp/hxl.erp.dept.delete` → `/erp-mdm/hxl.erp.dept.delete`
- **业务路径**: archives/goodsBranch/index.tsx

### 331. src\pages\archives\goodsBranch\index.tsx:28
- **业务模块**: 业务页面 > archives > goodsBranch
- **替换内容**: `/erp/hxl.erp.dept.save` → `/erp-mdm/hxl.erp.dept.save`
- **业务路径**: archives/goodsBranch/index.tsx

### 332. src\pages\archives\goodsBranch\index.tsx:28
- **业务模块**: 业务页面 > archives > goodsBranch
- **替换内容**: `/erp/hxl.erp.dept.save` → `/erp-mdm/hxl.erp.dept.save`
- **业务路径**: archives/goodsBranch/index.tsx

### 333. src\pages\archives\goodsBranch\index.tsx:57
- **业务模块**: 业务页面 > archives > goodsBranch
- **替换内容**: `/erp/hxl.erp.dept.update` → `/erp-mdm/hxl.erp.dept.update`
- **业务路径**: archives/goodsBranch/index.tsx

### 334. src\pages\archives\goodsBranch\server.ts:8
- **业务模块**: 业务页面 > archives > goodsBranch
- **替换内容**: `/erp/hxl.erp.dept.find` → `/erp-mdm/hxl.erp.dept.find`
- **业务路径**: archives/goodsBranch/server.ts

### 335. src\pages\archives\goodsBranch\server.ts:14
- **业务模块**: 业务页面 > archives > goodsBranch
- **替换内容**: `/erp/hxl.erp.dept.save` → `/erp-mdm/hxl.erp.dept.save`
- **业务路径**: archives/goodsBranch/server.ts

### 336. src\pages\archives\goodsBranch\server.ts:20
- **业务模块**: 业务页面 > archives > goodsBranch
- **替换内容**: `/erp/hxl.erp.dept.delete` → `/erp-mdm/hxl.erp.dept.delete`
- **业务路径**: archives/goodsBranch/server.ts

### 337. src\pages\archives\goodsBranch\server.ts:26
- **业务模块**: 业务页面 > archives > goodsBranch
- **替换内容**: `/erp/hxl.erp.dept.update` → `/erp-mdm/hxl.erp.dept.update`
- **业务路径**: archives/goodsBranch/server.ts

### 338. src\pages\archives\goodsBrand\index.tsx:17
- **业务模块**: 业务页面 > archives > goodsBrand
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: archives/goodsBrand/index.tsx

### 339. src\pages\archives\goodsBrand\index.tsx:27
- **业务模块**: 业务页面 > archives > goodsBrand
- **替换内容**: `/erp/hxl.erp.brand.delete` → `/erp-mdm/hxl.erp.brand.delete`
- **业务路径**: archives/goodsBrand/index.tsx

### 340. src\pages\archives\goodsBrand\index.tsx:27
- **业务模块**: 业务页面 > archives > goodsBrand
- **替换内容**: `/erp/hxl.erp.brand.delete` → `/erp-mdm/hxl.erp.brand.delete`
- **业务路径**: archives/goodsBrand/index.tsx

### 341. src\pages\archives\goodsBrand\index.tsx:31
- **业务模块**: 业务页面 > archives > goodsBrand
- **替换内容**: `/erp/hxl.erp.brand.save` → `/erp-mdm/hxl.erp.brand.save`
- **业务路径**: archives/goodsBrand/index.tsx

### 342. src\pages\archives\goodsBrand\index.tsx:31
- **业务模块**: 业务页面 > archives > goodsBrand
- **替换内容**: `/erp/hxl.erp.brand.save` → `/erp-mdm/hxl.erp.brand.save`
- **业务路径**: archives/goodsBrand/index.tsx

### 343. src\pages\archives\goodsBrand\index.tsx:66
- **业务模块**: 业务页面 > archives > goodsBrand
- **替换内容**: `/erp/hxl.erp.brand.update` → `/erp-mdm/hxl.erp.brand.update`
- **业务路径**: archives/goodsBrand/index.tsx

### 344. src\pages\archives\goodsOrder\component\batchChange.tsx:31
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: archives/goodsOrder/component/batchChange.tsx

### 345. src\pages\archives\goodsOrder\component\batchChange.tsx:31
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: archives/goodsOrder/component/batchChange.tsx

### 346. src\pages\archives\goodsOrder\component\batchChange.tsx:32
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: archives/goodsOrder/component/batchChange.tsx

### 347. src\pages\archives\goodsOrder\component\batchChange.tsx:32
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: archives/goodsOrder/component/batchChange.tsx

### 348. src\pages\archives\goodsOrder\component\batchChange.tsx:51
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: archives/goodsOrder/component/batchChange.tsx

### 349. src\pages\archives\goodsOrder\component\batchChange.tsx:51
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: archives/goodsOrder/component/batchChange.tsx

### 350. src\pages\archives\goodsOrder\component\batchChange.tsx:52
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: archives/goodsOrder/component/batchChange.tsx

### 351. src\pages\archives\goodsOrder\component\batchChange.tsx:52
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: archives/goodsOrder/component/batchChange.tsx

### 352. src\pages\archives\goodsOrder\component\batchChange.tsx:263
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: archives/goodsOrder/component/batchChange.tsx

### 353. src\pages\archives\goodsOrder\component\batchChange.tsx:331
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: archives/goodsOrder/component/batchChange.tsx

### 354. src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx:133
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: archives/goodsOrder/component/batchChangeSpecial.tsx

### 355. src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx:133
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: archives/goodsOrder/component/batchChangeSpecial.tsx

### 356. src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx:134
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: archives/goodsOrder/component/batchChangeSpecial.tsx

### 357. src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx:134
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: archives/goodsOrder/component/batchChangeSpecial.tsx

### 358. src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx:153
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: archives/goodsOrder/component/batchChangeSpecial.tsx

### 359. src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx:153
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: archives/goodsOrder/component/batchChangeSpecial.tsx

### 360. src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx:154
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: archives/goodsOrder/component/batchChangeSpecial.tsx

### 361. src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx:154
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: archives/goodsOrder/component/batchChangeSpecial.tsx

### 362. src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx:205
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: archives/goodsOrder/component/batchChangeSpecial.tsx

### 363. src\pages\archives\goodsOrder\component\batchChangeSpecial.tsx:278
- **业务模块**: 业务页面 > archives > goodsOrder
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: archives/goodsOrder/component/batchChangeSpecial.tsx

### 364. src\pages\archives\goodsUnits\index.tsx:14
- **业务模块**: 业务页面 > archives > goodsUnits
- **替换内容**: `/erp/hxl.erp.itemunit.find` → `/erp-mdm/hxl.erp.itemunit.find`
- **业务路径**: archives/goodsUnits/index.tsx

### 365. src\pages\archives\goodsUnits\index.tsx:24
- **业务模块**: 业务页面 > archives > goodsUnits
- **替换内容**: `/erp/hxl.erp.itemunit.delete` → `/erp-mdm/hxl.erp.itemunit.delete`
- **业务路径**: archives/goodsUnits/index.tsx

### 366. src\pages\archives\goodsUnits\index.tsx:24
- **业务模块**: 业务页面 > archives > goodsUnits
- **替换内容**: `/erp/hxl.erp.itemunit.delete` → `/erp-mdm/hxl.erp.itemunit.delete`
- **业务路径**: archives/goodsUnits/index.tsx

### 367. src\pages\archives\goodsUnits\index.tsx:28
- **业务模块**: 业务页面 > archives > goodsUnits
- **替换内容**: `/erp/hxl.erp.itemunit.save` → `/erp-mdm/hxl.erp.itemunit.save`
- **业务路径**: archives/goodsUnits/index.tsx

### 368. src\pages\archives\goodsUnits\index.tsx:28
- **业务模块**: 业务页面 > archives > goodsUnits
- **替换内容**: `/erp/hxl.erp.itemunit.save` → `/erp-mdm/hxl.erp.itemunit.save`
- **业务路径**: archives/goodsUnits/index.tsx

### 369. src\pages\archives\goodsUnits\index.tsx:56
- **业务模块**: 业务页面 > archives > goodsUnits
- **替换内容**: `/erp/hxl.erp.itemunit.update` → `/erp-mdm/hxl.erp.itemunit.update`
- **业务路径**: archives/goodsUnits/index.tsx

### 370. src\pages\archives\OBSPaymentEntity\index.tsx:146
- **业务模块**: 业务页面 > archives > OBSPaymentEntity
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: archives/OBSPaymentEntity/index.tsx

### 371. src\pages\archives\organizeManage\index.tsx:23
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: archives/organizeManage/index.tsx

### 372. src\pages\archives\organizeManage\index.tsx:40
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: archives/organizeManage/index.tsx

### 373. src\pages\archives\organizeManage\index.tsx:46
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.page` → `/erp-mdm/hxl.erp.org.page`
- **业务路径**: archives/organizeManage/index.tsx

### 374. src\pages\archives\organizeManage\index.tsx:114
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.read` → `/erp-mdm/hxl.erp.org.read`
- **业务路径**: archives/organizeManage/index.tsx

### 375. src\pages\archives\organizeManage\index.tsx:139
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: archives/organizeManage/index.tsx

### 376. src\pages\archives\organizeManage\index.tsx:373
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.server.org.check.default` → `/erp-mdm/hxl.erp.server.org.check.default`
- **业务路径**: archives/organizeManage/index.tsx

### 377. src\pages\archives\organizeManage\index.tsx:432
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.save` → `/erp-mdm/hxl.erp.org.save`
- **业务路径**: archives/organizeManage/index.tsx

### 378. src\pages\archives\organizeManage\index.tsx:432
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.save` → `/erp-mdm/hxl.erp.org.save`
- **业务路径**: archives/organizeManage/index.tsx

### 379. src\pages\archives\organizeManage\index.tsx:441
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.delete` → `/erp-mdm/hxl.erp.org.delete`
- **业务路径**: archives/organizeManage/index.tsx

### 380. src\pages\archives\organizeManage\index.tsx:441
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.delete` → `/erp-mdm/hxl.erp.org.delete`
- **业务路径**: archives/organizeManage/index.tsx

### 381. src\pages\archives\organizeManage\server.ts:8
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.server.org.check.default` → `/erp-mdm/hxl.erp.server.org.check.default`
- **业务路径**: archives/organizeManage/server.ts

### 382. src\pages\archives\organizeManage\server.ts:11
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.update` → `/erp-mdm/hxl.erp.org.update`
- **业务路径**: archives/organizeManage/server.ts

### 383. src\pages\archives\organizeManage\server.ts:14
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.save` → `/erp-mdm/hxl.erp.org.save`
- **业务路径**: archives/organizeManage/server.ts

### 384. src\pages\archives\organizeManage\server.ts:17
- **业务模块**: 业务页面 > archives > organizeManage
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: archives/organizeManage/server.ts

### 385. src\pages\archives\orgBusinessArea\index.tsx:63
- **业务模块**: 业务页面 > archives > orgBusinessArea
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: archives/orgBusinessArea/index.tsx

### 386. src\pages\archives\orgBusinessArea\index.tsx:119
- **业务模块**: 业务页面 > archives > orgBusinessArea
- **替换内容**: `/erp/hxl.erp.org.business.scope.item.batchdelete` → `/erp-mdm/hxl.erp.org.business.scope.item.batchdelete`
- **业务路径**: archives/orgBusinessArea/index.tsx

### 387. src\pages\archives\orgBusinessArea\index.tsx:119
- **业务模块**: 业务页面 > archives > orgBusinessArea
- **替换内容**: `/erp/hxl.erp.org.business.scope.item.batchdelete` → `/erp-mdm/hxl.erp.org.business.scope.item.batchdelete`
- **业务路径**: archives/orgBusinessArea/index.tsx

### 388. src\pages\archives\orgBusinessArea\index.tsx:129
- **业务模块**: 业务页面 > archives > orgBusinessArea
- **替换内容**: `/erp/hxl.erp.org.business.scope.item.export` → `/erp-mdm/hxl.erp.org.business.scope.item.export`
- **业务路径**: archives/orgBusinessArea/index.tsx

### 389. src\pages\archives\orgBusinessArea\index.tsx:129
- **业务模块**: 业务页面 > archives > orgBusinessArea
- **替换内容**: `/erp/hxl.erp.org.business.scope.item.export` → `/erp-mdm/hxl.erp.org.business.scope.item.export`
- **业务路径**: archives/orgBusinessArea/index.tsx

### 390. src\pages\archives\orgBusinessArea\index.tsx:136
- **业务模块**: 业务页面 > archives > orgBusinessArea
- **替换内容**: `/erp/hxl.erp.org.business.scope.item.import` → `/erp-mdm/hxl.erp.org.business.scope.item.import`
- **业务路径**: archives/orgBusinessArea/index.tsx

### 391. src\pages\archives\orgBusinessArea\index.tsx:136
- **业务模块**: 业务页面 > archives > orgBusinessArea
- **替换内容**: `/erp/hxl.erp.org.business.scope.item.import` → `/erp-mdm/hxl.erp.org.business.scope.item.import`
- **业务路径**: archives/orgBusinessArea/index.tsx

### 392. src\pages\archives\orgBusinessArea\index.tsx:138
- **业务模块**: 业务页面 > archives > orgBusinessArea
- **替换内容**: `/erp/hxl.erp.org.business.scope.item.template.download` → `/erp-mdm/hxl.erp.org.business.scope.item.template.download`
- **业务路径**: archives/orgBusinessArea/index.tsx

### 393. src\pages\archives\orgBusinessArea\index.tsx:142
- **业务模块**: 业务页面 > archives > orgBusinessArea
- **替换内容**: `/erp/hxl.erp.org.business.scope.item.page` → `/erp-mdm/hxl.erp.org.business.scope.item.page`
- **业务路径**: archives/orgBusinessArea/index.tsx

### 394. src\pages\archives\orgBusinessArea\server.ts:6
- **业务模块**: 业务页面 > archives > orgBusinessArea
- **替换内容**: `/erp/hxl.erp.org.business.scope.item.add` → `/erp-mdm/hxl.erp.org.business.scope.item.add`
- **业务路径**: archives/orgBusinessArea/server.ts

### 395. src\pages\archives\priceTemplate\header\component\batchChange.tsx:117
- **业务模块**: 业务页面 > archives > priceTemplate
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: archives/priceTemplate/header/component/batchChange.tsx

### 396. src\pages\archives\priceTemplate\header\component\batchChange.tsx:117
- **业务模块**: 业务页面 > archives > priceTemplate
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: archives/priceTemplate/header/component/batchChange.tsx

### 397. src\pages\archives\priceTemplate\header\component\batchChange.tsx:118
- **业务模块**: 业务页面 > archives > priceTemplate
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: archives/priceTemplate/header/component/batchChange.tsx

### 398. src\pages\archives\priceTemplate\header\component\batchChange.tsx:118
- **业务模块**: 业务页面 > archives > priceTemplate
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: archives/priceTemplate/header/component/batchChange.tsx

### 399. src\pages\archives\priceTemplate\header\component\batchChange.tsx:189
- **业务模块**: 业务页面 > archives > priceTemplate
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: archives/priceTemplate/header/component/batchChange.tsx

### 400. src\pages\archives\priceTemplate\item\index.tsx:510
- **业务模块**: 业务页面 > archives > priceTemplate
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: archives/priceTemplate/item/index.tsx

### 401. src\pages\archives\skuCeilingManagement\components\RecordModal.tsx:56
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.log.find` → `/erp-mdm/hxl.erp.org.skulimit.log.find`
- **业务路径**: archives/skuCeilingManagement/components/RecordModal.tsx

### 402. src\pages\archives\skuCeilingManagement\components\RecordModal.tsx:56
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.log.find` → `/erp-mdm/hxl.erp.org.skulimit.log.find`
- **业务路径**: archives/skuCeilingManagement/components/RecordModal.tsx

### 403. src\pages\archives\skuCeilingManagement\components\RecordModal.tsx:57
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.excludeitem.log.find` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.log.find`
- **业务路径**: archives/skuCeilingManagement/components/RecordModal.tsx

### 404. src\pages\archives\skuCeilingManagement\data.tsx:36
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: archives/skuCeilingManagement/data.tsx

### 405. src\pages\archives\skuCeilingManagement\header\index.tsx:460
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: archives/skuCeilingManagement/header/index.tsx

### 406. src\pages\archives\skuCeilingManagement\header\index.tsx:484
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.page` → `/erp-mdm/hxl.erp.org.skulimit.page`
- **业务路径**: archives/skuCeilingManagement/header/index.tsx

### 407. src\pages\archives\skuCeilingManagement\header\index.tsx:534
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimittemplate.download` → `/erp-mdm/hxl.erp.org.skulimittemplate.download`
- **业务路径**: archives/skuCeilingManagement/header/index.tsx

### 408. src\pages\archives\skuCeilingManagement\header\index.tsx:534
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimittemplate.download` → `/erp-mdm/hxl.erp.org.skulimittemplate.download`
- **业务路径**: archives/skuCeilingManagement/header/index.tsx

### 409. src\pages\archives\skuCeilingManagement\header\index.tsx:535
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.update.import` → `/erp-mdm/hxl.erp.org.skulimit.update.import`
- **业务路径**: archives/skuCeilingManagement/header/index.tsx

### 410. src\pages\archives\skuCeilingManagement\header\index.tsx:535
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.update.import` → `/erp-mdm/hxl.erp.org.skulimit.update.import`
- **业务路径**: archives/skuCeilingManagement/header/index.tsx

### 411. src\pages\archives\skuCeilingManagement\header\index.tsx:604
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.item.short.page` → `/erp-mdm/hxl.erp.item.short.page`
- **业务路径**: archives/skuCeilingManagement/header/index.tsx

### 412. src\pages\archives\skuCeilingManagement\header\index.tsx:644
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.excludeitemtemplate.download` → `/erp-mdm/hxl.erp.org.skulimit.excludeitemtemplate.download`
- **业务路径**: archives/skuCeilingManagement/header/index.tsx

### 413. src\pages\archives\skuCeilingManagement\header\index.tsx:644
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.excludeitemtemplate.download` → `/erp-mdm/hxl.erp.org.skulimit.excludeitemtemplate.download`
- **业务路径**: archives/skuCeilingManagement/header/index.tsx

### 414. src\pages\archives\skuCeilingManagement\header\index.tsx:645
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.excludeitem.import` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.import`
- **业务路径**: archives/skuCeilingManagement/header/index.tsx

### 415. src\pages\archives\skuCeilingManagement\header\index.tsx:645
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.excludeitem.import` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.import`
- **业务路径**: archives/skuCeilingManagement/header/index.tsx

### 416. src\pages\archives\skuCeilingManagement\item\index.tsx:103
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.read` → `/erp-mdm/hxl.erp.org.skulimit.read`
- **业务路径**: archives/skuCeilingManagement/item/index.tsx

### 417. src\pages\archives\skuCeilingManagement\server.ts:9
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.update` → `/erp-mdm/hxl.erp.org.skulimit.update`
- **业务路径**: archives/skuCeilingManagement/server.ts

### 418. src\pages\archives\skuCeilingManagement\server.ts:15
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.export` → `/erp-mdm/hxl.erp.org.skulimit.export`
- **业务路径**: archives/skuCeilingManagement/server.ts

### 419. src\pages\archives\skuCeilingManagement\server.ts:20
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.excludeitem.find` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.find`
- **业务路径**: archives/skuCeilingManagement/server.ts

### 420. src\pages\archives\skuCeilingManagement\server.ts:25
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.excludeitem.export` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.export`
- **业务路径**: archives/skuCeilingManagement/server.ts

### 421. src\pages\archives\skuCeilingManagement\server.ts:30
- **业务模块**: 业务页面 > archives > skuCeilingManagement
- **替换内容**: `/erp/hxl.erp.org.skulimit.excludeitem.save` → `/erp-mdm/hxl.erp.org.skulimit.excludeitem.save`
- **业务路径**: archives/skuCeilingManagement/server.ts

### 422. src\pages\archives\storeArea\header\component\batchChange.tsx:50
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.store.import` → `/erp-mdm/hxl.erp.storearea.store.import`
- **业务路径**: archives/storeArea/header/component/batchChange.tsx

### 423. src\pages\archives\storeArea\header\component\batchChange.tsx:50
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.store.import` → `/erp-mdm/hxl.erp.storearea.store.import`
- **业务路径**: archives/storeArea/header/component/batchChange.tsx

### 424. src\pages\archives\storeArea\header\component\batchChange.tsx:51
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: archives/storeArea/header/component/batchChange.tsx

### 425. src\pages\archives\storeArea\header\component\batchChange.tsx:51
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: archives/storeArea/header/component/batchChange.tsx

### 426. src\pages\archives\storeArea\header\index.tsx:42
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.store.import` → `/erp-mdm/hxl.erp.storearea.store.import`
- **业务路径**: archives/storeArea/header/index.tsx

### 427. src\pages\archives\storeArea\header\index.tsx:42
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.store.import` → `/erp-mdm/hxl.erp.storearea.store.import`
- **业务路径**: archives/storeArea/header/index.tsx

### 428. src\pages\archives\storeArea\header\index.tsx:43
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storeareatemplate.download` → `/erp-mdm/hxl.erp.storeareatemplate.download`
- **业务路径**: archives/storeArea/header/index.tsx

### 429. src\pages\archives\storeArea\header\index.tsx:43
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storeareatemplate.download` → `/erp-mdm/hxl.erp.storeareatemplate.download`
- **业务路径**: archives/storeArea/header/index.tsx

### 430. src\pages\archives\storeArea\header\index.tsx:72
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.export` → `/erp-mdm/hxl.erp.storearea.export`
- **业务路径**: archives/storeArea/header/index.tsx

### 431. src\pages\archives\storeArea\header\index.tsx:335
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.read` → `/erp-mdm/hxl.erp.storearea.read`
- **业务路径**: archives/storeArea/header/index.tsx

### 432. src\pages\archives\storeArea\header\index.tsx:350
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.update` → `/erp-mdm/hxl.erp.storearea.update`
- **业务路径**: archives/storeArea/header/index.tsx

### 433. src\pages\archives\storeArea\header\index.tsx:350
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.update` → `/erp-mdm/hxl.erp.storearea.update`
- **业务路径**: archives/storeArea/header/index.tsx

### 434. src\pages\archives\storeArea\header\index.tsx:360
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.save` → `/erp-mdm/hxl.erp.storearea.save`
- **业务路径**: archives/storeArea/header/index.tsx

### 435. src\pages\archives\storeArea\header\index.tsx:372
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.save` → `/erp-mdm/hxl.erp.storearea.save`
- **业务路径**: archives/storeArea/header/index.tsx

### 436. src\pages\archives\storeArea\header\index.tsx:372
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.save` → `/erp-mdm/hxl.erp.storearea.save`
- **业务路径**: archives/storeArea/header/index.tsx

### 437. src\pages\archives\storeArea\header\index.tsx:377
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.delete` → `/erp-mdm/hxl.erp.storearea.delete`
- **业务路径**: archives/storeArea/header/index.tsx

### 438. src\pages\archives\storeArea\header\index.tsx:377
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.delete` → `/erp-mdm/hxl.erp.storearea.delete`
- **业务路径**: archives/storeArea/header/index.tsx

### 439. src\pages\archives\storeArea\header\index.tsx:419
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.find` → `/erp-mdm/hxl.erp.storearea.find`
- **业务路径**: archives/storeArea/header/index.tsx

### 440. src\pages\archives\storeArea\server.ts:7
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.find` → `/erp-mdm/hxl.erp.storearea.find`
- **业务路径**: archives/storeArea/server.ts

### 441. src\pages\archives\storeArea\server.ts:12
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.save` → `/erp-mdm/hxl.erp.storearea.save`
- **业务路径**: archives/storeArea/server.ts

### 442. src\pages\archives\storeArea\server.ts:17
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.delete` → `/erp-mdm/hxl.erp.storearea.delete`
- **业务路径**: archives/storeArea/server.ts

### 443. src\pages\archives\storeArea\server.ts:22
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.batch.export` → `/erp-mdm/hxl.erp.storearea.batch.export`
- **业务路径**: archives/storeArea/server.ts

### 444. src\pages\archives\storeArea\server.ts:27
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.update` → `/erp-mdm/hxl.erp.storearea.update`
- **业务路径**: archives/storeArea/server.ts

### 445. src\pages\archives\storeArea\server.ts:32
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.batchupdate` → `/erp-mdm/hxl.erp.storearea.batchupdate`
- **业务路径**: archives/storeArea/server.ts

### 446. src\pages\archives\storeArea\server.ts:37
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storearea.read` → `/erp-mdm/hxl.erp.storearea.read`
- **业务路径**: archives/storeArea/server.ts

### 447. src\pages\archives\storeArea\server.ts:42
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: archives/storeArea/server.ts

### 448. src\pages\archives\storeArea\server.ts:47
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storeareacategory.delete` → `/erp-mdm/hxl.erp.storeareacategory.delete`
- **业务路径**: archives/storeArea/server.ts

### 449. src\pages\archives\storeArea\server.ts:52
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storeareacategory.find` → `/erp-mdm/hxl.erp.storeareacategory.find`
- **业务路径**: archives/storeArea/server.ts

### 450. src\pages\archives\storeArea\server.ts:57
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storeareacategory.save` → `/erp-mdm/hxl.erp.storeareacategory.save`
- **业务路径**: archives/storeArea/server.ts

### 451. src\pages\archives\storeArea\server.ts:62
- **业务模块**: 业务页面 > archives > storeArea
- **替换内容**: `/erp/hxl.erp.storeareacategory.update` → `/erp-mdm/hxl.erp.storeareacategory.update`
- **业务路径**: archives/storeArea/server.ts

### 452. src\pages\archives\supplierMainBody\index.tsx:13
- **业务模块**: 业务页面 > archives > supplierMainBody
- **替换内容**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
- **业务路径**: archives/supplierMainBody/index.tsx

### 453. src\pages\archives\supplierMainBody\index.tsx:20
- **业务模块**: 业务页面 > archives > supplierMainBody
- **替换内容**: `/erp/hxl.erp.suppliermainbody.delete` → `/erp-mdm/hxl.erp.suppliermainbody.delete`
- **业务路径**: archives/supplierMainBody/index.tsx

### 454. src\pages\archives\supplierMainBody\index.tsx:20
- **业务模块**: 业务页面 > archives > supplierMainBody
- **替换内容**: `/erp/hxl.erp.suppliermainbody.delete` → `/erp-mdm/hxl.erp.suppliermainbody.delete`
- **业务路径**: archives/supplierMainBody/index.tsx

### 455. src\pages\archives\supplierMainBody\index.tsx:24
- **业务模块**: 业务页面 > archives > supplierMainBody
- **替换内容**: `/erp/hxl.erp.suppliermainbody.save` → `/erp-mdm/hxl.erp.suppliermainbody.save`
- **业务路径**: archives/supplierMainBody/index.tsx

### 456. src\pages\archives\supplierMainBody\index.tsx:24
- **业务模块**: 业务页面 > archives > supplierMainBody
- **替换内容**: `/erp/hxl.erp.suppliermainbody.save` → `/erp-mdm/hxl.erp.suppliermainbody.save`
- **业务路径**: archives/supplierMainBody/index.tsx

### 457. src\pages\archives\supplierMainBody\index.tsx:70
- **业务模块**: 业务页面 > archives > supplierMainBody
- **替换内容**: `/erp/hxl.erp.suppliermainbody.update` → `/erp-mdm/hxl.erp.suppliermainbody.update`
- **业务路径**: archives/supplierMainBody/index.tsx

### 458. src\pages\archives\supplierMainBody\index.tsx:70
- **业务模块**: 业务页面 > archives > supplierMainBody
- **替换内容**: `/erp/hxl.erp.suppliermainbody.update` → `/erp-mdm/hxl.erp.suppliermainbody.update`
- **业务路径**: archives/supplierMainBody/index.tsx

### 459. src\pages\archives\supplierMainBody\server.ts:7
- **业务模块**: 业务页面 > archives > supplierMainBody
- **替换内容**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
- **业务路径**: archives/supplierMainBody/server.ts

### 460. src\pages\archives\supplierMainBody\server.ts:12
- **业务模块**: 业务页面 > archives > supplierMainBody
- **替换内容**: `/erp/hxl.erp.suppliermainbody.save` → `/erp-mdm/hxl.erp.suppliermainbody.save`
- **业务路径**: archives/supplierMainBody/server.ts

### 461. src\pages\archives\supplierMainBody\server.ts:16
- **业务模块**: 业务页面 > archives > supplierMainBody
- **替换内容**: `/erp/hxl.erp.suppliermainbody.update` → `/erp-mdm/hxl.erp.suppliermainbody.update`
- **业务路径**: archives/supplierMainBody/server.ts

### 462. src\pages\archives\supplierMainBody\server.ts:20
- **业务模块**: 业务页面 > archives > supplierMainBody
- **替换内容**: `/erp/hxl.erp.suppliermainbody.delete` → `/erp-mdm/hxl.erp.suppliermainbody.delete`
- **业务路径**: archives/supplierMainBody/server.ts

### 463. src\pages\archives\userBranch\index.tsx:14
- **业务模块**: 业务页面 > archives > userBranch
- **替换内容**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
- **业务路径**: archives/userBranch/index.tsx

### 464. src\pages\archives\userBranch\index.tsx:23
- **业务模块**: 业务页面 > archives > userBranch
- **替换内容**: `/erp/hxl.erp.userdept.delete` → `/erp-mdm/hxl.erp.userdept.delete`
- **业务路径**: archives/userBranch/index.tsx

### 465. src\pages\archives\userBranch\index.tsx:23
- **业务模块**: 业务页面 > archives > userBranch
- **替换内容**: `/erp/hxl.erp.userdept.delete` → `/erp-mdm/hxl.erp.userdept.delete`
- **业务路径**: archives/userBranch/index.tsx

### 466. src\pages\archives\userBranch\index.tsx:27
- **业务模块**: 业务页面 > archives > userBranch
- **替换内容**: `/erp/hxl.erp.userdept.save` → `/erp-mdm/hxl.erp.userdept.save`
- **业务路径**: archives/userBranch/index.tsx

### 467. src\pages\archives\userBranch\index.tsx:27
- **业务模块**: 业务页面 > archives > userBranch
- **替换内容**: `/erp/hxl.erp.userdept.save` → `/erp-mdm/hxl.erp.userdept.save`
- **业务路径**: archives/userBranch/index.tsx

### 468. src\pages\archives\userBranch\index.tsx:85
- **业务模块**: 业务页面 > archives > userBranch
- **替换内容**: `/erp/hxl.erp.userdept.save` → `/erp-mdm/hxl.erp.userdept.save`
- **业务路径**: archives/userBranch/index.tsx

### 469. src\pages\archives\userBranch\index.tsx:94
- **业务模块**: 业务页面 > archives > userBranch
- **替换内容**: `/erp/hxl.erp.userdept.update` → `/erp-mdm/hxl.erp.userdept.update`
- **业务路径**: archives/userBranch/index.tsx

### 470. src\pages\archives\userBranch\server.ts:7
- **业务模块**: 业务页面 > archives > userBranch
- **替换内容**: `/erp/hxl.erp.userdept.find` → `/erp-mdm/hxl.erp.userdept.find`
- **业务路径**: archives/userBranch/server.ts

### 471. src\pages\archives\userBranch\server.ts:11
- **业务模块**: 业务页面 > archives > userBranch
- **替换内容**: `/erp/hxl.erp.userdept.save` → `/erp-mdm/hxl.erp.userdept.save`
- **业务路径**: archives/userBranch/server.ts

### 472. src\pages\archives\userBranch\server.ts:16
- **业务模块**: 业务页面 > archives > userBranch
- **替换内容**: `/erp/hxl.erp.userdept.delete` → `/erp-mdm/hxl.erp.userdept.delete`
- **业务路径**: archives/userBranch/server.ts

### 473. src\pages\archives\userBranch\server.ts:21
- **业务模块**: 业务页面 > archives > userBranch
- **替换内容**: `/erp/hxl.erp.userdept.update` → `/erp-mdm/hxl.erp.userdept.update`
- **业务路径**: archives/userBranch/server.ts

### 474. src\pages\dataAnalysis\heightLowInventoryGoods\data.tsx:100
- **业务模块**: 业务页面 > dataAnalysis > heightLowInventoryGoods
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: dataAnalysis/heightLowInventoryGoods/data.tsx

### 475. src\pages\dataAnalysis\heightLowInventoryGoods\data.tsx:123
- **业务模块**: 业务页面 > dataAnalysis > heightLowInventoryGoods
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: dataAnalysis/heightLowInventoryGoods/data.tsx

### 476. src\pages\dataAnalysis\profitLossStatistic\data.tsx:166
- **业务模块**: 业务页面 > dataAnalysis > profitLossStatistic
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: dataAnalysis/profitLossStatistic/data.tsx

### 477. src\pages\dataAnalysis\profitLossStatistic\server.tsx:5
- **业务模块**: 业务页面 > dataAnalysis > profitLossStatistic
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: dataAnalysis/profitLossStatistic/server.tsx

### 478. src\pages\dataAnalysis\profitLossStatistic\server.tsx:10
- **业务模块**: 业务页面 > dataAnalysis > profitLossStatistic
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: dataAnalysis/profitLossStatistic/server.tsx

### 479. src\pages\delivery\advancePosition\server.ts:6
- **业务模块**: 业务页面 > delivery > advancePosition
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: delivery/advancePosition/server.ts

### 480. src\pages\delivery\basketStat\data.ts:59
- **业务模块**: 业务页面 > delivery > basketStat
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/basketStat/data.ts

### 481. src\pages\delivery\basketStat\data.ts:80
- **业务模块**: 业务页面 > delivery > basketStat
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/basketStat/data.ts

### 482. src\pages\delivery\collectDocument\data.tsx:147
- **业务模块**: 业务页面 > delivery > collectDocument
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: delivery/collectDocument/data.tsx

### 483. src\pages\delivery\collectDocument\data.tsx:190
- **业务模块**: 业务页面 > delivery > collectDocument
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/collectDocument/data.tsx

### 484. src\pages\delivery\collectDocument\item.tsx:75
- **业务模块**: 业务页面 > delivery > collectDocument
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/collectDocument/item.tsx

### 485. src\pages\delivery\collectDocument\item.tsx:101
- **业务模块**: 业务页面 > delivery > collectDocument
- **替换内容**: `/erp/hxl.erp.cargo.owner.pageforinner` → `/erp-mdm/hxl.erp.cargo.owner.pageforinner`
- **业务路径**: delivery/collectDocument/item.tsx

### 486. src\pages\delivery\deliveryanalysis\data.tsx:37
- **业务模块**: 业务页面 > delivery > deliveryanalysis
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/deliveryanalysis/data.tsx

### 487. src\pages\delivery\deliveryanalysis\data.tsx:62
- **业务模块**: 业务页面 > delivery > deliveryanalysis
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/deliveryanalysis/data.tsx

### 488. src\pages\delivery\deliveryanalysis\data.tsx:62
- **业务模块**: 业务页面 > delivery > deliveryanalysis
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/deliveryanalysis/data.tsx

### 489. src\pages\delivery\deliveryanalysis\data.tsx:63
- **业务模块**: 业务页面 > delivery > deliveryanalysis
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: delivery/deliveryanalysis/data.tsx

### 490. src\pages\delivery\deliveryanalysis\data.tsx:102
- **业务模块**: 业务页面 > delivery > deliveryanalysis
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/deliveryanalysis/data.tsx

### 491. src\pages\delivery\deliveryanalysis\data.tsx:138
- **业务模块**: 业务页面 > delivery > deliveryanalysis
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/deliveryanalysis/data.tsx

### 492. src\pages\delivery\deliveryanalysis\data.tsx:164
- **业务模块**: 业务页面 > delivery > deliveryanalysis
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/deliveryanalysis/data.tsx

### 493. src\pages\delivery\deliveryanalysis\data.tsx:164
- **业务模块**: 业务页面 > delivery > deliveryanalysis
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/deliveryanalysis/data.tsx

### 494. src\pages\delivery\deliveryanalysis\data.tsx:165
- **业务模块**: 业务页面 > delivery > deliveryanalysis
- **替换内容**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`
- **业务路径**: delivery/deliveryanalysis/data.tsx

### 495. src\pages\delivery\deliveryanalysis\data.tsx:195
- **业务模块**: 业务页面 > delivery > deliveryanalysis
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/deliveryanalysis/data.tsx

### 496. src\pages\delivery\deliveryanalysis\server.ts:20
- **业务模块**: 业务页面 > delivery > deliveryanalysis
- **替换内容**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
- **业务路径**: delivery/deliveryanalysis/server.ts

### 497. src\pages\delivery\deliveryCenterStore\component\batchChange.tsx:33
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: delivery/deliveryCenterStore/component/batchChange.tsx

### 498. src\pages\delivery\deliveryCenterStore\component\batchChange.tsx:33
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: delivery/deliveryCenterStore/component/batchChange.tsx

### 499. src\pages\delivery\deliveryCenterStore\component\batchChange.tsx:34
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/deliveryCenterStore/component/batchChange.tsx

### 500. src\pages\delivery\deliveryCenterStore\component\batchChange.tsx:34
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/deliveryCenterStore/component/batchChange.tsx

### 501. src\pages\delivery\deliveryCenterStore\index.tsx:593
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: delivery/deliveryCenterStore/index.tsx

### 502. src\pages\delivery\deliveryCenterStore\modal.tsx:72
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
- **业务路径**: delivery/deliveryCenterStore/modal.tsx

### 503. src\pages\delivery\deliveryCenterStore\modal.tsx:72
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
- **业务路径**: delivery/deliveryCenterStore/modal.tsx

### 504. src\pages\delivery\deliveryCenterStore\modal.tsx:73
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`
- **业务路径**: delivery/deliveryCenterStore/modal.tsx

### 505. src\pages\delivery\deliveryCenterStore\modal.tsx:73
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`
- **业务路径**: delivery/deliveryCenterStore/modal.tsx

### 506. src\pages\delivery\deliveryCenterStore\server.ts:25
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
- **业务路径**: delivery/deliveryCenterStore/server.ts

### 507. src\pages\delivery\deliveryCenterStore\server.ts:27
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/deliveryCenterStore/server.ts

### 508. src\pages\delivery\deliveryCenterStore\server.ts:33
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.store.orgdeliverycenter.find` → `/erp-mdm/hxl.erp.store.orgdeliverycenter.find`
- **业务路径**: delivery/deliveryCenterStore/server.ts

### 509. src\pages\delivery\deliveryCenterStore\server.ts:37
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.store.sharedeliverycenter.find` → `/erp-mdm/hxl.erp.store.sharedeliverycenter.find`
- **业务路径**: delivery/deliveryCenterStore/server.ts

### 510. src\pages\delivery\deliveryCenterStore\server.ts:42
- **业务模块**: 业务页面 > delivery > deliveryCenterStore
- **替换内容**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`
- **业务路径**: delivery/deliveryCenterStore/server.ts

### 511. src\pages\delivery\deliveryDetails\data.tsx:351
- **业务模块**: 业务页面 > delivery > deliveryDetails
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/deliveryDetails/data.tsx

### 512. src\pages\delivery\deliveryDetails\index.tsx:98
- **业务模块**: 业务页面 > delivery > deliveryDetails
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/deliveryDetails/index.tsx

### 513. src\pages\delivery\deliveryPriceMange\header\index.tsx:87
- **业务模块**: 业务页面 > delivery > deliveryPriceMange
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: delivery/deliveryPriceMange/header/index.tsx

### 514. src\pages\delivery\deliveryPriceMange\item\components\xlbBaseGoods\server.ts:45
- **业务模块**: 业务页面 > delivery > deliveryPriceMange
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: delivery/deliveryPriceMange/item/components/xlbBaseGoods/server.ts

### 515. src\pages\delivery\deliveryPriceMange\server.ts:66
- **业务模块**: 业务页面 > delivery > deliveryPriceMange
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: delivery/deliveryPriceMange/server.ts

### 516. src\pages\delivery\deliverySpecialPrice\api.ts:74
- **业务模块**: 业务页面 > delivery > deliverySpecialPrice
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: delivery/deliverySpecialPrice/api.ts

### 517. src\pages\delivery\deliverySpecialPrice\api.ts:78
- **业务模块**: 业务页面 > delivery > deliverySpecialPrice
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/deliverySpecialPrice/api.ts

### 518. src\pages\delivery\deliverySpecialPrice\api.ts:82
- **业务模块**: 业务页面 > delivery > deliverySpecialPrice
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/deliverySpecialPrice/api.ts

### 519. src\pages\delivery\deliverySpecialPrice\api.ts:86
- **业务模块**: 业务页面 > delivery > deliverySpecialPrice
- **替换内容**: `/erp/hxl.erp.org.findbylevel` → `/erp-mdm/hxl.erp.org.findbylevel`
- **业务路径**: delivery/deliverySpecialPrice/api.ts

### 520. src\pages\delivery\deliverySpecialPrice\item\index.tsx:77
- **业务模块**: 业务页面 > delivery > deliverySpecialPrice
- **替换内容**: `/erp/hxl.erp.delivery.cargo.owner.org.find` → `/erp-mdm/hxl.erp.delivery.cargo.owner.org.find`
- **业务路径**: delivery/deliverySpecialPrice/item/index.tsx

### 521. src\pages\delivery\deliverySpecialPrice\item\index.tsx:744
- **业务模块**: 业务页面 > delivery > deliverySpecialPrice
- **替换内容**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
- **业务路径**: delivery/deliverySpecialPrice/item/index.tsx

### 522. src\pages\delivery\deliverySpecialPrice\item\index.tsx:744
- **业务模块**: 业务页面 > delivery > deliverySpecialPrice
- **替换内容**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
- **业务路径**: delivery/deliverySpecialPrice/item/index.tsx

### 523. src\pages\delivery\deliverySpecialPrice\item\index.tsx:745
- **业务模块**: 业务页面 > delivery > deliverySpecialPrice
- **替换内容**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`
- **业务路径**: delivery/deliverySpecialPrice/item/index.tsx

### 524. src\pages\delivery\deliverySpecialPrice\item\index.tsx:745
- **业务模块**: 业务页面 > delivery > deliverySpecialPrice
- **替换内容**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`
- **业务路径**: delivery/deliverySpecialPrice/item/index.tsx

### 525. src\pages\delivery\deliverySpecialPrice\item\index.tsx:1117
- **业务模块**: 业务页面 > delivery > deliverySpecialPrice
- **替换内容**: `/erp/hxl.erp.store.area.find` → `/erp-mdm/hxl.erp.store.area.find`
- **业务路径**: delivery/deliverySpecialPrice/item/index.tsx

### 526. src\pages\delivery\deliverySpecialPriceAnalysis\index.tsx:45
- **业务模块**: 业务页面 > delivery > deliverySpecialPriceAnalysis
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/deliverySpecialPriceAnalysis/index.tsx

### 527. src\pages\delivery\deliverySpecialPriceAnalysis\index.tsx:82
- **业务模块**: 业务页面 > delivery > deliverySpecialPriceAnalysis
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/deliverySpecialPriceAnalysis/index.tsx

### 528. src\pages\delivery\directSupplyPoint\index.tsx:52
- **业务模块**: 业务页面 > delivery > directSupplyPoint
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/directSupplyPoint/index.tsx

### 529. src\pages\delivery\directSupplyPoint\index.tsx:355
- **业务模块**: 业务页面 > delivery > directSupplyPoint
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/directSupplyPoint/index.tsx

### 530. src\pages\delivery\distributionGross\index.tsx:81
- **业务模块**: 业务页面 > delivery > distributionGross
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/distributionGross/index.tsx

### 531. src\pages\delivery\distributionGross\index.tsx:135
- **业务模块**: 业务页面 > delivery > distributionGross
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/distributionGross/index.tsx

### 532. src\pages\delivery\distributionGross\index.tsx:162
- **业务模块**: 业务页面 > delivery > distributionGross
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/distributionGross/index.tsx

### 533. src\pages\delivery\distributionGross\index.tsx:212
- **业务模块**: 业务页面 > delivery > distributionGross
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/distributionGross/index.tsx

### 534. src\pages\delivery\distributionGross\index.tsx:272
- **业务模块**: 业务页面 > delivery > distributionGross
- **替换内容**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
- **业务路径**: delivery/distributionGross/index.tsx

### 535. src\pages\delivery\distributionGross\index.tsx:394
- **业务模块**: 业务页面 > delivery > distributionGross
- **替换内容**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`
- **业务路径**: delivery/distributionGross/index.tsx

### 536. src\pages\delivery\distributionGross\sever.ts:55
- **业务模块**: 业务页面 > delivery > distributionGross
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/distributionGross/sever.ts

### 537. src\pages\delivery\distributionGross\sever.ts:60
- **业务模块**: 业务页面 > delivery > distributionGross
- **替换内容**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
- **业务路径**: delivery/distributionGross/sever.ts

### 538. src\pages\delivery\distributionGross\sever.ts:65
- **业务模块**: 业务页面 > delivery > distributionGross
- **替换内容**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`
- **业务路径**: delivery/distributionGross/sever.ts

### 539. src\pages\delivery\forceDeliveryRule\components\addPCRules\index.tsx:45
- **业务模块**: 业务页面 > delivery > forceDeliveryRule
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/forceDeliveryRule/components/addPCRules/index.tsx

### 540. src\pages\delivery\forceDeliveryRule\components\addRules\index.tsx:52
- **业务模块**: 业务页面 > delivery > forceDeliveryRule
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/forceDeliveryRule/components/addRules/index.tsx

### 541. src\pages\delivery\goodsway\data.ts:25
- **业务模块**: 业务页面 > delivery > goodsway
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/goodsway/data.ts

### 542. src\pages\delivery\goodsway\data.ts:49
- **业务模块**: 业务页面 > delivery > goodsway
- **替换内容**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`
- **业务路径**: delivery/goodsway/data.ts

### 543. src\pages\delivery\goodsway\data.ts:72
- **业务模块**: 业务页面 > delivery > goodsway
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/goodsway/data.ts

### 544. src\pages\delivery\goodsway\data.ts:97
- **业务模块**: 业务页面 > delivery > goodsway
- **替换内容**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`
- **业务路径**: delivery/goodsway/data.ts

### 545. src\pages\delivery\goodsway\data.ts:126
- **业务模块**: 业务页面 > delivery > goodsway
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/goodsway/data.ts

### 546. src\pages\delivery\goodsway\index.tsx:160
- **业务模块**: 业务页面 > delivery > goodsway
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/goodsway/index.tsx

### 547. src\pages\delivery\marketingCampaign\index.tsx:85
- **业务模块**: 业务页面 > delivery > marketingCampaign
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: delivery/marketingCampaign/index.tsx

### 548. src\pages\delivery\marketingCampaign\item.tsx:53
- **业务模块**: 业务页面 > delivery > marketingCampaign
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/marketingCampaign/item.tsx

### 549. src\pages\delivery\marketingCampaign\item.tsx:79
- **业务模块**: 业务页面 > delivery > marketingCampaign
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: delivery/marketingCampaign/item.tsx

### 550. src\pages\delivery\marketingCampaign\server.ts:59
- **业务模块**: 业务页面 > delivery > marketingCampaign
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: delivery/marketingCampaign/server.ts

### 551. src\pages\delivery\receivingApplication\data.tsx:193
- **业务模块**: 业务页面 > delivery > receivingApplication
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/receivingApplication/data.tsx

### 552. src\pages\delivery\receivingApplication\data.tsx:251
- **业务模块**: 业务页面 > delivery > receivingApplication
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/receivingApplication/data.tsx

### 553. src\pages\delivery\receivingApplication\item.tsx:78
- **业务模块**: 业务页面 > delivery > receivingApplication
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/receivingApplication/item.tsx

### 554. src\pages\delivery\receivingApplication\item.tsx:104
- **业务模块**: 业务页面 > delivery > receivingApplication
- **替换内容**: `/erp/hxl.erp.cargo.owner.pageforinner` → `/erp-mdm/hxl.erp.cargo.owner.pageforinner`
- **业务路径**: delivery/receivingApplication/item.tsx

### 555. src\pages\delivery\replenishGoodsAnalysis\data.tsx:70
- **业务模块**: 业务页面 > delivery > replenishGoodsAnalysis
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/replenishGoodsAnalysis/data.tsx

### 556. src\pages\delivery\replenishGoodsAnalysis\data.tsx:125
- **业务模块**: 业务页面 > delivery > replenishGoodsAnalysis
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/replenishGoodsAnalysis/data.tsx

### 557. src\pages\delivery\replenishGoodsAnalysis\data.tsx:143
- **业务模块**: 业务页面 > delivery > replenishGoodsAnalysis
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: delivery/replenishGoodsAnalysis/data.tsx

### 558. src\pages\delivery\replenishTemplate\item\index.tsx:149
- **业务模块**: 业务页面 > delivery > replenishTemplate
- **替换内容**: `/erp/hxl.erp.item.short.page` → `/erp-mdm/hxl.erp.item.short.page`
- **业务路径**: delivery/replenishTemplate/item/index.tsx

### 559. src\pages\delivery\replenishTemplate\item\index.tsx:619
- **业务模块**: 业务页面 > delivery > replenishTemplate
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/replenishTemplate/item/index.tsx

### 560. src\pages\delivery\saleParam\index.tsx:401
- **业务模块**: 业务页面 > delivery > saleParam
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/saleParam/index.tsx

### 561. src\pages\delivery\saleParam\index.tsx:1225
- **业务模块**: 业务页面 > delivery > saleParam
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/saleParam/index.tsx

### 562. src\pages\delivery\saleParam\index.tsx:1297
- **业务模块**: 业务页面 > delivery > saleParam
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: delivery/saleParam/index.tsx

### 563. src\pages\delivery\stockForecasts\component\batchChange.tsx:78
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: delivery/stockForecasts/component/batchChange.tsx

### 564. src\pages\delivery\stockForecasts\component\batchChange.tsx:78
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: delivery/stockForecasts/component/batchChange.tsx

### 565. src\pages\delivery\stockForecasts\component\batchChange.tsx:79
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/stockForecasts/component/batchChange.tsx

### 566. src\pages\delivery\stockForecasts\component\batchChange.tsx:79
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/stockForecasts/component/batchChange.tsx

### 567. src\pages\delivery\stockForecasts\component\batchChange.tsx:162
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/stockForecasts/component/batchChange.tsx

### 568. src\pages\delivery\stockForecasts\index.tsx:199
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: delivery/stockForecasts/index.tsx

### 569. src\pages\delivery\stockForecasts\index.tsx:233
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.store.page` → `/erp-mdm/hxl.erp.store.page`
- **业务路径**: delivery/stockForecasts/index.tsx

### 570. src\pages\delivery\stockForecasts\server.ts:6
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.store.page` → `/erp-mdm/hxl.erp.store.page`
- **业务路径**: delivery/stockForecasts/server.ts

### 571. src\pages\delivery\stockForecasts\server.ts:35
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.storegroup.find` → `/erp-mdm/hxl.erp.storegroup.find`
- **业务路径**: delivery/stockForecasts/server.ts

### 572. src\pages\delivery\stockForecasts\server.ts:40
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.storegroup.delete` → `/erp-mdm/hxl.erp.storegroup.delete`
- **业务路径**: delivery/stockForecasts/server.ts

### 573. src\pages\delivery\stockForecasts\server.ts:45
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.storegroup.update` → `/erp-mdm/hxl.erp.storegroup.update`
- **业务路径**: delivery/stockForecasts/server.ts

### 574. src\pages\delivery\stockForecasts\server.ts:49
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.storegroup.save` → `/erp-mdm/hxl.erp.storegroup.save`
- **业务路径**: delivery/stockForecasts/server.ts

### 575. src\pages\delivery\stockForecasts\server.ts:61
- **业务模块**: 业务页面 > delivery > stockForecasts
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/stockForecasts/server.ts

### 576. src\pages\delivery\stockPrediction\data.ts:125
- **业务模块**: 业务页面 > delivery > stockPrediction
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/stockPrediction/data.ts

### 577. src\pages\delivery\stockTypeSetting\item\index.tsx:159
- **业务模块**: 业务页面 > delivery > stockTypeSetting
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/stockTypeSetting/item/index.tsx

### 578. src\pages\delivery\stockTypeSetting\item\index.tsx:464
- **业务模块**: 业务页面 > delivery > stockTypeSetting
- **替换内容**: `/erp/hxl.erp.file.upload` → `/erp-mdm/hxl.erp.file.upload`
- **业务路径**: delivery/stockTypeSetting/item/index.tsx

### 579. src\pages\delivery\storeDeliveryDay\index.tsx:55
- **业务模块**: 业务页面 > delivery > storeDeliveryDay
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: delivery/storeDeliveryDay/index.tsx

### 580. src\pages\delivery\storeDeliveryDay\item.tsx:191
- **业务模块**: 业务页面 > delivery > storeDeliveryDay
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/storeDeliveryDay/item.tsx

### 581. src\pages\delivery\storeDeliveryDay\item.tsx:191
- **业务模块**: 业务页面 > delivery > storeDeliveryDay
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/storeDeliveryDay/item.tsx

### 582. src\pages\delivery\storeDeliveryPrice\component\batchChange\batchChange.tsx:116
- **业务模块**: 业务页面 > delivery > storeDeliveryPrice
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: delivery/storeDeliveryPrice/component/batchChange/batchChange.tsx

### 583. src\pages\delivery\storeDeliveryPrice\component\batchChange\batchChange.tsx:116
- **业务模块**: 业务页面 > delivery > storeDeliveryPrice
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: delivery/storeDeliveryPrice/component/batchChange/batchChange.tsx

### 584. src\pages\delivery\storeDeliveryPrice\component\batchChange\batchChange.tsx:117
- **业务模块**: 业务页面 > delivery > storeDeliveryPrice
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/storeDeliveryPrice/component/batchChange/batchChange.tsx

### 585. src\pages\delivery\storeDeliveryPrice\component\batchChange\batchChange.tsx:117
- **业务模块**: 业务页面 > delivery > storeDeliveryPrice
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/storeDeliveryPrice/component/batchChange/batchChange.tsx

### 586. src\pages\delivery\storeDeliveryPrice\component\batchChange\batchChange.tsx:143
- **业务模块**: 业务页面 > delivery > storeDeliveryPrice
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: delivery/storeDeliveryPrice/component/batchChange/batchChange.tsx

### 587. src\pages\delivery\storeDeliveryPrice\component\batchChange\batchChange.tsx:143
- **业务模块**: 业务页面 > delivery > storeDeliveryPrice
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: delivery/storeDeliveryPrice/component/batchChange/batchChange.tsx

### 588. src\pages\delivery\storeDeliveryPrice\component\batchChange\batchChange.tsx:144
- **业务模块**: 业务页面 > delivery > storeDeliveryPrice
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: delivery/storeDeliveryPrice/component/batchChange/batchChange.tsx

### 589. src\pages\delivery\storeDeliveryPrice\component\batchChange\batchChange.tsx:144
- **业务模块**: 业务页面 > delivery > storeDeliveryPrice
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: delivery/storeDeliveryPrice/component/batchChange/batchChange.tsx

### 590. src\pages\delivery\storeDeliveryPrice\component\batchChange\batchChange.tsx:358
- **业务模块**: 业务页面 > delivery > storeDeliveryPrice
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/storeDeliveryPrice/component/batchChange/batchChange.tsx

### 591. src\pages\delivery\storeDeliveryPrice\component\copy\copy.tsx:145
- **业务模块**: 业务页面 > delivery > storeDeliveryPrice
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/storeDeliveryPrice/component/copy/copy.tsx

### 592. src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx:133
- **业务模块**: 业务页面 > delivery > storeItemReplenish
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx

### 593. src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx:133
- **业务模块**: 业务页面 > delivery > storeItemReplenish
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx

### 594. src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx:134
- **业务模块**: 业务页面 > delivery > storeItemReplenish
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx

### 595. src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx:134
- **业务模块**: 业务页面 > delivery > storeItemReplenish
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx

### 596. src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx:151
- **业务模块**: 业务页面 > delivery > storeItemReplenish
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx

### 597. src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx:151
- **业务模块**: 业务页面 > delivery > storeItemReplenish
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx

### 598. src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx:152
- **业务模块**: 业务页面 > delivery > storeItemReplenish
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx

### 599. src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx:152
- **业务模块**: 业务页面 > delivery > storeItemReplenish
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx

### 600. src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx:167
- **业务模块**: 业务页面 > delivery > storeItemReplenish
- **替换内容**: `/erp/hxl.erp.org.page` → `/erp-mdm/hxl.erp.org.page`
- **业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx

### 601. src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx:219
- **业务模块**: 业务页面 > delivery > storeItemReplenish
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx

### 602. src\pages\delivery\storeItemReplenish\header\component\batchChange\batchChange.tsx:288
- **业务模块**: 业务页面 > delivery > storeItemReplenish
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: delivery/storeItemReplenish/header/component/batchChange/batchChange.tsx

### 603. src\pages\delivery\storeOrderingDate\header\index.tsx:52
- **业务模块**: 业务页面 > delivery > storeOrderingDate
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: delivery/storeOrderingDate/header/index.tsx

### 604. src\pages\delivery\storeOrderingDate\item\index.tsx:150
- **业务模块**: 业务页面 > delivery > storeOrderingDate
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/storeOrderingDate/item/index.tsx

### 605. src\pages\delivery\storeOrderingDate\item\index.tsx:150
- **业务模块**: 业务页面 > delivery > storeOrderingDate
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: delivery/storeOrderingDate/item/index.tsx

### 606. src\pages\delivery\storeOrders\components\batchOrder\batchOrder.tsx:402
- **业务模块**: 业务页面 > delivery > storeOrders
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/storeOrders/components/batchOrder/batchOrder.tsx

### 607. src\pages\delivery\storeOrders\components\deliveryOrder\deliveryOrder.tsx:276
- **业务模块**: 业务页面 > delivery > storeOrders
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/storeOrders/components/deliveryOrder/deliveryOrder.tsx

### 608. src\pages\delivery\storeOrders\components\uploadPhotoGroup.tsx:86
- **业务模块**: 业务页面 > delivery > storeOrders
- **替换内容**: `/erp/hxl.erp.file.delete` → `/erp-mdm/hxl.erp.file.delete`
- **业务路径**: delivery/storeOrders/components/uploadPhotoGroup.tsx

### 609. src\pages\delivery\storeOrders\item\index.tsx:425
- **业务模块**: 业务页面 > delivery > storeOrders
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/storeOrders/item/index.tsx

### 610. src\pages\delivery\storeOrders\item\server.ts:87
- **业务模块**: 业务页面 > delivery > storeOrders
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/storeOrders/item/server.ts

### 611. src\pages\delivery\storeOrders\item\server.ts:95
- **业务模块**: 业务页面 > delivery > storeOrders
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: delivery/storeOrders/item/server.ts

### 612. src\pages\delivery\storeOrders\item\server.ts:99
- **业务模块**: 业务页面 > delivery > storeOrders
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/storeOrders/item/server.ts

### 613. src\pages\delivery\storeOrders\item\server.ts:106
- **业务模块**: 业务页面 > delivery > storeOrders
- **替换内容**: `/erp/hxl.erp.store.all.shortfind` → `/erp-mdm/hxl.erp.store.all.shortfind`
- **业务路径**: delivery/storeOrders/item/server.ts

### 614. src\pages\delivery\storeOrders\item\server.ts:110
- **业务模块**: 业务页面 > delivery > storeOrders
- **替换内容**: `/erp/hxl.erp.store.allcenter.find` → `/erp-mdm/hxl.erp.store.allcenter.find`
- **业务路径**: delivery/storeOrders/item/server.ts

### 615. src\pages\delivery\storeOrders\item\server.ts:114
- **业务模块**: 业务页面 > delivery > storeOrders
- **替换内容**: `/erp/hxl.erp.store.balance.read` → `/erp-mdm/hxl.erp.store.balance.read`
- **业务路径**: delivery/storeOrders/item/server.ts

### 616. src\pages\delivery\storeOrders\item\server.ts:118
- **业务模块**: 业务页面 > delivery > storeOrders
- **替换内容**: `/erp/hxl.erp.item.read` → `/erp-mdm/hxl.erp.item.read`
- **业务路径**: delivery/storeOrders/item/server.ts

### 617. src\pages\delivery\strongDataAnalysis\index.tsx:42
- **业务模块**: 业务页面 > delivery > strongDataAnalysis
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/strongDataAnalysis/index.tsx

### 618. src\pages\delivery\strongDataAnalysis\index.tsx:86
- **业务模块**: 业务页面 > delivery > strongDataAnalysis
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/strongDataAnalysis/index.tsx

### 619. src\pages\delivery\strongDataAnalysis\index.tsx:265
- **业务模块**: 业务页面 > delivery > strongDataAnalysis
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/strongDataAnalysis/index.tsx

### 620. src\pages\delivery\supplyAnalyze\server.ts:13
- **业务模块**: 业务页面 > delivery > supplyAnalyze
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: delivery/supplyAnalyze/server.ts

### 621. src\pages\delivery\supplyAnalyze\server.ts:18
- **业务模块**: 业务页面 > delivery > supplyAnalyze
- **替换内容**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
- **业务路径**: delivery/supplyAnalyze/server.ts

### 622. src\pages\delivery\transferDocument\components\batchOrder\batchOrder.tsx:453
- **业务模块**: 业务页面 > delivery > transferDocument
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/transferDocument/components/batchOrder/batchOrder.tsx

### 623. src\pages\delivery\transferDocument\index.tsx:115
- **业务模块**: 业务页面 > delivery > transferDocument
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/transferDocument/index.tsx

### 624. src\pages\delivery\transferDocument\item\index.tsx:625
- **业务模块**: 业务页面 > delivery > transferDocument
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: delivery/transferDocument/item/index.tsx

### 625. src\pages\delivery\transferDocument\item\index.tsx:625
- **业务模块**: 业务页面 > delivery > transferDocument
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: delivery/transferDocument/item/index.tsx

### 626. src\pages\delivery\transferDocument\item\index.tsx:626
- **业务模块**: 业务页面 > delivery > transferDocument
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: delivery/transferDocument/item/index.tsx

### 627. src\pages\delivery\transferDocument\server.ts:58
- **业务模块**: 业务页面 > delivery > transferDocument
- **替换内容**: `/erp/hxl.erp.store.center.find` → `/erp-mdm/hxl.erp.store.center.find`
- **业务路径**: delivery/transferDocument/server.ts

### 628. src\pages\delivery\transferWay\data.ts:68
- **业务模块**: 业务页面 > delivery > transferWay
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: delivery/transferWay/data.ts

### 629. src\pages\procurement\orderParamsConfig\data.ts:20
- **业务模块**: 业务页面 > procurement > orderParamsConfig
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: procurement/orderParamsConfig/data.ts

### 630. src\pages\procurement\orderValidDay\data.tsx:13
- **业务模块**: 业务页面 > procurement > orderValidDay
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: procurement/orderValidDay/data.tsx

### 631. src\pages\procurement\orderValidDay\index.tsx:126
- **业务模块**: 业务页面 > procurement > orderValidDay
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: procurement/orderValidDay/index.tsx

### 632. src\pages\procurement\purchaseLatestPrice\index.tsx:163
- **业务模块**: 业务页面 > procurement > purchaseLatestPrice
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: procurement/purchaseLatestPrice/index.tsx

### 633. src\pages\procurement\purchaseLatestPrice\index.tsx:250
- **业务模块**: 业务页面 > procurement > purchaseLatestPrice
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: procurement/purchaseLatestPrice/index.tsx

### 634. src\pages\procurement\purchasePrice\data.ts:72
- **业务模块**: 业务页面 > procurement > purchasePrice
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: procurement/purchasePrice/data.ts

### 635. src\pages\procurement\purchasePrice\server.ts:13
- **业务模块**: 业务页面 > procurement > purchasePrice
- **替换内容**: `/erp/hxl.erp.brand.find` → `/erp-mdm/hxl.erp.brand.find`
- **业务路径**: procurement/purchasePrice/server.ts

### 636. src\pages\procurement\purchaseReport\data.tsx:157
- **业务模块**: 业务页面 > procurement > purchaseReport
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: procurement/purchaseReport/data.tsx

### 637. src\pages\procurement\purchaseReport\data.tsx:207
- **业务模块**: 业务页面 > procurement > purchaseReport
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: procurement/purchaseReport/data.tsx

### 638. src\pages\procurement\purchaseReport\data.tsx:284
- **业务模块**: 业务页面 > procurement > purchaseReport
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: procurement/purchaseReport/data.tsx

### 639. src\pages\procurement\purchaseReport\data.tsx:438
- **业务模块**: 业务页面 > procurement > purchaseReport
- **替换内容**: `/erp/hxl.erp.store.area.find.all` → `/erp-mdm/hxl.erp.store.area.find.all`
- **业务路径**: procurement/purchaseReport/data.tsx

### 640. src\pages\procurement\purchaseReport\server.ts:73
- **业务模块**: 业务页面 > procurement > purchaseReport
- **替换内容**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`
- **业务路径**: procurement/purchaseReport/server.ts

### 641. src\pages\procurement\purchaseReport\server.ts:79
- **业务模块**: 业务页面 > procurement > purchaseReport
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: procurement/purchaseReport/server.ts

### 642. src\pages\procurement\purchaseShare\header\index.tsx:184
- **业务模块**: 业务页面 > procurement > purchaseShare
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: procurement/purchaseShare/header/index.tsx

### 643. src\pages\procurement\purchaseShare\header\server.ts:44
- **业务模块**: 业务页面 > procurement > purchaseShare
- **替换内容**: `/erp/hxl.erp.store.short.page` → `/erp-mdm/hxl.erp.store.short.page`
- **业务路径**: procurement/purchaseShare/header/server.ts

### 644. src\pages\procurement\purchaseShare\header\server.ts:49
- **业务模块**: 业务页面 > procurement > purchaseShare
- **替换内容**: `/erp/hxl.erp.store.all.find` → `/erp-mdm/hxl.erp.store.all.find`
- **业务路径**: procurement/purchaseShare/header/server.ts

### 645. src\pages\procurement\stockPlan\data.ts:46
- **业务模块**: 业务页面 > procurement > stockPlan
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: procurement/stockPlan/data.ts

### 646. src\pages\procurement\stockPlan\index.tsx:206
- **业务模块**: 业务页面 > procurement > stockPlan
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: procurement/stockPlan/index.tsx

### 647. src\pages\procurement\stockPlan\index.tsx:206
- **业务模块**: 业务页面 > procurement > stockPlan
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: procurement/stockPlan/index.tsx

### 648. src\pages\procurement\supplierRelationshipManagement\header\components\Additem\index.tsx:383
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: procurement/supplierRelationshipManagement/header/components/Additem/index.tsx

### 649. src\pages\procurement\supplierRelationshipManagement\header\index.tsx:62
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: procurement/supplierRelationshipManagement/header/index.tsx

### 650. src\pages\procurement\supplierRelationshipManagement\header\index.tsx:962
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: procurement/supplierRelationshipManagement/header/index.tsx

### 651. src\pages\procurement\supplierRelationshipManagement\header\index.tsx:962
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: procurement/supplierRelationshipManagement/header/index.tsx

### 652. src\pages\procurement\supplierRelationshipManagement\header\index.tsx:963
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: procurement/supplierRelationshipManagement/header/index.tsx

### 653. src\pages\procurement\supplierRelationshipManagement\header\index.tsx:963
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: procurement/supplierRelationshipManagement/header/index.tsx

### 654. src\pages\procurement\supplierRelationshipManagement\header\index.tsx:980
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: procurement/supplierRelationshipManagement/header/index.tsx

### 655. src\pages\procurement\supplierRelationshipManagement\header\index.tsx:980
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: procurement/supplierRelationshipManagement/header/index.tsx

### 656. src\pages\procurement\supplierRelationshipManagement\header\index.tsx:981
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: procurement/supplierRelationshipManagement/header/index.tsx

### 657. src\pages\procurement\supplierRelationshipManagement\header\index.tsx:981
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: procurement/supplierRelationshipManagement/header/index.tsx

### 658. src\pages\procurement\supplierRelationshipManagement\header\index.tsx:1100
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: procurement/supplierRelationshipManagement/header/index.tsx

### 659. src\pages\procurement\supplierRelationshipManagement\server.ts:9
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.supplier.producerandexecutivestandard.find` → `/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find`
- **业务路径**: procurement/supplierRelationshipManagement/server.ts

### 660. src\pages\procurement\supplierRelationshipManagement\server.ts:37
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
- **业务路径**: procurement/supplierRelationshipManagement/server.ts

### 661. src\pages\procurement\supplierRelationshipManagement\server.ts:41
- **业务模块**: 业务页面 > procurement > supplierRelationshipManagement
- **替换内容**: `/erp/hxl.erp.baseparam.read` → `/erp-mdm/hxl.erp.baseparam.read`
- **业务路径**: procurement/supplierRelationshipManagement/server.ts

### 662. src\pages\purchase\mustSellGoodsDetail\data.tsx:21
- **业务模块**: 业务页面 > purchase > mustSellGoodsDetail
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: purchase/mustSellGoodsDetail/data.tsx

### 663. src\pages\purchase\mustSellGoodsDetail\data.tsx:78
- **业务模块**: 业务页面 > purchase > mustSellGoodsDetail
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: purchase/mustSellGoodsDetail/data.tsx

### 664. src\pages\purchase\mustSellGoodsManagement\data.tsx:52
- **业务模块**: 业务页面 > purchase > mustSellGoodsManagement
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: purchase/mustSellGoodsManagement/data.tsx

### 665. src\pages\purchase\mustSellGoodsManagement\data.tsx:117
- **业务模块**: 业务页面 > purchase > mustSellGoodsManagement
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: purchase/mustSellGoodsManagement/data.tsx

### 666. src\pages\purchase\newItemPurchasePlan\data.tsx:244
- **业务模块**: 业务页面 > purchase > newItemPurchasePlan
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: purchase/newItemPurchasePlan/data.tsx

### 667. src\pages\purchase\orderWatch\data.tsx:94
- **业务模块**: 业务页面 > purchase > orderWatch
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: purchase/orderWatch/data.tsx

### 668. src\pages\purchase\orderWatch\data.tsx:171
- **业务模块**: 业务页面 > purchase > orderWatch
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: purchase/orderWatch/data.tsx

### 669. src\pages\purchase\orderWatch\data.tsx:240
- **业务模块**: 业务页面 > purchase > orderWatch
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: purchase/orderWatch/data.tsx

### 670. src\pages\purchase\purchaseReplenishAnalysis\data.tsx:84
- **业务模块**: 业务页面 > purchase > purchaseReplenishAnalysis
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: purchase/purchaseReplenishAnalysis/data.tsx

### 671. src\pages\purchase\purchaseReplenishAnalysis\data.tsx:149
- **业务模块**: 业务页面 > purchase > purchaseReplenishAnalysis
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: purchase/purchaseReplenishAnalysis/data.tsx

### 672. src\pages\purchase\purchaseReplenishAnalysis\data.tsx:200
- **业务模块**: 业务页面 > purchase > purchaseReplenishAnalysis
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: purchase/purchaseReplenishAnalysis/data.tsx

### 673. src\pages\purchase\purchaseReplenishAnalysis\data.tsx:260
- **业务模块**: 业务页面 > purchase > purchaseReplenishAnalysis
- **替换内容**: `/erp/hxl.erp.category.findmaxlevel` → `/erp-mdm/hxl.erp.category.findmaxlevel`
- **业务路径**: purchase/purchaseReplenishAnalysis/data.tsx

### 674. src\pages\purchasement\itemSku\header\index.tsx:123
- **业务模块**: 业务页面 > purchasement > itemSku
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: purchasement/itemSku/header/index.tsx

### 675. src\pages\purchasement\itemSku\header\index.tsx:361
- **业务模块**: 业务页面 > purchasement > itemSku
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: purchasement/itemSku/header/index.tsx

### 676. src\pages\purchasement\itemSku\header\index.tsx:385
- **业务模块**: 业务页面 > purchasement > itemSku
- **替换内容**: `/erp/hxl.erp.item.summary` → `/erp-mdm/hxl.erp.item.summary`
- **业务路径**: purchasement/itemSku/header/index.tsx

### 677. src\pages\purchasement\itemSku\server.ts:4
- **业务模块**: 业务页面 > purchasement > itemSku
- **替换内容**: `/erp/hxl.erp.item.summary` → `/erp-mdm/hxl.erp.item.summary`
- **业务路径**: purchasement/itemSku/server.ts

### 678. src\pages\purchasement\itemSku\server.ts:8
- **业务模块**: 业务页面 > purchasement > itemSku
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: purchasement/itemSku/server.ts

### 679. src\pages\purchasement\itemSku\server.ts:12
- **业务模块**: 业务页面 > purchasement > itemSku
- **替换内容**: `/erp/hxl.erp.item.summary.read` → `/erp-mdm/hxl.erp.item.summary.read`
- **业务路径**: purchasement/itemSku/server.ts

### 680. src\pages\purchasement\itemSku\server.ts:16
- **业务模块**: 业务页面 > purchasement > itemSku
- **替换内容**: `/erp/hxl.erp.item.summary.initial.save` → `/erp-mdm/hxl.erp.item.summary.initial.save`
- **业务路径**: purchasement/itemSku/server.ts

### 681. src\pages\purchasement\itemSku\server.ts:20
- **业务模块**: 业务页面 > purchasement > itemSku
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: purchasement/itemSku/server.ts

### 682. src\pages\purchasement\itemSku\server.ts:23
- **业务模块**: 业务页面 > purchasement > itemSku
- **替换内容**: `/erp/hxl.erp.item.summary.read` → `/erp-mdm/hxl.erp.item.summary.read`
- **业务路径**: purchasement/itemSku/server.ts

### 683. src\pages\stock\stockCollaborativeSharingr\components\addModel\index.tsx:159
- **业务模块**: 业务页面 > stock > stockCollaborativeSharingr
- **替换内容**: `/erp/hxl.erp.store.cargoownerdelivery.short.page` → `/erp-mdm/hxl.erp.store.cargoownerdelivery.short.page`
- **业务路径**: stock/stockCollaborativeSharingr/components/addModel/index.tsx

### 684. src\pages\stock\stockCollaborativeSharingr\data.ts:57
- **业务模块**: 业务页面 > stock > stockCollaborativeSharingr
- **替换内容**: `/erp/hxl.erp.store.cargoownerdelivery.short.page` → `/erp-mdm/hxl.erp.store.cargoownerdelivery.short.page`
- **业务路径**: stock/stockCollaborativeSharingr/data.ts

### 685. src\pages\stock\stockDyingPeriod\data.ts:285
- **业务模块**: 业务页面 > stock > stockDyingPeriod
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: stock/stockDyingPeriod/data.ts

### 686. src\pages\stock\stockDyingPeriod\server.ts:5
- **业务模块**: 业务页面 > stock > stockDyingPeriod
- **替换内容**: `/erp/hxl.erp.storehouse.page` → `/erp-mdm/hxl.erp.storehouse.page`
- **业务路径**: stock/stockDyingPeriod/server.ts

### 687. src\pages\stock\stockLog\data.tsx:189
- **业务模块**: 业务页面 > stock > stockLog
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: stock/stockLog/data.tsx

### 688. src\pages\stock\stockLog\data.tsx:257
- **业务模块**: 业务页面 > stock > stockLog
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: stock/stockLog/data.tsx

### 689. src\pages\stock\stockLog\data.tsx:317
- **业务模块**: 业务页面 > stock > stockLog
- **替换内容**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`
- **业务路径**: stock/stockLog/data.tsx

### 690. src\pages\stock\stockLog\server.ts:21
- **业务模块**: 业务页面 > stock > stockLog
- **替换内容**: `/erp/hxl.erp.settlementcategory.center.find` → `/erp-mdm/hxl.erp.settlementcategory.center.find`
- **业务路径**: stock/stockLog/server.ts

### 691. src\pages\stock\stockSearch\data.tsx:140
- **业务模块**: 业务页面 > stock > stockSearch
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: stock/stockSearch/data.tsx

### 692. src\pages\stock\stockSearch\data.tsx:203
- **业务模块**: 业务页面 > stock > stockSearch
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: stock/stockSearch/data.tsx

### 693. src\pages\stock\stockSearch\data.tsx:290
- **业务模块**: 业务页面 > stock > stockSearch
- **替换内容**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
- **业务路径**: stock/stockSearch/data.tsx

### 694. src\pages\stock\stockSearch\server.ts:26
- **业务模块**: 业务页面 > stock > stockSearch
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: stock/stockSearch/server.ts

### 695. src\pages\stock\stockSearch\server.ts:30
- **业务模块**: 业务页面 > stock > stockSearch
- **替换内容**: `/erp/hxl.erp.suppliermainbody.find` → `/erp-mdm/hxl.erp.suppliermainbody.find`
- **业务路径**: stock/stockSearch/server.ts

### 696. src\pages\stock\stockSearch\server.ts:34
- **业务模块**: 业务页面 > stock > stockSearch
- **替换内容**: `/erp/hxl.erp.category.maxlevel.read` → `/erp-mdm/hxl.erp.category.maxlevel.read`
- **业务路径**: stock/stockSearch/server.ts

### 697. src\pages\stock\unsalableItem\data.tsx:83
- **业务模块**: 业务页面 > stock > unsalableItem
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: stock/unsalableItem/data.tsx

### 698. src\pages\stock\unsalableItem\data.tsx:109
- **业务模块**: 业务页面 > stock > unsalableItem
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: stock/unsalableItem/data.tsx

### 699. src\pages\stock\unsalableItem\server.ts:9
- **业务模块**: 业务页面 > stock > unsalableItem
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: stock/unsalableItem/server.ts

### 700. src\pages\wholesale\buyParam\header\index.tsx:157
- **业务模块**: 业务页面 > wholesale > buyParam
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: wholesale/buyParam/header/index.tsx

### 701. src\pages\wholesale\customerGoodsAttributes\component\batchChange\batchChange.tsx:98
- **业务模块**: 业务页面 > wholesale > customerGoodsAttributes
- **替换内容**: `/erp/hxl.erp.clientname.import` → `/erp-mdm/hxl.erp.clientname.import`
- **业务路径**: wholesale/customerGoodsAttributes/component/batchChange/batchChange.tsx

### 702. src\pages\wholesale\customerGoodsAttributes\component\batchChange\batchChange.tsx:98
- **业务模块**: 业务页面 > wholesale > customerGoodsAttributes
- **替换内容**: `/erp/hxl.erp.clientname.import` → `/erp-mdm/hxl.erp.clientname.import`
- **业务路径**: wholesale/customerGoodsAttributes/component/batchChange/batchChange.tsx

### 703. src\pages\wholesale\customerGoodsAttributes\component\batchChange\batchChange.tsx:99
- **业务模块**: 业务页面 > wholesale > customerGoodsAttributes
- **替换内容**: `/erp/hxl.erp.clientnametemplate.download` → `/erp-mdm/hxl.erp.clientnametemplate.download`
- **业务路径**: wholesale/customerGoodsAttributes/component/batchChange/batchChange.tsx

### 704. src\pages\wholesale\customerGoodsAttributes\component\batchChange\batchChange.tsx:99
- **业务模块**: 业务页面 > wholesale > customerGoodsAttributes
- **替换内容**: `/erp/hxl.erp.clientnametemplate.download` → `/erp-mdm/hxl.erp.clientnametemplate.download`
- **业务路径**: wholesale/customerGoodsAttributes/component/batchChange/batchChange.tsx

### 705. src\pages\wholesale\customerGoodsAttributes\component\batchChange\batchChange.tsx:115
- **业务模块**: 业务页面 > wholesale > customerGoodsAttributes
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: wholesale/customerGoodsAttributes/component/batchChange/batchChange.tsx

### 706. src\pages\wholesale\customerGoodsAttributes\component\batchChange\batchChange.tsx:115
- **业务模块**: 业务页面 > wholesale > customerGoodsAttributes
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: wholesale/customerGoodsAttributes/component/batchChange/batchChange.tsx

### 707. src\pages\wholesale\customerGoodsAttributes\component\batchChange\batchChange.tsx:116
- **业务模块**: 业务页面 > wholesale > customerGoodsAttributes
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: wholesale/customerGoodsAttributes/component/batchChange/batchChange.tsx

### 708. src\pages\wholesale\customerGoodsAttributes\component\batchChange\batchChange.tsx:116
- **业务模块**: 业务页面 > wholesale > customerGoodsAttributes
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: wholesale/customerGoodsAttributes/component/batchChange/batchChange.tsx

### 709. src\pages\wholesale\customerGoodsAttributes\component\batchChange\batchChange.tsx:222
- **业务模块**: 业务页面 > wholesale > customerGoodsAttributes
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: wholesale/customerGoodsAttributes/component/batchChange/batchChange.tsx

### 710. src\pages\wholesale\wholeSaleAnalyze\data.tsx:138
- **业务模块**: 业务页面 > wholesale > wholeSaleAnalyze
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: wholesale/wholeSaleAnalyze/data.tsx

### 711. src\pages\wholesale\wholeSaleAnalyze\data.tsx:191
- **业务模块**: 业务页面 > wholesale > wholeSaleAnalyze
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: wholesale/wholeSaleAnalyze/data.tsx

### 712. src\pages\wholesale\wholeSaleAnalyze\index.tsx:380
- **业务模块**: 业务页面 > wholesale > wholeSaleAnalyze
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: wholesale/wholeSaleAnalyze/index.tsx

### 713. src\pages\wholesale\wholeSaleAnalyze\server.ts:13
- **业务模块**: 业务页面 > wholesale > wholeSaleAnalyze
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: wholesale/wholeSaleAnalyze/server.ts

### 714. src\pages\wholesale\wholeSaleAnalyze\server.ts:18
- **业务模块**: 业务页面 > wholesale > wholeSaleAnalyze
- **替换内容**: `/erp/hxl.erp.itemlabel.find` → `/erp-mdm/hxl.erp.itemlabel.find`
- **业务路径**: wholesale/wholeSaleAnalyze/server.ts

### 715. src\pages\wholesale\wholeSaleDetail\data.ts:154
- **业务模块**: 业务页面 > wholesale > wholeSaleDetail
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: wholesale/wholeSaleDetail/data.ts

### 716. src\pages\wholesale\wholeSaleDetail\index.tsx:156
- **业务模块**: 业务页面 > wholesale > wholeSaleDetail
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: wholesale/wholeSaleDetail/index.tsx

### 717. src\pages\wholesale\wholeSaleDetail\server.ts:11
- **业务模块**: 业务页面 > wholesale > wholeSaleDetail
- **替换内容**: `/erp/hxl.erp.storehouse.store.find` → `/erp-mdm/hxl.erp.storehouse.store.find`
- **业务路径**: wholesale/wholeSaleDetail/server.ts

### 718. src\pages\wholesale\wholesaleOrgSetting\index.tsx:185
- **业务模块**: 业务页面 > wholesale > wholesaleOrgSetting
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: wholesale/wholesaleOrgSetting/index.tsx

### 719. src\pages\wholesale\wholesaleOrgSetting\modal.tsx:62
- **业务模块**: 业务页面 > wholesale > wholesaleOrgSetting
- **替换内容**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
- **业务路径**: wholesale/wholesaleOrgSetting/modal.tsx

### 720. src\pages\wholesale\wholesaleOrgSetting\modal.tsx:62
- **业务模块**: 业务页面 > wholesale > wholesaleOrgSetting
- **替换内容**: `/erp/hxl.erp.commonstorename.import` → `/erp-mdm/hxl.erp.commonstorename.import`
- **业务路径**: wholesale/wholesaleOrgSetting/modal.tsx

### 721. src\pages\wholesale\wholesaleOrgSetting\modal.tsx:63
- **业务模块**: 业务页面 > wholesale > wholesaleOrgSetting
- **替换内容**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`
- **业务路径**: wholesale/wholesaleOrgSetting/modal.tsx

### 722. src\pages\wholesale\wholesaleOrgSetting\modal.tsx:63
- **业务模块**: 业务页面 > wholesale > wholesaleOrgSetting
- **替换内容**: `/erp/hxl.erp.storenametemplate.download` → `/erp-mdm/hxl.erp.storenametemplate.download`
- **业务路径**: wholesale/wholesaleOrgSetting/modal.tsx

### 723. src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx:132
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: wholesale/wholesalePrice/components/batchChange/batchChange.tsx

### 724. src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx:132
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.storename.import` → `/erp-mdm/hxl.erp.storename.import`
- **业务路径**: wholesale/wholesalePrice/components/batchChange/batchChange.tsx

### 725. src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx:133
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: wholesale/wholesalePrice/components/batchChange/batchChange.tsx

### 726. src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx:133
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.storecodetemplate.download` → `/erp-mdm/hxl.erp.storecodetemplate.download`
- **业务路径**: wholesale/wholesalePrice/components/batchChange/batchChange.tsx

### 727. src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx:149
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: wholesale/wholesalePrice/components/batchChange/batchChange.tsx

### 728. src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx:149
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.items.batchimport` → `/erp-mdm/hxl.erp.items.batchimport`
- **业务路径**: wholesale/wholesalePrice/components/batchChange/batchChange.tsx

### 729. src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx:150
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: wholesale/wholesalePrice/components/batchChange/batchChange.tsx

### 730. src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx:150
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.item.shorttemplate.download` → `/erp-mdm/hxl.erp.item.shorttemplate.download`
- **业务路径**: wholesale/wholesalePrice/components/batchChange/batchChange.tsx

### 731. src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx:166
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: wholesale/wholesalePrice/components/batchChange/batchChange.tsx

### 732. src\pages\wholesale\wholesalePrice\components\batchChange\batchChange.tsx:373
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: wholesale/wholesalePrice/components/batchChange/batchChange.tsx

### 733. src\pages\wholesale\wholesalePrice\components\copy\copy.tsx:410
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: wholesale/wholesalePrice/components/copy/copy.tsx

### 734. src\pages\wholesale\wholesalePrice\data.tsx:103
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: wholesale/wholesalePrice/data.tsx

### 735. src\pages\wholesale\wholesalePrice\data.tsx:183
- **业务模块**: 业务页面 > wholesale > wholesalePrice
- **替换内容**: `/erp/hxl.erp.category.find` → `/erp-mdm/hxl.erp.category.find`
- **业务路径**: wholesale/wholesalePrice/data.tsx

### 736. src\pages\wholesale\wholesalePriceAdjustment\data.ts:83
- **业务模块**: 业务页面 > wholesale > wholesalePriceAdjustment
- **替换内容**: `/erp/hxl.erp.org.tree` → `/erp-mdm/hxl.erp.org.tree`
- **业务路径**: wholesale/wholesalePriceAdjustment/data.ts

### 737. src\pages\wholesale\wholesalePriceAdjustment\header\index.tsx:321
- **业务模块**: 业务页面 > wholesale > wholesalePriceAdjustment
- **替换内容**: `/erp/hxl.erp.org.find` → `/erp-mdm/hxl.erp.org.find`
- **业务路径**: wholesale/wholesalePriceAdjustment/header/index.tsx

### 738. src\provider.tsx:42
- **业务模块**: 其他 > src
- **替换内容**: `/erp/hxl.erp.usercolumn.get` → `/erp-mdm/hxl.erp.usercolumn.get`
- **业务路径**: src\provider.tsx

### 739. src\provider.tsx:43
- **业务模块**: 其他 > src
- **替换内容**: `/erp/hxl.erp.usercolumn.update` → `/erp-mdm/hxl.erp.usercolumn.update`
- **业务路径**: src\provider.tsx

### 740. src\services\system\index.ts:10
- **业务模块**: 服务接口 > services > system
- **替换内容**: `/erp/hxl.erp.user.account.login` → `/erp-mdm/hxl.erp.user.account.login`
- **业务路径**: services/system/index.ts

### 741. src\utils\purchaseUrl.ts:14
- **业务模块**: 工具函数 > utils
- **替换内容**: `/erp/hxl.erp.supplier.producerandexecutivestandard.find` → `/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find`
- **业务路径**: utils/purchaseUrl.ts

### 742. src\utils\utils.ts:18
- **业务模块**: 工具函数 > utils
- **替换内容**: `/erp/hxl.erp.usercolumn.get` → `/erp-mdm/hxl.erp.usercolumn.get`
- **业务路径**: utils/utils.ts


---
