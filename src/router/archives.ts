interface IRoute {
  component?: any;
  exact?: boolean;
  path?: string;
  routes?: IRoute[];
  wrappers?: string[];
  title?: string;
  __toMerge?: boolean;
  __isDynamic?: boolean;
  [key: string]: any;
}

export const routeList: IRoute[] = [
  {
    path: '/xlb_erp/priceTemplate/index',
    component: '@/pages/archives/priceTemplate/header/index',
    title: '零售价模板',
    subTitle: '基础资料',
    subMenu: 'archives',
    tabClass: 'priceTemplate',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/companyHeader/index',
    component: '@/pages/archives/companyHeader/index',
    title: '公司抬头管理',
    subTitle: '基础资料',
    subMenu: 'archives',
    tabClass: 'companyHeader',
  },
  {
    path: '/xlb_erp/skuCeilingManagement/index', //
    component: '@/pages/archives/skuCeilingManagement/header/index',
    title: 'SKU上限管理',
    subTitle: '基础资料',
    subMenu: 'archives',
    tabClass: 'companyHeader',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  // management
  {
    path: '/xlb_erp/organizeManage/index',
    component: '@/pages/archives/organizeManage/index',
    title: '组织管理',
    subTitle: '基础资料',
    subMenu: 'archives',
    tabClass: 'organizeManage',
  },
  {
    path: '/xlb_erp/goodsBrand/index',
    component: '@/pages/archives/goodsBrand/index',
    title: '商品品牌',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'goodsBrand',
  },
  {
    path: '/xlb_erp/userBranch/index',
    component: '@/pages/archives/userBranch/index',
    title: '用户部门',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'userBranch',
  },
  {
    path: '/xlb_erp/storeArea/index',
    component: '@/pages/archives/storeArea/header/index',
    title: '门店区域',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'storeArea',
  },
  {
    path: '/xlb_erp/goodsBranch/index',
    component: '@/pages/archives/goodsBranch/index',
    title: '商品部门',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'goodsBranch',
  },
  {
    path: '/xlb_erp/goodsUnits/index',
    component: '@/pages/archives/goodsUnits/index',
    title: '商品单位',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'goodsUnits',
  },
  {
    path: '/xlb_erp/wholesaleReturnReason/index',
    component: '@/pages/archives/wholesaleReturnReason/index',
    title: '批发退货原因',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'wholesaleReturnReason',
  },
  {
    path: '/xlb_erp/cashBank/index',
    component: '@/pages/archives/cashBank/index',
    title: '现金银行',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'cashBank',
  },
  {
    path: '/xlb_erp/supplierMainBody/index',
    component: '@/pages/archives/supplierMainBody/index',
    title: '供货主体',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'supplierMainBody',
  },
  {
    path: '/xlb_erp/orgBusinessArea/index',
    component: '@/pages/archives/orgBusinessArea/index',
    title: '组织经营范围',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'orgBusinessArea',
  },
  {
    path: '/xlb_erp/payMode/index',
    component: '@/pages/archives/payMode/header/index',
    title: '支付方式',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'payMode',
  },
  {
    path: '/xlb_erp/devicesBrand/index',
    component: '@/pages/archives/devicesBrand/index',
    title: '设备品牌管理',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'devicesBrand',
  },
  {
    path: '/xlb_erp/administrativeRegion/index',
    component: '@/pages/archives/administrativeRegion/index',
    title: '行政区域',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'administrativeRegion',
  },
  {
    path: '/xlb_erp/strongReason/index',
    component: '@/pages/archives/strongReason/header/index',
    title: '统配原因',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'strongReason',
  },

  {
    path: '/xlb_erp/OBSPaymentEntity/index',
    component: '@/pages/archives/OBSPaymentEntity/index',
    title: 'CBS付款主体',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'OBSPaymentEntity',
  },
  {
    path: '/xlb_erp/contractTemplateSetup/index',
    component: '@/pages/archives/contractTemplateSetup/index',
    title: '合同管理模快',
    subTitle: '基础项目',
    subMenu: 'archives',
    tabClass: 'OBSPaymentEntity',
  },
  {
    path: '/xlb_erp/contractMangement/index',
    component: '@/pages/archives/contractMangement/index',
    title: '合同管理',
    subTitle: '业务模块',
    subMenu: 'archives',
    tabClass: 'contractMangement',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/cargoOwner/index',
    component: '@/pages/archives/cargoOwner/index',
    title: '货主管理',
    subTitle: '基础资料',
    subMenu: 'archives',
    tabClass: 'cargoOwner',
  },
  {
    path: '/xlb_erp/cargoOwnerCenter/index',
    component: '@/pages/archives/cargoOwnerCenter/index',
    title: '配送中心货主配置',
    subTitle: '基础资料',
    subMenu: 'archives',
    tabClass: 'cargoOwnerCenter',
  },
  {
    path: '/xlb_erp/storeProperties/index',
    component: '@/pages/archives/storeProperties/index',
    title: '门店商品属性',
    subTitle: '基础资料',
    subMenu: 'archives',
    tabClass: 'storeProperties',
  },
  {
    path: '/xlb_erp/goodsOrder/index',
    component: '@/pages/archives/goodsOrder/index',
    title: '商品订购属性',
    subTitle: '基础资料',
    subMenu: 'archives',
    tabClass: 'goodsOrder',
  },
  {
    path: '/xlb_erp/contractTemplateSetup/index',
    component: '@/pages/archives/contractTemplateSetup/index.tsx',
    title: '合同模板设置',
    subTitle: '基础资料',
    subMenu: 'archives',
    tabClass: 'cargoOwnerCenter',
    wrappers: ['@/pages/public/proPageProvide'],
  },
];

export { routeList as archivesRouteList };
