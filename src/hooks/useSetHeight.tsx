import { useEffect, useState } from 'react';

const useSetHeight = (tableRef: { current: any }, deviation = 0) => {
  const [tableHeight, setTableHeight] = useState(100);

  const calculateTableHeight = () => {
    if (tableRef.current) {
      const windowHeight = window.innerHeight;
      const aboveTableRect = tableRef.current.dom.getBoundingClientRect();
      const aboveTableHeight = aboveTableRect.top;
      setTableHeight(windowHeight - aboveTableHeight - deviation);
    }
  };

  useEffect(() => {
    calculateTableHeight();
    window.addEventListener('resize', calculateTableHeight);
    return () => {
      window.removeEventListener('resize', calculateTableHeight);
    };
  }, []);
  return { tableHeight };
};
export default useSetHeight;
