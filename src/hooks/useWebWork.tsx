/**
 *
 * @param url
 * @param config
 * @returns
 */

import { wujieBus } from '@/wujie/utils'
import { useEffect } from 'react'

// 版本检查Worker的Blob实现
const createVersionWorker = (url: string) => {
  const checkTime = 3 * 60 * 1000 // 3分钟检查一次
  const workerCode = `
      let checkInterval = ${checkTime};
      let currentVersion = '';
      const fetchVersion = async () => {
        try {
          const response = await fetch('${url}?t=' + Date.now());
          const data = await response.json();
          return data;
        } catch (error) {
          console.error('Version check failed:', error);
          return {};
        }
      };

      const checkVersion = async () => {
        const { version: newVersion,application } = await fetchVersion();
        if (!newVersion) return;
        if (!currentVersion) {
          currentVersion = newVersion
          self.postMessage({
            type: 'VERSION_INIT',
            data: {
              oldVersion: currentVersion,
              newVersion: newVersion,
              application: application
            }
          })
        } else if (newVersion !== currentVersion) {
          self.postMessage({
            type: 'VERSION_UPDATE',
            data: {
              oldVersion: currentVersion,
              newVersion: newVersion,
              application: application
            }
          });
          currentVersion = newVersion;
        }
      };
      setInterval(checkVersion, checkInterval);
      checkVersion();
    `

  const blob = new Blob([workerCode], { type: 'application/javascript' })
  const worker = new Worker(URL.createObjectURL(blob))

  return worker
}
export const useWebWork = (url: string) => {
  useEffect(() => {
    console.assert(url, 'url is required')
    if (!url) return
    const worker = createVersionWorker(url)
    if (worker) {
      worker.onmessage = (event) => {
        switch (event.data.type) {
          case 'VERSION_UPDATE':
            wujieBus?.$emit('VERSION_UPDATE', event.data)
            break
        }
      }
    }
    return () => {
      worker.terminate()
    }
  }, [])
}
