import omit from 'lodash/omit';

export const InterWarehouseTransferKeyMap = {
  erpTimeType: 'erpTimeType',
  erpAuditBy: 'erpAuditBy',
  outOrgIds: 'outOrgIds',
  outStoreIds: 'outStoreIds',
  inStoreIds: 'inStoreIds',
  inOrgIds: 'inOrgIds',
  erpOutStorehouseId: 'erpOutStorehouseId',
};

export const interWarehouseTransferConfig: any[] = [
  {
    tag: 'ERP',
    label: '时间类型',
    id: InterWarehouseTransferKeyMap.erpTimeType,
    name: 'date_type',
    componentType: 'select',
    fieldProps: {
      options: [
        { label: '制单时间', value: 'create_date' },
        { label: '审核时间', value: 'audit_date' },
        { label: '作废时间', value: 'invalid_date' },
      ],
    },
  },
  {
    tag: 'ERP',
    componentType: 'input',
    label: '审核人',
    name: 'audit_by',
    id: InterWarehouseTransferKeyMap?.erpAuditBy,
  },
  {
    tag: 'ERP',
    label: '调出组织',
    id: InterWarehouseTransferKeyMap.outOrgIds,
    name: 'out_org_ids',
    componentType: 'select',
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
    url: '/erp/hxl.erp.org.find',
  },
  {
    tag: 'ERP',
    label: '调出门店',
    id: InterWarehouseTransferKeyMap.outStoreIds,
    name: 'out_store_ids',
    dependencies: ['out_org_ids'],
    fieldProps: (form: any) => {
      const data = {
        org_ids: form.getFieldsValue(['out_org_ids']),
      };
      if (!form?.getFieldsValue(true).out_org_ids) {
        delete data?.org_ids;
      }
      return {
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        } as any,
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            center_flag: true,
            ...data,
          },
        },
      };
    },
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      return formValues;
    },
    handleDefaultValue: (data: any) => {
      if (data.out_org_ids?.length > 0) {
        return null;
      }
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '调入组织',
    id: InterWarehouseTransferKeyMap.inOrgIds,
    name: 'in_org_ids',
    componentType: 'select',
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
    url: '/erp/hxl.erp.org.find',
  },
  {
    tag: 'ERP',
    label: '调入门店',
    id: InterWarehouseTransferKeyMap.inStoreIds,
    name: 'in_store_ids',
    dependencies: ['in_org_ids'],
    // disabled: (data) => {
    //   console.log('🚀 ~ data:', data)
    //   return !data.out_store_ids
    // },
    fieldProps: (form: any) => {
      const data = {
        org_ids: form.getFieldsValue(['in_org_ids']),
      };
      if (!form?.getFieldsValue(true).in_org_ids) {
        delete data?.org_ids;
      }
      return {
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        } as any,
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            center_flag: true,
            ...data,
          },
        },
      };
    },
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      return formValues;
    },
    handleDefaultValue: (data: any) => {
      if (data.in_org_ids?.length > 0) {
        return null;
      }
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '调出仓库',
    id: InterWarehouseTransferKeyMap?.erpOutStorehouseId,
    name: 'out_storehouse_id',
    dependencies: ['out_store_ids'],
    async request(params: any, anybaseURL: any, globalFetch: any) {
      const { out_store_ids } = params;
      let store_idT: number;
      if (!Array.isArray(out_store_ids) || !out_store_ids.length) {
        console.warn('out_store_ids不存在');
        return [];
      }

      if (out_store_ids?.length > 1) {
        console.warn('多个store_id');
        return [];
      }
      store_idT = out_store_ids?.[0];
      const result = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.storehouse.store.find`,
        {
          ...omit(params, 'store_ids'),
          store_id: store_idT,
        },
      );
      if (Array.isArray(result.data)) {
        return result.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
    },
    // showSearch:true,
    handleDefaultValue(data: any) {
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return defaultStoreHouse?.value;
    },
    componentType: 'select',
  },
];