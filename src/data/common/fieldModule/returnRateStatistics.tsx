import { XlbFetch as ErpRequest } from '@xlb/utils';

export const ReturnRateStatisticsKeyMap = {
  /**三级组织 */
  orgIdsWithThreeLevel: 'orgIdsWithThreeLevel',
  erpCategoryLevel: 'erpCategoryLevel',
  /**异常类别 */
  erpExceptioncategoryParent: 'erpExceptioncategoryParent',
  /**异常二级 */
  erpExceptioncategory: 'erpExceptioncategory',
  /**单据号 */
  erpDocumentNumber: 'erpDocumentNumber',
  /**申请原因 */
  erpReasonApplication: 'erpReasonApplication',
  erpBillTimeType: 'erpBillTimeType',
  /**商品品牌 */
  erpItemBrandName: 'erpItemBrandName',
  /**操作人 */
  erpOperator: 'erpOperator',
  /**申请门店 */
  erpReturnRateInStoreIds2: 'erpReturnRateInStoreIds2',
  /**质量问题分类 */
  erpQualityProblemCategory: 'erpQualityProblemCategory',
};

export const returnRateStatisticsConfig: any[] = [
  {
    tag: 'ERP',
    label: '组织',
    id: ReturnRateStatisticsKeyMap.orgIdsWithThreeLevel,
    name: 'org_ids',
    componentType: 'select',
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
    },
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(anybaseURL + '/erp/hxl.erp.org.find', {
        company_ids: formValues.company_ids || [],
        level: 3,
      });
      if (res.code == 0) {
        return res.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
      return [];
    },
  },
  {
    tag: 'ERP',
    label: '类别等级',
    id: ReturnRateStatisticsKeyMap.erpCategoryLevel,
    name: 'category_level',
    componentType: 'select',
    dependencies: ['summary_types'],
    hidden: (formValues: any) => {
      return !formValues?.summary_types?.includes(2);
    },
    fieldProps: {
      options: [
        {
          label: '按当前类别',
          value: 0,
        },
        {
          label: '按一级类别',
          value: 1,
        },
        {
          label: '按二级类别',
          value: 2,
        },
        {
          label: '按三级类别',
          value: 3,
        },
      ],
    },
  },
  {
    componentType: 'select',
    tag: 'ERP',
    label: '异常类别',
    id: ReturnRateStatisticsKeyMap.erpExceptioncategoryParent,
    name: 'abnormal_type_id',
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(
        anybaseURL + '/scm/hxl.scm.abnormalcategory.findTree',
        {
          level: 1,
        },
      );
      if (res?.code === 0) {
        return res.data.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));
      }
      return [];
    },
  },
  {
    componentType: 'select',
    tag: 'ERP',
    id: ReturnRateStatisticsKeyMap.erpExceptioncategory,
    name: 'abnormal_sub_type_id',
    dependencies: ['abnormal_type_id'],
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      if (!formValues?.abnormal_type_id) return [];
      const res = await globalFetch.post(
        anybaseURL + '/scm/hxl.scm.abnormalcategory.findTree',
        {
          level: 2,
          parent_id: formValues?.abnormal_type_id,
        },
      );
      if (res?.code === 0) {
        return res.data.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));
      }
      return [];
    },
  },
  {
    tag: 'ERP',
    label: '单据号',
    id: ReturnRateStatisticsKeyMap.erpDocumentNumber,
    name: 'fid',
    formItemProps: {
      rules: [{ max: 20 }],
    },
    componentType: 'input',
  },
  {
    componentType: 'select',
    tag: 'ERP',
    label: '申请原因',
    id: ReturnRateStatisticsKeyMap.erpReasonApplication,
    name: 'reasons',
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.reason.find',
        {
          type: 'RETURN_REQUEST',
        },
      );
      if (res?.code === 0) {
        return res.data.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.name,
        }));
      }
      return [];
    },
  },
  {
    tag: 'ERP',
    label: '时间类型',
    id: ReturnRateStatisticsKeyMap.erpBillTimeType,
    name: 'date_type',
    componentType: 'select',
    fieldProps: {
      options: [
        { label: '制单日期', value: 'CREATE' },
        { label: '审核日期', value: 'AUDIT' },
        { label: '处理日期', value: 'HANDLE' },
        { label: '失效日期', value: 'INVALID' },
        { label: '生效日期', value: 'EFFECT' },
      ],
    },
  },
  {
    componentType: 'select',
    tag: 'ERP',
    label: '商品品牌',
    id: ReturnRateStatisticsKeyMap.erpItemBrandName,
    name: 'item_brand_ids',
    request: async (formValues: any, anybaseURL?: any, globalFetch?: any) => {
      const res = await ErpRequest.post('/erp/hxl.erp.brand.find',{})
      if (res?.code === 0) {
        return res.data.map((item: any) => ({
          ...item,
          label: item.name,
          value: item.id,
        }));
      }
      return [];
    },
  },
  {
    tag: 'ERP',
    label: '操作人',
    id: ReturnRateStatisticsKeyMap.erpOperator,
    name: 'operator_by',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '门店',
    id: ReturnRateStatisticsKeyMap?.erpReturnRateInStoreIds2,
    name: 'in_store_ids',
    dependencies: ['in_org_ods', 'summary_types'],
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['in_org_ods', 'summary_types']),
      };
      if (!form?.getFieldsValue(true).in_org_ods) {
        delete data?.org_ids;
      }
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...data,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  {
    componentType: 'inputDialog',
    tag: 'ERP',
    label: '质量问题分类',
    name: 'problem_category_id',
    id: ReturnRateStatisticsKeyMap.erpQualityProblemCategory,
    dependencies: ['reasons'], // level：组织管理、id: 组织管理-组织id
    hidden: (formValues: any) => {
      return !formValues?.reasons?.includes('质量问题退货');
    },
    fieldProps: {
      treeModalConfig: {
        title: '选择质量原因', // 标题
        url: '/erp/hxl.center.problemcategory.find', // 请求地址
        dataType: 'lists',
        checkable: false, // 是否多选
        primaryKey: 'id',
        data: {
          enabled: true,
        },
        width: 360, // 模态框宽度
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value[0];
      },
    },
  },
];
