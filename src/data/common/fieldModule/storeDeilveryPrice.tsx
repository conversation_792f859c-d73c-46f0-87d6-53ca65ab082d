export const StoreDeliveryPriceKeyMap = {
  /**批发客户 inputDialog 单选 */
  erpStoreDeliveryPriceId: 'erpStoreDeliveryPriceId',
  erpStoreDeliveryPriceItemId: 'erpStoreDeliveryPriceItemId',
  erpStoreDeliveryPriceItemCategoryIds: 'erpStoreDeliveryPriceItemCategoryIds',
  erpStoreDeilveryPriceBusinessScope: 'erpStoreDeilveryPriceBusinessScope',
  erpStoreDeliveryGroupId: 'erpStoreDeliveryGroupId',
  erpStoreDeilveryPriceShowPanel: 'erpStoreDeilveryPriceShowPanel',
};

export const StoreDeliveryPriceKeyMapKeys: any[] = [
  {
    tag: 'ERP',
    label: '门店',
    id: StoreDeliveryPriceKeyMap?.erpStoreDeliveryPriceId,
    name: 'store_ids',
    dependencies: ['org_ids'],
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['org_ids']),
      };
      if (!form?.getFieldsValue(true).org_ids) {
        delete data?.org_ids;
      }
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...data,
            enable_organization: false,
            status: true,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '商品档案',
    id: StoreDeliveryPriceKeyMap.erpStoreDeliveryPriceItemId,
    name: 'item_ids',
    fieldProps: {
      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isMultiple: true,
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    id: StoreDeliveryPriceKeyMap.erpStoreDeliveryPriceItemCategoryIds,
    name: 'item_category_ids',
    componentType: 'inputDialog',
    fieldProps: {
      treeModalConfig: {
        title: '选择商品分类', // 标题
        url: '/erp/hxl.erp.category.find', // 请求地址
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
        data: {
          enabled: true,
        },
        width: 360, // 模态框宽度
      },
    },
  },
  {
    tag: 'ERP',
    label: '经营范围',
    id: StoreDeliveryPriceKeyMap?.erpStoreDeilveryPriceBusinessScope,
    name: 'business_scope_ids',
    fieldProps: {
      dialogParams: {
        type: 'businessRecordRange',
        dataType: 'lists',
        isMultiple: true,
        isLeftColumn: false,
        data: { business_type: 1 },
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '价格比较',
    id: StoreDeliveryPriceKeyMap.erpStoreDeliveryGroupId,
    componentType: 'group',
    fieldProps: {
      groupResizeable: true,
      formList: [
        {
          tag: 'ERP',
          id: 'CommonSelect',
          componentType: 'select',
          label: '',
          name: 'one',
          fieldProps: {
            style: { width: '100%' },
            allowClear: false,
            options: [{ value: 'price', label: '配送价' }],
            popupMatchSelectWidth: false,
          },
        },
        {
          tag: 'ERP',
          id: 'CommonSelect',
          componentType: 'select',
          label: '',
          name: 'symbol',
          fieldProps: {
            style: { width: '100%' },
            allowClear: true,
            options: [
              { value: '>', label: '>' },
              { value: '<', label: '<' },
              { value: '=', label: '=' },
              { value: '>=', label: '≥' },
              { value: '<=', label: '≤' },
            ],
          },
        },
        {
          tag: 'ERP',
          id: 'CommonSelect',
          componentType: 'select',
          label: '',
          name: 'three',
          fieldProps: {
            style: { width: '100%' },
            allowClear: false,
            options: [{ value: 'purchasePrice', label: '组织采购价' }],
            popupMatchSelectWidth: false,
          },
        },
      ],
    },
  },
  {
    tag: 'ERP',
    name: 'showPanel',
    componentType: 'inputPanel',
    id: StoreDeliveryPriceKeyMap.erpStoreDeilveryPriceShowPanel,
    label: '其他条件',
    fieldProps: {
      allowClear: true,
      notTransform: true,
      options: [
        {
          label: '停购',
          value: 'stop_purchase',
        },
        {
          label: '停售',
          value: 'stop_sale',
        },
        {
          label: '停止要货',
          value: 'stop_request',
        },
      ],
      items: [
        {
          label: '仅显示',
          key: 'true',
        },
        {
          label: '不显示',
          key: 'false',
        },
      ],
    },
    formItemProps(formInstance, ...arg) {
      return {
        label: '其他条件',
        getValueFromEvent(checkValue) {
          if (Array.isArray(checkValue)) {
            [
              {
                label: '停购',
                value: 'stop_purchase',
              },
              {
                label: '停售',
                value: 'stop_sale',
              },
              {
                label: '停止要货',
                value: 'stop_request',
              },
            ].forEach((item) =>
              formInstance.setFieldValue(item.value, undefined),
            );
            checkValue.forEach((item) => {
              formInstance.setFieldValue(item.value, item.itemKey === 'true');
            });
          }
          return checkValue;
        },
      };
    },
  },
];
