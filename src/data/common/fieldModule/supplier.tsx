export const SupplierKeyMap = {
  activedStatus: 'activedStatus',
  erpPurchaseUser: 'erpPurchaseUser',
  erpSupplierMainBody: 'erpSupplierMainBody',
  erpSupplierOrgIds: 'erpSupplierOrgIds',
  taxNum: 'taxNum' //税号
}

export const supplierConfig: any[] = [
  {
    tag: 'ERP',
    label: '状态',
    id: SupplierKeyMap.activedStatus,
    name: 'actived',
    componentType: 'select'
  },
  {
    tag: 'ERP',
    label: '采购员',
    id: SupplierKeyMap?.erpPurchaseUser,
    name: 'user_ids',
    fieldProps: {
      dialogParams: {
        isLeftColumn: false,
        type: 'user',
        dataType: 'lists',
        isMultiple: true,
        data: {
          business_dept: 'SUPPLY_CENTER'
        }
      }
    },
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    componentType: 'select',
    label: '供货主体',
    id: SupplierKeyMap?.erpSupplierMainBody,
    request: async (_formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(`${anybaseURL}/erp/hxl.erp.suppliermainbody.find`, {})
      if (res.code == 0) {
        return res.data?.map((i: any) => ({ value: i.id, label: i.name }))
      }
      return []
    },
    fieldProps: {
      width: 260,
      // mode: 'multiple',
      allowClear: true
    }
  },
  {
    tag: 'ERP',
    label: '供货组织',
    name: 'supply_ogs_ids',
    id: SupplierKeyMap?.erpSupplierOrgIds,
    // dependencies: ['level供货组织', 'id'], // level：组织管理、id: 组织管理-组织id
    fieldProps: {
      treeModalConfig: {
        title: '选择组织',
        checkable: true,
        url: '/erp/hxl.erp.org.tree',
        dataType: 'lists',
        primaryKey: 'id',
        params: { level: 3 },
        afterPost(data: any) {
          // console.log('afterPost', data)
          return data.filter((i: any) => i.level == 3)
        }
      }
    },
    // formItemProps: {
    //   getValueFromEvent: (value: any) => {
    //     if (!Array.isArray(value)) return
    //     return value[0]
    //   }
    // },
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    label: '税号',
    id: SupplierKeyMap?.taxNum,
    name: 'tax_num',
    componentType: 'input'
  }
]