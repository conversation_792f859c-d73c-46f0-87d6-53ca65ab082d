import type { CreateMapItem } from '@xlb/components/dist/lowcodes/XlbProForm/type';

export const StoreDeviceManageKeyMap = {
  // 设备类型
  erpDeviceType: 'erpDeviceType',
  // 设备状态
  erpDeviceStatus: 'erpDeviceStatus',
  erpStorehouseIdDeliveryOrder: 'erpStorehouseIdDeliveryOrder',
};

export const storeDeviceManageConfig: CreateMapItem[] = [
  {
    tag: 'ERP',
    id: 'erpDeviceType',
    componentType: 'select',
    label: '设备类型',
    name: '_category_ids',
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(anybaseURL + '/erp/hxl.erp.storehardware.category.find', {
        company_ids: formValues.company_ids || []
      })
      if (res.code == 0) {
        return res?.data?.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
      return [];
    },
  },
  {
    tag: 'ERP',
    label: '设备状态',
    id: StoreDeviceManageKeyMap.erpDeviceStatus,
    name: '_status',
    componentType: 'select',

    fieldProps: {
      options: [
        {
          label: '正常',
          value: 'ENABLE',
          type: 'success',
        },
        {
          label: '报修中',
          value: 'REPAIRING',
          type: 'warning',
        },
        {
          label: '停用',
          value: 'DISABLE',
          type: 'danger',
        },
      ],
    },
  },
  // 拉新配置防止影响
  {
    tag: 'ERP',
    label: '仓库',
    id: StoreDeviceManageKeyMap?.erpStorehouseIdDeliveryOrder,
    name: 'storehouse_id',
    dependencies: ['store_id'],
    async request(params: any, anybaseURL: any, globalFetch: any) {
      const { store_id } = params;
      let store_idT: number;
      if (!Array.isArray(store_id) || !store_id.length) {
        console.warn('store_id不存在');
        return [];
      }

      if (store_id.length > 1) {
        console.warn('多个store_id');
        return [];
      }
      store_idT = store_id[0];
      const result = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.storehouse.store.find`,
        {
          ...omit(params, 'store_ids'),
          store_id: store_idT,
        },
      );
      if (Array.isArray(result.data)) {
        return result.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
    },
    // showSearch:true,
    handleDefaultValue(data: any) {
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return defaultStoreHouse?.value;
    },
    componentType: 'select',
  },
];
