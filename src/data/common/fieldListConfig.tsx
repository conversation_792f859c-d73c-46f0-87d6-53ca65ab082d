import type { XlbInputPanelProps } from '@xlb/components';
import type { CreateMapItem } from '@xlb/components/dist/lowcodes/XlbProForm/type';
import type { FormInstance } from 'antd';
import {
  advancePositionConfig,
  AdvancePositionKeyMap,
} from './fieldModule/advancePosition';
import {
  applyPaySysAnalysisConfig,
  ApplyPaySysAnalysisKeyMap,
} from './fieldModule/applyPaySysAnalysis';
import {
  businessRangeConfig,
  BusinessRangeMap,
} from './fieldModule/businessRange';
import {
  bussinessRefundConfig,
  bussinessRefundKeyMap,
} from './fieldModule/bussinessRefund';
import {
  companyHeaderConfig,
  CompanyHeaderKeyMap,
} from './fieldModule/companyHeader';
import {
  couponDataAnalysisConfig,
  CouponDataAnalysisKeyMap,
} from './fieldModule/couponDataAnalysis';
import {
  deliveryCenterStoreConfig,
  DeliveryCenterStoreKeyMap,
} from './fieldModule/deliveryCenterStore';
import {
  deliverySpecialPriceConfig,
  DeliverySpecialPriceKeyMap,
} from './fieldModule/deliverySpecialPrice';
import {
  devicesBrandConfig,
  DevicesBrandKeyMap,
} from './fieldModule/devicesBrand';
import { goodsFilesConfig, GoodsFilesKeyMap } from './fieldModule/goodsFiles';
import { goodsOrderConfig, GoodsOrderKeyMap } from './fieldModule/goodsOrder';
import { goodsUnitsConfig, GoodsUnitsKeyMap } from './fieldModule/goodsUnits';
import {
  interWarehouseTransferConfig,
  InterWarehouseTransferKeyMap,
} from './fieldModule/interWarehouseTransfer';
import {
  invoiceCheckConfig,
  InvoiceCheckKeyMap,
} from './fieldModule/invoiceCheck';
import { moneyApplyConfig, MoneyApplyKeyMap } from './fieldModule/moneyApply';
import {
  newYearGoodsPlanDetailsConfig,
  NewYearGoodsPlanDetailsMap,
} from './fieldModule/newYearGoodsPlanDetails';
import {
  organizeManageConfig,
  OrganizeManageKeyMap,
} from './fieldModule/organizeManage';
import { payModeConfig, PayModeKeyMap } from './fieldModule/payMode';
import {
  purchaseBusinessRangeConfig,
  PurchaseBusinessRangeKeyMap,
} from './fieldModule/purchaseBusinessRange';
import {
  purchaseOrderingConfig,
  PurchaseOrderingKeyMap,
} from './fieldModule/purchaseOrdering';
import {
  purchaseShareConfig,
  PurchaseShareKeyMap,
} from './fieldModule/purchaseShare';
import {
  returnRateStatisticsConfig,
  ReturnRateStatisticsKeyMap,
} from './fieldModule/returnRateStatistics';
import {
  saleAnalysisConfig,
  SaleAnalysisKeyMap,
} from './fieldModule/saleAnalysis';
import {
  stockCheckOrderConfig,
  stockCheckOrderKeyMap,
} from './fieldModule/stockCheckOrder';
import { storeAreaConfig, StoreAreaKeyMap } from './fieldModule/storeArea';
import {
  StoreDeliveryPriceKeyMap,
  StoreDeliveryPriceKeyMapKeys,
} from './fieldModule/storeDeilveryPrice';
import {
  storeDeviceManageConfig,
  StoreDeviceManageKeyMap,
} from './fieldModule/storeDeviceManage';
import { storeHouseConfig, StoreHouseKeyMap } from './fieldModule/storeHouse';
import {
  storeItemReplenishConfig,
  StoreItemReplenishKeyMap,
} from './fieldModule/storeItemReplenish';
import {
  storeManageConfig,
  StoreManageKeyMap,
} from './fieldModule/storeManage';
import { storeOrderConfig, StoreOrderKeyMap } from './fieldModule/storeOrder';
import {
  storePropertiesConfig,
  StorePropertiesKeyMap,
} from './fieldModule/storeProperties';
import { supplierConfig, SupplierKeyMap } from './fieldModule/supplier';
import {
  supplierMonthlyBillConfig,
  SupplierMonthlyBillKeyMap,
} from './fieldModule/supplierMonthlyBill';
import {
  tiktokcouponrefundConfig,
  TiktokcouponrefundKeyMap,
} from './fieldModule/tiktokcouponrefund';
import {
  transferBasketConfig,
  TransferBasketKeyMap,
} from './fieldModule/transferBasket';
import { userBranchConfig, UserBranchKeyMap } from './fieldModule/userBranch';
import { userManageConfig, UserManageKeyMap } from './fieldModule/userManage';
import {
  wholesaleCustomerConfig,
  WholesaleCustomerKeyMap,
} from './fieldModule/wholesaleCustomer';
import {
  wholesaleOrgSettingConfig,
  WholesaleOrgSettingKeyMap,
} from './fieldModule/wholesaleOrgSetting';

import { BIConfig } from './fieldModule/bi';
import { goodsBrandConfig, GoodsBrandKeyMap } from './fieldModule/goodsBrand';

export const ErpFieldKeyMap = {
  ...GoodsFilesKeyMap,
  ...StoreManageKeyMap,
  ...WholesaleCustomerKeyMap,
  ...SupplierKeyMap,
  ...UserManageKeyMap,
  ...ReturnRateStatisticsKeyMap,
  ...OrganizeManageKeyMap,
  ...DeliveryCenterStoreKeyMap,
  ...TiktokcouponrefundKeyMap,
  ...StoreAreaKeyMap,
  ...SaleAnalysisKeyMap,
  ...CompanyHeaderKeyMap,
  ...GoodsUnitsKeyMap,
  ...PurchaseBusinessRangeKeyMap,
  ...UserBranchKeyMap,
  ...SupplierMonthlyBillKeyMap,
  ...MoneyApplyKeyMap,
  ...CouponDataAnalysisKeyMap,
  ...PurchaseOrderingKeyMap,
  ...bussinessRefundKeyMap,
  ...StoreHouseKeyMap,
  ...BusinessRangeMap,
  ...NewYearGoodsPlanDetailsMap,
  ...InterWarehouseTransferKeyMap,
  ...ApplyPaySysAnalysisKeyMap,
  ...AdvancePositionKeyMap,
  ...StorePropertiesKeyMap,
  ...StoreOrderKeyMap,
  ...TransferBasketKeyMap,
  ...StoreItemReplenishKeyMap,
  ...PayModeKeyMap,
  ...GoodsOrderKeyMap,
  ...StoreDeviceManageKeyMap,
  ...WholesaleOrgSettingKeyMap,
  ...InvoiceCheckKeyMap,
  ...DevicesBrandKeyMap,
  ...PurchaseShareKeyMap,
  ...StoreDeliveryPriceKeyMap,
  ...DeliverySpecialPriceKeyMap,
  ...StoreDeliveryPriceKeyMap,
  ...DeliverySpecialPriceKeyMap,
  ...PurchaseShareKeyMap,

  ...stockCheckOrderKeyMap,
  ...GoodsBrandKeyMap,

  erpCommonSelect: 'erpCommonSelect',
  erpCommonUpload: 'erpCommonUpload',
  erpDownstreamDocuments: 'erpDownstreamDocuments',
  /**载具名称 */
  basketIds: 'basketIds',
  erpItemCategory: 'erpItemCategory',
  erpItemKeyword: 'erpItemKeyword',
  erpReturnRateInStoreIds: 'erpReturnRateInStoreIds',
  /**批发用户权限 */
  erpClientAuthority: 'erpClientAuthority',
  /**pos用户权限 */
  erpPosAuthority: 'erpPosAuthority',
  /**供应商用户权限 */
  erpSupplierAuthority: 'erpSupplierAuthority',
  /**用户权限 */
  erpUserAuthority: 'erpUserAuthority',
  /**商品类别 */
  erpRateStatisticsItemCategory: 'erpRateStatisticsItemCategory',
  /**供应商 inputDialog 单选 */
  erpSupplierId: 'erpSupplierId',
  /**组织 inputDialog 单选 */
  erpOrgId: 'erpOrgId',
  /**组织 公司抬头 inputDialog 单选 */
  erpOrgIdByCompanyHeader: 'erpOrgIdByCompanyHeader',
  /**组织 inputDialog 多选 */
  erpOrgIds: 'erpOrgIds',
  erpOutOrgIds: 'erpOutOrgIds',
  erpShippingOrganization: 'erpShippingOrganization',
  /**@name 门店区域类别 inputDialog 单选 */
  erpStoreAreaCategoriesId: 'erpStoreAreaCategoriesId',
  /**@name 门店 inputDialog 多选 */
  erpStoreIds: 'erpStoreIds',
  erpStoreIdsOrder: 'erpStoreIdsOrder',
  erpOrderTimeruleStoreIds: 'erpOrderTimeruleStoreIds',
  erpStoreId: 'erpStoreId',
  erpDeliveryType: 'erpDeliveryType',
  erpPoiId: 'erpPoiId',
  /**@name 门店 inputDialog 单选 */
  erpStoreIdName: 'erpStoreIdName',
  erpSummaryTypes: 'erpSummaryTypes',
  erpFidTooltip: 'erpFidTooltip',
  erpIsDefault: 'erpIsDefault',
  erpMemo: 'erpMemo',
  otherIncomeExpensesName: 'otherIncomeExpensesName',
  module: 'module_name',
  menu_name: 'menu_name',
  erpBusinessScopeType: 'businessScopeType',
  erpName: 'erpName',
  erpCategoryIds: 'erpCategoryIds',
  erpPurchaseBusinessScope: 'erpPurchaseBusinessScope',
  erpBusinessScope: 'erpBusinessScope',
  erpSupplierIdsMain: 'erpSupplierIdsMain',
  erpStoreName: 'erpStoreName',
  erpItemType: 'erpItemType',
  erpinputPanel: 'erpinputPanel',
  erpBillState: 'erpBillState',
  erpBillFid: 'erpBillFid',
  billOrgIds: 'billOrgIds',
  billStoreIds: 'billStoreIds',
  billStoreCenterId: 'billStoreCenterId',
  billDate: 'billDate',
  verifyDate: 'verifyDate',
  storeIds: 'storeIds',
  /**@name 门店 配送中心 inputDialog 多选 */
  erpCenterStoreIdsMultiple: 'erpCenterStoreIdsMultiple',

  orgName: 'orgName',
  businessOrgIds: 'businessOrgIds',
  businessRangeOrgIds: 'businessRangeOrgIds',
  erpKeyword: 'erpKeyword',
  erpIsShow: 'erpIsShow',
  erpIsShowCheckbox: 'erpIsShowCheckbox',
  erpIsShowCheckboxNew: 'erpIsShowCheckboxNew',
  erpKingdeeCode: 'erpKingdeeCode',
  // 仓库管理
  erpWarehouseType: 'erpWarehouseType',
  // 供应商弹窗多选
  erpSupplierIds: 'erpSupplierIds',
  // 非配送中心门店
  erpCenterStoreIdsMultipleF: 'erpCenterStoreIdsMultipleF',

  // 二级组织多选
  erpOrgIdsMultiple: 'erpOrgIdsMultiple',
  // 单据状态
  erpOrderStatus: 'erpOrderStatus',
  // start
  erpitemIds: 'erpitemIds',
  erpitemId: 'erpitemId',
  erpCreateBy: 'erpCreateBy',
  itemDeptNames: 'itemDeptNames',
  validDate: 'validDate',
  deliveryDate: 'deliveryDate',
  companyName: 'companyName',
  cityArea: 'cityArea',
  businessArea: 'businessArea',
  erpSummerType: 'erpSummerType',
  // 依赖项
  settlement_model: 'settlement_model',
  erpSummaryTypesForAlipay: 'erpSummaryTypesForAlipay',
  erpOrgIdsLevel2: 'erpOrgIdsLevel2',
  // 应用用户部门
  erpUserDeptIds: 'erpUserDeptIds',
  // 经营范围&采购经营范围 商品类型
  erpItemTypeStyle: 'erpItemTypeStyle',
  // 门店管理-启用门店价格
  erpEnableStorePrice: 'erpEnableStorePrice',
  // 门店管理-营业执照
  erpHasBusinessLicense: 'erpHasBusinessLicense',
  // 门店管理-行政区域
  erpAreaCodes: 'erpAreaCodes',
  // 门店管理-业财核算组织
  erpFinanceOrganization: 'erpFinanceOrganization',
  // 是否启用
  erpRuleEnable: 'erpRuleEnable',
  // 采购计划 - 单选
  purchasePlan: 'purchasePlan',

  // 采购计划 - 多选
  purchasePlans: 'purchasePlans',

  // 订单员,
  purchaseManager: 'purchaseManager',
  purchaseManagerName: 'purchaseManagerName',
  // 表单里直接存入商品信息
  erpItem: 'erpItem',
  //
  erpHiddenItemId: 'erpHiddenItemId',
  // 采购计划
  purchasePlanBuyNumber: 'purchasePlanBuyNumber',
  // 采购金额
  purchasePlanMoney: 'purchasePlanMoney',
  cooperateStoreIds: 'cooperateStoreIds',
  shareCenterStoreIds: 'shareCenterStoreIds',
  // 去掉管理中心门店
  erpStoreIdsEnableOrganization: 'erpStoreIdsEnableOrganization',
  erpRepeatTel: 'erpRepeatTel',
  erpStoreIdsForArea: 'erpStoreIdsForArea',
  // 调出门店单选
  erpCenterStoreIdForOrg: 'erpCenterStoreIdForOrg',

  erpCenterSingleStoreId: 'erpCenterSingleStoreId',
  erpCenteMultipleStoreId: 'erpCenteMultipleStoreId',
  deliveryCenterStores: 'deliveryCenterStores',
  erpSaleItemId: 'erpSaleItemId',
  erpSaleproductDeptId: 'erpSaleproductDeptId',
  erpModifyPrice: 'erpModifyPrice',
  erpRefundItemIds: 'erpRefundItemIds',
  erpKingdeeCode2: 'erpKingdeeCode2',
  erpKingdeeCode1: 'erpKingdeeCode1',
  transferDocumentBatchOrder: 'transferDocumentBatchOrder',
  recordDates: 'recordDates',
  estimatedArrivalDate: 'estimatedArrivalDate',
  commonInput: 'commonInput',
  outStoreIdsNoOrg: 'outStoreIdsNoOrg',
};
export const fieldListConfig: CreateMapItem[] = [
  ...goodsFilesConfig,
  ...storeManageConfig,
  ...wholesaleCustomerConfig,
  ...supplierConfig,
  ...userManageConfig,
  ...returnRateStatisticsConfig,
  ...organizeManageConfig,
  ...deliveryCenterStoreConfig,
  ...tiktokcouponrefundConfig,
  ...storeAreaConfig,
  ...saleAnalysisConfig,
  ...companyHeaderConfig,
  ...goodsUnitsConfig,
  ...purchaseBusinessRangeConfig,
  ...userBranchConfig,
  ...supplierMonthlyBillConfig,
  ...bussinessRefundConfig,
  ...moneyApplyConfig,
  ...couponDataAnalysisConfig,
  ...purchaseOrderingConfig,
  ...storeHouseConfig,
  ...businessRangeConfig,
  ...newYearGoodsPlanDetailsConfig,
  ...interWarehouseTransferConfig,
  ...applyPaySysAnalysisConfig,
  ...advancePositionConfig,
  ...storePropertiesConfig,
  ...transferBasketConfig,
  ...storeItemReplenishConfig,
  ...payModeConfig,
  ...goodsOrderConfig,
  ...storeDeviceManageConfig,
  ...wholesaleOrgSettingConfig,
  ...invoiceCheckConfig,
  ...devicesBrandConfig,
  ...StoreDeliveryPriceKeyMapKeys,
  ...deliverySpecialPriceConfig,
  ...BIConfig,
  ...purchaseShareConfig,
  ...storeOrderConfig,
  ...deliverySpecialPriceConfig,
  ...BIConfig,
  ...StoreDeliveryPriceKeyMapKeys,
  ...purchaseShareConfig,
  ...storeOrderConfig,
  ...stockCheckOrderConfig,
  ...goodsBrandConfig,
  {
    tag: 'ERP',
    label: '权限',
    name: 'authority_ids',
    id: 'erpClientAuthority',
    componentType: 'select',
    onSearch: async (params, baseURL, globalFetch) => {
      const { data } = await globalFetch.post(
        `${baseURL}/erp/hxl.erp.authority.search`,
        {
          keyword: params.keyword,
          type: 'CLIENT',
        },
      );
      if (Array.isArray(data)) {
        return data.map((item) => {
          return {
            label:
              item.app_type +
              '-' +
              item.path.replace('/', '-') +
              '-' +
              item.action,
            value: item.id,
          };
        });
      }
    },
    request: async (formValues, anybaseURL, globalFetch) => {
      const { data } = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.authority.search`,
        {
          type: 'CLIENT',
        },
      );
      if (Array.isArray(data)) {
        return data?.map((item) => {
          return {
            label: item.app_type + '-' + item.name + '-' + item.action,
            value: item.id,
          };
        });
      }
      return [];
    },
    fieldProps: { mode: 'multiple', popupMatchSelectWidth: false },
  },
  {
    tag: 'ERP',
    label: '权限',
    name: 'authority_ids',
    id: 'erpPosAuthority',
    componentType: 'select',
    onSearch: async (params, baseURL, globalFetch) => {
      const { data } = await globalFetch.post(
        `${baseURL}/erp/hxl.erp.authority.search`,
        {
          keyword: params.keyword,
          type: 'POS',
        },
      );
      if (Array.isArray(data)) {
        return data.map((item) => {
          return {
            label:
              item.app_type +
              '-' +
              item.path.replace('/', '-') +
              '-' +
              item.action,
            value: item.id,
          };
        });
      }
    },
    request: async (formValues, anybaseURL, globalFetch) => {
      const { data } = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.authority.search`,
        {
          type: 'POS',
        },
      );
      if (Array.isArray(data)) {
        return data?.map((item) => {
          return {
            label: item.app_type + '-' + item.name + '-' + item.action,
            value: item.id,
          };
        });
      }
      return [];
    },
    fieldProps: { mode: 'multiple', popupMatchSelectWidth: false },
  },
  {
    tag: 'ERP',
    label: '权限',
    name: 'authority_ids',
    id: 'erpSupplierAuthority',
    componentType: 'select',
    onSearch: async (params, baseURL, globalFetch) => {
      const { data } = await globalFetch.post(
        `${baseURL}/erp/hxl.erp.authority.search`,
        {
          keyword: params.keyword,
          type: 'SUPPLIER',
        },
      );
      if (Array.isArray(data)) {
        return data.map((item) => {
          return {
            label:
              item.app_type +
              '-' +
              item.path.replace('/', '-') +
              '-' +
              item.action,
            value: item.id,
          };
        });
      }
    },
    request: async (formValues, anybaseURL, globalFetch) => {
      const { data } = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.authority.search`,
        {
          type: 'SUPPLIER',
        },
      );
      if (Array.isArray(data)) {
        return data?.map((item) => {
          return {
            label: item.app_type + '-' + item.name + '-' + item.action,
            value: item.id,
          };
        });
      }
      return [];
    },
    fieldProps: { mode: 'multiple', popupMatchSelectWidth: false },
  },
  {
    tag: 'ERP',
    label: '权限',
    name: 'authority_ids',
    id: 'erpUserAuthority',
    componentType: 'select',
    onSearch: async (params, baseURL, globalFetch) => {
      const { data } = await globalFetch.post(
        `${baseURL}/erp/hxl.erp.authority.search`,
        {
          keyword: params?.keyword,
          type: 'USER',
        },
      );
      if (Array.isArray(data)) {
        return data?.map((item) => {
          return {
            label:
              item.app_type +
              '-' +
              item.path.replace('/', '-') +
              '-' +
              item.action,
            value: item.id,
          };
        });
      }
    },
    request: async (formValues, anybaseURL, globalFetch) => {
      const { data } = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.authority.search`,
        {
          type: 'USER',
        },
      );
      if (Array.isArray(data)) {
        return data?.map((item) => {
          return {
            label: item.app_type + '-' + item.name + '-' + item.action,
            value: item.id,
          };
        });
      }
      return [];
    },
    fieldProps: { mode: 'multiple', popupMatchSelectWidth: false },
  },
  {
    tag: 'ERP',
    id: 'erpSupplierIdsMain',
    componentType: 'group',
    fieldProps: {
      formList: [
        {
          tag: 'ERP',
          label: '供应商',
          id: ErpFieldKeyMap.erpSupplierIds,
          name: 'supplier_ids',
          fieldProps: {
            dialogParams: {
              type: 'supplier',
              dataType: 'lists',
              isLeftColumn: true,
              isMultiple: true,
              primaryKey: 'id',
              data: {
                enabled: true,
              },
            },
          },
          componentType: 'inputDialog',
        },
        {
          componentType: 'checkbox',
          name: 'main_supplier',
          id: 'erpSupplierIdsMainGroup1',
          tag: 'ERP',
          group: false,
          colon: false,
          fieldProps: {
            options: [
              {
                label: '主供应商',
                value: 'main_supplier',
              },
            ],
          },
        },
      ],
    },
  },
  {
    tag: 'ERP',
    label: '供应商',
    id: ErpFieldKeyMap?.erpSupplierId,
    name: 'supplier_id',
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isMultiple: false,
      },
    },
    dependencies: ['type'],
    formItemProps: {
      getValueFromEvent: (value: any) => {
        console.log('value', value);
        if (!Array.isArray(value)) return;
        return value[0];
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    id: ErpFieldKeyMap?.erpUserDeptIds,
    componentType: 'inputDialog',
    label: '应用用户部门',
    name: 'user_dept_ids',
    fieldProps: {
      dialogParams: {
        type: 'userdept',
        dataType: 'lists',
        isLeftColumn: false,
        isMultiple: true,
      },
    },
  },
  {
    tag: 'ERP',
    label: '经营范围',
    id: ErpFieldKeyMap?.erpBusinessScope,
    name: 'business_scope_ids',
    fieldProps: {
      dialogParams: {
        type: 'businessRecordRange',
        dataType: 'lists',
        isMultiple: true,
        isLeftColumn: false,
        data: { business_type: 1 },
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    id: ErpFieldKeyMap?.basketIds,
    componentType: 'inputDialog',
    label: '载具名称',
    name: 'basket_ids',
    fieldProps: {
      dialogParams: {
        type: 'basket',
        dataType: 'lists',
        isLeftColumn: false,
        isMultiple: true,
      },
    },
  },
  {
    tag: 'ERP',
    label: '采购经营范围',
    id: ErpFieldKeyMap?.erpPurchaseBusinessScope,
    name: 'purchase_business_scope_ids',
    fieldProps: {
      dialogParams: {
        type: 'businessRecordRange',
        dataType: 'lists',
        isLeftColumn: false,
        isMultiple: true,
        data: { business_type: 0 },
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '组织',
    name: 'org_id',
    id: ErpFieldKeyMap?.erpOrgId,
    dependencies: ['level', 'id'], // level：组织管理、id: 组织管理-组织id
    fieldProps: {
      treeModalConfig: {
        title: '选择组织',
        url: '/erp/hxl.erp.org.tree',
        dataType: 'lists',
        checkable: false, // 是否多选
        primaryKey: 'id',
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value[0];
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '组织',
    name: 'org_id',
    id: ErpFieldKeyMap?.erpOrgIdByCompanyHeader,
    dependencies: ['level', 'id'], // level：组织管理、id: 组织管理-组织id
    fieldProps: {
      treeModalConfig: {
        title: '选择组织',
        url: '/erp/hxl.erp.org.tree.invoice',
        dataType: 'lists',
        checkable: false, // 是否多选
        primaryKey: 'id',
        topLevelTreeDisabled: true,
        afterPost: (data: any) => {
          return data.map((item: any) => ({
            ...item,
            disabled: !!item.is_record,
          }));
        },
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value[0];
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '组织',
    name: 'org_ids',
    id: ErpFieldKeyMap?.erpOrgIds,
    dependencies: ['level', 'id'], // level：组织管理、id: 组织管理-组织id
    fieldProps: {
      treeModalConfig: {
        title: '选择组织',
        url: '/erp/hxl.erp.org.tree',
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value;
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '收货组织',
    name: 'org_ids',
    id: ErpFieldKeyMap?.erpOutOrgIds,
    fieldProps: {
      treeModalConfig: {
        title: '选择组织',
        url: '/erp/hxl.erp.org.tree',
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value;
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '发货组织',
    name: 'out_org_ids',
    id: ErpFieldKeyMap?.erpShippingOrganization,
    fieldProps: {
      treeModalConfig: {
        title: '选择组织',
        url: '/erp/hxl.erp.org.tree',
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value;
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '批发组织',
    name: 'org_ids',
    id: ErpFieldKeyMap?.erpOrgIdsMultiplelevel3,
    dependencies: ['level', 'id'], // level：组织管理、id: 组织管理-组织id
    fieldProps: {
      treeModalConfig: {
        title: '选择组织',
        url: '/erp/hxl.erp.org.tree',
        dataType: 'trees',
        checkable: true, // 是否多选
        primaryKey: 'id',
        afterPost: (data: any) => {
          return data.filter((item: any) => item.level === 3);
        },
        params: {
          level: 3,
        },
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value;
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '门店区域类别',
    id: ErpFieldKeyMap.erpStoreAreaCategoriesId,
    name: 'store_area_categories_id',
    fieldProps: {
      dialogParams: {
        type: 'storeAreaCategories',
        dataType: 'lists',
        isLeftColumn: false,
        isMultiple: false,
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '门店',
    id: ErpFieldKeyMap?.erpStoreIds,
    name: 'store_ids',
    dependencies: ['org_ids', 'summary_types'],
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['org_ids', 'summary_types']),
      };
      if (!form?.getFieldsValue(true).org_ids) {
        delete data?.org_ids;
      }
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...data,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '门店',
    id: ErpFieldKeyMap?.erpStoreIdsOrder,
    name: 'store_ids',
    dependencies: ['summary_types'],
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['summary_types']),
      };
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...data,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '门店',
    id: ErpFieldKeyMap?.erpOrderTimeruleStoreIds,
    name: 'store_ids',
    // dependencies: ['org_ids', 'summary_types'],
    fieldProps: (form) => {
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            center_flag: false,
            status: true,
            supplierSwitch: false,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '门店',
    id: ErpFieldKeyMap?.erpOrderTimeruleStoreIds,
    name: 'store_ids',
    // dependencies: ['org_ids', 'summary_types'],
    fieldProps: (form) => {
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            center_flag: false,
            status: true,
            supplierSwitch: false,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '调往门店',
    id: ErpFieldKeyMap?.erpReturnRateInStoreIds,
    name: 'in_store_ids',
    fieldProps: {
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: true,
        data: {
          center_flag: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '配送中心门店',
    id: ErpFieldKeyMap?.erpCenterStoreIdsMultiple,
    dependencies: ['org_ids'],
    name: 'store_ids',
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['org_ids']),
      };
      if (!form?.getFieldsValue(true).org_ids) {
        delete data?.org_ids;
      }
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...data,
            center_flag: true,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '配送类型',
    id: ErpFieldKeyMap.erpDeliveryType,
    name: 'delivery_type',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '直营',
          value: 'DIRECT',
        },
        {
          label: '加盟',
          value: 'JOIN',
        },
      ],
    },
  },
  {
    tag: 'ERP',
    label: '下游单据',
    id: ErpFieldKeyMap.erpDownstreamDocuments,
    name: 'delivery_type',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '采购订单',
          value: 'PURCHASE',
        },
      ],
    },
  },
  {
    tag: 'ERP',
    label: '是否启用',
    id: ErpFieldKeyMap.erpBasketEnabled,
    name: 'enabled',
    componentType: 'checkbox',
    fieldProps: {
      options: [{ label: '启用', value: 'enabled' }],
    },
    formItemProps: {
      label: ' ',
    },
    group: false,
    colon: false,
  },
  {
    tag: 'ERP',
    label: '应用模块',
    id: ErpFieldKeyMap.erpPayModelChecked,
    componentType: 'group',
    formItemProps: {
      label: '应用模块',
      colon: true,
      style: {
        paddingLeft: '52px',
      },
    },
    fieldProps: {
      formList: [
        {
          componentType: 'checkbox',
          name: 'un_login_days_flag',
          id: 'erpPayModelCheckedgroup1',
          tag: 'ERP',
          group: false,
          colon: false,
          fieldProps: {
            options: [
              { label: 'POS业务', value: 'pos' },
              { label: '门店结算', value: 'store_settlement' },
              { label: '供应商结算', value: 'supplier_settlement' },
              { label: '批发客户结算', value: 'wholesale_settlement' },
            ],
            style: {
              width: '120px',
            },
          },
        },
      ],
    },
  },
  //  抖音门店设置
  {
    tag: 'ERP',
    label: '抖音门店编号',
    id: ErpFieldKeyMap.erpPoiId,
    name: 'poi_id',
    componentType: 'input',
    fieldProps: {
      width: 180,
    },
  },
  {
    tag: 'ERP',
    label: '新零帮门店',
    id: ErpFieldKeyMap.erpStoreIdName,
    name: 'store_id',
    fieldProps: {
      width: 180,
      dialogParams: {
        type: 'store',
        nullable: true,
        dataType: 'lists',
        isMultiple: false,
      },
      // @ts-ignore
      fieldNames: { idKey: 'id', nameKey: 'store_name' },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '汇总条件',
    id: ErpFieldKeyMap.erpSummaryTypes,
    name: 'summary_types',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '门店',
          value: 'STORE',
        },
        {
          label: '营业日',
          value: 'BIZDAY',
        },
        {
          label: '省',
          value: 'PROVINCE',
        },
        {
          label: '市',
          value: 'CITY',
        },
        {
          label: '区/县',
          value: 'DISTRICT',
        },
        {
          label: '配送类型',
          value: 'STORE_TYPE',
        },
      ],
      mode: 'multiple',
    },
  },
  // 公司抬头管理
  {
    tag: 'ERP',
    label: '设为默认',
    id: ErpFieldKeyMap.erpIsDefault,
    componentType: 'checkbox',
    fieldProps: {
      options: [{ label: '设为默认', value: 'is_default' }],
    },
    formItemProps: {
      label: ' ',
    },
    colon: false,
    group: false,
  },
  {
    tag: 'ERP',
    label: '备注',
    id: ErpFieldKeyMap.erpMemo,
    name: 'memo',
    componentType: 'textArea',
  },
  //  其他收支项目名称设置
  {
    tag: 'ERP',
    label: '其他收支项目名称',
    id: ErpFieldKeyMap?.otherIncomeExpensesName,
    name: 'name',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '模块',
    name: 'module_name',
    type: 'inputDialog',
    allowClear: true,
    placeholder: '',
    linkId: 'module_name',
    fieldNames: { idKey: 'module_path', nameKey: 'module_name' },
    onChange: async (e: string[], _: any, form: any) => {
      form.setFieldValue('menu_name', '');
      form.setFieldValue(
        'storehouse',
        _[0]?.menus?.map((v: any) => {
          return {
            label: v.menu_name,
            value: v.menu_name,
            type: v.app_type,
          };
        }),
      );
      form.setFieldValue('app_type', _[0]?.app_type);
      if (!_[0].parent_module_path) {
        form.setFieldValue('module_name', null);
      }
    },
    treeModalConfig: {
      title: '选择模块',
      url: '/erp/hxl.erp.printtemplate.menus',
      dataType: 'lists',
      primaryKey: 'module_path',
      data: {
        enabled: true,
      },
      fieldName: {
        id: 'module_path',
        name: 'module_name',
        parent_id: 'parent_module_path',
      },
      onSelect: (info: any, selectedKeys: string[]) => {
        // if(info.)
        // selectedKeys = selectedKeys.filter(key => key !== info.node.key);
      },
      width: 360, // 模态框宽度
    },
  },
  {
    tag: 'ERP',
    label: '单据',
    id: ErpFieldKeyMap.menu_name,
    name: 'menu_name',
    componentType: 'select',
    fieldProps: {
      options: [],
    },
  },
  {
    tag: 'BI',
    componentType: 'select',
    id: 'centerStores',
    name: 'store_ids',
    label: '配送中心',
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
    },
    dependencies: ['company_ids'],
    handleDefaultValue: (data) => {
      return data?.map((v: any) => v.value) || [];
    },
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        anybaseURL + '/center/hxl.center.store.delivery.find',
        {
          company_ids: formValues.company_ids || [],
        },
      );
      if (res.code == 0) {
        return res.data.map((item: any) => {
          return {
            // ...item,
            label: item.store_name,
            value: item.erp_store_id,
          };
        });
      }
      return [];
    },
  },
  // 采购经营范围
  {
    tag: 'ERP',
    label: '范围类型',
    id: ErpFieldKeyMap.erpBusinessScopeType,
    name: 'business_scope_type',
    componentType: 'select',
    fieldProps: {
      options: [
        { label: '商品明细', value: 0 },
        // { label: '商品分类', value: 1 }
      ],
    },
  },

  {
    tag: 'ERP',
    label: '依赖项',
    dependencies: ['supplier_id'],
    id: ErpFieldKeyMap.settlement_model,
    linkId: 'settlement_model',
    name: 'settlement_model',
    componentType: 'input',
  },
  // 智能订货
  {
    tag: 'ERP',
    label: '名称',
    id: ErpFieldKeyMap.erpName,
    name: 'name',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '商品分类',
    id: ErpFieldKeyMap.erpCategoryIds,
    name: 'category_ids',
    fieldProps: {
      treeModalConfig: {
        title: '选择商品分类', // 标题
        url: '/erp/hxl.erp.category.find', // 请求地址
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
        data: {
          enabled: true,
        },
        width: 360,
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '应用门店',
    id: ErpFieldKeyMap.erpStoreName,
    name: 'store_names',
    fieldProps: {
      width: 180,
      dialogParams: {
        type: 'store',
        nullable: true,
        dataType: 'lists',
        isMultiple: true,
      },
      // @ts-ignore
      fieldNames: { idKey: 'id', nameKey: 'store_name' },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    componentType: 'input',
    label: '组织',
    name: 'org_name',
    id: ErpFieldKeyMap?.orgName,
  },
  {
    tag: 'ERP',
    label: '商品类型',
    id: ErpFieldKeyMap.erpItemType,
    name: 'item_type',
    componentType: 'select',
    placeholder: '商品类型',
    fieldProps: {
      options: [
        {
          label: '标准商品',
          value: 'STANDARD',
        },
        {
          label: '组合商品',
          value: 'COMBINATION',
        },
        {
          label: '成分商品',
          value: 'COMPONENT',
        },
        {
          label: '制单组合',
          value: 'MAKEBILL',
        },
        {
          label: '分级商品',
          value: 'GRADING',
        },
        {
          label: '主规格商品',
          value: 'MAINSPEC',
        },
        {
          label: '多规格商品',
          value: 'MULTIPLESPEC',
        },
      ],
    },
    // formItemProps: {
    //   style: {
    //     width: 220
    //   }
    // }
  },
  // pages\xlb_erp\src\pages\wholesalePriceAdjustment\header\index.tsx 中已被注释
  {
    tag: 'ERP',
    label: '单据状态',
    id: ErpFieldKeyMap.erpBillState,
    name: 'state',
    componentType: 'select',
    fieldProps: {
      options: [
        { label: '制单', value: 'INIT', type: 'info' },
        { label: '审核', value: 'AUDIT', type: 'warning' },
        { label: '通过', value: 'PASS', type: 'success' },
        { label: '拒绝', value: 'DENY', type: 'danger' },
        { label: '已失效', value: 'INVALID', type: 'danger' },
        { label: '已生效', value: 'EFFECT', type: 'success' },
      ],
    },
  },
  {
    tag: 'ERP',
    componentType: 'input',
    label: '单据号',
    name: 'fid',
    id: ErpFieldKeyMap?.erpFidTooltip,
    formItemProps: {
      tooltip: '单据号不受其他查询条件限制',
    },
  },
  {
    tag: 'ERP',
    componentType: 'input',
    label: '单据号',
    name: 'fid',
    id: ErpFieldKeyMap?.erpBillFid,
  },
  {
    tag: 'ERP',
    label: '供应商',
    id: ErpFieldKeyMap.erpSupplierIds,
    name: 'supplier_ids',
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
    },
    componentType: 'inputDialog',

    dependencies: ['summary_types'],
  },
  {
    tag: 'ERP',
    label: '组织',
    id: ErpFieldKeyMap.billOrgIds,
    name: 'org_ids',
    componentType: 'select',
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
    },
    dependencies: ['enable_organization'],
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.org.find',
        {
          company_ids: formValues.company_ids || [],
        },
      );
      if (res.code == 0) {
        return res.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
      return [];
    },
  },
  {
    tag: 'ERP',
    label: '应用门店',
    id: ErpFieldKeyMap.billStoreIds,
    name: 'store_ids',
    dependencies: ['org_ids'],
    fieldProps: (form) => {
      const data = {
        org_ids: form.getFieldValue('org_ids'),
      };
      // 左侧树和查询条件同时存在时，若查询条件为空删除查询条件（即使为undefined/null）
      if (!form.getFieldsValue(true)?.org_ids) {
        delete data.org_ids;
      }
      return {
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        } as any,
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: data,
        },
      };
    },
    // 升级pro2.0预用
    // fieldProps:(form) =>{
    //   return  {
    //     fieldNames: {
    //       idKey: 'id',
    //       nameKey: 'store_name'
    //     },
    //     dialogParams: {
    //           type: 'store',
    //           dataType: 'lists',
    //           isMultiple: true,
    //           data: {
    //             org_ids: form?.getFieldValue(org_ids) || null
    //           }
    //         }
    //       }
    //   },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '组织',
    id: ErpFieldKeyMap.orgIds,
    name: 'org_ids',
    componentType: 'select',
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
    },
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.org.find',
        {
          company_ids: formValues.company_ids || [],
        },
      );
      if (res.code == 0) {
        return res.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
      return [];
    },
  },
  {
    tag: 'ERP',
    label: '对账门店',
    id: ErpFieldKeyMap.storeIds,
    name: 'store_ids',
    dependencies: ['org_ids'],
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['org_ids']),
      };
      if (!form?.getFieldsValue(true).org_ids) {
        delete data?.org_ids;
      }
      return {
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        } as any,
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: data,
        },
      };
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    componentType: 'select',
    id: ErpFieldKeyMap.businessOrgIds,
    name: 'org_ids',
    label: '组织',
    fieldProps: {
      mode: 'multiple',
    },
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.org.find',
        {},
      );
      if (res.code == 0) {
        return res.data
          ?.filter((v: any) => v.id)
          ?.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
      }
      return [];
    },
    dependencies: ['summary_types'],
  },
  {
    tag: 'ERP',
    componentType: 'select',
    id: ErpFieldKeyMap.businessRangeOrgIds,
    name: 'org_ids',
    label: '组织',
    fieldProps: {
      mode: 'multiple',
    },
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.org.find',
        {
          level: 2,
        },
      );
      if (res.code == 0) {
        return res.data
          ?.filter((v: any) => v.id)
          ?.map((item: any) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
      }
      return [];
    },
    dependencies: ['summary_types'],
  },
  {
    tag: 'ERP',
    componentType: 'input',
    label: '关键字',
    name: 'keyword',
    placeholder: '商品代码/条码/名称/速记码',
    id: ErpFieldKeyMap?.erpKeyword,
    // fieldProps: {
    // },
  },
  //  pages\xlb_erp\src\pages\orgBusinessArea\index.tsx 中已被注释
  {
    tag: 'ERP',
    name: 'showPanel',
    componentType: 'inputPanel',
    id: 'erpinputPanel',
    label: '',
    fieldProps: {
      allowClear: true,
      notTransform: true,
      options: [
        {
          label: '正常',
          value: 'normal',
        },
        {
          label: '停购',
          value: 'stop_purchase',
        },
        {
          label: '停止要货',
          value: 'stop_request',
        },
      ],
      items: [
        {
          label: '仅显示',
          key: true,
        },
        {
          label: '不显示',
          key: false,
        },
      ],
    },
    formItemProps: (form: FormInstance) => ({
      label: '  ',
      colon: false,
      getValueFromEvent: (
        event: Parameters<NonNullable<XlbInputPanelProps['onChange']>>[0],
      ) => {
        const reqValue = {
          normal: undefined,
          stop_purchase: undefined,
          stop_request: undefined,
        };
        Object.keys(reqValue).forEach((v) => {
          event.forEach((item: any) => {
            if (item.value == v) {
              reqValue[v as keyof typeof reqValue] = item.itemKey;
            }
          });
        });
        form.setFieldsValue({ ...reqValue });
        return reqValue;
      },
      getValueProps: () => {
        const data = form.getFieldsValue(true);
        const values: any = [];
        const objKeys = Object?.keys(data);
        const allListVualue = ['normal', 'stop_purchase', 'stop_request'];
        objKeys?.forEach(
          (v) =>
            allListVualue?.includes(v) &&
            data[v] != undefined &&
            values.push(v),
        );
        return {
          value: values,
        };
      },
    }),
  },
  {
    tag: 'ERP',
    componentType: 'select',
    name: 'is_show',
    id: ErpFieldKeyMap?.erpIsShow,
    fieldProps: {
      options: [
        {
          label: '仅显示',
          value: true,
        },
        {
          label: '不显示',
          value: false,
        },
      ],
    },
    formItemProps: {
      colon: false,
      initialValue: true,
      style: {
        width: 100,
        height: 26,
        marginLeft: 10,
        marginRight: 10,
      },
    },
  },
  {
    tag: 'ERP',
    name: 'is_show_checkbox',
    id: ErpFieldKeyMap.erpIsShowCheckbox,
    componentType: 'checkbox',
    fieldProps: {
      options: [
        {
          label: '正常',
          value: 'normal',
        },
        {
          label: '停购',
          value: 'stop_purchase',
        },
        {
          label: '停止要货',
          value: 'stop_request',
        },
      ],
    },
    formItemProps: {
      colon: false,
      initialValue: ['normal'],
    },
  },
  {
    tag: 'ERP',
    name: 'is_show_checkbox_new',
    id: ErpFieldKeyMap.erpIsShowCheckboxNew,
    componentType: 'checkbox',
    fieldProps: {
      options: [
        {
          label: '正常',
          value: 'normal',
        },
        {
          label: '停购',
          value: 'stop_purchase',
        },
        {
          label: '停止要货',
          value: 'stop_request',
        },
      ],
    },
    formItemProps: {
      colon: false,
    },
  },
  // 供应商月度账单
  {
    tag: 'ERP',
    label: '组织',
    id: ErpFieldKeyMap.erpOrgIdsMultiple,
    name: 'org_ids',
    componentType: 'select',
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
    },
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.org.find',
        {
          company_ids: formValues.company_ids || '',
          level: 2,
        },
      );
      if (res.code == 0) {
        return res.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
      return [];
    },
  },
  {
    tag: 'ERP',
    id: ErpFieldKeyMap?.erpCenterStoreIdsMultipleF,
    name: 'store_ids',
    dependencies: ['org_ids'],
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['org_ids']),
      };
      if (!form?.getFieldsValue(true).org_ids) {
        delete data?.org_ids;
      }
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...data,
            center_flag: false,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '单据状态',
    id: ErpFieldKeyMap.erpOrderStatus,
    name: 'state',
    componentType: 'select',
    fieldProps: {
      options: [
        { label: '制单', value: 'INIT', type: 'info' },
        { label: '审核', value: 'AUDIT', type: 'warning' },
        { label: '作废', value: 'INVALID', type: 'danger' },
      ],
    },
  },
  {
    tag: 'ERP',
    label: '商品档案',
    id: 'erpitemIds',
    name: 'item_ids',
    fieldProps: {
      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isMultiple: true,
        // isLeftColumn: false,
      },
    },
    componentType: 'inputDialog',
    dependencies: ['summary_types'],
  },
  {
    tag: 'ERP',
    label: '商品档案',
    id: ErpFieldKeyMap.erpitemId,
    name: 'item_id',
    fieldProps: {
      dialogParams: {
        type: 'goods',
        dataType: 'lists',
      },
    },
    formItemProps: {
      normalize: (value) => {
        return Array.isArray(value) ? value?.[0] : value;
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    componentType: 'input',
    label: '制单人',
    name: 'create_by',
    id: ErpFieldKeyMap?.erpCreateBy,
  },
  {
    tag: 'ERP',
    componentType: 'input',
    label: '商品部门',
    name: 'item_dept_names',
    id: ErpFieldKeyMap?.itemDeptNames,
  },
  {
    tag: 'ERP',
    label: '配送日期',
    id: ErpFieldKeyMap?.deliveryDate,
    name: 'delivery_date',
    componentType: 'datePicker',
  },
  {
    tag: 'ERP',
    label: '有效日期',
    id: ErpFieldKeyMap?.validDate,
    name: 'valid_date',
    componentType: 'datePicker',
  },
  // 支付宝碰一碰
  {
    tag: 'ERP',
    id: ErpFieldKeyMap?.companyName,
    label: '公司',
    name: 'company_name',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '省市区',
    name: 'area_codes',
    id: ErpFieldKeyMap?.cityArea,
    fieldProps: {
      treeModalConfig: {
        title: '选择区域',
        url: '/erp/hxl.erp.store.area.find.all', // /erp/hxl.erp.store.area.find.all
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'code',
        fieldName: {
          id: 'code',
          parent_id: 'parent_code',
        },
      },
      fieldNames: {
        // @ts-ignore
        idKey: 'code',
        nameKey: 'name',
      },
    },

    // formItemProps: {
    //   getValueFromEvent: (value: any) => {
    //     if (!Array.isArray(value)) return
    //     return value[0]
    //   }
    // },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '业务区域',
    id: ErpFieldKeyMap?.businessArea,
    name: 'business_area_ids',
    fieldProps: {
      dialogParams: {
        isLeftColumn: true,
        type: 'businessArea',
        dataType: 'lists',
        isMultiple: true,
        // initAfterPost: (data: any) => {
        //   const dataValueDetails = data?.value_details?.map((v) => {
        //     return {
        //       ...v,
        //       show_name:
        //         v.six_name ||
        //         v.five_name ||
        //         v.four_name ||
        //         v.third_name ||
        //         v.second_name ||
        //         v.first_name
        //     }
        //   })
        //   return { list: dataValueDetails, total: data?.value_details?.length }
        // },
        handleDefaultValue: (data) => {
          console.log('🚀 ~ UserManageItem ~ data:', data);
          const _data = data.map((v) => {
            return {
              ...v,
              id: v.id,
              show_name:
                v.six_name ||
                v.five_name ||
                v.four_name ||
                v.third_name ||
                v.second_name ||
                v.first_name,
            };
          });
          return _data;
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'show_name',
      },
    },
    componentType: 'inputDialog',
  },
  // 汇总条件单选
  {
    tag: 'ERP',
    label: '汇总条件',
    id: ErpFieldKeyMap.erpSummerType,
    name: 'summary_types',
    componentType: 'select',
  },
  {
    tag: 'ERP',
    label: '汇总条件',
    id: ErpFieldKeyMap.erpSummaryTypesForAlipay,
    name: 'summary_types_alipay',
    componentType: 'select',
    fieldProps: {
      mode: 'multiple',
    },
  },
  {
    tag: 'ERP',
    componentType: 'input',
    label: '关键字',
    name: 'keyword',
    id: ErpFieldKeyMap?.erpItemKeyword,
  },
  {
    tag: 'ERP',
    componentType: 'compactDatePicker',
    formItemProps: {
      normalize: (value: any) => {
        console.log('🚀 ~ value:', value);
        if (!Array.isArray(value) || value.length === 0) {
          return null;
        } else {
          return value;
        }
      },
    },
    label: '日期',
    name: 'date',
    id: 'dateCommonGoodsFiles',
  },
  {
    tag: 'ERP',
    label: '商品类型',
    id: ErpFieldKeyMap.erpItemTypeStyle,
    name: 'item_type',
    componentType: 'select',
    placeholder: '商品类型',
    fieldProps: {
      options: [
        {
          label: '标准商品',
          value: 'STANDARD',
        },
        {
          label: '组合商品',
          value: 'COMBINATION',
        },
        {
          label: '成分商品',
          value: 'COMPONENT',
        },
        {
          label: '制单组合',
          value: 'MAKEBILL',
        },
        {
          label: '分级商品',
          value: 'GRADING',
        },
        {
          label: '主规格商品',
          value: 'MAINSPEC',
        },
        {
          label: '多规格商品',
          value: 'MULTIPLESPEC',
        },
      ],
    },
    // formItemProps: {
    //   style: {
    //     width: 220
    //   }
    // }
  },
  {
    tag: 'ERP',
    id: 'erpStoreLists',
    componentType: 'select',
    label: '门店',
    name: 'role_id',
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.store.center.find`,
        {},
      );
      if (res.code == 0) {
        return res.data.map((item: any) => ({
          label: item.store_name,
          value: item.id,
        }));
      }
      return [];
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
      popupMatchSelectWidth: false,
    },
  },
  {
    tag: 'ERP',
    label: '门店标签',
    id: ErpFieldKeyMap.storeLabels,
    name: 'store_labels',
    fieldProps: {
      dialogParams: {
        type: 'storeLabel',
        dataType: 'lists',
        isMultiple: true,
        isLeftColumn: false,
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_label_name',
      },
    },
    componentType: 'inputDialog',
  },
  // 门店状态
  {
    tag: 'ERP',
    label: '门店状态',
    id: ErpFieldKeyMap.erpStoreState,
    name: 'states',
    componentType: 'select',
    fieldProps: {
      options: [
        { value: 'TO_BE_DELIVERED', label: '待交付' },
        { value: 'BUILDING', label: '营建中' },
        { value: 'TO_BE_OPENED', label: '待营业' },
        { value: 'ON_LEAVE', label: '休息中' },
        { value: 'OPENED', label: '营业中' },
        { value: 'CLOSED', label: '已闭店' },
        { value: 'CANCELLED', label: '已取消' },
        { value: 'TO_BE_CLOSED', label: '待停业' },
      ],
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        return value ? [value] : null;
      },
    },
  },
  // 门店管理-经营类型
  {
    tag: 'ERP',
    label: '经营类型',
    id: ErpFieldKeyMap.erpManagementType,
    name: 'management_type',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '直营店',
          value: 0,
        },
        {
          label: '加盟店',
          value: 1,
        },
      ],
    },
  },
  // 门店管理-地区属性
  {
    tag: 'ERP',
    label: '地区属性',
    id: ErpFieldKeyMap.erpAreaAttributes,
    name: 'area_attributes',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '地级市',
          value: 0,
        },
        {
          label: '县级市',
          value: 1,
        },
        {
          label: '乡镇',
          value: 2,
        },
      ],
    },
  },
  // 门店管理-门店属性
  {
    tag: 'ERP',
    label: '门店属性',
    id: ErpFieldKeyMap.erpStoreAttributes,
    name: 'store_attributes',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '标准店',
          value: 0,
        },
        {
          label: '综合店',
          value: 1,
        },
        {
          label: '旗舰店',
          value: 2,
        },
      ],
    },
  },
  // 门店管理-商圈属性
  {
    tag: 'ERP',
    label: '商圈属性',
    id: ErpFieldKeyMap.erpBusinessDistrictAttributes,
    name: 'business_district_attributes',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '商业圈',
          value: 0,
        },
        {
          label: '居民区',
          value: 1,
        },
        {
          label: '校区',
          value: 2,
        },
      ],
    },
  },
  // 是否启用
  {
    tag: 'ERP',
    label: '启用',
    id: ErpFieldKeyMap.erpStoreStatus,
    name: 'status',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },
  },
  // 是否启用
  {
    tag: 'ERP',
    label: '启用',
    id: ErpFieldKeyMap.erpRuleEnable,
    name: 'status',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '启用',
          value: true,
        },
        {
          label: '停用',
          value: false,
        },
      ],
    },
  },
  // 营业执照
  {
    tag: 'ERP',
    label: '营业执照',
    id: ErpFieldKeyMap.erpHasBusinessLicense,
    name: 'has_business_license',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '有',
          value: true,
        },
        {
          label: '无',
          value: false,
        },
      ],
    },
  },
  // 门店管理-行政区域
  {
    tag: 'ERP',
    label: '行政区域',
    name: 'city_codes',
    id: ErpFieldKeyMap?.erpAreaCodes,
    fieldProps: {
      treeModalConfig: {
        zIndex: 2002,
        title: '选择区域', // 标题
        url: '/erp/hxl.erp.store.area.find.all', // 请求地址
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'code',
        fieldName: {
          id: 'code',
          parent_id: 'parent_code',
          name: 'name',
        },
        width: 360,
      },
      fieldNames: {
        // @ts-ignore
        idKey: 'code',
        nameKey: 'name',
      },
    },
    componentType: 'inputDialog',
  },
  // 门店管理-业财核算组织
  {
    tag: 'ERP',
    id: ErpFieldKeyMap?.erpFinanceOrganization,
    componentType: 'select',
    label: '业财核算组织',

    name: 'finance_organization',
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.baseparam.read`,
        {},
      );
      if (res.code == 0) {
        return res.data?.finance_organizations?.map((item: any) => ({
          label: item,
          value: item,
        }));
      }
      return [];
    },
    fieldProps: {
      allowClear: true,
      popupMatchSelectWidth: false,
    },
  },
  {
    tag: 'ERP',
    label: '组织',
    id: ErpFieldKeyMap.erpOrgIdsLevel2,
    name: 'org_ids',
    componentType: 'select',
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
    },
    dependencies: ['enable_organization'],
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.org.find',
        {
          company_ids: formValues.company_ids || [],
          level: 2,
        },
      );
      if (res?.code == 0) {
        return res.data.map((item: any) => {
          return {
            label: item?.name,
            value: item?.id,
            level: item?.level,
          };
        });
      }
      return [];
    },
  },
  {
    tag: 'ERP',
    label: '商品类别',
    id: ErpFieldKeyMap.erpRateStatisticsItemCategory,
    name: 'item_category_ids',
    componentType: 'inputDialog',
    fieldProps: {
      treeModalConfig: {
        title: '选择商品分类', // 标题
        url: '/erp/hxl.erp.category.find', // 请求地址
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
        data: {
          enabled: true,
        },
        width: 360, // 模态框宽度
      },
    },
  },
  {
    tag: 'ERP',
    label: '订单员',
    id: ErpFieldKeyMap.purchaseManager,
    name: 'purchaser_id',
    componentType: 'inputDialog',
    fieldProps: {
      dialogParams: {
        title: '选择订单员',
        dataType: 'list',
        type: 'user',
        isLeftColumn: false,
        isMultiple: false,
        data: {
          un_login_days: 30,
        },
      },
    },
    formItemProps: {
      normalize(data) {
        return Array.isArray(data) ? data?.[0] : undefined;
      },
    },
    dependencies: ['summary_types'],
  },
  {
    tag: 'ERP',
    label: '订单员',
    id: ErpFieldKeyMap.purchaseManagerName,
    name: 'purchaser_name',
    componentType: 'input',
    dependencies: ['summary_types'],
  },
  {
    tag: 'ERP',
    label: '基础选择',
    id: ErpFieldKeyMap.erpCommonSelect,
    name: 'common_select',
    componentType: 'select',
    fieldProps: {
      options: [],
    },
  },
  {
    tag: 'ERP',
    label: '硬件名称',
    id: 'name',
    name: 'name',
    componentType: 'input',
    fieldProps: {
      width: 260,
    },
  },
  {
    tag: 'ERP',
    label: '厂商名称',
    id: 'factory',
    name: 'factory',
    componentType: 'input',
    fieldProps: {
      width: 260,
    },
  },
  {
    tag: 'ERP',
    label: '分类',
    id: 'category_id',
    name: 'category_id',
    componentType: 'select',
    options: [],
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.hardwarecategory.find',
        {},
      );
      if (res.code == 0) {
        return res.data?.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
      return [];
    },
    fieldProps: {
      width: 260,
      // mode: 'multiple',
      allowClear: true,
    },
  },
  // 供应商管理
  {
    tag: 'ERP',
    label: '保修期限(天)',
    id: 'warranty_period',
    name: 'warranty_period',
    componentType: 'inputNumber',
    fieldProps: {
      width: 260,
    },
  },
  {
    tag: 'ERP',
    label: '供货组织',
    name: 'supply_ogs_ids',
    id: ErpFieldKeyMap?.erpSupplierOrgIds,
    // dependencies: ['level供货组织', 'id'], // level：组织管理、id: 组织管理-组织id
    fieldProps: {
      treeModalConfig: {
        title: '选择组织',
        checkable: true,
        url: '/erp/hxl.erp.org.tree',
        dataType: 'lists',
        primaryKey: 'id',
        params: { level: 3 },
        afterPost(data: any) {
          // console.log('afterPost', data)
          return data.filter((i) => i.level == 3);
        },
      },
    },
    // formItemProps: {
    //   getValueFromEvent: (value: any) => {
    //     if (!Array.isArray(value)) return
    //     return value[0]
    //   }
    // },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '税号',
    id: ErpFieldKeyMap?.taxNum,
    name: 'tax_num',
    componentType: 'input',
  },
  // 批发组织
  {
    tag: 'ERP',
    label: '合作门店',
    id: ErpFieldKeyMap?.cooperateStoreIds,
    name: 'cooperate_store_ids',
    componentType: 'select',
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.wholesalecenterstore.find',
        {},
      );
      if (res.code == 0 && Array.isArray(res?.data)) {
        const map = new Map();
        const newArr = res?.data?.filter(
          (v: any) =>
            !map.has(v.cooperate_store_id) && map.set(v.cooperate_store_id, v),
        );
        return newArr?.map((item: any) => {
          return {
            label: item.cooperate_store_name,
            value: item.cooperate_store_id,
          };
        });
      }
      return [];
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
    },
  },
  {
    tag: 'ERP',
    label: '批发共享中心',
    id: ErpFieldKeyMap?.shareCenterStoreIds,
    name: 'share_center_store_ids',
    componentType: 'select',
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.wholesalecenterstore.find',
        {},
      );
      if (res.code == 0 && Array.isArray(res?.data)) {
        const map = new Map();
        const newArr = res?.data?.filter(
          (v: any) =>
            !map.has(v.share_center_store_id) &&
            map.set(v.share_center_store_id, v),
        );
        return newArr?.map((item: any) => {
          return {
            label: item.share_center_store_name,
            value: item.share_center_store_id,
          };
        });
      }
      return [];
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
    },
  },
  {
    tag: 'ERP',
    label: '门店',
    id: ErpFieldKeyMap?.erpStoreIdsEnableOrganization,
    name: 'store_ids',
    dependencies: ['org_ids', 'summary_types'],
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['org_ids', 'summary_types']),
      };
      if (!form?.getFieldsValue(true).org_ids) {
        delete data?.org_ids;
      }
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...data,
            enable_organization: false,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '仅查重复手机号',
    id: ErpFieldKeyMap.erpRepeatTel,
    componentType: 'checkbox',
    fieldProps: {
      options: [{ label: '仅查重复手机号', value: 'repeat_tel' }],
    },
    formItemProps: {
      label: ' ',
      colon: false,
    },
    group: false,
  },
  // 行政区域
  {
    tag: 'ERP',
    label: '行政区域门店',
    id: ErpFieldKeyMap?.erpStoreIdsForArea,
    name: 'store_ids',
    fieldProps: {
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: true,
        data: {
          enabled: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      },
    },
    formItemProps: {
      label: '门店',
    },
    componentType: 'inputDialog',
  },
  // 调出单
  {
    tag: 'ERP',
    label: '门店',
    id: ErpFieldKeyMap?.erpCenterStoreIdForOrg,
    name: 'store_id',
    fieldProps: {
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: false,
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      },
    },
    formItemProps: {
      label: '门店',
    },

    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    componentType: 'select',
    label: '',
    name: 'center_store_ids',
    dependencies: ['org_ids'],
    id: ErpFieldKeyMap?.erpCenteMultipleStoreId,
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.store.short.page`,
        {
          center_flag: true,
          org_ids: formValues.org_ids || void 0,
        },
      );
      if (res.code == 0) {
        return res.data?.content?.map((i: any) => ({
          value: i.id,
          label: i.store_name,
        }));
      }
      return [];
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
    },
  },
  {
    tag: 'ERP',
    componentType: 'select',
    label: '',
    name: 'center_store_ids',
    id: ErpFieldKeyMap?.erpCenterSingleStoreId,
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(
        `${anybaseURL}/erp/hxl.erp.store.short.page`,
        {
          center_flag: true,
        },
      );
      if (res.code == 0) {
        return res.data?.content?.map((i: any) => ({
          value: i.id,
          label: i.store_name,
        }));
      }
      return [];
    },
    fieldProps: {
      mode: 'single',
      allowClear: true,
    },
  },
  {
    tag: 'ERP',
    label: '商品档案',
    id: ErpFieldKeyMap.erpSaleItemId,
    name: 'item_ids',
    fieldProps: {
      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isMultiple: true,
        data: {
          show_sale_unit: true,
        },
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '商品部门',
    id: ErpFieldKeyMap.erpSaleproductDeptId,
    name: 'item_dept_ids',
    fieldProps: {
      dialogParams: {
        type: 'productDept',
        dataType: 'lists',
        isMultiple: true,
        isLeftColumn: false,
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '修改价',
    id: ErpFieldKeyMap.erpModifyPrice,
    name: 'modify_price',
    componentType: 'checkbox',
    fieldProps: {
      options: [{ label: '修改价', value: 'modify_price' }],
    },
    formItemProps: {
      label: ' ',
      colon: false,
    },
    group: false,
  },
  {
    tag: 'ERP',
    name: 'red',
    id: 'invoiceCheckIsRed',
    componentType: 'checkbox',
    fieldProps: {
      options: [
        {
          label: '是否红票',
          value: 'red',
        },
      ],
    },
    group: false,
    colon: false,
  },
  {
    tag: 'ERP',
    label: '供应商',
    id: 'invoiceCheckSupplierIds',
    name: 'supplier_ids',
    fieldProps: {
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '对账门店',
    id: 'invoiceCheckStoreIds',
    name: 'store_ids',
    fieldProps: {
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: true,
        data: {},
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '商品档案',
    id: 'erpRefundItemIds',
    name: 'item_ids',
    fieldProps: {
      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isMultiple: true,
        // isLeftColumn: false,
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    componentType: 'input',
    name: 'start_time',
    id: ErpFieldKeyMap?.erpKingdeeCode1,
    dependencies: ['time'],
    request: async (formValues, anybaseURL, globalFetch) => {
      return formValues;
    },
    handleDefaultValue(data) {
      return data?.time?.[0] ? `${data.time[0]?.split('T')?.[0]} 00:00:00` : '';
    },
  },
  {
    tag: 'ERP',
    componentType: 'input',
    name: 'end_time',
    id: ErpFieldKeyMap?.erpKingdeeCode2,
    dependencies: ['time'],
    request: async (formValues, anybaseURL, globalFetch) => {
      return formValues;
    },
    handleDefaultValue(data) {
      return data?.time?.[1] ? `${data.time[1]?.split('T')?.[0]} 23:59:59` : '';
    },
  },
  {
    tag: 'ERP',
    componentType: 'upload',
    id: ErpFieldKeyMap.erpCommonUpload,
    dependencies: ['fid'],
    // formItemProps: {
    //   rules: [{ required: true, message: '请上传文件' }]
    // }
  },
  {
    tag: 'ERP',
    label: '对账门店',
    id: ErpFieldKeyMap?.erpMoneyApplyStoreName,
    dependencies: ['supplier_id', 'settlement_model'],
    name: 'moneyApply_store_id',
    fieldProps: (form) => {
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: false,
          data: {
            status: true,
            supplier_id: form.getFieldValue('supplier_id'),
            query_center:
              form.getFieldValue('settlement_model') == 2 ? true : null,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value[0];
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '对账门店',
    id: ErpFieldKeyMap?.transferDocumentBatchOrder,
    name: 'store_id',
    fieldProps: (form) => {
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: false,
          data: {
            status: true,
            center_flag: true,
          },
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        },
      };
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value[0];
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    label: '记录日期',
    id: ErpFieldKeyMap?.recordDates,
    name: 'record_dates',
    componentType: 'rangePicker',
    formItemProps: {
      getValueFromEvent: (value: any) => {
        console.log('value', value);
        if (!Array.isArray(value) || value?.length !== 2) return;
        return value;
      },
    },
  },
  {
    tag: 'ERP',
    label: '',
    id: ErpFieldKeyMap.erpBasketEnabled,
    name: 'enabled',
    componentType: 'checkbox',
    fieldProps: {
      options: [{ label: '启用', value: 'enabled' }],
    },
    formItemProps: {
      label: ' ',
    },
    group: false,
    colon: false,
  },
  {
    tag: 'ERP',
    label: '预计到货日期',
    id: ErpFieldKeyMap?.estimatedArrivalDate,
    name: 'estimated_arrival_date_str',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '',
    id: 'commonInput',
    name: 'commonInput',
    componentType: 'input',
  },
  {
    tag: 'ERP',
    label: '调出门店',
    id: ErpFieldKeyMap.outStoreIdsNoOrg,
    name: 'out_store_ids',
    dependencies: ['out_org_ids'],
    fieldProps: (form: any) => {
      return {
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name',
        } as any,
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            center_flag: true,
          },
        },
      };
    },
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      return formValues;
    },
    handleDefaultValue: (data: any) => {
      if (data.out_org_ids?.length > 0) {
        return null;
      }
    },
    componentType: 'inputDialog',
  },
];