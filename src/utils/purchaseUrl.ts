import { LStorage } from './storage'

export const purchaseUrl = [
  // --采购补货分析
  '/erp/hxl.erp.purchasereplenishanalysis.find',
  '/erp/hxl.erp.purchasereplenishanalysis.item.add',
  '/erp/hxl.erp.purchasereplenishanalysis.export',
  '/erp/hxl.erp.purchasereplenishanalysis.item.page',
  '/erp/hxl.erp.purchasereplenishanalysis.itemstore.find',
  '/erp/hxl.erp.purchasereplenishanalysis.itemstore.export',
  '/erp/hxl.erp.purchasereplenishanalysis.purchaseorder.batchcreate',
  '/erp/hxl.erp.purchasereplenishanalysis.purchaseorder.create',
  '/erp/hxl.erp.purchasereplenishanalysis.read',
  '/erp/hxl.erp.supplier.producerandexecutivestandard.find',
  // --采购智能订货
  '/erp/hxl.erp.purchase.order.analysis',
  '/erp/hxl.erp.purchase.order.analysis.read',
  '/erp/hxl.erp.purchase.order.analysis.export',
  '/erp/hxl.erp.purchase.order.analysis.log',
  // --采购订单
  '/erp/hxl.erp.purchaseorder.save',
  '/erp/hxl.erp.purchaseorder.batchdelete',
  '/erp/hxl.erp.purchaseorder.page',
  '/erp/hxl.erp.purchaseorder.read',
  '/erp/hxl.erp.purchaseorder.update',
  '/erp/hxl.erp.purchaseorder.invalid',
  '/erp/hxl.erp.purchaseorder.audit',
  '/erp/hxl.erp.purchaseorder.batchaudit',
  '/erp/hxl.erp.purchaseorder.copy',
  '/erp/hxl.erp.purchaseorder.item.page',
  '/erp/hxl.erp.purchaseorder.template.download',
  '/erp/hxl.erp.purchaseorder.ordertemplate.download',
  '/erp/hxl.erp.purchaseorder.order.import',
  '/erp/hxl.erp.purchaseorder.order.import.save',
  '/erp/hxl.erp.purchaseorder.import',
  '/erp/hxl.erp.purchaseorder.export',
  '/erp/hxl.erp.purchaseorder.detail.export',
  '/erp/hxl.erp.purchaseorder.file.upload',
  '/erp/hxl.erp.purchaseorder.onway',
  '/erp/hxl.erp.purchaseorder.reportsummary',
  '/erp/hxl.erp.purchaseorder.receivesummary',
  '/erp/hxl.erp.purchaseorder.print.readdetail',
  '/erp/hxl.erp.purchaseorder.print.read',
  '/erp/hxl.erp.purchaseorder.print',
  '/erp/hxl.erp.purchaseorder.relationorder.find',
  '/erp/hxl.erp.documentrelate.find',
  // --采购收货单
  '/erp/hxl.erp.receiveorder.page',
  '/erp/hxl.erp.receiveorde.short.page',
  '/erp/hxl.erp.receiveorder.find.olderp',
  '/erp/hxl.erp.receiveorder.read',
  '/erp/hxl.erp.receiveorder.save',
  '/erp/hxl.erp.receiveorder.update',
  '/erp/hxl.erp.receiveorder.audit',
  '/erp/hxl.erp.receiveorder.batchdelete',
  '/erp/hxl.erp.receiveorder.copy',
  '/erp/hxl.erp.receiveorder.batchcopy',
  '/erp/hxl.erp.receiveorder.reverse',
  '/erp/hxl.erp.receiveorder.item.page',
  '/erp/hxl.erp.receiveorder.file.upload',
  '/erp/hxl.erp.receiveorder.export',
  '/erp/hxl.erp.receiveorder.detail.export',
  '/erp/hxl.erp.receiveorder.template.download',
  '/erp/hxl.erp.receiveorder.import',
  '/erp/hxl.erp.receiveorder.findbypurchaseorder',
  '/erp/hxl.erp.receiveorder.createbypurchaseorder',
  '/erp/hxl.erp.receiveorder.print.readdetail',
  '/erp/hxl.erp.receiveorder.print.read',
  '/erp/hxl.erp.receiveorder.print',
  '/erp/hxl.erp.receiveorder.reportsummary',
  '/erp/hxl.erp.receiveorder.itemsummary.find',
  '/erp/hxl.erp.receiveorder.reportreconciled',
  '/erp/hxl.erp.receiveorder.batchaudit',
  '/erp/hxl.erp.receiveorder.batchreverse',
  '/erp/hxl.erp.receiveorder.reverseandcopy',
  // --采购退货单
  '/erp/hxl.erp.returnorder.page',
  '/erp/hxl.erp.returnorder.read',
  '/erp/hxl.erp.returnorder.batchread',
  '/erp/hxl.erp.returnorder.save',
  '/erp/hxl.erp.returnorder.update',
  '/erp/hxl.erp.returnorder.audit',
  '/erp/hxl.erp.returnorder.batchaudit',
  '/erp/hxl.erp.returnorder.batchdelete',
  '/erp/hxl.erp.returnorder.copy',
  '/erp/hxl.erp.returnorder.reverse',
  '/erp/hxl.erp.returnorder.batchreverse',
  '/erp/hxl.erp.returnorder.file.upload',
  '/erp/hxl.erp.returnorder.export',
  '/erp/hxl.erp.returnorder.detail.export',
  '/erp/hxl.erp.returnorder.template.download',
  '/erp/hxl.erp.returnorder.import',
  '/erp/hxl.erp.returnorder.findbyreceiveorder',
  '/erp/hxl.erp.returnorder.createbyreceiveorder',
  '/erp/hxl.erp.returnorder.print.readdetail',
  '/erp/hxl.erp.returnorder.print.read',
  '/erp/hxl.erp.returnorder.print',
  // --采购退订单
  '/erp/hxl.erp.returnapplicationorder.save',
  '/erp/hxl.erp.returnapplicationorder.update',
  '/erp/hxl.erp.returnapplicationorder.audit',
  '/erp/hxl.erp.returnapplicationorder.invalid',
  '/erp/hxl.erp.returnapplicationorder.outapplicationorder.invalid',
  '/erp/hxl.erp.returnapplicationorder.read',
  '/erp/hxl.erp.returnapplicationorder.page',
  '/erp/hxl.erp.returnapplicationorder.delete',
  '/erp/hxl.erp.returnapplicationorder.export',
  '/erp/hxl.erp.returnapplicationorder.findbyupdatetime',
  // --采价计划
  '/erp-purchase/hxl.erp.priceorder.save',
  '/erp-purchase/hxl.erp.priceorder.update',
  '/erp-purchase/hxl.erp.priceorder.batchdelete',
  '/erp-purchase/hxl.erp.priceorder.page',
  '/erp-purchase/hxl.erp.priceorder.read',
  '/erp-purchase/hxl.erp.priceorder.approve',
  '/erp-purchase/hxl.erp.priceorder.refuse',
  '/erp-purchase/hxl.erp.priceorder.export',
  // --采购计划
  '/erp-purchase/hxl.erp.purchaseplan.page',
  '/erp-purchase/hxl.erp.purchaseplan.save',
  '/erp-purchase/hxl.erp.purchaseplan.update',
  '/erp-purchase/hxl.erp.purchaseplan.export',
  '/erp-purchase/hxl.erp.purchaseplan.read',
  '/erp-purchase/hxl.erp.purchaseplandetail.page',
  '/erp-purchase/hxl.scm.erp.purchaseplandetail.page',
  '/erp-purchase/hxl.scm.erp.purchaseplandetail.export',
  '/erp-purchase/hxl.scm.erp.purchaseplandetail.update',
  '/erp-purchase/hxl.erp.purchaseplandetail.save',
  '/erp-purchase/hxl.erp.purchaseplandetail.template.download',
  '/erp-purchase/hxl.erp.purchaseplandetail.import',
  '/erp-purchase/hxl.erp.purchaseplandetail.export',
  '/erp-purchase/hxl.erp.purchaseplan.report',
  '/erp-purchase/hxl.erp.purchaseplan.report.export',
  // --备货计划
  '/erp-purchase/hxl.erp.stockplan.page',
  '/erp-purchase/hxl.erp.stockplan.save',
  '/erp-purchase/hxl.scm.erp.stockplan.update',
  '/erp-purchase/hxl.erp.stockplan.export',
  '/erp-purchase/hxl.erp.stockplandetail.page',
  '/erp-purchase/hxl.erp.stockplandetail.export',
  '/erp/hxl.erp.storeitemsupplier.originplace.find',
  '/erp/hxl.erp.storeitemsupplier.purchaseperiod.import',
  '/erp/hxl.erp.storeitemsupplier.export',
  '/erp/hxl.erp.storeitemsupplier.import',
  '/erp/hxl.erp.storeitemsupplier.import.cancel',
  '/erp/hxl.erp.storeitemsupplier.importprogress.check',
  '/erp/hxl.erp.storeitemsupplier.batchupdateattr',
  '/erp/hxl.erp.storeitemsupplier.batchupdate',
  '/erp/hxl.erp.storeitemsupplier.main.batchupdate',
  '/erp/hxl.erp.storeitemsupplier.batchupdateprice',
  '/erp/hxl.erp.storeitemsupplier.update',
  '/erp/hxl.erp.storeitemsupplier.producer.read',
  '/erp/hxl.erp.storeitemsupplier.batchdelete',
  '/erp/hxl.erp.storeitemsupplier.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasemonitor.orderstate.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasemonitor.orderarrivalrate.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasemonitor.expireorder.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasemonitor.orderdetail.page',
  '/erp-purchase/hxl.erp.purchasereport.latestprice.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasesummary.receiveorderdetail.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasesummary.returnorderdetail.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasesummary.storeitem.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasesummary.supplier.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasesummary.storecategory.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasesummary.supplierstore.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasesummary.supplieritem.page',
  '/erp-purchase/hxl.erp.purchasereport.purchasesummary.supplierstoreitem.page',
  '/erp/hxl.erp.purchaseparam.read',
  '/erp/hxl.erp.purchaseparam.save',
  '/erp-purchase/hxl.erp.purchasereport.latestprice.export',
  '/erp/hxl.erp.purchasereport.purchasemonitor.orderstate.export',
  '/erp/hxl.erp.purchasereport.purchasemonitor.orderarrivalrate.export',
  '/erp/hxl.erp.purchasereport.purchasemonitor.expireorder.export',
  '/erp/hxl.erp.purchasereport.purchasemonitor.orderdetail.export',
  '/erp/hxl.erp.purchasereport.purchasesummary.receiveorderdetail.export',
  '/erp/hxl.erp.purchasereport.purchasesummary.returnorderdetail.export',
  '/erp/hxl.erp.purchasereport.purchasesummary.storeitem.export',
  '/erp/hxl.erp.purchasereport.purchasesummary.supplier.export',
  '/erp/hxl.erp.purchasereport.purchasesummary.storecategory.export',
  '/erp/hxl.erp.purchasereport.purchasesummary.supplierstore.export',
  '/erp/hxl.erp.purchasereport.purchasesummary.supplieritem.export',
  '/erp/hxl.erp.purchasereport.purchasesummary.supplierstoreitem.export',
  '/erp/hxl.erp.purchasereport.unsalableanalysis.export',
  '/erp/hxl.erp.purchasereport.storeoutstockwarning.export',
  '/erp/hxl.erp.purchasereport.purchasemonitor.orderstate.export',
  '/erp/hxl.erp.purchasereport.storeoutstockwarning.find',
  '/erp/hxl.erp.purchasereport.volumepriceanalysis.find'
]
const PurchaseOssConfig = LStorage.get('PurchaseOssConfig')

export const    replaceUrl = (URL: string, companyId: any) => {
  let url: string,
    param: string = ''

  if (URL?.includes('?')) {
    url = URL?.split('?')?.[0]
    param = URL?.split('?')?.[1]
  } else {
    url = URL
  }
  const PurchaseOssConfigCompany = PurchaseOssConfig ? JSON.parse(PurchaseOssConfig) || [] : []
  const isBelongConfigCompany = PurchaseOssConfigCompany?.includes(companyId?.toString())

  if (isBelongConfigCompany && purchaseUrl?.includes(url)) {
    return url?.replace('/erp/', '/purchase/') + (param ? `?${param}` : '')
  }
  if (url?.includes('.xlbsoft.com')) {
    const splitUrl = url.split('.xlbsoft.com')
    const baseUrl = splitUrl?.[0] // 获取 .xlbsoft.com 之前的部分
    const path = splitUrl?.[1] // 获取 .xlbsoft.com 之后的部分
    if (isBelongConfigCompany && purchaseUrl?.includes(path)) {
      // 替换第一个 erp 为 purchase
      const updatedPath = path?.replace('erp', 'purchase')
      return `${baseUrl}.xlbsoft.com${updatedPath}${param ? `?${param}` : ''}`
    }
  }
  return URL
}