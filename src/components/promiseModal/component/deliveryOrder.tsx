import { columnWidthEnum } from '@/data/common/constant';
import { hasAuth } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import {
  SearchFormType,
  XlbBasicForm,
  XlbForm,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { readInfoOut } from '../server';

const formList: SearchFormType[] = [
  {
    type: 'input',
    disabled: true,
    name: 'in_store_name',
    label: '调入门店',
    width: 180,
  },
  {
    type: 'input',
    disabled: true,
    name: 'store_name',
    label: '调出门店',
    width: 180,
  },
  {
    type: 'select',
    disabled: true,
    name: 'storehouse_name',
    label: '调出仓库',
    width: 180,
  },
  {
    type: 'input',
    disabled: true,
    name: 'fid',
    label: '单据号',
    width: 180,
  },
  {
    type: 'select',
    disabled: true,
    name: 'state',
    label: '单据状态',
    width: 180,
    options: [
      { label: '制单', value: 'INIT' },
      { label: '审核', value: 'AUDIT' },
      { label: '已完成', value: 'FINISH' },
      { label: '已作废', value: 'CANCEL' },
    ],
  },
  {
    type: 'input',
    disabled: true,
    name: 'item_dept_names',
    label: '商品部门',
    width: 180,
  },
  {
    type: 'datePicker',
    disabled: true,
    name: 'operate_date',
    label: '调出日期',
    width: 180,
  },
  {
    type: 'datePicker',
    disabled: true,
    name: 'payment_date',
    label: '付款日期',
    width: 180,
  },
  {
    type: 'input',
    disabled: true,
    name: 'store_order',
    label: '门店补货单',
    width: 180,
  },
  {
    type: 'input',
    disabled: true,
    name: 'memo',
    label: '留言备注',
    width: 470,
  },
];
const columnsList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '操作',
    code: 'operation',
    align: 'center',
    width: 60,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '销项税率(%)',
    code: 'tax_rate',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '税费',
    code: 'tax_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '成本',
    code: 'cost_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '成本(去税)',
    code: 'no_tax_cost_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单件皮重',
    code: 'tare',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '换算率',
    code: 'ratio',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '时点成本价(去税)',
    code: 'original_no_tax_cost_money',
    width: 160,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '零售单价',
    code: 'sale_price',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '赠品单位',
    code: 'present_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '库存数量',
    code: 'basic_stock_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '可用库存',
    code: 'basic_available_stock_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '原价',
    code: 'origin_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '特价单据号',
    code: 'special_fid',
    width: 210,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
];

const deliveryOrder = (props: any) => {
  const { fid } = props;
  const [formModel] = XlbBasicForm.useForm<any>();
  const [info, setInfo] = useState({
    state: 'INIT',
  });
  const [tableLoading, setTableLoading] = useState<any>(false);
  // 列表数据
  const [fidDataList, setFidDataList] = useState<any>([]);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [itemArrdetail] = useState<XlbTableColumnProps<any>[]>(
    columnsList.map((i: any) => ({
      ...i,
      code: i.code == 'index' ? '_index' : i.code,
      hidden: i.code == 'operation' ? true : i.hidden,
    })) as any[],
  );

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'item_name':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div>
              <span>{value}</span>
              <span
                style={{
                  fontSize: 12,
                  color: '#ff8400',
                  border: '1px solid #ff8400',
                  borderRadius: 3,
                  marginLeft: 5,
                  padding: '1px 2px',
                  visibility: !record.special_fid ? 'hidden' : 'visible',
                }}
              >
                {'特价'}
              </span>
            </div>
          );
        };
        break;
      case 'tax_money':
      case 'cost_money':
      case 'no_tax_cost_money':
      case 'original_no_tax_cost_money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div>
              {hasAuth(['调出单/成本价', '查询']) ? (Number(value) || 0).toFixed(2) : '****'}
            </div>
          );
        };
        break;
      case 'money': //金额(含税)
        item.render = (value: any, record: any, index: number) => {
          return (
            <div>
              {hasAuth(['调出单/配送价', '查询']) ? (Number(value) || 0).toFixed(2) : '****'}
            </div>
          );
        };
        break;
      case 'sale_price':
        item.render = (value: any, record: any, index: number) => (
          <div>
            {hasAuth(['调出单/零售价', '查询']) ? (Number(value) || 0).toFixed(2) : '****'}
          </div>
        );
        break;
      case 'price': //单价(含税)
      case 'basic_price': //基本单价(含税)
        item.render = (value: any, record: any, index: number) => (
          <div>
            {hasAuth(['调出单/配送价', '查询']) ? (Number(value) || 0).toFixed(4) : '****'}
          </div>
        );
        break;
      case 'origin_price':
        item.render = (value: any, record: any, index: number) => {
          return info.state !== 'INIT' && record.basic_original_price ? (
            hasAuth(['调出单/配送价', '查询']) ? (
              <div>
                {Number(
                  safeMath.multiply(
                    Number(record.basic_original_price),
                    Number(record.ratio),
                  ),
                ).toFixed(4)}
              </div>
            ) : (
              '****'
            )
          ) : null;
        };
        break;
      case 'special_fid':
        item.render = (value: any, record: any, index: number) => {
          return info.state !== 'INIT' && record.special_fid ? (
            <div>
              <span className="link cursors">{value}</span>
            </div>
          ) : null;
        };
        break;
      case 'memo':
        item.render = (value: any) => {
          return (
            <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div>{value}</div>
            </Tooltip>
          );
        };
        break;
      case 'tare':
      case 'ratio':
      case 'quantity':
      case 'basic_quantity':
      case 'present_quantity':
      case 'basic_stock_quantity':
      case 'basic_available_stock_quantity':
        item.render = (value: any, record: any, index: number) => {
          return <div>{value ? (Number(value) || 0).toFixed(3) : '0.000'}</div>;
        };
        break;
    }
    return item;
  };

  const openRefOrder = _.debounce(async (fid, summary: boolean = false) => {
    setTableLoading(true);
    setFidDataList([]);
    formModel.setFieldsValue({});
    const res = await readInfoOut({ fid: fid, summary: summary });
    if (res?.code == 0) {
      setInfo({ state: res.data.state });
      // 表单的值
      formModel.setFieldsValue({
        ...res?.data,
        operate_date: res.data.operate_date
          ? dayjs(res.data.operate_date)
          : null,
        payment_date: res.data.payment_date
          ? dayjs(res.data.payment_date)
          : null,
        store_order: res.data.request_orders?.map((v: any) => v.fid).join(','),
        create_time: res.data.create_time?.slice(0, 10),
        audit_time: res.data.audit_time?.slice(0, 10),
        updata_time: res.data.updata_time?.slice(0, 10),
      });
      // 列表的值
      setFidDataList(res?.data?.details);
      // 计算合计
      let rowData = res?.data?.details;
      footerData[0] = {
        index: '合计',
        money: rowData
          .reduce(
            (sum: any, v: any) => sum + Number(v?.newRow ? 0 : v.money),
            0,
          )
          .toFixed(2),
        quantity: rowData
          .reduce(
            (sum: any, v: any) => sum + Number(v?.newRow ? 0 : v.quantity),
            0,
          )
          .toFixed(3),
        tax_money: rowData
          .reduce(
            (sum: any, v: any) => sum + Number(v?.newRow ? 0 : v.tax_money),
            0,
          )
          .toFixed(2),
        basic_quantity: rowData
          .reduce(
            (sum: any, v: any) =>
              sum + Number(v?.newRow ? 0 : v.basic_quantity),
            0,
          )
          .toFixed(3),
        present_quantity: rowData
          .reduce(
            (sum: any, v: any) =>
              sum + Number(v?.newRow ? 0 : v.present_quantity),
            0,
          )
          .toFixed(3),
      };
      setFooterData([...footerData]);
    }
    setTableLoading(false);
  }, 50);

  useEffect(() => {
    openRefOrder(fid);
    itemArrdetail.map((v) => tableRender(v));
  }, []);

  return (
    <>
      <XlbForm
        style={{ marginTop: 15 }}
        formList={formList}
        form={formModel}
        isHideDate={true}
      />
      <XlbTable
        isLoading={tableLoading}
        style={{
          height: 'calc(100vh - 590px)',
          maxHeight: 400,
          overflowY: 'scroll',
        }}
        hideOnSinglePage={false}
        showSearch={true}
        columns={itemArrdetail}
        total={fidDataList?.length}
        dataSource={fidDataList}
        key={fidDataList?.length}
      ></XlbTable>
    </>
  );
};

export default deliveryOrder;