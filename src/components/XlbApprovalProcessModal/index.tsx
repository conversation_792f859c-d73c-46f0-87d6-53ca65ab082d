import NiceModal, { useModal } from "@ebay/nice-modal-react";
import { XlbApprovalProcess, XlbModal } from "@xlb/components";


type GoodsModalProps= {
  params?: any, // 接口参数
  requestUrl?: string,  // 业务接口 url
}

/**样式古老后续建议优化 */
const openModal = (props: any) => {
  const  {
    params,
    requestUrl
  } = props

  const modal = useModal();

  const onOK = async () => {
    modal.resolve(true);
    modal.hide();
  };

  return (
    <XlbModal
      title={"审批流"}
      open={modal.visible}
      width={720}
      onCancel={onOK}
      onOk={onOK}
      zIndex={2002}
    >
      <div style={{padding: '10px 0px', minHeight: 200}}>
        <XlbApprovalProcess params={params} requestUrl={requestUrl} />
      </div>
    </XlbModal>
  );
};

const XlbApprovalProcessModal = (props: GoodsModalProps): Promise<string> => {
  return NiceModal.show(NiceModal.create(openModal), props);
};

export default XlbApprovalProcessModal;