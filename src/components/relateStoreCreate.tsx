import NiceModal from '@ebay/nice-modal-react';
import type { XlbTableColumnProps } from '@xlb/components';
import { XlbModal, XlbTable } from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { useEffect, useState } from 'react';

const Index = (props: any) => {
  const { relateStoreRecord, storeIds } = props;
  const [dataSource, setDataSource] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const modal = NiceModal.useModal();
  const getTableInfo = async () => {
    setLoading(true);
    const res = await XlbFetch.post(
      process.env.BASE_URL + '/erp/hxl.erp.businessarea.store.find',
      { id: relateStoreRecord?.id, store_ids: storeIds || [] },
    );
    if (res?.code === 0) {
      setDataSource(res?.data);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (modal.visible) {
      getTableInfo();
    }
  }, [modal.visible]);

  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 130,
      align: 'center',
    },
    {
      name: '门店代码',
      code: 'store_code',
      width: 100,
    },
    {
      name: '门店名称',
      code: 'store_name',
      width: 280,
      features: { sortable: true },
    },
  ];

  return (
    <XlbModal
      wrapClassName={'xlbDialog'}
      title={'关联门店'}
      keyboard={false}
      open={modal.visible}
      maskClosable={false}
      onCancel={() => modal.hide()}
      onOk={() => modal.hide()}
      width={800}
      zIndex={2012}
      centered
    >
      <XlbTable
        columns={tableList}
        dataSource={dataSource}
        selectMode="single"
        style={{ height: 400 }}
        primaryKey="id"
        total={dataSource?.length}
        loading={loading}
        // pageNum={1}
        // pageSize={9999}
      />
    </XlbModal>
  );
};

export default NiceModal.create(Index);