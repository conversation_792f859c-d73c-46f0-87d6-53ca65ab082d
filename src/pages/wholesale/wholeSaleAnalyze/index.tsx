import { columnWidthEnum } from '@/data/common/constant';
import { useBaseParams } from '@/hooks/useBaseParams';
import useDownload from '@/hooks/useDownload';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { wujieBus } from '@/wujie/utils';
import {
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbTipsModal,
} from '@xlb/components';
import XlbPageContainer, {
  XlbPageContainerRef,
} from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { Form } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import {
  basicTable,
  categoryName,
  delivery_org_name,
  financeName,
  formListData,
  inStore,
  itemTable,
  outStore,
  wholesaleDate,
} from './data';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;
const wholeSaleAnalyze = () => {
  const userInfo = LStorage.get('userInfo');
  const { downByProgress } = useDownload();
  const pageRef = useRef<XlbPageContainerRef>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // searchFormList
  const [form] = Form.useForm();
  const [formList, setFormList] = useState(formListData);
  const { enable_organization } = useBaseParams((state) => state);
  // table
  const [itemArr, setItemArr] = useState(basicTable);

  const onValuesChange = (e: Object) => {
    if (e?.org_ids?.length) {
      form.setFieldsValue({
        store_name: '',
        store_id: [],
      });
    }
  };

  // 获取数据
  const prevPost = () => {
    const {
      client_ids = [],
      store_ids = [],
      time_type,
      summary_types,
    } = form.getFieldsValue(true);
    const [start_time, end_time] = form.getFieldValue('compactDatePicker');
    const time = [
      dayjs(start_time).format('YYYY-MM-DD') + ' 00:00:00',
      dayjs(end_time).format('YYYY-MM-DD') + ' 23:59:59',
    ];
    if (!client_ids.length && !store_ids.length) {
      XlbTipsModal({
        isCancel: true,
        tips: `发货门店和批发客户不能同时为空`,
      });
      return false;
    }
    onChangeKey(summary_types);
    const data: any = {
      ...form.getFieldsValue(true),
      create_date: time_type == 'create_date' ? time : undefined,
      audit_date: time_type == 'audit_date' ? time : undefined,
      operate_date: time_type == 'operate_date' ? time : undefined,
      receive_date: time_type == 'receive_date' ? time : undefined,
      summary_types: form.getFieldValue('summary_types') || ['STORE'],
      storehouse_ids: form.getFieldValue('storehouse_ids')
        ? [form.getFieldValue('storehouse_ids')]
        : null,
    };
    delete data.compactDatePicker;
    delete data.Data_Compact_RangeType_compactDatePicker;
    return data;
  };
  const getData = async () => {
    // setFormData();
    setIsLoading(true);
    pageRef?.current?.fetchData();
    setIsLoading(false);
  };

  // 合计
  const getFooterData = (data: any) => {
    const _footerData = {
      _index: '合计',
      wholesale_gross_profit_rate: hasAuth(['批发分析/毛利', '查询'])
        ? data?.wholesale_gross_profit_rate || null
        : '****', // 配送毛利率合计
      no_tax_wholesale_gross_profit_rate: hasAuth(['批发分析/毛利', '查询'])
        ? data?.no_tax_wholesale_gross_profit_rate || null
        : '****', // 配送毛利率合计
      wholesale_gross_profit: hasAuth(['批发分析/毛利', '查询'])
        ? data?.wholesale_gross_profit || null
        : '****', // 配送毛利合计
      return_cost_money: hasAuth(['批发分析/成本价', '查询'])
        ? Number(data?.return_cost_money).toFixed(3) || '0.000'
        : '****', // 批发退货成本合计
      return_money: hasAuth(['批发分析/批发价', '查询'])
        ? Number(data?.return_money).toFixed(2) || '0.00'
        : '****', //	批发退货金额合计
      return_quantity: Number(data?.return_quantity).toFixed(3) || '000', // 批发退货数量合计
      basic_return_quantity:
        Number(data?.basic_return_quantity || 0).toFixed(3) || '000', // 批发退货基本数量合计
      cost_money: hasAuth(['批发分析/成本价', '查询'])
        ? Number(data?.cost_money).toFixed(2) || '0.00'
        : '****', // 批发退货数量合计
      no_tax_cost_money: hasAuth(['批发分析/成本价', '查询'])
        ? Number(data?.no_tax_cost_money).toFixed(2) || '0.00'
        : '****',
      cost_tax_money: hasAuth(['批发分析/成本价', '查询'])
        ? Number(data?.cost_tax_money).toFixed(2) || '0.00'
        : '****',
      original_cost_money: hasAuth(['批发分析/成本价', '查询'])
        ? Number(data?.original_cost_money_total ?? 0).toFixed(2)
        : '****',
      original_no_tax_cost_money: hasAuth(['批发分析/成本价', '查询'])
        ? Number(data?.original_no_tax_cost_money_total ?? 0).toFixed(2)
        : '****',
      no_tax_money: hasAuth(['批发分析/批发价', '查询'])
        ? Number(data?.no_tax_money).toFixed(2) || '0.00'
        : '****',
      tax_money: hasAuth(['批发分析/批发价', '查询'])
        ? Number(data?.tax_money).toFixed(2) || '0.00'
        : '****',
      return_no_tax_cost_money: hasAuth(['批发分析/成本价', '查询'])
        ? data?.no_tax_return_cost_money
          ? Number(data?.no_tax_return_cost_money).toFixed(2)
          : '0.00'
        : '****',
      return_cost_tax_money: hasAuth(['批发分析/成本价', '查询'])
        ? Number(data?.return_cost_tax_money).toFixed(2) || '0.00'
        : '****',
      return_original_cost_money: hasAuth(['批发分析/成本价', '查询'])
        ? Number(data?.return_original_cost_money_total ?? 0).toFixed(2)
        : '****',
      return_original_no_tax_cost_money: hasAuth(['批发分析/成本价', '查询'])
        ? Number(data?.return_original_no_tax_cost_money_total ?? 0).toFixed(2)
        : '****',
      cost_tax_money_total:
        Number(data?.cost_tax_money_total).toFixed(2) || '0.00',
      wholesale_gross_profit_tax_money: hasAuth(['批发分析/毛利', '查询'])
        ? Number(data?.wholesale_gross_profit_tax_money).toFixed(2) || '0.00'
        : '****',
      return_no_tax_money: hasAuth(['批发分析/批发价', '查询'])
        ? Number(data?.no_tax_return_money).toFixed(2) || '0.00'
        : '****',
      no_tax_money_total: hasAuth(['批发分析/批发价', '查询'])
        ? Number(data?.no_tax_money_total).toFixed(2) || '0.00'
        : '****', //	去税金额合计
      tax_money_total: hasAuth(['批发分析/批发价', '查询'])
        ? Number(data?.tax_money_total).toFixed(2) || '0.00'
        : '****', //	税额合计
      cost_money_total: hasAuth(['批发分析/成本价', '查询'])
        ? Number(data?.cost_money_total).toFixed(3) || '0.000'
        : '****', //批发销售成本合计
      no_tax_cost_money_total: hasAuth(['批发分析/成本价', '查询'])
        ? Number(data?.no_tax_cost_money_total).toFixed(3) || '0.000'
        : '****', //批发销售成本合计
      money: hasAuth(['批发分析/批发价', '查询'])
        ? Number(data?.money).toFixed(3) || '0.000'
        : '****', // 批发销售金额合计
      money_total: hasAuth(['批发分析/批发价', '查询'])
        ? Number(data?.money_total).toFixed(2) || '0.00'
        : '****', //	批发销售金额合计
      quantity: Number(data?.quantity).toFixed(3) || '0.000', //	批发销售数量

      no_tax_delivery_gross_profit_rate: hasAuth(['批发分析/毛利', '查询'])
        ? Number(data?.all_no_tax_delivery_gross_profit_rate_total).toFixed(
            3,
          ) || '000'
        : '****', //	调出数量合计
      no_tax_delivery_gross_profit:
        Number(data?.all_no_tax_delivery_gross_profit_total).toFixed(3) ||
        '000', //	调出数量合计
      no_tax_in_cost:
        Number(data?.all_no_tax_in_cost_total).toFixed(3) || '000', //	调出数量合计
      no_tax_in_money:
        Number(data?.all_no_tax_in_money_total).toFixed(3) || '000', //	调出数量合计
      no_tax_out_cost:
        Number(data?.all_no_tax_out_cost_total).toFixed(3) || '000', //	调出数量合计
      no_tax_out_cost_total:
        Number(data?.all_no_tax_out_cost_total_total).toFixed(3) || '000', //	调出数量合计
      no_tax_out_money:
        Number(data?.all_no_tax_out_money_total).toFixed(3) || '000', //	调出数量合计
      no_tax_out_money_total:
        Number(data?.all_no_tax_out_money_total_total).toFixed(3) || '000', //	调出数量合计
    };
    return [_footerData];
  };

  // 导出
  const exportItem = async (e: any) => {
    setIsLoading(true);
    const data = prevPost();
    const res = await ErpRequest.post(
      '/erp/hxl.erp.wholesalereport.wholesaleanalyze.export',
      data,
    );
    setIsLoading(false);
    if (res?.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };

  const tableRender = (item: any = itemArr) => {
    switch (item.code) {
      case 'quantity':
      case 'basic_return_quantity':
      case 'return_quantity':
        return (item.render = (value: any) => (
          <div className="info overwidth">
            {value ? Number(value).toFixed(3) : '0.000'}
          </div>
        ));
      // 销售金额、销售金额（去税）、销售税额、退货金额、退货金额（去税）、退货税额、金额合计、金额合计（去税）、金额合计（税额）---批发价控制
      case 'money':
      case 'no_tax_money':
      case 'tax_money':
      case 'return_money':
      case 'return_no_tax_money':
      case 'return_tax_money':
      case 'money_total':
      case 'no_tax_money_total':
      case 'tax_money_total':
        return (item.render = (value: any) => (
          <div className="info overwidth">
            {hasAuth(['批发分析/批发价', '查询'])
              ? value
                ? Number(value).toFixed(2)
                : '0.00'
              : '***'}
          </div>
        ));
      case 'no_tax_delivery_gross_profit':
        return (item.render = (value: any) => (
          <div className="info overwidth">{Number(value).toFixed(2)}</div>
        ));
      // 销售成本、销售成本（去税）、销售成本（税额）、退货成本、退货成本（去税）、退货成本（税额）、成本合计、成本合计（去税）、成本合计（税额）---成本价控制
      case 'cost_money': // cost_money
      case 'no_tax_cost_money':
      case 'cost_tax_money':
      case 'return_cost_money': // return_cost_money
      case 'return_no_tax_cost_money':
      case 'return_cost_tax_money':
      case 'cost_money_total': // cost_money_total
      case 'no_tax_cost_money_total':
      case 'cost_tax_money_total':
      case 'original_cost_money':
      case 'original_no_tax_cost_money':
      case 'return_original_cost_money':
      case 'return_original_no_tax_cost_money':
        return (item.render = (value: any) => (
          <div className="info overwidth">
            {hasAuth(['批发分析/成本价', '查询'])
              ? Number(value ?? 0).toFixed(2)
              : '***'}
          </div>
        ));

      //毛利、毛利（去税）、毛利（税额）、毛利率、毛利率(去税)---毛利控制
      case 'no_tax_wholesale_gross_profit':
      case 'wholesale_gross_profit_tax_money':
      case 'wholesale_gross_profit_rate':
        return (item.render = (value: any) => (
          <div className="info overwidth">
            {hasAuth(['批发分析/毛利', '查询']) ? value : '***'}
          </div>
        ));

      case 'no_tax_wholesale_gross_profit_rate':
        return (item.render = (value: any) => (
          <div className="info overwidth">
            {hasAuth(['批发分析/毛利', '查询'])
              ? value
                ? value
                : '0.00%'
              : '***'}
          </div>
        ));
      case 'wholesale_gross_profit':
        return (item.render = (value: any) => (
          <div className="info overwidth">
            {value === '****' ? value : Number(value).toFixed(2)}
          </div>
        ));
      default:
        return (item.render = (value: any) => (
          <div className="info overwidth">{value}</div>
        ));
    }
    return item;
  };

  const onChangeKey = (ids: any = ['STORE']) => {
    let TableName = JSON.parse(JSON.stringify(basicTable));
    TableName.unshift({
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
    });
    // 根据汇总条件计算不同的表头
    const showName = {
      STORE: outStore, //发货门店
      CLIENT: inStore, //批发客户
      ITEM: itemTable, // 商品档案
      CATEGORY: categoryName, //商品类别
      WHOLESALE_DATE: wholesaleDate, //批发日期
      FINANCECODE: financeName, // 业财核算分类
      DELIVERY_ORG: delivery_org_name, // 货主
    };
    type ItemId = keyof typeof showName;
    const spliceTable: any = [];
    ids?.forEach((v: ItemId) => {
      spliceTable.push(...showName[v]);
    });
    TableName.splice(1, 0, ...spliceTable);
    if (ids.some((v: string) => v == 'ITEM')) {
      const index = TableName.findIndex((v: any) => v.code == 'item_spec');
      TableName.splice(index + 1, 0, {
        name: '单位',
        code: 'unit',
        width: 78,
        align: 'center',
        features: { sortable: true },
      });
    }
    // 根据查询单位计算不同的表头
    if (form.getFieldValue('unit_type') === 'ORDER') {
      const li = [
        'unit',
        'price',
        'no_tax_price',
        'cost_price',
        'return_price',
        'return_no_tax_price',
        'return_cost_price',
      ];
      TableName = TableName.filter((v: any) => !li.includes(v.code));
    }
    if (
      enable_organization &&
      (!ids ||
        !ids?.length ||
        ids.includes('WHOLESALE_DATE') ||
        ids.includes('STORE'))
    ) {
      TableName.splice(1, 0, {
        name: '组织',
        code: 'org_name',
        width: 120,
        align: 'center',
      });
    }
    setItemArr(TableName);
  };

  useEffect(() => {
    getOrgList();
  }, [enable_organization]);
  const getOrgList = async () => {
    formList.find((i) => i.name === 'org_ids')!.hidden = !enable_organization;
    if (enable_organization) {
      const res = await ErpRequest.post('/erp/hxl.erp.org.find', { level: 2 });
      if (res?.code == 0) {
        const org_list = res.data.map((i: any) => ({
          value: i.id,
          label: i.name,
        }));
        formList.find((i) => i.name === 'org_ids')!.options = org_list;
      }
    }
    setFormList([...formList]);
  };

  itemArr.map((v) => tableRender(v));
  useEffect(() => {
    onChangeKey();
    const { store_id, client, store_name } = userInfo;
    const storeId = store_id || client.store_id || '';
    const storeName = store_name || client.store_name || '';
    form.setFieldsValue({
      store_name: storeName,
      store_ids: [storeId],
      client_name: client?.name,
      client_ids: client?.name && [client.id],
    });
    if (client?.id) {
      formList.find((item) => item.label === '批发客户')!.disabled = true;
    }
    setFormList([...formList]);
  }, []);

  return (
    <XlbPageContainer
      ref={pageRef}
      url={'/erp/hxl.erp.wholesalereport.wholesaleanalyze.page'}
      tableColumn={itemArr}
      immediatePost={false}
      prevPost={() => prevPost()}
      changeColumnAndResetDataSource={false}
      footerDataSource={(data: any) => {
        return getFooterData(data);
      }}
    >
      <SearchForm>
        <XlbForm
          form={form}
          formList={formList}
          initialValues={{
            time_type: 'audit_date',
            unit_type: 'ORDER',
            compactDatePicker: [dayjs(), dayjs()],
          }}
          isHideDate
          onValuesChange={onValuesChange}
        />
      </SearchForm>
      <ToolBtn showColumnsSetting>
        {(context) => {
          const { dataSource } = context;
          return (
            <XlbButton.Group>
              {hasAuth(['批发分析', '查询']) && (
                <XlbButton
                  label="查询"
                  type={'primary'}
                  disabled={isLoading}
                  onClick={() => getData()}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['批发分析', '导出']) && (
                <XlbButton
                  label="导出"
                  type={'primary'}
                  disabled={!dataSource?.length || isLoading}
                  onClick={(e: any) => exportItem(e)}
                  icon={<XlbIcon name="daochu" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table
        primaryKey="_index"
        selectMode={'single'}
        key="store_id"
        isLoading={isLoading}
      />
    </XlbPageContainer>
  );
};
export default wholeSaleAnalyze;
