import {
  SearchFormType,
  XlbBasicForm,
  XlbCheckbox,
  XlbInputNumber,
  XlbTableColumnProps,
  type SelectType,
} from '@xlb/components';
import { message } from 'antd';

export const newsCycleOptions = [
  {
    label: '停购',
    value: 'stop_purchase',
  },
  {
    label: '停售',
    value: 'stop_sale',
  },
  {
    label: '停止要货',
    value: 'stop_request',
  },
  {
    label: '淘汰',
    value: 'eliminate',
  },
  {
    label: '必卖品',
    value: 'must_sell',
  },
  {
    label: '新品',
    value: 'open_news_cycle',
  },
];
export const queryModeSelect = [
  { label: '汇总', value: 0 },
  { label: '明细', value: 1 },
  { label: '仓汇总', value: 5 },
  { label: '商品汇总', value: 4 },
  { label: '门店汇总', value: 2 },
  { label: '门店销售模式汇总', value: 3 },
];
const itemTypesSelect = [
  {
    label: '主规格商品',
    value: 'MAINSPEC',
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC',
  },
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
];
export const searchFormList: SearchFormType[] = [
  {
    type: 'select',
    label: '查询模式',
    name: 'query_mode',
    allowClear: false,
    check: true,
    initialValue: 0,
    Change: () => {},
    options: [
      { label: '汇总', value: 0 },
      { label: '明细', value: 1 },
      { label: '仓汇总', value: 5 },
      { label: '商品汇总', value: 4 },
      { label: '门店汇总', value: 2 },
      { label: '门店销售模式汇总', value: 3 },
    ],
    width: 200,
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        status: true,
      },
      nullable: false,
    },
    allowClear: false,
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
    removeIcon: null,
    width: 200,
  },
  {
    label: '仓库',
    name: 'storehouse_ids',
    type: 'select',
    multiple: true,
    dependencies: ['store_ids'],
    dropdownStyle: { width: 200 },
    disabled: (form) => {
      const store_ids = form.getFieldValue('store_ids');
      return !store_ids || store_ids?.length > 1;
    },
    handleDefaultValue: (data: any, formData: any) => {
      if (data?.length === 0) {
        return null;
      }
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return defaultStoreHouse?.value;
    },
    // @ts-ignore
    selectRequestParams: (params: any, form) => {
      form?.setFieldsValue({
        storehouse_ids: null,
      });
      if (params?.store_ids?.length == 1) {
        return {
          url: '/erp/hxl.erp.storehouse.store.find',
          postParams: {
            store_id: params?.store_ids?.[0],
          },
          responseTrans(data: any) {
            const options = data.map((item: any) => ({
              label: item.name,
              value: item.id,
              default_flag: item.default_flag,
            }));
            return options;
          },
        };
      }
    },
  },
  {
    label: '货主',
    name: 'cargo_owner_ids',
    type: 'inputDialog',
    hidden: true,
    clear: true,
    check: true,
    dialogParams: {
      type: 'cargoOwner',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    allowClear: false,
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'source_name',
    },
    width: 200,
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    width: 200,
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    } as any,
    width: 200,
  },
  {
    type: 'select',
    name: 'category_level',
    label: '类别等级',
    options: [
      { label: '当前分类', value: 'empty' },
      { label: '一级类别', value: 1 },
      { label: '二级类别', value: 2 },
      { label: '三级类别', value: 3 },
      { label: '四级类别', value: 4 },
      { label: '五级类别', value: 5 },
    ],
    width: 200,
  },
  {
    label: '商品部门',
    name: 'item_dept_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'productDept',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '采购类型',
    name: 'purchase_types',
    type: 'select',
    multiple: true,
    clear: true,
    check: true,
    options: [
      {
        label: '集采品',
        value: 'COLLECTIVE_PURCHASE',
      },
      {
        label: '集售品',
        value: 'COLLECTIVE_SALE',
      },
      {
        label: '地采品',
        value: 'GROUND_PURCHASE',
      },
      {
        label: '店采品',
        value: 'SHOP_PURCHASE',
      },
    ],
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    type: 'select',
    label: '供货主体',
    name: 'main_body_id',
    clear: true,
    selectRequestParams: {
      url: '/erp/hxl.erp.suppliermainbody.find',
      responseTrans(data) {
        const options: SelectType[] = data.map((item: any) => {
          const obj: SelectType = {
            label: item.name,
            value: item.id,
          };
          return obj;
        });
        return options;
      },
    },
    options: [],
  },
  {
    label: '关键字',
    name: 'keyword',
    type: 'input',
    clear: true,
    check: true,
  },
  {
    label: '商品类型',
    name: 'item_types',
    type: 'select',
    clear: true,
    check: true,
    multiple: true,
    hidden: false,
    options: itemTypesSelect,
  },
  {
    type: 'select',
    name: 'unit_type',
    label: '查询单位',
    allowClear: false,
    options: [
      { label: '基本单位', value: 'BASIC' },
      { label: '采购单位', value: 'PURCHASE' },
      { label: '配送单位', value: 'DELIVERY' },
      { label: '库存单位', value: 'STOCK' },
      { label: '批发单位', value: 'WHOLESALE' },
    ],
    width: 200,
  },
  {
    label: '库存数',
    name: 'filter_stock',
    type: 'select',
    multiple: true,
    check: true,
    options: [
      {
        label: '负库存',
        value: 0,
      },
      {
        label: '零库存',
        value: 1,
      },
      {
        label: '正库存',
        value: 2,
      },
    ],
  },
  {
    label: '商品状态',
    name: 'item_status_list',
    type: 'select',
    hidden: true,
    clear: true,
    check: true,
    options: [
      { label: '新品', value: 'NEW_ITEM' },
      { label: '正常品', value: 'NORMAL' },
      { label: '一次性商品', value: 'ONE_TIME_ITEM' },
      { label: '停购', value: 'STOP_BUY' },
      { label: '停要', value: 'STOP_WANT' },
      { label: '停售', value: 'STOP_SALE' },
      { label: '淘汰', value: 'ELIMINATE' },
    ],
  },
  {
    label: '调改店商品',
    name: 'adjust_store_product',
    type: 'select',
    clear: true,
    check: true,
    options: [
      { label: '是', value: 'true' },
      { label: '否', value: 'false' },
      { label: '　', value: null },
    ],
  },
  {
    label: '其他条件',
    className: 'inputPanelValue',
    type: 'inputPanel',
    name: 'panelValue',
    placeholder: '停购/停售',
    allowClear: true,
    items: [
      {
        label: '仅显示',
        key: 'true',
      },
      {
        label: '不显示',
        key: 'false',
      },
    ],
    options: newsCycleOptions,
    width: 120,
  },
  {
    label: '',
    name: 'inputInterval',
    type: 'custom',
    render(itemData, form, itemSetting) {
      return (
        <>
          <XlbBasicForm.Item shouldUpdate noStyle>
            {(form) => {
              const query_mode = form.getFieldValue('query_mode');
              const store_ids = form.getFieldValue('store_ids') || '';
              const { storehouse_ids } = form.getFieldsValue(true);
              const showItems = Array.isArray(storehouse_ids)
                ? storehouse_ids.length === 1
                : !!storehouse_ids;
              return query_mode === 0 &&
                store_ids?.length === 1 &&
                showItems ? (
                <>
                  <XlbBasicForm.Item
                    style={{
                      display: 'inline-block',
                      marginBottom: 0,
                      marginLeft: 46,
                    }}
                    name="loe_lock_quantity"
                  >
                    <XlbInputNumber
                      width={60}
                      min={0}
                      max={100000000}
                      step={1}
                      size="small"
                      controls={false}
                    />
                  </XlbBasicForm.Item>
                  <span
                    style={{
                      display: 'inline-block',
                      margin: '0 5px',
                      lineHeight: '32px',
                    }}
                  >
                    {'<=占用数量<='}
                  </span>
                  <XlbBasicForm.Item
                    style={{ display: 'inline-block', marginBottom: 0 }}
                    name="goe_lock_quantity"
                  >
                    <XlbInputNumber
                      width={60}
                      min={0}
                      max={100000000}
                      step={1}
                      size="small"
                      controls={false}
                    />
                  </XlbBasicForm.Item>
                </>
              ) : null;
            }}
          </XlbBasicForm.Item>
          <XlbBasicForm.Item shouldUpdate noStyle>
            {(form) => {
              const query_mode = form.getFieldValue('query_mode');

              return query_mode == 1 ? (
                <>
                  <XlbBasicForm.Item
                    style={{ display: 'inline-block', marginBottom: 0 }}
                    name="left_near_expiry_day"
                    validateTrigger="onBlur"
                    rules={[
                      {
                        validator: (_, value) => {
                          const goeLockQuantity = form.getFieldValue(
                            'right_near_expiry_day',
                          );
                          if (!goeLockQuantity) {
                            return Promise.resolve();
                          } else if (value > goeLockQuantity) {
                            message.error('左右值大小异常，请重新输入');
                            form.setFieldsValue({
                              left_near_expiry_day: '',
                            });
                            return;
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <XlbInputNumber
                      width={100}
                      min={-99999999}
                      max={99999999}
                      step={1}
                      size="small"
                      controls={false}
                    />
                  </XlbBasicForm.Item>
                  <span
                    style={{
                      display: 'inline-block',
                      margin: '0 5px',
                      lineHeight: '32px',
                    }}
                  >
                    {'<=近效期<='}
                  </span>
                  <XlbBasicForm.Item
                    style={{ display: 'inline-block', marginBottom: 0 }}
                    name="right_near_expiry_day"
                    validateTrigger="onBlur"
                    rules={[
                      {
                        validator: (_, value) => {
                          const leftLockQuantity = form.getFieldValue(
                            'left_near_expiry_day',
                          );
                          if (!leftLockQuantity) {
                            return Promise.resolve();
                          } else if (value < leftLockQuantity) {
                            message.error('左右值大小异常，请重新输入');
                            form.setFieldsValue({
                              right_near_expiry_day: '',
                            });
                            return;
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <XlbInputNumber
                      width={100}
                      min={-99999999}
                      max={99999999}
                      step={1}
                      size="small"
                      controls={false}
                    />
                  </XlbBasicForm.Item>
                </>
              ) : null;
            }}
          </XlbBasicForm.Item>
        </>
      );
    },
  },
  {
    label: '过滤',
    name: 'checkValue',
    type: 'custom',
    render(itemData, form, itemSetting) {
      return (
        <XlbBasicForm.Item name={'checkValue'} label="过滤">
          <XlbCheckbox.Group>
            <XlbCheckbox value="query_main_supplier">
              供应商、供货主体按主体供应商查询
            </XlbCheckbox>
            <XlbCheckbox value="show_batch_unit">
              批次商品仅按入库单位展示查询
            </XlbCheckbox>
            <XlbCheckbox value="filter_zero_stock">不显示0库存</XlbCheckbox>
            <XlbCheckbox value="COMPONENT">不显示成分商品</XlbCheckbox>
            <XlbCheckbox value="MULTIPLESPEC">不显示多规格商品</XlbCheckbox>
          </XlbCheckbox.Group>
        </XlbBasicForm.Item>
      );
    },
  },
];

// Table
export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 80,
    align: 'center',
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '门店',
    code: 'store_id',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '仓库',
    code: 'storehouse_name',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '采购类型',
    code: 'purchase_type',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '停购',
    code: 'item_stop_purchase',
    hidden: true,
    width: 100,
    features: { sortable: true },
  },
  {
    name: '停售',
    code: 'item_stop_sale',
    hidden: true,
    width: 100,
    features: { sortable: true },
  },
  {
    name: '停止要货',
    code: 'item_stop_request',
    hidden: true,
    width: 100,
    features: { sortable: true },
  },
  {
    name: '停止批发',
    code: 'item_stop_wholesale',
    hidden: true,
    width: 100,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '采购单位',
    code: 'unit',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '可用数量',
    code: 'valid_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '占用数量',
    code: 'lock_quantity',
    width: 110,
    align: 'right',
    // features: { sortable: true },
  },
  {
    name: '单价',
    code: 'cost_price',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '可用金额',
    code: 'valid_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '单价(去税)',
    code: 'no_tax_cost_price',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额(去税)',
    code: 'no_tax_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '可用金额(去税)',
    code: 'valid_no_tax_money',
    width: 150,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '零售价',
    code: 'sale_price',
    width: 100,
    align: 'right',
    features: { sortable: false },
  },
  {
    name: '零售金额',
    code: 'sale_money',
    width: 120,
    align: 'right',
    features: { sortable: false },
  },
  {
    name: '可用零售金额',
    code: 'valid_sale_money',
    width: 120,
    align: 'right',
    features: { sortable: false },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '可用基本数量',
    code: 'basic_valid_quantity',
    width: 130,
    align: 'right',
    features: { sortable: false },
  },
  {
    name: '基本单价',
    code: 'basic_cost_price',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '基本单价(去税)',
    code: 'basic_no_tax_cost_price',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '入库供应商',
    code: 'latest_supplier_name',
    width: 280,
  },
  {
    name: '主供应商',
    code: 'main_supplier_name',
    width: 280,
  },
  {
    name: '是否调改店品',
    code: 'adjust_store_product',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '最新日期',
    code: 'producing_date',
    width: 132,
    features: {
      sortable: true,
      format: 'TIME',
      tips: '根据商品日期录入规则展示最近一次收货单入库的生产日期或到期日期',
    },
  } as any,
  {
    name: '最近入库日期',
    code: 'latest_in_date',
    width: 160,
    features: {
      sortable: true,
      format: 'TIME',
    },
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '关联商品',
    code: 'item_assemble_item',
    width: 160,
  },
];
export const tableList1: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 100,
    align: 'center',
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '门店',
    code: 'store_id',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '仓库',
    code: 'storehouse_name',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '货主',
    code: 'cargo_owner_name',
    hiddenInXlbColumns: true,
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 140,
    features: { sortable: true },
  },

  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '采购类型',
    code: 'purchase_type',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '停购',
    code: 'item_stop_purchase',
    hidden: true,
    width: 70,
    features: { sortable: true },
  },
  {
    name: '停售',
    code: 'item_stop_sale',
    hidden: true,
    width: 70,
    features: { sortable: true },
  },
  {
    name: '停止要货',
    code: 'item_stop_request',
    hidden: true,
    width: 100,
    features: { sortable: true },
  },
  {
    name: '停止批发',
    code: 'item_stop_wholesale',
    hidden: true,
    width: 100,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: 100,
    features: {
      sortable: true,
      format: 'TIME',
    },
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: 120,
    features: {
      sortable: true,
      format: 'TIME',
    },
  },
  {
    name: '库存单位',
    code: 'unit',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '可用数量',
    code: 'valid_quantity',
    width: 110,
    align: 'right',
    features: { sortable: false },
  },
  {
    name: '占用数量',
    code: 'lock_quantity',
    width: 110,
    align: 'right',
    // features: { sortable: true },
  },
  {
    name: '单价',
    code: 'cost_price',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '可用金额',
    code: 'valid_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '单价(去税)',
    code: 'no_tax_cost_price',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额(去税)',
    code: 'no_tax_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '可用金额(去税)',
    code: 'valid_no_tax_money',
    width: 150,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '零售价',
    code: 'sale_price',
    width: 100,
    align: 'right',
    features: { sortable: false },
  },
  {
    name: '零售金额',
    code: 'sale_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '可用零售金额',
    code: 'valid_sale_money',
    width: 120,
    align: 'right',
    features: { sortable: false },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '皮重',
    code: 'tare',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '可用基本数量',
    code: 'basic_valid_quantity',
    width: 130,
    align: 'right',
    features: { sortable: false },
  },
  {
    name: '基本单价',
    code: 'basic_cost_price',
    width: 110,
    align: 'right',
    // hidden: true,
    features: { sortable: true },
  },
  {
    name: '基本单价(去税)',
    code: 'basic_no_tax_cost_price',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '入库供应商',
    code: 'latest_supplier_name',
    width: 280,
    // features: { sortable: true }
  },
  {
    name: '主供应商',
    code: 'main_supplier_name',
    width: 280,
    // features: { sortable: true }
  },
  {
    name: '是否调改店品',
    code: 'adjust_store_product',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '关联商品',
    code: 'item_assemble_item',
    width: 140,
  },
];
export const tableList2: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 120,
    align: 'center',
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '门店',
    code: 'store_id',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '采购类型',
    code: 'purchase_type',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额(去税)',
    code: 'no_tax_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '零售价',
    code: 'sale_price',
    width: 100,
    align: 'right',
    features: { sortable: false },
  },
  {
    name: '零售金额',
    code: 'sale_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
];
export const tableList3: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 100,
    align: 'center',
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '门店',
    code: 'store_id',
    width: 120,
    features: { sortable: true },
  },
  // {
  //   name: '销售模式',
  //   code: 'sale_mode',
  //   width: 150,
  //   features: { sortable: true }
  // },
  {
    name: '采购类型',
    code: 'purchase_type',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额(去税)',
    code: 'no_tax_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '零售价',
    code: 'sale_price',
    width: 100,
    align: 'right',
    features: { sortable: false },
  },
  {
    name: '零售金额',
    code: 'sale_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
];
export const tableList5: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 120,
    align: 'center',
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '门店名称',
    code: 'store_id',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '仓库',
    code: 'storehouse_name',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额(去税)',
    code: 'no_tax_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
];
export const goodsList4: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 120,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '采购类型',
    code: 'purchase_type',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '停购',
    code: 'item_stop_purchase',
    hidden: true,
    width: 70,
    features: { sortable: true },
  },
  {
    name: '停售',
    code: 'item_stop_sale',
    hidden: true,
    width: 70,
    features: { sortable: true },
  },
  {
    name: '停止要货',
    code: 'item_stop_request',
    hidden: true,
    width: 100,
    features: { sortable: true },
  },
  {
    name: '停止批发',
    code: 'item_stop_wholesale',
    hidden: true,
    width: 100,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'unit',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '单价',
    code: 'cost_price',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '单价(去税)',
    code: 'no_tax_cost_price',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额(去税)',
    code: 'no_tax_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '零售价',
    code: 'sale_price',
    width: 100,
    align: 'right',
    features: { sortable: false },
  },
  {
    name: '零售金额',
    code: 'sale_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '基本单价',
    code: 'basic_cost_price',
    width: 110,
    align: 'right',
    // hidden: true,
    features: { sortable: true },
  },
  {
    name: '基本单价(去税)',
    code: 'basic_no_tax_cost_price',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '入库供应商',
    code: 'latest_supplier_name',
    width: 280,
    // features: { sortable: true }
  },
  {
    name: '主供应商',
    code: 'main_supplier_name',
    width: 280,
    // features: { sortable: true }
  },
  {
    name: '最近入库日期',
    code: 'latest_in_date',
    width: 160,
    features: {
      sortable: true,
      format: 'TIME',
    },
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '关联商品',
    code: 'item_assemble_item',
    width: 160,
  },
];

//查询单位query_unit
export const queryUnit = [
  {
    label: '库存单位',
    value: 'STOCK',
  },
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
  {
    label: '批发单位',
    value: 'WHOLESALE',
  },
];

// 页面
export const item_type_list = [
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
  {
    label: '分级商品',
    value: 'GRADING',
  },
  {
    label: '主规格商品',
    value: 'MAINSPEC',
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC',
  },
];

export const purchase_type = [
  {
    label: '集采品',
    value: 'COLLECTIVE_PURCHASE',
  },
  {
    label: '集售品',
    value: 'COLLECTIVE_SALE',
  },
  {
    label: '地采品',
    value: 'GROUND_PURCHASE',
  },
  {
    label: '店采品',
    value: 'SHOP_PURCHASE',
  },
];

export const maxLevel = [
  {
    label: '按当前类别',
    value: '',
  },
  {
    label: '按一级类别',
    value: 1,
  },
  {
    label: '按二级类别',
    value: 2,
  },
  {
    label: '按三级类别',
    value: 3,
  },
  {
    label: '按四级类别',
    value: 4,
  },
  {
    label: '按五级类别',
    value: 5,
  },
  {
    label: '按六级类别',
    value: 6,
  },
  {
    label: '按七级类别',
    value: 7,
  },
  {
    label: '按八级类别',
    value: 8,
  },
  {
    label: '按九级类别',
    value: 9,
  },
];
