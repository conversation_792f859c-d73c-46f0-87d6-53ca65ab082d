import { columnWidthEnum } from '@/data/common/constant';
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import type { XlbTableColumnProps } from '@xlb/components';
import type { SearchListItem } from '@xlb/components/dist/lowcodes/XlbProForm/components/ProFormList';
import type { ArtColumn } from 'ali-react-table';

//单据状态
export const Options1 = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审批中',
    value: 'AUDIT',
    type: 'warning',
  },
  {
    label: '处理通过',
    value: 'HANDLE',
    type: 'success',
  },
  {
    label: '处理拒绝',
    value: 'HANDLE_REFUSE',
    type: 'danger',
  },
];

export const judge: any[] = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
  // {
  //   label: '空',
  //   value: '',
  // },
];
export const types1 = [
  {
    label: 'true',
    value: 2,
  },
  {
    label: 'false',
    value: 1,
  },
  {
    label: 'null',
    value: 0,
  },
];
export const types = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
  {
    label: ' ',
    value: null,
  },
];
export const types_approvalForm = [
  {
    label: '是',
    value: 2,
  },
  {
    label: '否',
    value: 1,
  },
  {
    label: '空',
    value: 0,
  },
];
//商品类型
export const goodsType = [
  {
    label: '主规格商品',
    value: 'MAINSPEC',
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC',
  },
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
  {
    label: '分级商品',
    value: 'GRADING',
  },
];

export const itemStatus = [
  { label: '新品', value: 'NEW_ITEM', disabled: true },
  { label: '正常品', value: 'NORMAL' },
  { label: '一次性商品', value: 'ONE_TIME_ITEM' },
  { label: '停购', value: 'STOP_BUY' },
  { label: '停要', value: 'STOP_WANT' },
  { label: '停售', value: 'STOP_SALE' },
  { label: '预淘汰', value: 'PRE_ELIMINATE' },
  { label: '淘汰', value: 'ELIMINATE' },
  { label: '空', value: 'EMPTY' },
];

export const formList: SearchListItem[] = [
  {
    id: ErpFieldKeyMap?.billOrgIds,
    name: 'org_ids',
    hidden: true,
    // onChange: (e: string[], form: any, _: any) => {
    //   form.setFieldsValue({
    //     store_ids: null,
    //   });
    // },
  },
  {
    id: ErpFieldKeyMap?.erpCenterStoreIdsMultiple,
    name: 'store_ids',
    rules: [{ required: true, message: '请选择配送中心' }],
    label: '配送中心',
  },
  {
    id: ErpFieldKeyMap?.erpitemIds,
    name: 'item_ids',
  },
  {
    id: ErpFieldKeyMap?.erpCommonSelect,
    name: 'demolition',
    label: '拆零',
    fieldProps: {
      options: judge,
    },
  },
  {
    id: ErpFieldKeyMap?.erpCommonSelect,
    name: 'stop_request',
    label: '停止要货',
    fieldProps: {
      options: judge,
    },
  },
  {
    id: ErpFieldKeyMap?.erpCommonSelect,
    name: 'stop_wholesale',
    label: '停止批发',
    fieldProps: {
      options: judge,
    },
  },
  {
    label: '',
    id: ErpFieldKeyMap.erpIsDefault,
    fieldProps: {
      options: [
        {
          label: '查询实际拆零属性',
          value: 'product_actual_attribute',
        },
      ],
    },
  },
];
export const approvalFormList: SearchListItem[] = [
  {
    id: 'dateCommon',
    name: 'create_date',
    label: '日期范围',
  },
  {
    id: 'timeType',
    label: '时间类型',
    name: 'time_type',
    fieldProps: {
      allowClear: false,
      options: [
        { label: '制单时间', value: 0 },
        { label: '审核时间', value: 1 },
        { label: '处理时间', value: 2 },
      ],
    },
  },
  {
    id: ErpFieldKeyMap?.erpFidTooltip,
    name: 'fid',
    label: '单据号',
  },
  {
    id: ErpFieldKeyMap?.businessRangeOrgIds,
    label: '组织',
    name: 'org_ids',
    onChange: (e: string[], form: any, _: any) => {
      form.setFieldsValue({ store_ids: [] });
    },
  },
  {
    id: ErpFieldKeyMap?.erpStoreIdsEnableOrganization,
    name: 'store_ids',
    rules: [
      ({ getFieldsValue }: any) => ({
        required: !getFieldsValue(true)?.fid,
        message: '请选择门店',
      }),
    ],
  },
  {
    id: ErpFieldKeyMap?.erpitemIds,
    name: 'item_ids',
  },
  {
    id: ErpFieldKeyMap?.erpRateStatisticsItemCategory,
    name: 'item_category_ids',
  },
  {
    id: ErpFieldKeyMap?.erpOrderStatus,
    label: '单据状态',
    name: 'states',
    fieldProps: {
      mode: 'multiple',
      options: Options1,
    },
  },
];

// 批量修改
export const batch = [
  {
    label: '拆零属性',
    value: 'demolitionType',
  },
  {
    label: '仓配单位',
    value: 'distributionType',
  },
  // {
  //   label: '统配',
  //   value: 'force_transfer',
  // },
  // {
  //   label: '停售',
  //   value: 'stop_sale',
  // },
  // {
  //   label: '停止批发',
  //   value: 'stop_wholesale',
  // },
  // {
  //   label: '预定',
  //   value: 'reserve',
  // },
  // {
  //   label: '前台折扣',
  //   value: 'pos_discount',
  // },
  // {
  //   label: '前台议价',
  //   value: 'pos_bargain',
  // },
  // {
  //   label: '直配',
  //   value: 'direct_delivery',
  // },
  // {
  //   label: '禁止退仓',
  //   value: 'stop_return',
  // },
  // {
  //   label: '必卖品',
  //   value: 'must_sell',
  // },
  // {
  //   label: '销售模式',
  //   value: 'sale_mode',
  // },
  // {
  //   label: '越库',
  //   value: 'skip_warehouse',
  // },
  // {
  //   label: '商品状态',
  //   value: 'item_status',
  // },
];

export const historyArr: ArtColumn[] = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
  },
  {
    name: '修改时间',
    code: 'create_time',
    width: columnWidthEnum.TIME,
    align: 'left',
  },
  {
    name: '修改人',
    code: 'create_by',
    width: columnWidthEnum.BY,
    align: 'left',
  },
  {
    name: '操作明细',
    code: 'content',
    width: columnWidthEnum.MEMO,
    align: 'left',
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '组织',
    code: 'org_name',
    width: 100,
    hidden: true,
  },
  {
    name: '配送中心',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '停止要货',
    code: 'stop_request',
    width: 100,
    features: { sortable: true },
    render: text => text === true ? '是' : text === false ? '否' : ''
  },
  {
    name: '停止批发',
    code: 'stop_wholesale',
    width: 100,
    features: { sortable: true },
    render: text => text === true ? '是' : text === false ? '否' : ''
  },
  {
    name: '拆零',
    code: 'demolition',
    width: 100,
    features: { sortable: true },
    render: text => text === true ? '是' : text === false ? '否' : '-'
  },
  {
    name: '仓配单位',
    code: 'distribution_unit',
    width: 100,
    features: { sortable: true },
    render: text => text || '-'
  },
  {
    name: '仓配换算率',
    code: 'distribution_ratio',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '基本单位',
    code: 'unit',
    width: 140,
    features: { sortable: true },
  },
];

export const categoryLevelList = [
  {
    levelValue: 1,
    name: '一级分类',
    code: 'one_item_category_name',
    width: 120,
    // features: { sortable: true }  排序去除。后端不支持该字段排序，没有单子，口述-任芳
  },
  {
    levelValue: 2,
    name: '二级分类',
    code: 'two_item_category_name',
    width: 120,
    // features: { sortable: true }
  },
  {
    levelValue: 3,
    name: '三级分类',
    code: 'three_item_category_name',
    width: 120,
    // features: { sortable: true }
  },
];

export const approvalTableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '审批单号',
    code: 'fid',
    width: 160,
  },
  {
    name: '组织',
    code: 'org_name',
    width: 160,
    render: (text, render) => render?.organization?.name,
  },
  {
    name: '门店',
    code: 'store_names',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '单据状态',
    code: 'state',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '应用进度',
    code: 'applicationProgress',
    width: 100,
    features: { sortable: true },
  },
  // {
  //   name: '同步乐檬进度',
  //   code: 'item_spec',
  //   width: 200,
  //   features: { sortable: true }
  // },
  {
    name: '应用时间',
    code: 'apply_date',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '制单人',
    code: 'create_by',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 170,
    features: { sortable: true },
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 170,
    features: { sortable: true },
  },
  {
    name: '处理人',
    code: 'handle_by',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '处理时间',
    code: 'handle_time',
    width: 170,
    features: { sortable: true },
  },
];