import { wujieBus } from '@/wujie/utils';
import {
  XlbBasicForm,
  XlbCheckbox,
  XlbInputDialog,
  XlbMessage,
  XlbModal,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch as ErpRequest, LStorage } from '@xlb/utils';
import { Fragment, useEffect, useState } from 'react';
import { batch } from '../../data';
import style from './batchChange.less';

const { Option } = XlbSelect;

const BatchChange = (props: any) => {
  const { visible, handleCancel, getData, currentData } = props;
  const [loading, setLoading] = useState(false);
  const [distributionUnit, setDistributionUnit] = useState([
    { label: '跟随主档', value: 'FOLLOW' },
  ]);
  const [form] = XlbBasicForm.useForm();

  useEffect(() => {
    if (visible && currentData?.length) {
      form.setFieldsValue({
        item_ids: [currentData[0].item_id],
        store_ids: [currentData[0].store_id],
      });
    }
  }, [visible]);

  const handleDownLoad = async (
    e: any,
    typeT: string,
    item_id: any,
    store_ids: any,
  ) => {
    const res = await ErpRequest.post(
      '/erp/hxl.erp.mdm.order.item.on.way.export',
      {
        company_id: LStorage.get('userInfo')?.company_id,
        item_id,
        store_ids,
        item_enum: typeT,
      },
    );
    if (res?.code === 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success(res?.data);
    }
  };
  const getCheckOrder = async (item_id: any, store_ids: any) => {
    setLoading(true);
    const res = await ErpRequest.post(
      '/erp/hxl.erp.mdm.order.item.on.way.find',
      {
        company_id: LStorage.get('userInfo')?.company_id,
        item_id,
        enum_list: ['DELIVERY', 'WHOLESALE'],
        store_ids,
      },
    );
    setLoading(false);
    if (res?.code === 0) {
      const arr = [];
      if (res?.data?.delivery_fid_count > 0) {
        arr.push(
          <div key={'DELIVERY'}>
            无法修改【配送换算率】,请通知{res?.data?.delivery_store_count}
            个仓处理
            {res?.data?.delivery_fid_count}笔在途订单！
            <a
              onClick={(e) => handleDownLoad(e, 'DELIVERY', item_id, store_ids)}
            >
              下载明细
            </a>
          </div>,
        );
      }
      if (res?.data?.wholesale_fid_count > 0) {
        arr.push(
          <div key={'WHOLESALE'}>
            无法修改【批发换算率】,请通知{res?.data?.wholesale_store_count}
            个仓处理
            {res?.data?.wholesale_fid_count}笔在途订单！
            <a
              onClick={(e) =>
                handleDownLoad(e, 'WHOLESALE', item_id, store_ids)
              }
            >
              下载明细
            </a>
          </div>,
        );
      }
      if (arr?.length > 0) {
        XlbTipsModal({
          tips: arr,
          isCancel: false,
        });
        return true;
      }
    }
  };

  const handleOk = async () => {
    if (!form.getFieldValue('item_ids')) {
      XlbTipsModal({
        tips: '请先选择商品名称',
      });
      return;
    }
    if (!form.getFieldValue('store_ids')) {
      XlbTipsModal({
        tips: '请先选择配送中心',
      });
      return;
    }
    const checkValue = form.getFieldValue('checkValue')
    if (!checkValue?.length) {
      XlbTipsModal({
        tips: '请先选择修改内容',
      });
      return;
    }
    const data = {
      ...form.getFieldsValue(true),
    };
    let checkContent = false; // 是否选择了内容
    batch.forEach((item: any) => {
      // 筛选勾选项并赋值
      if (checkValue?.includes(item.value)) {
        data[item.value] = form.getFieldValue(item.value);
        if (
          !(
            form.getFieldValue(item.value) ||
            form.getFieldValue(item.value) === false
          )
        ) {
          checkContent = true;
        }
      } else {
        data[item.value] = undefined;
      }
    });
    if (checkContent) {
      XlbTipsModal({
        tips: '请先选择修改内容',
      });
      return;
    }

    // 根据商品id查询在途单据汇总
    if (checkValue?.includes('distributionType') && await getCheckOrder(data?.item_ids?.[0], data?.store_ids)) return;

    const params = data?.store_ids?.map((item: any) => {
      return {
        store_id: item,
        item_id: data?.item_ids?.[0],
        ...(data?.checkValue?.includes('demolitionType')
          ? {
              demolition:
                data?.demolitionType === 'FOLLOW' ? null : data?.demolitionType,
              demolition_type:
                data?.demolitionType === 'FOLLOW' ? 'FOLLOW' : 'MANUAL',
            }
          : {}),

        ...(data?.checkValue?.includes('distributionType')
          ? {
              distribution_type:
                data?.distributionType === 'FOLLOW' ? 'FOLLOW' : 'MANUAL',
              distribution_ratio:
                data?.distributionType === 'FOLLOW'
                  ? null
                  : (distributionUnit?.find(
                      (t) => t?.value === data?.distributionType,
                    )?.distribution_ratio ?? null),
              distribution_unit:
                data?.distributionType === 'FOLLOW'
                  ? null
                  : (distributionUnit?.find(
                      (t) => t?.value === data?.distributionType,
                    )?.distribution_unit ?? null),
            }
          : {}),
      };
    });
    // 确认
    setLoading(true);
    const res = await ErpRequest.post(
      '/erp/hxl.erp.storeitemdistribution.attribute.batchupdate',
      {
        calc_detail_list: params,
        company_id: LStorage.get('userInfo').company_id,
      },
    );
    if (res?.code === 0) {
      XlbMessage.success('批量设置成功');
      handleCancel();
      form.resetFields();
      getData();
    }
    setLoading(false);
  };

  const renderCheckBox = (item: string, disabled = false) => {
    switch (item) {
      case '拆零属性':
        return (
          <XlbSelect
            size="small"
            onChange={(e) => {
              const checkValue = form.getFieldValue('checkValue');
              if (checkValue?.includes('sale_mode') && e === 'AGENCY') {
                form.setFieldsValue({
                  pos_bargain: 1,
                  pos_discount: 1,
                  checkValue: [
                    ...new Set([...checkValue, 'pos_bargain', 'pos_discount']),
                  ],
                });
              }
            }}
          >
            <Option value={'FOLLOW'}>跟随主档</Option>
            <Option value={true}>是</Option>
            <Option value={false}>否</Option>
          </XlbSelect>
        );
      case '仓配单位':
        return <XlbSelect size="small" options={distributionUnit} />;
    }
  };

  const onValuesChange = async (e: any, val: any) => {
    if (Object.hasOwn(e, 'item_ids') && val.item_ids?.length) {
      const res = await ErpRequest.post(
        '/erp/hxl.erp.storeitemdemolition.attribute.item.unit',
        {
          id: val?.item_ids?.[0],
        },
      );
      if (res?.code === 0) {
        const first = res?.data?.[0];
        const ls = res?.data?.map((t: any, index: number) => ({
          ...t,
          key: t?.distribution_unit,
          value:
            index === 0
              ? t?.distribution_unit
              : `${t?.distribution_unit} (${Number.isFinite(t.distribution_ratio) ? t.distribution_ratio?.toFixed(3) : ''}${first?.distribution_unit || ''})`,
        }));
        setDistributionUnit([{ label: '跟随主档', value: 'FOLLOW' }, ...ls]);
      }
    }
  };

  return (
    <XlbModal
      title={'批量修改'}
      centered
      isCancel={true}
      open={visible}
      maskClosable={false}
      wrapClassName="xlbDialog"
      onOk={() => handleOk()}
      onCancel={() => {
        handleCancel();
        form.resetFields();
      }}
      width={420}
      confirmLoading={loading}
    >
      <XlbBasicForm form={form} onValuesChange={onValuesChange}>
        <div className={style.box} style={{ marginBottom: 8 }}>
          <p className={style.title}>修改范围</p>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <XlbBasicForm.Item
              name="item_ids"
              labelCol={{ span: 6 }}
              label="商品名称"
            >
              <XlbInputDialog
                dialogParams={{
                  type: 'goods',
                  isMultiple: false,
                  nullable: true,
                  dataType: 'lists',
                  // data: { status: 1 }
                }}
                width={245}
              />
            </XlbBasicForm.Item>
            <XlbBasicForm.Item
              name="store_ids"
              label="配送中心"
              labelCol={{ span: 6 }}
            >
              <XlbInputDialog
                dialogParams={{
                  type: 'store',
                  isMultiple: true,
                  data: {
                    center_flag: true,
                  },
                }}
                fieldNames={{
                  idKey: 'id',
                  nameKey: 'store_name',
                }}
                width={245}
              />
            </XlbBasicForm.Item>
          </div>
        </div>

        <div
          className={style.box}
          style={{ marginBottom: 20, paddingBottom: 0 }}
        >
          <p className={style.title}>修改内容</p>
          <XlbBasicForm.Item noStyle dependencies={['checkValue']}>
            {({ getFieldValue }) => {
              return (
                <XlbBasicForm.Item name="checkValue">
                  <XlbCheckbox.Group style={{ width: '100%' }}>
                    {batch.map((item) => {
                      return (
                        <Fragment key={item.value}>
                          <XlbCheckbox
                            value={item.value}
                            className={style.cbox}
                          >
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '5px',
                                height: '30px',
                              }}
                            >
                              {item.label}
                            </div>
                          </XlbCheckbox>
                          <XlbBasicForm.Item name={item.value} key={item.value}>
                            {renderCheckBox(item.label)}
                          </XlbBasicForm.Item>
                        </Fragment>
                      );
                    })}
                  </XlbCheckbox.Group>
                </XlbBasicForm.Item>
              );
            }}
          </XlbBasicForm.Item>
        </div>
      </XlbBasicForm>
    </XlbModal>
  );
};
export default BatchChange;