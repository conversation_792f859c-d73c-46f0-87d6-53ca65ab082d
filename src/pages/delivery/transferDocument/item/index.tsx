// import XlbBasicData from '@/components/common/xlbBasicData';
import { orderStatusIcons } from '@/components/common/data';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { wujieBus } from '@/wujie/utils';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbIcon,
  XlbInput,
  XlbInputDialog,
  XlbInputNumber,
  XlbSelect,
  XlbTable,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { Col, ConfigProvider, Modal, Row, Select, Tabs, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { inItemTableList, itemTableList, outItemTableList } from '../data';
import {
  addBasket,
  approveBasket,
  auditBasket,
  handleBasket,
  readBasket,
  reauditBasket,
  rejectBasket,
  updateBasket,
} from '../server';
import styles from './index.less';
const { TabPane } = Tabs;
const { confirm } = Modal;
const { Option } = Select;
const TransferDocumentItem = (props) => {
  const { enable_organization } = useBaseParams((state) => state);
  const { record, onBack } = props;
  const [rowData, setRowData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify([...itemTableList, ...outItemTableList])),
  );
  const [hasOutStoreId, setHasOutStoreId] = useState<boolean>(false);
  const [hasStoreId, setHasStoreId] = useState<boolean>(false);
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [footerData, setFooterData] = useState<any[]>([]);
  const [form] = XlbBasicForm.useForm();
  const [Fid, setFid] = useState<any>();
  const [info, setInfo] = useState({
    state: 'INIT',
  });
  const [allRow, setAllRow] = useState<any>({}); //主页查询数据
  const [edit, setEdit] = useState<boolean>(false); //触发表格编辑
  const [centerStore, setCenterStore] = useState(); //记录登录所属门店是否为配送中心
  const [storeModal, setStoreModal] = useState<any>({
    modalVisible: false, // 弹窗是否展示
    storeItem: {}, // 接收弹窗选中的值
    modalState: {
      // hasChosenItem:[],
      isMultiple: true, //（默认false单选，true多选）是否多选
      modalType: 'store', //弹窗类型 （必传）'item'商品 'store'门店  'supplier'供应商
      // modalParams: { a:'a', b:'b' }, // 弹窗查询参数
      // filterData: { a:'1', b:'2' } // 弹窗过滤参数2
      value: '',
    },
  });
  const [TipModalVisible, setTipModalVisible] = useState<boolean>(false);
  const [stateTips, setStateTips] = useState<{
    tips: any;
    isConfirm: boolean;
    isCancel: boolean;
    width: string;
    showDesc: string;
  }>({
    tips: '',
    isConfirm: true,
    isCancel: false,
    width: '450px',
    showDesc: '',
  });
  const InvoiceRender = (item: any) => {
    switch (item.code) {
      case 'operation':
        item.render = (value: any, record: any, index: number) => {
          return record.index === '合计' ? null : (
            <div className="overwidth">
              <XlbIcon
                name="shanchu"
                className="link cursors"
                style={{ fontSize: '16px' }}
                onClick={(e) => {
                  e.stopPropagation();
                  if (info.state !== 'INIT') return;
                  showDeleteModal(record, index);
                }}
              />
            </div>
          );
        };
        break;
      case 'quantity': //数量
        item.render = (value: any, record: any, index: number) => {
          return record._click && info.state == 'INIT' ? (
            <XlbInputNumber
              key={item.code + '-' + index.toString()}
              defaultValue={value}
              onFocus={(e) => e.target.select()}
              onChange={(e) => inputChange(e, index, item.code, record)}
              onBlur={(e) => inputBlur(e, index, item.code, record)}
              style={{ textAlign: 'right' }}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index);
              }}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'out_quantity': //数量
        item.render = (value: any, record: any, index: number) => {
          return record._click && info.state == 'HANDLE' ? (
            <XlbInputNumber
              key={item.code + '-' + index.toString()}
              defaultValue={value}
              onFocus={(e) => e.target.select()}
              onChange={(e) => inputChange(e, index, item.code, record)}
              onBlur={(e) => inputBlur(e, index, item.code, record)}
              style={{ textAlign: 'right' }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index);
              }}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'money':
        item.render = (value: any, record: any, index: number) => {
          return record.index === '合计' ? (
            <div className="info overwidth">{value}</div>
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'out_money':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info overwidth">{value}</div>;
        };
    }
    return item;
  };
  //回车事件
  const onPressEnter = (code: any, index: any) => {
    Promise.resolve()
      .then(() => {
        rowData[index]['edit'] = false;
        index + 1 == rowData.length
          ? (rowData[0]['edit'] = true)
          : (rowData[index + 1]['edit'] = true);
        setRowData(JSON.parse(JSON.stringify(rowData)));
      })
      .then(() => {
        let inputBox =
          index + 1 == rowData.length
            ? document.getElementById(code + '-' + (0).toString())
            : document.getElementById(code + '-' + (index + 1).toString());
        inputBox?.focus();
      });
  };
  //输入框改变
  const inputChange = (e: any, index: any, key: any, record: any) => {
    //记录编辑
    setEdit(true);
    record[key] = Number(e);
    if (key == 'quantity') {
      //编辑【数量】,金额（含税）;
      // rowData[index][key] = Number(e.target.value).toFixed(3)
      record['money'] = (record['price'] * record[key]).toFixed(2);
    }
    setRowData([...rowData]);
  };

  //失去焦点
  const inputBlur = (e: any, index: any, key: any, record: any) => {
    let regPos = /^[0-9]\d*$/;
    if (
      (key == 'quantity' || key == 'out_quantity') &&
      !regPos.test(e.target.value)
    ) {
      XlbTipsModal({
        tips: '请输入正整数',
      });
      record[key] = 0;
      return false;
    }
    setRowData([...rowData]);
  };

  //读取
  const readinfo = async (fid: any) => {
    setIsLoading(true);
    const res = await readBasket({ fid });
    setIsLoading(false);
    if (res.code === 0) {
      res.data.details.map((v: any) => {
        v.money = v.money?.toFixed(2);
        v.out_money = v.out_money?.toFixed(2);
        v.price = v.price?.toFixed(4);
      });
      setInfo({ state: res.data.state });
      setRowData(res.data.details);
      form.setFieldsValue({
        ...res?.data,
      });
    }
    setItemArr([
      ...itemTableList,
      ...(res.data.flag ? outItemTableList : inItemTableList),
    ]);
  };

  //操作删除
  const showDeleteModal = (record: any, index: any) => {
    const newRowData = rowData.filter((v, i) => i !== index['index']);
    setRowData([...newRowData]);
  };
  //弹窗取消事件
  const handleCancel = () => {
    setStoreModal({
      ...storeModal,
      modalVisible: false,
    });
  };
  //返回前判断保存状态
  const goBack = async () => {
    if (edit) {
      await XlbTipsModal({
        tips: '单据未保存，是否确认返回？',
        onOkBeforeFunction: () => {
          onBack(true);
          return true;
        },
      });
      return false;
    }
    onBack(true);
  };
  // 保存 审核 反审核t:string
  const operateOrder = async (t: string) => {
    if (!form.getFieldValue('store_id')) {
      XlbTipsModal({
        tips: `请先选择收货门店！`,
      });
      return false;
    }
    if (rowData.length == 0) {
      XlbTipsModal({
        tips: `请先添加商品`,
      });
      return false;
    }
    const data = {
      out_org_id: form.getFieldValue('out_org_id') || '',
      out_org_name: form.getFieldValue('out_org_name') || '',
      out_store_id: form.getFieldValue('out_store_id') || '',
      store_id: form.getFieldValue('store_id') || '',
      memo: form.getFieldValue('memo'),
      fid: form.getFieldValue('fid'),
      flag: form.getFieldValue('flag'),
      details: rowData,
    };
    let res: any = null;
    setIsLoading(true);
    t === '保存' && Fid === 1 && (res = await addBasket(data));
    t === '保存' && Fid !== 1 && (res = await updateBasket(data));
    t === '审核' && (res = await auditBasket(data));
    t === '反审核' &&
      (res = await reauditBasket({ fid: form.getFieldValue('fid') }));
    setIsLoading(false);
    if (res.code == 0) {
      readinfo(res.data?.fid || form.getFieldValue('fid'));
      setEdit(false);
      message.success('操作成功');
      if (t === '保存') {
        setFid(res.data?.fid);
      }
      return;
    }
  };
  //导出
  const exportItem = async (e: any) => {
    console.log('导出');

    setIsLoading(true);
    let data = { fid: form.getFieldValue('fid') };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.basketorder.detail.export',
      data,
    );
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      message.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };
  useEffect(() => {
    // 设置合计行
    footerData[0] = {};
    footerData[0]._index = '合计';
    footerData[0].money = rowData
      .reduce((sum, v) => sum + Number(v.money || 0), 0)
      .toFixed(2);
    footerData[0].out_money = rowData
      .reduce((sum, v) => sum + Number(v.out_money || 0), 0)
      .toFixed(2);
    setFooterData([...footerData]);
    setPagin({
      ...pagin,
      pageSize: rowData.length || 200,
      total: rowData.length,
    });
  }, [JSON.stringify(rowData)]);
  useEffect(() => {
    const center = LStorage.get('userInfo').store.enable_delivery_center;
    setCenterStore(center);
    setFid(record.fid);
    setAllRow(record);
    if (record.fid === 1) {
      form.setFieldsValue({
        flag: true,
      });
    } else {
      readinfo(record.fid);
    }
  }, []);
  const referenceRef = useRef<HTMLDivElement | null>(null);
  const [dialogWidth, setDialogWidth] = useState(180); // 默认值

  useEffect(() => {
    if (referenceRef.current) {
      const observer = new ResizeObserver(() => {
        const width = referenceRef.current?.offsetWidth - 118 || 180;
        setDialogWidth(width);
      });

      observer.observe(referenceRef.current);

      return () => observer.disconnect(); // 清理
    }
  }, []);
  return (
    <ConfigProvider
      theme={{
        token: {
          screenXLMin: 1280,
          screenXL: 1280,
        },
      }}
    >
      <div
        style={{
          padding: 12,
          height: 'calc(100vh - 120px)',
          display: 'flex',
          flexDirection: 'column',
        }}
        className={styles.form_container_wrapper}
      >
        <XlbButton.Group>
          {hasAuth(['物资进出单', '编辑']) && (
            <XlbButton
              label="保存"
              disabled={info.state !== 'INIT' || isLoading}
              onClick={() => operateOrder('保存')}
              type="primary"
              icon={<XlbIcon size={16} name="baocun" />}
            ></XlbButton>
          )}
          {hasAuth(['物资进出单', '审核']) && (
            <XlbButton
              label="审核"
              type="primary"
              disabled={info.state !== 'INIT' || Fid === 1 || isLoading}
              onClick={() => operateOrder('审核')}
              icon={<XlbIcon size={16} name="shenhe" />}
            ></XlbButton>
          )}
          {hasAuth(['物资进出单', '处理']) && (
            <XlbDropdownButton
              label="处理"
              dropList={[
                {
                  label: '处理通过',
                  disabled: info.state !== 'AUDIT' || isLoading || true,
                },
              ]}
              dropdownItemClick={async (index: number, item: any, e) => {
                let res: any = null;
                switch (item?.label) {
                  case '处理通过':
                    const data = {
                      details: rowData,
                      ...form.getFieldsValue(true),
                    };
                    setIsLoading(true);
                    res = await handleBasket(data);
                    setIsLoading(false);
                    break;
                  default:
                    break;
                }
                if (res.code === 0) {
                  message.success('处理成功');
                  readinfo(form.getFieldValue('fid'));
                  setEdit(false);
                }
              }}
            />
          )}
          {hasAuth(['物资进出单', '批复']) && (
            <XlbDropdownButton
              label="批复"
              dropList={[
                {
                  label: '批复通过',
                  disabled:
                    info.state !== 'HANDLE' ||
                    !hasAuth(['物资进出单', '批复']) ||
                    (!centerStore && form.getFieldValue('flag')) ||
                    isLoading ||
                    true,
                },
                {
                  label: '驳回',
                  disabled:
                    info.state !== 'HANDLE' ||
                    !hasAuth(['物资进出单', '批复']) ||
                    (!centerStore && form.getFieldValue('flag')) ||
                    isLoading ||
                    true,
                },
              ]}
              dropdownItemClick={async (index: number, item: any, e) => {
                let res: any = null;
                switch (item.label) {
                  case '批复通过':
                    setIsLoading(true);
                    res = await approveBasket({
                      out_store_id: form.getFieldValue('out_store_id') || '',
                      store_id: form.getFieldValue('store_id') || '',
                      memo: form.getFieldValue('memo'),
                      fid: form.getFieldValue('fid'),
                      flag: form.getFieldValue('flag'),
                      details: rowData,
                    });
                    setIsLoading(false);
                    break;
                  case '驳回':
                    setIsLoading(true);
                    res = await rejectBasket({
                      out_store_id: form.getFieldValue('out_store_id') || '',
                      store_id: form.getFieldValue('store_id') || '',
                      memo: form.getFieldValue('memo'),
                      fid: form.getFieldValue('fid'),
                      flag: form.getFieldValue('flag'),
                      details: rowData,
                    });
                    setIsLoading(false);
                    break;
                }
                if (res.code === 0) {
                  message.success('操作成功');
                  readinfo(form.getFieldValue('fid'));
                }
              }}
            />
          )}
          {!hasAuth(['物资进出单', '导出']) &&
          !hasAuth(['物资进出单', '打印']) ? null : (
            <XlbDropdownButton
              label="业务操作"
              dropList={[
                {
                  label: '导出',
                  disabled:
                    isLoading || !form.getFieldValue('fid') || !rowData.length,
                  isNoAuth: !hasAuth(['物资进出单', '导出']),
                },
                {
                  label: '打印',
                  disabled: !rowData.length,
                  isNoAuth: !hasAuth(['物资进出单', '打印']),
                },
              ]}
              dropdownItemClick={(index: number, item: any, e: any) => {
                switch (item?.label) {
                  case '导出':
                    exportItem(e);
                    break;
                  case '打印':
                    // printItem().then()
                    message.warning('开发中...');
                    break;
                }
              }}
            />
          )}
          <XlbButton
            label="返回"
            type="primary"
            onClick={() => goBack()}
            icon={<XlbIcon size={16} name="fanhui" />}
          />
        </XlbButton.Group>
        <XlbBasicForm
          colon
          form={form}
          autoComplete="off"
          layout="horizontal"
          className={styles.contractTab}
        >
          <Tabs defaultActiveKey="baseInfo">
            <TabPane tab="基本信息" key="baseInfo">
              <div className={styles.form_container_transferDocument}>
                <div className="row-flex" style={{ marginTop: 12 }}>
                  <div
                    className="row-flex"
                    style={{ flex: 1, flexWrap: 'wrap' }}
                  >
                    <Row gutter={12} wrap>
                      <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                        <XlbBasicForm.Item label="发货门店" name="out_store_id">
                          <XlbInputDialog
                            width={dialogWidth}
                            disabled={
                              (Fid !== 1 && info.state !== 'INIT') ||
                              (Fid !== 1 &&
                                info.state == 'INIT' &&
                                rowData.length !== 0)
                            }
                            dialogParams={{
                              type: 'store',
                              dataType: 'lists',
                              isMultiple: false,
                              data: {
                                center_flag: form?.getFieldValue('flag')
                                  ? false
                                  : null,
                              },
                            }}
                            fieldNames={{
                              idKey: 'id',
                              nameKey: 'store_name',
                            }}
                            onChange={(value: any) => {
                              if (value?.length) {
                                form.setFieldsValue({
                                  out_store_id: value[0],
                                });
                                setHasOutStoreId(true);
                              }
                              if (!value?.length) {
                                setHasOutStoreId(false);
                              }
                            }}
                            handleValueChange={(value: any, options: any) => {
                              if (options?.length) {
                                form.setFieldsValue({
                                  out_org_id: options[0]?.org_id,
                                  out_org_name: options[0]?.org_name,
                                });
                              }
                            }}
                          ></XlbInputDialog>
                        </XlbBasicForm.Item>
                      </Col>
                      {enable_organization && (
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item
                            hidden
                            label="发货组织"
                            name="out_org_id"
                          >
                            <XlbInput disabled style={{ width: '100%' }} />
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            label="发货组织"
                            name="out_org_name"
                          >
                            <XlbInput disabled style={{ width: '100%' }} />
                          </XlbBasicForm.Item>
                        </Col>
                      )}
                      <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                        <XlbBasicForm.Item label="收货门店" name="store_id">
                          <XlbInputDialog
                            width={dialogWidth}
                            disabled={
                              (Fid !== 1 && info.state !== 'INIT') ||
                              (Fid !== 1 &&
                                info.state == 'INIT' &&
                                rowData.length !== 0)
                            }
                            dialogParams={{
                              type: 'store',
                              dataType: 'lists',
                              // url: form?.getFieldValue('flag')
                              //   ? '/erp/hxl.erp.store.center.find'
                              //   : '/erp/hxl.erp.store.short.page',
                              isMultiple: false,
                              data: {
                                center_flag: form?.getFieldValue('flag')
                                  ? true
                                  : null,
                              },
                            }}
                            fieldNames={{
                              idKey: 'id',
                              nameKey: 'store_name',
                            }}
                            onChange={(value: any) => {
                              if (value?.length) {
                                form.setFieldsValue({
                                  store_id: value[0],
                                });
                                setHasStoreId(true);
                              }
                              if (!value?.length) {
                                setHasStoreId(false);
                              }
                            }}
                          ></XlbInputDialog>
                        </XlbBasicForm.Item>
                      </Col>
                      <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                        <div style={{ width: '100%' }} ref={referenceRef}>
                          <XlbBasicForm.Item label="进出方向" name="flag">
                            <XlbSelect
                              style={{ width: '100%' }}
                              disabled={
                                // !centerStore ||
                                // (Fid !== 1 && info.state !== 'INIT') ||
                                // (Fid !== 1 && info.state == 'INIT' && rowData.length !== 0)
                                true
                              }
                            >
                              <Option value={false}>发货</Option>
                              <Option value={true}>退货</Option>
                            </XlbSelect>
                          </XlbBasicForm.Item>
                        </div>
                      </Col>
                      <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                        <XlbBasicForm.Item label="单据号" name="fid">
                          <XlbInput
                            style={{ width: '100%' }}
                            disabled
                            // suffix={
                            //   <SearchOutlined style={{ color: '#F5F5F5' }} />
                            // }
                          />
                        </XlbBasicForm.Item>
                      </Col>
                      {/* <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                        <XlbBasicForm.Item label="单据状态">
                          <XlbInput
                            style={{ width: '100%' }}
                            disabled
                            suffix={
                              <SearchOutlined style={{ color: '#F5F5F5' }} />
                            }
                            value={
                              Options1.find((s) => s.value === info.state)
                                ?.label
                            }
                          />
                        </XlbBasicForm.Item>
                      </Col> */}
                      <Col xs={16} sm={16} md={16} lg={16} xl={12} xxl={12}>
                        <XlbBasicForm.Item label="留言备注" name="memo">
                          <XlbInput
                            style={{ width: '100%', height: 26 }}
                            disabled={info.state !== 'INIT'}
                          />
                        </XlbBasicForm.Item>
                      </Col>
                    </Row>
                  </div>
                  {info?.state && (
                    <div
                      style={{
                        width: '150px',
                        flexBasis: '150px',
                        display: 'flex',
                        justifyContent: 'center',
                        marginLeft: 20,
                      }}
                    >
                      <img
                        src={orderStatusIcons[info?.state]}
                        width={86}
                        height={78}
                      />
                    </div>
                  )}
                </div>
              </div>
            </TabPane>
            <TabPane tab="其他信息" key="otherInfo">
              <div className={styles.form_container_transferDocument}>
                <Row gutter={12} wrap>
                  <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                    <XlbBasicForm.Item label="制单人" name="create_by">
                      <XlbInput style={{ width: '100%' }} disabled />
                    </XlbBasicForm.Item>
                  </Col>
                  <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                    <XlbBasicForm.Item label="制单时间" name="create_time">
                      <XlbInput style={{ width: '100%' }} disabled />
                    </XlbBasicForm.Item>
                  </Col>
                  <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                    <XlbBasicForm.Item label="审核人" name="audit_by">
                      <XlbInput style={{ width: '100%' }} disabled />
                    </XlbBasicForm.Item>
                  </Col>
                  <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                    <XlbBasicForm.Item label="审核时间" name="audit_time">
                      <XlbInput style={{ width: '100%' }} disabled />
                    </XlbBasicForm.Item>
                  </Col>
                  <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                    <XlbBasicForm.Item label={'批复人'} name={'approve_by'}>
                      <XlbInput style={{ width: '100%' }} disabled />
                    </XlbBasicForm.Item>
                  </Col>
                  <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                    <XlbBasicForm.Item label={'批复时间'} name={'approve_time'}>
                      <XlbInput style={{ width: '100%' }} disabled />
                    </XlbBasicForm.Item>
                  </Col>
                  <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                    <XlbBasicForm.Item label={'处理人'} name={'handle_by'}>
                      <XlbInput style={{ width: '100%' }} disabled />
                    </XlbBasicForm.Item>
                  </Col>
                  <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                    <XlbBasicForm.Item label={'处理时间'} name={'handle_time'}>
                      <XlbInput style={{ width: '100%' }} disabled />
                    </XlbBasicForm.Item>
                  </Col>
                </Row>
              </div>
            </TabPane>
          </Tabs>
        </XlbBasicForm>
        <XlbButton.Group>
          <XlbButton
            label="批量添加"
            disabled={
              !(
                hasOutStoreId &&
                hasStoreId &&
                form.getFieldValue('flag') !== undefined &&
                info.state === 'INIT'
              )
            }
            onClick={
              async () => {
                const bool = await XlbBasicData({
                  type: 'basket',
                  isMultiple: true,
                  dataType: 'lists',
                  primaryKey: 'id',
                  resetForm: true,
                  isLeftColumn: false,
                  url: '/erp/hxl.erp.basket.page',
                  data: {
                    enabled: true,
                  },
                });
                if (bool) {
                  const ids = rowData.map((v) => v.basket_id);
                  const repeatArr = bool.filter((v: any) => ids.includes(v.id));
                  let rName = [
                    repeatArr.map((v: any) => `【${v.name}】`).join('、'),
                  ];
                  if (repeatArr.length) {
                    XlbTipsModal({
                      tips: `以下商品已存在，不允许重复添加，系统已自动过滤！`,
                    });
                    return false;
                  }
                  const newArr = bool.filter((v: any) => !ids.includes(v.id));
                  const newList = newArr.map((v: any) => {
                    return {
                      basket_name: v.name,
                      basket_id: v.id,
                      price: v.price.toFixed(4),
                      money: '0.00',
                      out_money: '0.00',
                      quantity: 0,
                      out_quantity: 0,
                      driver_confirm_quantity: 0,
                    };
                  });
                  setRowData([...rowData, ...newList]);
                }
              }
              // setStoreModal({
              //   ...storeModal,
              //   modalState: {
              //     isMultiple: true,
              //     isLeftColumn: true,
              //     modalType: 'basket',
              //     filterData: {
              //       enabled: true,
              //     },
              //   },
              //   modalVisible: true,
              // })
            }
            type="primary"
            icon={<XlbIcon size={16} name="jia" />}
          ></XlbButton>
        </XlbButton.Group>
        <XlbTable
          columns={itemArr.map((v) => InvoiceRender(v))}
          isLoading={isLoading}
          pagin={pagin}
          style={{ flex: 1, marginTop: 12 }}
          dataSource={rowData}
          total={rowData?.length}
          selectMode="single"
          primaryKey="_index"
          footerDataSource={footerData}
        />
      </div>
    </ConfigProvider>
  );
};

export default TransferDocumentItem;
