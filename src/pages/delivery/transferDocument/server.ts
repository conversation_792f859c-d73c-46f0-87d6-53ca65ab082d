import { XlbFetch } from '@xlb/utils';

// 批量制单
export const batchOrderSave = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.basketorder.batchsave', data);
};

// 删除
export const deleteBasket = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.batchdelete', data);
};

// 获取载具名称数据
export const getBasket = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.page', data);
};

// 新增
export const addBasket = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.save', data);
};
// 更新
export const updateBasket = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.update', data);
};

// 审核
export const auditBasket = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.audit', data);
};
// 批复通过
export const approveBasket = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.approve', data);
};
// 处理通过
export const handleBasket = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.handle', data);
};
// 读取
export const readBasket = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.read', data);
};
// 反审核
export const reauditBasket = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.reaudit', data);
};
// 驳回
export const rejectBasket = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.reject', data);
};
// 获取退货数
export const getOutNum = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.basketorder.getoutnum', data);
};

//查询配送中心门店
export const getCenterStores = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.store.center.find', data);
};