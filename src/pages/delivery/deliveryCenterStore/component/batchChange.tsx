import { XlbProgress } from '@/components/common';
import NiceModal from '@ebay/nice-modal-react';
import type { BaseModalProps } from '@xlb/components';
import {
  XlbBasicForm,
  XlbImportModal,
  XlbInputDialog,
  XlbModal,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { Button, Checkbox, message, Radio, Space } from 'antd';
import { cloneDeep } from 'lodash';
import type { FC } from 'react';
import { useEffect, useState } from 'react';
import { batch, deveveryPriceValues, keyMap } from '../data';
import Api from '../server';
import style from './batchChange.less';

interface Props extends Pick<BaseModalProps, 'title'> {
  requestForm?: any;
  fetchData?: any;
}

export const BatchChangeModal: FC<Props> = ({ requestForm, fetchData }) => {
  const [form] = XlbBasicForm.useForm();
  const modal = NiceModal.useModal();
  const [shareStore, setShareStore] = useState<any[]>([]);
  const [loading, setIsLoading] = useState(false);

  const importStores = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.storename.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.storecodetemplate.download`,
      templateName: '门店导入模板',
    });
    if (res.code !== 0) return;
    // let data = res.data?.stores
    form.setFieldsValue({
      store_ids: res?.data?.store_ids,
      store_names: res?.data?.store_names_str,
    });
  };

  const getDeliveryPriceValues = (checked: any[]) => {
    let deveveryPriceValuesT = cloneDeep(deveveryPriceValues);
    for (const [key, value] of Object.entries(keyMap)) {
      if (!checked?.includes(value)) {
        deveveryPriceValuesT = deveveryPriceValuesT.filter(
          (i) => i.value !== key,
        );
      }
    }
    return deveveryPriceValuesT || [];
  };

  const queryShareStore = async () => {
    const res = await Api.getShareStore({});
    if (res.code == 0) {
      setShareStore(
        res.data?.map((i: any) => ({ value: i.id, label: i.store_name })),
      );
    }
  };

  useEffect(() => {
    queryShareStore();
  }, []);

  const renderCheckBox = (item: string, checked: any[]) => {
    switch (item) {
      case '上游配送中心':
      case '中转配送中心':
        return (
          <XlbInputDialog
            dialogParams={{
              type: 'store',
              // isMultiple: true,
              data: {
                center_flag: true,
                enable_organization: 0,
              },
            }}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_name',
            }}
            width={156}
          />
        );
      case '共享配送中心':
        return (
          <XlbSelect
            style={{ width: 156 }}
            options={shareStore ?? []}
            showSearch
            filterOption={(input, option) => {
              return (
                (
                  `${option!.label ? option!.label.toString() : ''}` as unknown as string
                )
                  .toLowerCase()
                  .includes(input.toLowerCase()) ||
                (
                  `${option!.value ? option!.value.toString() : ''}` as unknown as string
                )
                  .toLowerCase()
                  .includes(input.toLowerCase())
              );
            }}
            allowClear={true}
          />
        );
      case '配送价取值':
        return (
          <XlbSelect
            style={{ width: 156 }}
            options={getDeliveryPriceValues(checked) ?? []}
            showSearch
            allowClear={false}
          />
        );
      default:
        return null;
    }
  };

  const handleOk = async (values: any) => {
    const {
      batch_modify_type,
      checkValue,
      center_store_id,
      transit_store_id,
      share_store_id,
      delivery_price_val,
    } = values;
    if (![0, 1, 2].includes(batch_modify_type)) {
      XlbTipsModal({
        tips: `请选择修改范围`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    if (batch_modify_type == 1 && !values?.store_ids?.length) {
      XlbTipsModal({
        tips: `请选择指定门店`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    if (!checkValue?.length) {
      XlbTipsModal({
        tips: `请选择修改内容`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    if (checkValue?.includes('center_store_id') && !center_store_id?.length) {
      XlbTipsModal({
        tips: `请选择上游配送中心`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    if (checkValue?.includes('transit_store_id') && !transit_store_id?.length) {
      XlbTipsModal({
        tips: `请选择中转配送中心`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    if (checkValue?.includes('share_store_id') && !share_store_id) {
      XlbTipsModal({
        tips: `请选择共享配送中心`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    // if (!checkValue?.includes('delivery_price_val')) {
    //   XlbTipsModal({
    //     tips: `配送价取值必选`,
    //     isConfirm: true,
    //     isCancel: false
    //   })
    //   return
    // }
    if (checkValue?.includes('delivery_price_val') && !delivery_price_val) {
      XlbTipsModal({
        tips: `请选择配送价取值`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    let store_find_condition = {};
    if (batch_modify_type == 1) {
      store_find_condition = { store_ids: values?.store_ids };
      delete values?.store_ids;
    }
    if (batch_modify_type == 2) {
      store_find_condition = requestForm || {};
    }
    // if (batch_modify_type == 0) {
    //   store_find_condition = { company_id: LStorage.get('userInfo')?.company_id }
    // }
    const data = {
      ...values,
      center_store_id: center_store_id?.[0],
      transit_store_id: transit_store_id?.[0],
      share_store_id: share_store_id,
      store_find_condition,
    };
    batch.forEach((i) => {
      if (!checkValue?.includes(i?.value)) {
        delete data[i?.value];
      }
    });
    const res = await Api.batchEdit(data);
    if (res.code == 0) {
      message.success('操作成功');
      modal.resolve(false);
      if (
        form.getFieldValue('checkValue')?.includes('center_store_id') &&
        data.center_store_id > 0
      ) {
        setRecreate({
          ...data,
          ids: res?.data?.ids,
          upstream_center_update_res_dto:
            res?.data?.upstream_center_update_res_dto,
        });
      }
      modal.hide();
      fetchData();
    }
  };
  const setRecreate = async (parameterSaving: any) => {
    if (
      parameterSaving?.upstream_center_update_res_dto?.need_update_store_ids
        ?.length > 0 ||
      parameterSaving?.upstream_center_update_res_dto?.upstream_updates
        ?.length > 0
    ) {
      const { need_update_store_ids, upstream_updates, upstream_center_id } =
        parameterSaving?.upstream_center_update_res_dto;
      const { ids } = parameterSaving;

      // const storeNamesArray = form.getFieldValue('store_names');
      // const updatedIds =
      //   need_update_store_ids?.length > 0
      //     ? need_update_store_ids
      //     : upstream_updates?.length > 0
      //       ? upstream_updates.map((v) => v.store_id)
      //       : [];
      // const updatedStoreNames = updatedIds.map((id) => {
      //   const index = ids.indexOf(id);
      //   return storeNamesArray?.[index] || '';
      // });
      // const storeCondition = updatedIds.map((id: number, index: number) => {
      //   return {
      //     ...parameterSaving,
      //     store_find_condition: {
      //       ids: [id],
      //     },
      //     store_name: updatedStoreNames[index] || '',
      //     upstream_center_id: parameterSaving?.center_store_id,
      //   };
      // });
      let storeCondition = [];
      if (need_update_store_ids?.length > 0) {
        storeCondition = need_update_store_ids.map(
          (id: number, index: number) => {
            return {
              // ...parameterSaving,
              store_find_condition: {
                ids: [id],
              },
              // store_name: updatedStoreNames[index] || '',
              upstream_center_id: upstream_center_id,
            };
          },
        );
      }
      if (upstream_updates?.length > 0) {
        storeCondition = upstream_updates.map((v: any) => {
          return {
            // ...parameterSaving,
            store_find_condition: {
              ids: [v.store_id],
            },
            // store_name: v.store_name,
            upstream_center_id: v.upstream_center_id,
          };
        });
      }
      await XlbTipsModal({
        tips: '是否重新生成修改门店今日未调出的补货单',
        onOkBeforeFunction: async () => {
          NiceModal.show(XlbProgress, {
            requestApi: '/erp/hxl.erp.requestorder.recreate',
            items: storeCondition || [],
            // titleList: updatedStoreNames,
            promptTitle: '正在操作：',
          }).then(() => {
            message.success('操作成功');
            fetchData();
          });
          return true;
        },
      });
    }
  };
  const handleCheck = (checked: any[]) => {
    if (
      !checked?.includes(keyMap?.[form?.getFieldValue('delivery_price_val')])
    ) {
      form.setFieldValue('delivery_price_val', '');
    }
  };
  return (
    <XlbModal
      width={650}
      open={modal.visible}
      title="批量修改"
      isCancel={true}
      onOk={async () => {
        form.submit();
      }}
      onCancel={() => {
        modal.resolve(false);
        modal.hide();
      }}
    >
      <div>
        <XlbBasicForm
          form={form}
          style={{ marginTop: 20 }}
          onFinish={() => {
            const values = form.getFieldsValue(true);
            handleOk(values);
          }}
        >
          <div className={style.box}>
            <p className={style.title}>修改范围</p>
            <XlbBasicForm.Item name="batch_modify_type">
              <Radio.Group style={{ marginTop: 10, marginBottom: 10 }}>
                <Space direction="vertical">
                  <Radio value={0}>全部门店</Radio>
                  <Radio value={2}> 仅查询到的门店</Radio>
                  <Radio value={1}>
                    指定门店
                    <XlbBasicForm.Item
                      name="store_ids"
                      label=""
                      style={{ marginBottom: '0 !important' }}
                    >
                      <XlbInputDialog
                        dialogParams={{
                          type: 'store',
                          isMultiple: true,
                          data: {
                            center_flag: false,
                          },
                        }}
                        // disabled={!params?.enable_auto_stop_request}
                        fieldNames={{
                          idKey: 'id',
                          nameKey: 'store_name',
                        }}
                        onChange={(value, options) => {
                          form.setFieldsValue({
                            store_names: options.map((i: any) => i.store_name),
                          });
                        }}
                        width={156}
                      />
                    </XlbBasicForm.Item>
                    <Button
                      type="primary"
                      size="small"
                      style={{ marginLeft: -15 }}
                      onClick={() => importStores()}
                    >
                      导入门店
                    </Button>
                  </Radio>
                </Space>
              </Radio.Group>
            </XlbBasicForm.Item>
          </div>
          <div className={style.box} style={{ marginBottom: 0 }}>
            <p className={style.title}>修改内容</p>
            <XlbBasicForm.Item name="checkValue" style={{ marginTop: 15 }}>
              <Checkbox.Group style={{ width: '100%' }} onChange={handleCheck}>
                {batch.map((item) => {
                  return (
                    <div key={item.value}>
                      <Checkbox value={item.value}>{item.label}</Checkbox>
                      <XlbBasicForm.Item dependencies={['checkValue']} noStyle>
                        {({ getFieldValue }) => {
                          return (
                            <XlbBasicForm.Item
                              name={item.value}
                              key={item.value}
                            >
                              {renderCheckBox(
                                item.label,
                                getFieldValue('checkValue'),
                              )}
                            </XlbBasicForm.Item>
                          );
                        }}
                      </XlbBasicForm.Item>
                    </div>
                  );
                })}
              </Checkbox.Group>
            </XlbBasicForm.Item>
          </div>
        </XlbBasicForm>
      </div>
    </XlbModal>
  );
};
