import { XlbFetch as ErpRequest } from '@xlb/utils';

export default {
  page: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.deliverycenterstore.page', data),
  batchupdate: async (data: any) =>
    await ErpRequest.post(
      '/erp/hxl.erp.deliverycenterstore.store.update',
      data,
    ),
  delete: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.deliverycenterstore.delete', data),
  save: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.deliverycenterstore.batchupdate', data),
  update: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.deliverycenterstore.update', data),
  detailsExport: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.deliverycenterstore.export', data),
  summaryExport: async (data: any) =>
    await ErpRequest.post(
      '/erp/hxl.erp.deliverycenterstore.storesummary.export',
      data,
    ),
  import: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.commonstorename.import', data),
  short: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.store.short.page', data),
  // 详情
  getDetails: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.deliverycenterstore.find', data),
  // 获取上游配送中心
  getCenterStore: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.store.orgdeliverycenter.find', data),
  // 获取共享仓
  getShareStore: async (data: any) =>
    await ErpRequest.post(
      '/erp/hxl.erp.store.sharedeliverycenter.find',
      data,
    ),
  // 获取中转配送中心
  getTransitStore: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.store.all.shortfind', data),
  // 批量修改
  batchEdit: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.deliverycenterstore.batchedit', data),
};
