import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import {
  XlbButton,
  XlbCheckbox,
  XlbIcon,
  XlbInputDialog,
  XlbInputNumber,
  XlbSelect,
  XlbTooltip,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { Form, Input, message, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less';
import { getSaleParam, saveSaleParam } from './server';
const { Option } = XlbSelect;
const compareOptions = Object.freeze([
  { label: '>=', value: '>=', compare: 'more' },
  { label: '<=', value: '<=', compare: 'less' },
  { label: '>', value: '>', compare: 'more' },
  { label: '<', value: '<', compare: 'less' },
]);
const SaleParam = () => {
  const [form] = Form.useForm();
  const { enable_organization } = useBaseParams((state) => state);
  //门店补货单校验可用库存[门店叫货][统配订单][预定单]
  const [ischecked1, setchecked1] = useState<boolean>(false);
  const [ischecked2, setchecked2] = useState<boolean>(false);
  const [ischecked3, setchecked3] = useState<boolean>(false);
  //门店补货单有效天数
  const [days, setDays] = useState<any>(0);
  //调出单制单有效天数
  const [days2, setDays2] = useState<any>(0);
  // 仓间调拨有效天数
  const [days3, setDays3] = useState<any>(7);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  //门店补货单校验配送日订购上限[门店叫货][统配订单][预定单]
  const [ischecked4, setchecked4] = useState<boolean>(false);
  const [ischecked5, setchecked5] = useState<boolean>(false);
  const [ischecked6, setchecked6] = useState<boolean>(false);
  // //门店补货单支持跨配送中心下单
  // const [ischecked7, setchecked7] = useState<boolean>(false)
  //门店补货单同个配送日商品重复下单提醒
  const [ischecked8, setchecked8] = useState<boolean>(false);
  //非配送中心门店支持跨配送中心相互调拨
  const [ischecked9, setchecked9] = useState<boolean>(false);
  //配送中心门店调入按成本价入库
  const [ischecked0, setchecked0] = useState<boolean>(false);
  //配送中心调出单审核门店自动调入
  const [ischecked11, setchecked11] = useState<boolean>(false);
  //非配送中心门店启用调拨确认
  const [ischecked12, setchecked12] = useState<boolean>(false);
  //非配送中心门店店间调拨允许直营调加盟/加盟调直营
  const [ischecked13, setchecked13] = useState<boolean>(false);
  //非同一组织门店，不允许店间调拨
  const [ischecked14, setchecked14] = useState<boolean>(false);
  // 调出单审核，自动产生调入单，且仅可发起调出单不可发起调入单
  const [ischecked15, setchecked15] = useState<boolean>(false);
  //门店管理未上传营业执照，不允许补货
  const [businessLicenseRequest, setBusinessLicenseRequest] =
    useState<boolean>(false);
  //门店补货单透支额度
  const [balanceTypes, setBalanceTypes] = useState<any>({
    FORCE: false,
    RESERVE: false,
    overdraw_balance: '0.00',
  });
  // 门店申请单统配商品退货售后校验
  const [orderRetuenInfo, setOrderRetuenInfo] = useState<any>({
    enable_nearly_expired: false,
    nearly_expired_opt: '>=',
    nearly_expired_value: null,
    enable_request_order_range: false,
    enable_return_quantity: false,
    return_quantity_opt: '>=',
    enable_first_order: false,
    enable_period: false,
    period_value: null,
  });
  // 门店申请单统配商品退货触发审批
  const [orderRetuenFlow, setOrderRetuenFlow] = useState<any>('');
  // 门店申请单非统配商品退货时，选择原单退货组织
  const [orgIds, setOrgIds] = useState<number[]>([]);
  const [orgList, setOrgList] = useState<any[]>([]);
  // 仓间调拨计划单
  const [warehouseTransferPlanOrgIds, setWarehouseTransferPlanOrgIds] =
    useState<number[]>([]);
  const [warehouseTransferPlanNumber, setWarehouseTransferPlanNumber] =
    useState<any>();
  const [info, setInfo] = useState<any>({
    app_request_recommend_types: [],
    app_new_product_types: [],
  });
  const [StoreSupplyOrderParamInfo, setStoreSupplyOrderParamInfo] =
    useState<any>({});

  const keepTwoDecimalPlaces = (value: any) => {
    const valueTofixed = !isNaN(value)
      ? Number(value)?.toFixed(2) || '0.00'
      : '0.00';
    form.setFieldValue('overdraw_balance', valueTofixed);
    setBalanceTypes({ ...balanceTypes, overdraw_balance: valueTofixed });
  };

  // 保存
  const saveOrder = async () => {
    if (days === '') {
      message.error('门店补货单有效天数请输入0-99整数');
      return;
    }
    if (days2 === '') {
      message.error('调出单制单有效天数请输入0-99整数');
      return;
    }
    if (balanceTypes.overdraw_balance < 0) {
      message.error('门店补货单透支额度不能小于0');
      return;
    }
    if (!info.request_order_paid_duration) {
      message.error('请选择门店补货单付款时长限制');
      return;
    }
    const reg1 = /^[1-9]\d*$/;
    if (!reg1.test(days3)) {
      message.error('仓间调拨有效日期请选择大于0的整数');
      return;
    }
    if (Number.isInteger(days - 0) && days - 0 >= 0 && days - 0 <= 99) {
      if (Number.isInteger(days2 - 0) && days2 - 0 >= 0 && days2 - 0 <= 99) {
        const check_stock_types = [
          ischecked1 ? 'NORMAL' : '',
          ischecked2 ? 'FORCE' : '',
          ischecked3 ? 'RESERVE' : '',
        ];
        const check_order_upper_types = [
          ischecked4 ? 'NORMAL' : '',
          ischecked5 ? 'FORCE' : '',
          ischecked6 ? 'RESERVE' : '',
        ];
        for (var i = 0; i < check_stock_types.length; i++) {
          //这里为过滤的值
          if (check_stock_types[i] == '') {
            check_stock_types.splice(i, 1);
            i = i - 1;
          }
        }
        for (var i = 0; i < check_order_upper_types.length; i++) {
          //这里为过滤的值
          if (check_order_upper_types[i] == '') {
            check_order_upper_types.splice(i, 1);
            i = i - 1;
          }
        }
        const product_types = [info.ITEM, info.STOCK];
        const recommend_types = [info.RECOMMEND, info.DELIVERY_SPECIAL_PRICE];
        info.app_request_recommend_types =
          info.app_request_recommend_types_checked
            ? recommend_types.filter((item) => item)
            : [];
        info.app_new_product_types = info.app_new_product_types_checked
          ? product_types.filter((item) => item)
          : [];

        const data = {
          ...info,
          ...StoreSupplyOrderParamInfo,
          force_delivery_order_return_opt: {
            ...orderRetuenInfo,
          },
          force_delivery_order_return_flow_condition: orderRetuenFlow,
          store_return_org_opts: orgIds,
          warehouse_transfer_valid_days: Math.floor(days3),
          request_order_valid_days: Math.floor(days), //门店补货单有效天数:默认为0，支持录入≥0的整数，最大99
          out_order_valid_days: Math.floor(days2),
          check_stock_types: check_stock_types, //门店补货单校验可用库存
          check_order_upper_types: check_order_upper_types, //门店补货单校验配送日订购上限
          // request_order_other_center: ischecked7,
          request_order_repeat_item_remind: ischecked8,
          un_center_transform: ischecked9,
          center_use_cost_price_in: ischecked0,
          center_out_order_auto_in: ischecked11,
          enable_in_check_order: ischecked12,
          un_center_store_application: ischecked13,
          org_transfer: ischecked14,
          audit_auto_out_order_auto_in: ischecked15,
          business_license_request: businessLicenseRequest,
          direct_delivery_price_type: form.getFieldValue(
            'direct_delivery_price_type',
          ),
          auth_required_store_ids: form.getFieldValue(
            'auth_required_store_ids',
          ),
          limit_create_out_order: form.getFieldValue('limit_create_out_order'),
          delivery_price_type: form.getFieldValue('delivery_price_type'),
          app_component_item_request_by_make_bill:
            info.app_component_item_request_by_make_bill,
          un_request_day: form.getFieldValue('un_request_day'),
          store_application_order_param_required: form.getFieldValue(
            'store_application_order_param_required',
          ),
          check_overdraw_balance_types: Object.keys(balanceTypes).filter(
            (key) => balanceTypes[key] === true && key != 'overdraw_balance',
          ),
          overdraw_balance: form.getFieldValue('overdraw_balance'),
          warehouse_transfer_generated_order_state: form.getFieldValue(
            'warehouse_transfer_generated_order_state',
          ),
          warehouse_transfer_need_plan_org_ids: warehouseTransferPlanOrgIds,
          warehouse_transfer_check_plan_quantity_org_ids:
            warehouseTransferPlanNumber,
        };
        setIsLoading(true);
        const res = await saveSaleParam({
          ...data,
        });
        setIsLoading(false);
        if (res?.code == '0') {
          message.success('操作成功');
        }
      } else {
        message.error('调出单制单有效天数请输入0-99整数');
      }
    } else {
      message.error('门店补货单有效天数请输入0-99整数');
    }
  };
  // 统一处理参数设置
  const parameterSetting = (name: string, value: boolean) => {
    if (name == 'app_request_recommend_types_checked' && !value) {
      setInfo({
        ...info,
        [name]: value,
        RECOMMEND: false,
        DELIVERY_SPECIAL_PRICE: false,
      });
      return;
    }
    if (name == 'app_new_product_types_checked' && !value) {
      setInfo({
        ...info,
        [name]: value,
        STOCK: false,
        ITEM: false,
      });
      return;
    }
    if (
      name == 'RECOMMEND' ||
      name == 'DELIVERY_SPECIAL_PRICE' ||
      name == 'STOCK' ||
      name == 'ITEM'
    ) {
      setInfo({
        ...info,
        [name]: value ? name : '',
      });
    } else {
      setInfo({
        ...info,
        [name]: value,
      });
    }
  };
  // 门店补货展示参考字段 要统计数量进行判断, 单独处理  怕样式不一致没有使用Checkbox.Group  勿喷
  const paramSetting = (name: string, value: boolean) => {
    const newReadParam = Object.keys(StoreSupplyOrderParamInfo).filter(
      (key) => StoreSupplyOrderParamInfo[key],
    );
    // if (newReadParam.length >= 4 && value) {
    //   message.error('最多可设置4个字段')
    //   return false
    // } else {
    setStoreSupplyOrderParamInfo({
      ...StoreSupplyOrderParamInfo,
      [name]: value,
    });
    // }
  };

  type OrderRetuenInfo = boolean | number | null | string;
  const storeRetuenInfoSetting = async (
    name: string,
    value: OrderRetuenInfo,
  ) => {
    setOrderRetuenInfo({
      ...orderRetuenInfo,
      [name]: value,
    });
  };

  //查询
  const getSaleParamInfo = async () => {
    const ress = await getSaleParam({});
    if (ress?.code == 0) {
      const _data = ress.data;
      setchecked1(_data.check_stock_types?.includes('NORMAL'));
      setchecked2(_data.check_stock_types?.includes('FORCE'));
      setchecked3(_data.check_stock_types?.includes('RESERVE'));
      setchecked4(_data.check_order_upper_types?.includes('NORMAL'));
      setchecked5(_data.check_order_upper_types?.includes('FORCE'));
      setchecked6(_data.check_order_upper_types?.includes('RESERVE'));
      setDays(_data.request_order_valid_days);
      setDays2(_data.out_order_valid_days);
      setDays3(_data.warehouse_transfer_valid_days);
      // setchecked7(_data.request_order_other_center)
      setchecked8(_data.request_order_repeat_item_remind);
      setchecked9(_data.un_center_transform);
      setchecked0(_data.center_use_cost_price_in);
      setchecked11(_data.center_out_order_auto_in);
      setchecked12(_data.enable_in_check_order);
      setchecked13(_data.un_center_store_application);
      setchecked14(_data.org_transfer);
      setchecked15(_data.audit_auto_out_order_auto_in);
      setWarehouseTransferPlanOrgIds(
        _data?.warehouse_transfer_need_plan_org_ids,
      );
      setWarehouseTransferPlanNumber(
        _data?.warehouse_transfer_check_plan_quantity_org_ids,
      );
      setBusinessLicenseRequest(_data.business_license_request);
      form.setFieldValue(
        'direct_delivery_price_type',
        _data.direct_delivery_price_type,
      );
      form.setFieldValue(
        'auth_required_store_ids',
        _data.auth_required_store_ids,
      );
      form.setFieldValue(
        'limit_create_out_order',
        _data.limit_create_out_order,
      );
      form.setFieldValue(
        'store_application_order_param_required',
        _data.store_application_order_param_required,
      );
      form.setFieldValue('delivery_price_type', _data.delivery_price_type);
      form.setFieldValue('un_request_day', _data.un_request_day);
      form.setFieldValue(
        'overdraw_balance',
        Number(_data.overdraw_balance).toFixed(2),
      );
      form.setFieldValue(
        'warehouse_transfer_generated_order_state',
        _data.warehouse_transfer_generated_order_state || 'INIT',
      );
      setBalanceTypes({
        FORCE: _data.check_overdraw_balance_types?.includes('FORCE'),
        RESERVE: _data.check_overdraw_balance_types?.includes('RESERVE'),
        overdraw_balance: Number(_data.overdraw_balance).toFixed(2),
      });
      // name == 'RECOMMEND' || name == '' || name == '' || name == ''
      setInfo({
        ..._data,
        item_category_level: _data.item_category_level,
        app_request_recommend_types_checked:
          _data.app_request_recommend_types?.length > 0,
        app_new_product_types_checked: _data.app_new_product_types?.length > 0,
        RECOMMEND: _data.app_request_recommend_types?.includes('RECOMMEND')
          ? 'RECOMMEND'
          : false,
        DELIVERY_SPECIAL_PRICE: _data.app_request_recommend_types?.includes(
          'DELIVERY_SPECIAL_PRICE',
        )
          ? 'DELIVERY_SPECIAL_PRICE'
          : false,
        STOCK: _data.app_new_product_types?.includes('STOCK') ? 'STOCK' : false,
        ITEM: _data.app_new_product_types?.includes('ITEM') ? 'ITEM' : false,
      });
      const readParam = {
        display_available_days: _data.display_available_days,
        display_center_stock_quantity: _data.display_center_stock_quantity,
        display_daily_sale_quantity: _data.display_daily_sale_quantity,
        request_order_show_last_week_same_day_stock:
          _data.request_order_show_last_week_same_day_stock,
        request_order_show7_day_average_sale_stock:
          _data.request_order_show7_day_average_sale_stock,
        display_gross_profit_rate: _data.display_gross_profit_rate,
        display_volume: _data.display_volume,
        display_lower_limit: _data.display_lower_limit,
        display_multiple: _data.display_multiple,
        display_on_way_quantity: _data.display_on_way_quantity,
        display_period: _data.display_period,
        display_sale_price: _data.display_sale_price,
        display_store_stock_quantity: _data.display_store_stock_quantity,
        display_store_turnover_days: _data.display_store_turnover_days,
        display_today_sale_quantity: _data.display_today_sale_quantity,
        display_upper_limit: _data.display_upper_limit,
        display_item_date: _data.display_item_date,
        display_yesterday_sale_quantity: _data.display_yesterday_sale_quantity,
        display7_day_sale_ratio: _data.display7_day_sale_ratio,
        display30_day_sale_ratio: _data.display30_day_sale_ratio,
      };

      setStoreSupplyOrderParamInfo({
        ...readParam,
      });

      setOrderRetuenInfo({
        ..._data.force_delivery_order_return_opt,
        nearly_expired_value:
          _data.force_delivery_order_return_opt?.nearly_expired_value || null,
        period_value:
          _data.force_delivery_order_return_opt?.period_value || null,
      });
      setOrderRetuenFlow(_data.force_delivery_order_return_flow_condition);
      setOrgIds(_data.store_return_org_opts);
    }
  };
  // 获取二级组织
  const getOrgList = async () => {
    const data = {
      level: 2,
    };
    const res = await ErpRequest.post('/erp/hxl.erp.org.find', data);
    if (res?.code == 0) {
      setOrgList(
        res?.data?.map((item: any) => ({ label: item.name, value: item.id })),
      );
    }
  };

  useEffect(() => {
    form.setFieldValue('direct_delivery_price_type', 0);
    form.setFieldValue('warehouse_transfer_generated_order_state', 'INIT');
    getSaleParamInfo();
    getOrgList();
  }, []);

  return (
    <div
      className="container"
      style={{ padding: '12px 5px', height: '100%', overflow: 'auto' }}
    >
      <div className={'button_box row-flex'} style={{ marginLeft: '14px' }}>
        {hasAuth(['配送参数', '编辑']) ? (
          <div style={{ width: '90%' }} className="row-flex">
            <XlbButton
              label="保存"
              disabled={isLoading}
              onClick={saveOrder}
              type="primary"
              icon={<XlbIcon size={16} name="baocun" />}
            />
          </div>
        ) : null}
      </div>
      <div className={styles.code}>
        门店补货单有效天数
        <input
          onChange={(e: any) => setDays(e.target.value.replace(/\s*/g, ''))}
          type="text"
          defaultValue={0}
          disabled={LStorage.get('userInfo')?.company_id == 66666}
          className={styles.period_input}
          value={days}
        />
        天
      </div>

      <div className={styles.code}>
        门店补货单校验可用库存:
        <XlbCheckbox
          checked={ischecked1}
          onChange={(e: any) => setchecked1(e.target.checked)}
          style={{ marginLeft: '10px' }}
        >
          门店叫货
        </XlbCheckbox>
        <XlbCheckbox
          checked={ischecked2}
          onChange={(e: any) => setchecked2(e.target.checked)}
          style={{ marginLeft: '10px' }}
        >
          统配订单
        </XlbCheckbox>
        <XlbCheckbox
          checked={ischecked3}
          onChange={(e: any) => setchecked3(e.target.checked)}
          style={{ marginLeft: '10px' }}
        >
          预订单
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        门店补货单校验配送日订购上限:
        <XlbCheckbox
          checked={ischecked4}
          onChange={(e: any) => setchecked4(e.target.checked)}
          style={{ marginLeft: '10px' }}
        >
          门店叫货
        </XlbCheckbox>
        <XlbCheckbox
          checked={ischecked5}
          onChange={(e: any) => setchecked5(e.target.checked)}
          style={{ marginLeft: '10px' }}
        >
          统配订单
        </XlbCheckbox>
        <XlbCheckbox
          checked={ischecked6}
          onChange={(e: any) => setchecked6(e.target.checked)}
          style={{ marginLeft: '10px' }}
        >
          预订单
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={info.no_display_no_valid_stock_item}
          onChange={(e: any) =>
            parameterSetting('no_display_no_valid_stock_item', e.target.checked)
          }
        >
          门店补货隐藏无可用库存商品
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={info.app_request_no_stock}
          onChange={(e: any) =>
            parameterSetting('app_request_no_stock', e.target.checked)
          }
        >
          APP商品无可用库存不允许补货
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={info.request_order_show_center_stock}
          onChange={(e: any) =>
            parameterSetting(
              'request_order_show_center_stock',
              e.target.checked,
            )
          }
        >
          门店补货单显示中心可用库存
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={info.display_demolition_state}
          onChange={(e: any) =>
            parameterSetting('display_demolition_state', e.target.checked)
          }
        >
          门店补货单支持展示整件、拆零商品属性
        </XlbCheckbox>
      </div>
      {/* <div className={styles.code}>
        <input
          type="checkbox"
          checked={ischecked7}
          onChange={(e: any) => setchecked7(e.target.checked)}
          style={{ marginRight: '10px' }}
        />
        门店补货单支持跨配送中心下单
      </div> */}
      <div className={styles.code}>
        <XlbCheckbox
          checked={ischecked8}
          onChange={(e: any) => setchecked8(e.target.checked)}
        >
          门店补货单同个配送日商品重复下单提醒
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        调出单制单有效天数
        <input
          onChange={(e: any) => setDays2(e.target.value.replace(/\s*/g, ''))}
          type="text"
          defaultValue={0}
          className={styles.period_input}
          value={days2}
        />
        天
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={ischecked9}
          onChange={(e: any) => setchecked9(e.target.checked)}
        >
          非配送中心门店支持跨配送中心相互调拨
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={ischecked0}
          onChange={(e: any) => setchecked0(e.target.checked)}
        >
          配送中心门店调入按成本价入库
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={ischecked11}
          onChange={(e: any) => setchecked11(e.target.checked)}
          disabled={ischecked15 && enable_organization}
        >
          调出单审核，非配送中心门店自动调入
        </XlbCheckbox>
      </div>
      {!enable_organization ? null : (
        <div className={styles.code}>
          <XlbCheckbox
            checked={ischecked15}
            onChange={(e: any) => {
              setchecked15(e.target.checked);
              if (e.target.checked) setchecked11(true);
            }}
          >
            调出单审核，自动产生调入单，且仅可发起调出单不可发起调入单
          </XlbCheckbox>
          <Tooltip
            overlayClassName={styles.tooltipCustom}
            title={'不区分调出门店和调入门店是否为非/配送中心店'}
          >
            <span>
              <XlbIcon
                color="#979faa"
                style={{ marginLeft: '5px' }}
                hoverColor="#3D66FE"
                name="bangzhu"
                size={16}
              />
            </span>
          </Tooltip>
        </div>
      )}
      <div className={styles.code}>
        <XlbCheckbox
          checked={ischecked12}
          onChange={(e: any) => setchecked12(e.target.checked)}
        >
          非配送中心门店启用调拨确认
        </XlbCheckbox>
      </div>
      {/* <div className={styles.code}>
        <XlbCheckbox checked={ischecked13} disabled={enable_organization} onChange={(e: any) => setchecked13(e.target.checked)}>
          非配送中心门店“直调”&“店间调拨”允许直营调加盟/加盟调直营
        </XlbCheckbox>
        <Tooltip
          overlayClassName={styles.tooltipCustom}
          title={
            '勾选后，非配送中心的门店申请单，“直调申请”&“店间调拨”单据，直营店与加盟店之间允许调拨，否则不可调拨'
          }
        >
          <span>
            <XlbIcon
              color="#979faa"
              style={{ marginLeft: '5px' }}
              hoverColor="#3D66FE"
              name="bangzhu"
              size={16}
            />
          </span>
        </Tooltip>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={ischecked14}
          disabled={enable_organization}
          onChange={(e: any) => setchecked14(e.target.checked)}
        >
          直营门店非同一组织，不允许店间调拨
        </XlbCheckbox>
        <Tooltip
          overlayClassName={styles.tooltipCustom}
          title={
            '勾选后，门店申请单中，直营门店之间的直调申请与店间调拨，“申请门店”与“调往门店”需要归属于同一个组织，否则不允许操作'
          }
        >
          <span>
            <XlbIcon
              color="#979faa"
              style={{ marginLeft: '5px' }}
              hoverColor="#3D66FE"
              name="bangzhu"
              size={16}
            />
          </span>
        </Tooltip>
      </div> */}
      <div className={styles.code}>
        <span style={{ marginRight: '10px' }}>直配采购订单商品价格取值</span>
        <Form colon form={form} autoComplete={'off'} layout={'inline'}>
          <Form.Item name={'direct_delivery_price_type'}>
            <XlbSelect style={{ width: 100 }}>
              <Option key={'abc1'} value={0}>
                采购价
              </Option>
              <Option key={'abc2'} value={1}>
                配送价
              </Option>
            </XlbSelect>
          </Form.Item>
        </Form>
      </div>
      <div className={styles.code}>
        门店补货单透支额度:
        <XlbCheckbox
          checked={balanceTypes.FORCE}
          onChange={(e: any) =>
            setBalanceTypes({ ...balanceTypes, FORCE: e.target.checked })
          }
          style={{ marginLeft: '10px' }}
        >
          统配订单
        </XlbCheckbox>
        <XlbCheckbox
          checked={balanceTypes.RESERVE}
          onChange={(e: any) =>
            setBalanceTypes({ ...balanceTypes, RESERVE: e.target.checked })
          }
          style={{ marginLeft: '10px' }}
        >
          预定单
        </XlbCheckbox>
        <Form colon form={form} autoComplete={'off'} layout={'inline'}>
          <Form.Item name={'overdraw_balance'}>
            <Input
              // onChange={(e: any) => setBalanceTypes({...balanceTypes,overdraw_balance:e.target.value.replace(/\s*/g, '')})}
              onBlur={(e: any) => keepTwoDecimalPlaces(e.target.value)}
              min={0}
              defaultValue={0}
              // precision={2}
              style={{ width: '100px' }}
              className={styles.period_input}
              // value={balanceTypes.overdraw_balance}
            />
          </Form.Item>
        </Form>
        元
      </div>
      <div className={styles.code}>
        超期未补货天数{' '}
        <Form colon form={form} autoComplete={'off'} layout={'inline'}>
          <Form.Item name={'un_request_day'}>
            <Input
              // onBlur={(e: any) => keepTwoDecimalPlaces(e.target.value)}
              min={0}
              defaultValue={0}
              // precision={2}
              style={{ width: '100px' }}
              className={styles.period_input}
            />
          </Form.Item>
        </Form>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={businessLicenseRequest}
          onChange={(e: any) => setBusinessLicenseRequest(e.target.checked)}
        >
          门店管理未上传营业执照，不允许补货
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={info.app_component_item_request_by_make_bill}
          onChange={(e: any) =>
            parameterSetting(
              'app_component_item_request_by_make_bill',
              e.target.checked,
            )
          }
        >
          APP成分商品按制单组合补货
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={info.app_new_product_types_checked}
          onChange={(e: any) =>
            parameterSetting('app_new_product_types_checked', e.target.checked)
          }
        >
          APP补货启用新品分类:
        </XlbCheckbox>
        <XlbCheckbox
          checked={info.ITEM}
          disabled={!info.app_new_product_types_checked}
          onChange={(e: any) => parameterSetting('ITEM', e.target.checked)}
          style={{ marginLeft: '10px' }}
        >
          档案新品
        </XlbCheckbox>
        <XlbCheckbox
          checked={info.STOCK}
          disabled={!info.app_new_product_types_checked}
          onChange={(e: any) => parameterSetting('STOCK', e.target.checked)}
          style={{ marginLeft: '10px' }}
        >
          无库存记录商品
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={info.app_request_recommend_types_checked}
          onChange={(e: any) =>
            parameterSetting(
              'app_request_recommend_types_checked',
              e.target.checked,
            )
          }
        >
          APP补货启用推荐商品分类:
        </XlbCheckbox>
        <XlbCheckbox
          checked={info.RECOMMEND}
          disabled={!info.app_request_recommend_types_checked}
          onChange={(e: any) => parameterSetting('RECOMMEND', e.target.checked)}
          style={{ marginLeft: '10px' }}
        >
          推荐商品
        </XlbCheckbox>
        <XlbCheckbox
          disabled={!info.app_request_recommend_types_checked}
          checked={info.DELIVERY_SPECIAL_PRICE}
          onChange={(e: any) =>
            parameterSetting('DELIVERY_SPECIAL_PRICE', e.target.checked)
          }
          style={{ marginLeft: '10px' }}
        >
          配送特价商品
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={info.app_request_end_cap_category}
          onChange={(e: any) =>
            parameterSetting('app_request_end_cap_category', e.target.checked)
          }
        >
          APP补货启用端头商品分类
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={info.must_sell}
          onChange={(e: any) => parameterSetting('must_sell', e.target.checked)}
        >
          APP补货启用必卖品商品分类
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        门店补货单付款时长限制（分钟）：
        <XlbSelect
          style={{ width: 80 }}
          onChange={(e: any) =>
            parameterSetting('request_order_paid_duration', e)
          }
          value={info.request_order_paid_duration}
        >
          <XlbSelect.Option value={15}>15</XlbSelect.Option>
          <XlbSelect.Option value={30}>30</XlbSelect.Option>
          <XlbSelect.Option value={60}>60</XlbSelect.Option>
          <XlbSelect.Option value={90}>90</XlbSelect.Option>
          <XlbSelect.Option value={120}>120</XlbSelect.Option>
        </XlbSelect>
      </div>
      <div className={styles.code}>
        APP门店补货显示分类级别：
        <XlbSelect
          value={info.item_category_level}
          onChange={(e: any) => parameterSetting('item_category_level', e)}
          style={{ width: 160 }}
        >
          <Option key={'key1'} value={0}>
            全部分类
          </Option>
          <Option key={'key2'} value={1}>
            一级分类
          </Option>
          <Option key={'key3'} value={2}>
            一、二级分类
          </Option>
        </XlbSelect>
      </div>
      <div style={{ display: 'flex', paddingLeft: 14, alignItems: 'center' }}>
        <div
          style={{
            flex: '0 0 135px',
            alignItems: 'flex-start',
            paddingTop: 10,
          }}
        >
          门店补货展示参考字段:{' '}
        </div>
        <div
          className={styles.code}
          style={{ paddingLeft: 0, flexWrap: 'wrap' }}
        >
          <div>
            <XlbCheckbox
              checked={
                !!StoreSupplyOrderParamInfo.request_order_show7_day_average_sale_stock
              }
              onChange={(e: any) =>
                paramSetting(
                  'request_order_show7_day_average_sale_stock',
                  e.target.checked,
                )
              }
              style={{ marginLeft: '10px' }}
            >
              7日均销
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_daily_sale_quantity}
              onChange={(e: any) =>
                paramSetting('display_daily_sale_quantity', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              14日均销
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={
                !!StoreSupplyOrderParamInfo.request_order_show_last_week_same_day_stock
              }
              onChange={(e: any) =>
                paramSetting(
                  'request_order_show_last_week_same_day_stock',
                  e.target.checked,
                )
              }
              style={{ marginLeft: '10px' }}
            >
              上周同日
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_store_stock_quantity}
              onChange={(e: any) =>
                paramSetting('display_store_stock_quantity', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              库存
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_on_way_quantity}
              onChange={(e: any) =>
                paramSetting('display_on_way_quantity', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              在途
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_today_sale_quantity}
              onChange={(e: any) =>
                paramSetting('display_today_sale_quantity', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              今日销量
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={
                !!StoreSupplyOrderParamInfo.display_center_stock_quantity
              }
              onChange={(e: any) =>
                paramSetting('display_center_stock_quantity', e.target.checked)
              }
              disabled={!info?.request_order_show_center_stock}
              style={{ marginLeft: '10px' }}
            >
              中心库存
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_lower_limit}
              onChange={(e: any) =>
                paramSetting('display_lower_limit', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              订购下限
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_upper_limit}
              onChange={(e: any) =>
                paramSetting('display_upper_limit', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              订购上限
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_multiple}
              onChange={(e: any) =>
                paramSetting('display_multiple', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              订购倍数
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_period}
              onChange={(e: any) =>
                paramSetting('display_period', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              保质期
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={
                !!StoreSupplyOrderParamInfo.display_yesterday_sale_quantity
              }
              onChange={(e: any) =>
                paramSetting(
                  'display_yesterday_sale_quantity',
                  e.target.checked,
                )
              }
              style={{ marginLeft: '10px' }}
            >
              昨日销量
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_store_turnover_days}
              onChange={(e: any) =>
                paramSetting('display_store_turnover_days', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              本店周转
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_available_days}
              onChange={(e: any) =>
                paramSetting('display_available_days', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              可用天数
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_sale_price}
              onChange={(e: any) =>
                paramSetting('display_sale_price', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              零售价
            </XlbCheckbox>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_gross_profit_rate}
              onChange={(e: any) =>
                paramSetting('display_gross_profit_rate', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              毛利率
            </XlbCheckbox>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              position: 'relative',
            }}
          >
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_item_date}
              onChange={(e: any) =>
                paramSetting('display_item_date', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              商品日期
            </XlbCheckbox>
            <XlbTooltip title={'仅使用wms时有效'}>
              <XlbIcon
                color="#979faa"
                hoverColor="#3D66FE"
                name="bangzhu"
                size={16}
              />
            </XlbTooltip>
          </div>
          <div>
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display_volume}
              onChange={(e: any) =>
                paramSetting('display_volume', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              商品体积
            </XlbCheckbox>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              position: 'relative',
            }}
          >
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display7_day_sale_ratio}
              onChange={(e: any) =>
                paramSetting('display7_day_sale_ratio', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              7日配销比
            </XlbCheckbox>
            <XlbTooltip title={'近7日配销比=近7日配送额/近7日销售额'}>
              <XlbIcon
                color="#979faa"
                hoverColor="#3D66FE"
                name="bangzhu"
                size={16}
              />
            </XlbTooltip>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              position: 'relative',
            }}
          >
            <XlbCheckbox
              checked={!!StoreSupplyOrderParamInfo.display30_day_sale_ratio}
              onChange={(e: any) =>
                paramSetting('display30_day_sale_ratio', e.target.checked)
              }
              style={{ marginLeft: '10px' }}
            >
              30日配销比
            </XlbCheckbox>
            <XlbTooltip title={'近30日配销比=近30日配送额/近30日销售额'}>
              <XlbIcon
                color="#979faa"
                hoverColor="#3D66FE"
                name="bangzhu"
                size={16}
              />
            </XlbTooltip>
          </div>
        </div>
      </div>
      <div className={styles.code}>
        <span style={{ marginRight: '10px' }}>
          门店补货单生成的调出单价格取值
        </span>
        <Form colon form={form} autoComplete={'off'} layout={'inline'}>
          <Form.Item name={'delivery_price_type'}>
            <XlbSelect style={{ width: 160 }}>
              <Option key={'实时配送价'} value={0}>
                实时配送价
              </Option>
              <Option key={'门店补货单配送价'} value={1}>
                门店补货单配送价
              </Option>
            </XlbSelect>
          </Form.Item>
        </Form>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={info.hide_delivery_date}
          onChange={(e: any) =>
            parameterSetting('hide_delivery_date', e.target.checked)
          }
        >
          门店补货隐藏配送日期
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <Form form={form}>
          <Form.Item
            name="auth_required_store_ids"
            label="门店补货审核电子合同未签署限制"
            style={{ marginBottom: 0 }}
          >
            <XlbInputDialog
              dialogParams={{
                type: 'store',
                isMultiple: true,
                data: {
                  center_flag: true,
                },
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: 'store_name',
              }}
              width={260}
            />
          </Form.Item>
        </Form>
      </div>
      <div className={styles.code}>
        <Form form={form}>
          <Form.Item
            name="limit_create_out_order"
            label="配货额度小于单据额度时允许创建调入调出单"
            style={{ marginBottom: 0 }}
          >
            <XlbInputDialog
              treeModalConfig={{
                title: '选择组织',
                topLevelTreeDisabled: true,
                url: '/erp/hxl.erp.org.find',
                dataType: 'lists',
                checkable: true, // 是否多选
                primaryKey: 'id',
                params: {
                  level: 3,
                },
              }}
              width={260}
            />
          </Form.Item>
        </Form>
      </div>
      <div className={styles.code}>
        <span style={{ marginRight: '10px' }}>仓间调拨生成的下游单据状态</span>
        <Form colon form={form} autoComplete={'off'} layout={'inline'}>
          <Form.Item name={'warehouse_transfer_generated_order_state'}>
            <XlbSelect style={{ width: 160 }}>
              <Option key={'INIT'} value={'INIT'}>
                制单
              </Option>
              <Option key={'AUDIT'} value={'AUDIT'}>
                审核
              </Option>
            </XlbSelect>
          </Form.Item>
        </Form>
      </div>
      <div className={styles.code}>
        仓间调拨有效期天数
        <input
          onChange={(e: any) => setDays3(e.target.value.replace(/\s*/g, ''))}
          type="text"
          // defaultValue={0}
          min={1}
          className={styles.period_input}
          value={days3}
        />
        天
      </div>
      <div className={styles.code}>
        <Form form={form}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            门店申请单品控参数必填限制
            <XlbTooltip
              title={
                '选择组织后该组织下的用户拥有退货申请权限(质量原因退货)在门店审批单中需要填写品控参数'
              }
            >
              <XlbIcon
                style={{ margin: '0 8px' }}
                color="#979faa"
                hoverColor="#3D66FE"
                name="bangzhu"
                size={16}
              />
            </XlbTooltip>
            <Form.Item
              name="store_application_order_param_required"
              style={{ marginBottom: 0 }}
            >
              <XlbInputDialog
                // dialogParams={{
                //   type: 'organization',
                //   dataType: 'lists',
                //   isMultiple: true,
                //   isLeftColumn: false,
                //   data: { level: 3 }
                // }}
                treeModalConfig={{
                  title: '选择组织',
                  topLevelTreeDisabled: true,
                  url: '/erp/hxl.erp.org.tree',
                  dataType: 'lists',
                  checkable: true, // 是否多选
                  primaryKey: 'id',
                  params: {
                    level: 2,
                  },
                }}
                width={260}
              />
            </Form.Item>
          </div>
        </Form>
      </div>
      <div className={styles.code}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          门店申请单统配商品退货售后校验：
          <div className="flex-wrap" style={{ flex: 1 }}>
            <div className="v-flex">
              <XlbCheckbox
                checked={orderRetuenInfo.enable_nearly_expired}
                onChange={(e: any) =>
                  storeRetuenInfoSetting(
                    'enable_nearly_expired',
                    e.target.checked,
                  )
                }
                style={{ marginLeft: '10px' }}
              >
                临期时间
              </XlbCheckbox>
              <XlbSelect
                style={{ width: 80 }}
                onChange={(e: any) =>
                  storeRetuenInfoSetting('nearly_expired_opt', e)
                }
                value={orderRetuenInfo.nearly_expired_opt}
              >
                {compareOptions.map((item) => (
                  <XlbSelect.Option key={item.value} value={item.value}>
                    {item.label}
                  </XlbSelect.Option>
                ))}
              </XlbSelect>
              <XlbInputNumber
                style={{ width: 60, margin: '0 8px' }}
                precision={0}
                min={1}
                onChange={(e) =>
                  storeRetuenInfoSetting('nearly_expired_value', e)
                }
                value={orderRetuenInfo.nearly_expired_value}
              />
              天
              <XlbTooltip title={'距离过期日期有多少天'}>
                <XlbIcon
                  style={{ margin: '0 8px' }}
                  color="#979FAA"
                  hoverColor="#3D66FE"
                  name="bangzhu"
                  size={16}
                />
              </XlbTooltip>
            </div>
            <div className="v-flex" style={{ marginRight: 8 }}>
              <XlbCheckbox
                checked={orderRetuenInfo.enable_request_order_range}
                onChange={(e: any) =>
                  storeRetuenInfoSetting(
                    'enable_request_order_range',
                    e.target.checked,
                  )
                }
                style={{ marginLeft: '10px' }}
              >
                没有进行过二次补单
              </XlbCheckbox>
              <XlbTooltip title={'12个月内门店没有补货过该品'}>
                <XlbIcon
                  color="#979faa"
                  hoverColor="#3D66FE"
                  name="bangzhu"
                  size={16}
                />
              </XlbTooltip>
            </div>
            <div className="v-flex" style={{ marginRight: 8 }}>
              <XlbCheckbox
                checked={orderRetuenInfo.enable_return_quantity}
                onChange={(e: any) =>
                  storeRetuenInfoSetting(
                    'enable_return_quantity',
                    e.target.checked,
                  )
                }
                style={{ marginLeft: '10px' }}
              >
                退货数量
              </XlbCheckbox>
              <XlbSelect
                style={{ width: 80, marginRight: 8 }}
                onChange={(e: any) =>
                  storeRetuenInfoSetting('return_quantity_opt', e)
                }
                value={orderRetuenInfo.return_quantity_opt}
              >
                {compareOptions.map((item) => (
                  <XlbSelect.Option key={item.value} value={item.value}>
                    {item.label}
                  </XlbSelect.Option>
                ))}
              </XlbSelect>
              剩余库存数量
            </div>
            <div className="v-flex" style={{ marginRight: 8 }}>
              <XlbCheckbox
                checked={orderRetuenInfo.enable_first_order}
                onChange={(e: any) =>
                  storeRetuenInfoSetting('enable_first_order', e.target.checked)
                }
                style={{ marginLeft: '10px' }}
              >
                首单可退
              </XlbCheckbox>
              <XlbTooltip title={'新品统配首单可以退货'}>
                <XlbIcon
                  color="#979faa"
                  hoverColor="#3D66FE"
                  name="bangzhu"
                  size={16}
                />
              </XlbTooltip>
            </div>
            <div className="v-flex">
              <XlbCheckbox
                checked={orderRetuenInfo.enable_period}
                onChange={(e: any) =>
                  storeRetuenInfoSetting('enable_period', e.target.checked)
                }
                style={{ marginLeft: '10px' }}
              >
                商品的保质期超过
              </XlbCheckbox>
              <XlbInputNumber
                style={{ width: 60, marginRight: 8 }}
                precision={0}
                min={1}
                onChange={(e) => storeRetuenInfoSetting('period_value', e)}
                value={orderRetuenInfo.period_value}
              />
              个月
            </div>
          </div>
        </div>
      </div>
      <div className={styles.code}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          门店申请单统配商品退货触发审批：
          <div className="v-flex">
            单品超过
            <XlbInputNumber
              style={{ width: 60, margin: '0 8px' }}
              precision={2}
              min={1}
              onChange={(e) => setOrderRetuenFlow(e)}
              value={orderRetuenFlow}
            />
            元触发审批
          </div>
        </div>
      </div>
      <div className={styles.code}>
        <div
          style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}
        >
          门店申请单非统配商品退货时，选择原单退货：
          <XlbSelect
            value={orgIds}
            mode="multiple"
            allowClear
            size="small"
            style={{ width: '124px', marginLeft: '8px' }}
            placeholder="请选择"
            options={orgList}
            onChange={setOrgIds}
          />
        </div>
      </div>
      <div className={styles.code}>
        <div
          style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}
        >
          仓间调拨必须通过计划单生成
          <XlbSelect
            value={warehouseTransferPlanOrgIds}
            mode="multiple"
            allowClear
            size="small"
            style={{ width: '180px', marginLeft: '8px' }}
            placeholder="请选择"
            options={orgList}
            filterOption={(input, option) => {
              return (
                (
                  `${option!.label ? option!.label.toString() : ''}` as unknown as string
                )
                  .toLowerCase()
                  .includes(input.toLowerCase()) ||
                (
                  `${option!.value ? option!.value.toString() : ''}` as unknown as string
                )
                  .toLowerCase()
                  .includes(input.toLowerCase())
              );
            }}
            onChange={(value) => {
              setWarehouseTransferPlanOrgIds(value);
            }}
          />
          <XlbTooltip
            title={
              '做仓间调拨单时若调入门店为该参数配置下二级组织货主内的门店则必选关联原计划单'
            }
          >
            <XlbIcon
              color="#979faa"
              style={{ marginLeft: '5px' }}
              hoverColor="#3D66FE"
              name="bangzhu"
              size={16}
            ></XlbIcon>
          </XlbTooltip>
        </div>
      </div>
      <div className={styles.code}>
        <div
          style={{ display: 'flex', alignItems: 'center', marginBottom: 10 }}
        >
          仓间调拨数量必须小于计划单剩余数量
          <XlbSelect
            value={warehouseTransferPlanNumber}
            allowClear
            mode="multiple"
            size="small"
            style={{ width: '180px', marginLeft: '8px' }}
            placeholder="请选择"
            options={orgList}
            filterOption={(input, option) => {
              return (
                (
                  `${option!.label ? option!.label.toString() : ''}` as unknown as string
                )
                  .toLowerCase()
                  .includes(input.toLowerCase()) ||
                (
                  `${option!.value ? option!.value.toString() : ''}` as unknown as string
                )
                  .toLowerCase()
                  .includes(input.toLowerCase())
              );
            }}
            onChange={(e) => {
              setWarehouseTransferPlanNumber(e);
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default SaleParam;
