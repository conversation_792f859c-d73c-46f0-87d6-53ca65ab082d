import { columnWidthEnum } from '@/constants/index';
import { LStorage } from '@/utils/storage';
import {
  SearchFormType,
  XlbBasicForm,
  XlbCheckbox,
  XlbTableColumnProps,
} from '@xlb/components';

export const formList: SearchFormType[] = [
  {
    width: 392,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
  },
  {
    label: '调出组织',
    name: 'out_org_ids',
    type: 'select',
    multiple: true,
    clear: true,
    check: true,
    options: [],
    // @ts-ignore
    onChange: (e: any, formData: any) => {
      console.log(e, formData, 'e11');
      if (e?.length > 0) {
        formData.setFieldsValue({
          out_store_ids: [],
        });
      }
    },
    selectRequestParams: (params: Record<string, any>) => {
      return {
        url: '/erp/hxl.erp.org.find',
        responseTrans(data) {
          const options = data.map((item: any) => {
            const obj = {
              label: item.name,
              value: item.id,
            };
            return obj;
          });
          return options;
        },
      };
    },
  },
  {
    label: '调出门店',
    name: 'out_store_ids',
    type: 'inputDialog',
    dependencies: ['out_org_ids'],
    dialogParams: (params: any) => {
      return {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        url: LStorage?.get('userInfo')?.store?.enable_delivery_center
          ? '/erp/hxl.erp.store.short.page'
          : '/erp/hxl.erp.store.center.find',
        isMultiple: true,
        primaryKey: 'id',
        data: {
          center_flag: LStorage?.get('userInfo')?.store?.enable_delivery_center
            ? true
            : undefined,
        },
      };
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '调出仓库',
    name: 'storehouse_id',
    type: 'select',
    dependencies: ['out_store_ids'],
    dropdownStyle: { width: 200 },
    handleDefaultValue: (data: any, formData: any) => {
      if (data?.length === 0) {
        return null;
      }
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return defaultStoreHouse?.value;
    },
    // @ts-ignore
    disabled: (form: any) => {
      const store_ids = form.getFieldValue('out_store_ids');
      return !store_ids || store_ids?.length > 1;
    },
    // @ts-ignore
    selectRequestParams: (params: any, form: any) => {
      form?.setFieldValue('storehouse_id', null);
      if (params?.out_store_ids?.length == 1) {
        return {
          url: '/erp/hxl.erp.storehouse.store.find',
          postParams: {
            store_id: params?.out_store_ids?.[0],
          },
          responseTrans(data) {
            const options = data
              .filter((v: any) => v.distribution)
              .map((item: any) => ({
                label: item.name,
                value: item.id,
                default_flag: item.default_flag,
              }));
            return options;
          },
        };
      }
    },
  },
  {
    label: '调入组织',
    name: 'in_org_ids',
    type: 'select',
    multiple: true,
    clear: true,
    check: true,
    options: [],
    // @ts-ignore
    onChange: (e: any, formData: any) => {
      if (e?.length > 0) {
        formData.setFieldsValue({
          in_store_ids: [],
        });
      }
    },
    selectRequestParams: (params: Record<string, any>) => {
      return {
        url: '/erp/hxl.erp.org.find',
        responseTrans(data) {
          const options = data.map((item: any) => {
            const obj = {
              label: item.name,
              value: item.id,
            };
            return obj;
          });
          return options;
        },
      };
    },
  },
  {
    label: '调入门店',
    name: 'in_store_ids',
    type: 'inputDialog',
    dependencies: ['in_org_ids'],
    dialogParams: (params: any) => {
      return {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        url: !LStorage?.get('userInfo')?.store?.enable_delivery_center
          ? '/erp/hxl.erp.store.short.page'
          : '/erp/hxl.erp.store.all.shortfind',
        primaryKey: 'id',
        data: {
          status: true,
        },
      };
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
      resetForm: true,
    },
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      // @ts-ignore
      title: '选择商品分类', // 标题
      url: '/erp/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    },
  },
  {
    label: '单据号',
    name: 'request_order_fid',
    type: 'input',
    clear: true,
    check: true,
    tooltip: '单据号不受其他查询条件限制',
  },
  {
    label: '订单类型',
    name: 'request_order_type',
    type: 'select',
    multiple: true,
    clear: true,
    check: true,
    options: [
      {
        label: '门店叫货',
        value: 'NORMAL',
      },
      {
        label: '预定单',
        value: 'RESERVE',
      },
      {
        label: '统配订单',
        value: 'FORCE',
      },
    ],
  },
  {
    label: '汇总条件',
    name: 'summary',
    type: 'select',
    clear: true,
    check: true,
    options: [
      {
        label: '审核日期',
        value: 'AUDIT_TIME',
      },
    ],
  },
  {
    label: '过滤',
    name: 'checkValue',
    type: 'custom',
    render(itemData, form, itemSetting) {
      return (
        <XlbBasicForm.Item name={'checkValue'} label="过滤">
          <XlbCheckbox.Group>
            <XlbCheckbox value="filter_not_out_store_request_order">
              过滤未调出的门店补货单数据
            </XlbCheckbox>
            <XlbCheckbox value="filter_not_actual_hundred_rate">
              多规格商品实时库存、销量按主规格商品统计
            </XlbCheckbox>
          </XlbCheckbox.Group>
        </XlbBasicForm.Item>
      );
    },
  },
];

export const StoreGoodsArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '门店补货单',
    code: 'request_order_fid',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出仓库',
    code: 'storehouse_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'in_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '配送日',
    code: 'delivery_date',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '配送单位',
    code: 'delivery_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '采购员',
    code: 'purchase_by',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '要货数量',
    code: 'request_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '要货基本数量',
    code: 'basic_request_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '缺货数量',
    code: 'shortage_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '缺货基本数量',
    code: 'basic_shortage_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '预发数量',
    code: 'pre_delivery_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '预发基本数量',
    code: 'basic_pre_delivery_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '预发率',
    code: 'pre_delivery_rate',
    width: 90,
    align: 'right',
    features: { sortable: true, format: 'COMPARE' },
  },
  {
    name: '实发数量',
    code: 'delivery_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发基本数量',
    code: 'basic_delivery_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发缺货数量',
    code: 'delivery_shortage_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发缺货基本数量',
    code: 'basic_delivery_shortage_quantity',
    width: 160,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发率',
    code: 'delivery_rate',
    width: 90,
    align: 'right',
    features: { sortable: true, format: 'COMPARE' },
  },
];

export const GoodsArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出仓库',
    code: 'storehouse_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '审核日期',
    code: 'audit_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '配送单位',
    code: 'delivery_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '采购员',
    code: 'purchase_by',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '要货数量',
    code: 'request_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '要货基本数量',
    code: 'basic_request_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '缺货数量',
    code: 'shortage_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '缺货基本数量',
    code: 'basic_shortage_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '预发数量',
    code: 'pre_delivery_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '预发基本数量',
    code: 'basic_pre_delivery_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '预发率',
    code: 'pre_delivery_rate',
    width: 90,
    align: 'right',
    features: { sortable: true, format: 'COMPARE' },
  },
  {
    name: '实发数量',
    code: 'delivery_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发基本数量',
    code: 'basic_delivery_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发缺货数量',
    code: 'delivery_shortage_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发缺货基本数量',
    code: 'basic_delivery_shortage_quantity',
    width: 160,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发率',
    code: 'delivery_rate',
    width: 90,
    align: 'right',
    features: { sortable: true, format: 'COMPARE' },
  },
];

export const StoreArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出仓库',
    code: 'storehouse_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'in_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '审核日期',
    code: 'audit_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '要货数量',
    code: 'request_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '要货基本数量',
    code: 'basic_request_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '缺货数量',
    code: 'shortage_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '缺货基本数量',
    code: 'basic_shortage_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '预发数量',
    code: 'pre_delivery_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '预发基本数量',
    code: 'basic_pre_delivery_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '预发率',
    code: 'pre_delivery_rate',
    width: 90,
    align: 'right',
    features: { sortable: true, format: 'COMPARE' },
  },
  {
    name: '实发数量',
    code: 'delivery_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发基本数量',
    code: 'basic_delivery_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发缺货数量',
    code: 'delivery_shortage_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发缺货基本数量',
    code: 'basic_delivery_shortage_quantity',
    width: 160,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '实发率',
    code: 'delivery_rate',
    width: 90,
    align: 'right',
    features: { sortable: true, format: 'COMPARE' },
  },
];

export const djTotalArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '调出单号',
    code: 'delivery_out_fid',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'in_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出数量',
    code: 'out_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '调出金额',
    code: 'out_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '调入数量',
    code: 'in_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '调入金额',
    code: 'in_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '在途数量',
    code: 'on_way_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '在途金额',
    code: 'on_way_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '单据备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
  }
];