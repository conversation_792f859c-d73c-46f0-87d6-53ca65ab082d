// import { useKeepAliveRefresh } from '@/hooks'
import { orderStatusIcons } from '@/components/data';
import { useBaseParams } from '@/hooks/useBaseParams';
import useDownload from '@/hooks/useDownload';
import { formatWithCommas, hasAuth } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import { LStorage } from '@/utils/storage';
import toFixed from '@/utils/toFixed';
import { XlbTable, XlbTableColumnProps } from '@xlb/components';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbDatePicker,
  XlbDropdownButton,
  XlbIcon,
  XlbInput,
  XlbInputDialog,
  XlbMessage,
  XlbModal,
  XlbPrintModal,
  XlbShortTable,
  XlbTable as XLBTable,
  XlbTabs,
  XlbTipsModal,
  XlbTooltip,
} from '@xlb/components';
import { useNavigation } from '@xlb/max';
import { XlbFetch as ErpRequest, XlbFetch } from '@xlb/utils';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { useAliveController } from 'react-activation';
import { v4 as uuidv4 } from 'uuid';
import CountdownBox from '../components/Countdown';
import ImgModel from '../components/imgModel';
import SelectDiscountedProducts from '../components/SelectDiscountedProducts';
import { _itemTableListCopy, deliveryState, goodDataColumns, itemLogisticsMode } from '../data';
import {
  addInfo,
  auditInfo,
  checkAuditInfo,
  deliveryparamRead,
  getCenterAble,
  getItemInfo,
  getStoreAmout,
  lockDetail,
  payInfo,
  print,
  readInfo,
  unlockDetail,
  updateInfo,
} from '../item/server';
import styles from './index.less';

import { wujieBus } from '@/wujie/utils';
import { XlbButton } from '@xlb/components';
import { Col, ConfigProvider, Row } from 'antd';

const columns: XlbTableColumnProps<any>[] = [
  {
    name: '',
    code: 'type',
    width: 60,
    align: 'center',
    features: { autoRowSpan: true },
  },
  {
    name: '',
    code: 'type1',
    width: 60,
  },
  {
    name: '要货',
    code: 'quantity',
    width: 112,
    align: 'center',
  },
  {
    name: '预发',
    code: 'actual_quantity',
    width: 112,
    align: 'center',
  },
  {
    name: '实发',
    code: 'delivery_quantity',
    width: 112,
    align: 'center',
  },
];

const DeliveryOrderItem = (props: any) => {
  const { record, onBack } = props;
  const { dropScope } = useAliveController();
  dropScope('/xlb_erp/storeOrders/item');
  const forbidenClickRowEvent = useRef(false);
  const userInfo = LStorage.get('userInfo');
  const { navigate } = useNavigation();

  const [itemArr, setdetailItemArr] = useState<any>(_itemTableListCopy);
  const [pagin, setPagin] = useState({
    pageSize: 2000000,
    pageNum: 1,
    total: 0,
  });
  const [imgModelVisible, setImgModelVisible] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [selectDiscVis, setSelectDiscVis] = useState<boolean>(false);
  const [discountedProductsList, setDiscountedProductsList] = useState<any>([]);
  const [deliverySpacialRowData, setDeliverySpacialRowData] = useState<any>([]);
  const [deliveryparam, setDeliveryparam] = useState<any>({});
  const [visible, setVisible] = useState(false);
  const [fidDataList, setFidDataList] = useState<any[]>([]);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [info, setInfo] = useState<any>({
    code: 0,
    state: 'INIT',
    value: {
      state: 'INIT',
    },
  });
  const [AmountInfo, setAmount] = useState<any>({});
  const [edit, setEdit] = useState<boolean>(false); //触发表格编辑
  const [rowData, setRowData] = useState<any[]>([]);
  const [imgInfo, setImgInfo] = useState<any>({});
  const [fid, setFid] = useState<any>();
  const [form] = XlbBasicForm.useForm();
  const [centerOpen, setCenterOpen] = useState<boolean>(false); //中心库存参数开启
  const { downByProgress } = useDownload();
  const [storeModal, setStoreModal] = useState<any>({
    modalVisible: false, // 弹窗是否展示
    storeItem: {}, // 接收弹窗选中的值
    modalState: {
      isMultiple: true, //（默认false单选，true多选）是否多选
      modalType: 'store', //弹窗类型 （必传）'item'商品 'store'门店  'supplier'供应商
      value: '',
    },
  });
  const { enable_organization } = useBaseParams();
  const [hover, setHover] = useState(false);
  const [firstOrder, setFirstOrder] = useState<string>('');
  const [remainingCount, setRemainingCount] = useState<number>(0);
  const [remainingOrders, setRemainingOrders] = useState<any[]>([]);
  const [fidsList, setFidsList] = useState<any[]>([]);
  const [baseInfoActiveKey, setBaseInfoActiveKey] = useState('baseInfo');
  const [add, setAdd] = useState<boolean>(true);
  const [storeId, setStoreId] = useState<any>('');
  const onPressEnter = (code: any, index: number) => {
    Promise.resolve()
      .then(() => {
        rowData[index].edit = false;
        index + 1 == rowData?.length
          ? (rowData[0].edit = true)
          : (rowData[index + 1].edit = true);
        setRowData(JSON.parse(JSON.stringify(rowData)));
      })
      .then(() => {
        const inputBox =
          index + 1 == rowData?.length
            ? document.getElementById(code + '-' + (0).toString())
            : document.getElementById(code + '-' + (index + 1).toString());
        inputBox?.focus();
      });
  };

  //失去焦点
  const inputBlur = (e: any, index: number, key: any, record: any) => {
    const regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (key == 'quantity' && record.item_id) {
      if (!regPos.test(e) || e < 0) {
        XlbTipsModal({
          tips: '要货数量请输入>=0的数字',
        });
        rowData[index][key] = 0;
        rowData[index].actual_quantity = 0;
        return false;
      }
      if (
        e % (rowData[index].multiple == 0 ? 1 : rowData[index].multiple) !==
        0
      ) {
        XlbTipsModal({
          tips: `要货数量需满足为${
            rowData[index].multiple == 0 ? 1 : rowData[index].multiple
          }的倍数！`,
        });
        rowData[index][key] = 0;
        rowData[index].actual_quantity = 0;
        return false;
      }
      if (rowData[index].lower_limit && e < rowData[index].lower_limit) {
        XlbTipsModal({
          tips: `要货数量需≥订购下限${rowData[index].lower_limit}！`,
        });
        rowData[index][key] = 0;
        rowData[index].actual_quantity = 0;
        return false;
      }
      if (
        rowData[index].initial_quantity &&
        e < rowData[index].initial_quantity
      ) {
        XlbTipsModal({
          tips: `要货数量需≥起始数量${rowData[index].initial_quantity}！`,
        });
        rowData[index][key] = 0;
        rowData[index].actual_quantity = 0;
        return false;
      }
      if (
        rowData[index].remain_quantity &&
        e > rowData[index].remain_quantity
      ) {
        XlbTipsModal({
          tips: `要货数量需≤剩余数量${rowData[index].remain_quantity}！`,
        });
        rowData[index][key] = 0;
        rowData[index].actual_quantity = 0;
        return false;
      }
      setRowData([...rowData]);
    }
  };

  const lessStateSwitch = (v: any) => {
    const less_quantity = v.quantity - v.actual_quantity;
    const obj = { label: '', color: '' };
    if (less_quantity === 0) {
      obj.color = '#47CC28';
      obj.label = '正常';
    }
    if (less_quantity === v.quantity) {
      obj.color = '#F53B3B';
      obj.label = '无货';
    }
    if (less_quantity !== 0 && less_quantity !== v.quantity) {
      obj.color = '#FFAA31';
      obj.label = '缺货';
    }
    return obj;
  };

  const InvoiceRender = (item: any) => {
    switch (item.code) {
      case 'item_code':
        item.render = (value: any, record: any) => (
          <div
            onClick={() => {
              record.item_id && showImage(record);
            }}
          >
            <>
              <span className="link">{value}</span>
            </>
          </div>
        );
        break;
      case 'item_bar_code':
        item.render = (value: any, record: any) => (
          <div
            style={{
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {record.demolition_state &&
              deliveryparam.display_demolition_state ? (
                <span className={styles.demolition}>拆</span>
              ) : null}
            </div>
            <div>{value}</div>
            <span
              style={{
                fontSize: 12,
                color: '#ff8400',
                border: '1px solid #ff8400',
                borderRadius: 3,
                marginLeft: 5,
                padding: '1px 2px',
                display: !record.special_fid ? 'none' : 'block',
              }}
            >
              {'特价'}
            </span>
            <XlbTooltip title={'该商品参与满赠活动,数量满足后自动添加赠品'}>
              <span
                style={{
                  color: '#fff',
                  background: 'rgb(133, 3, 255)',
                  borderRadius: '2px',
                  fontSize: '12px',
                  marginLeft: '5px',
                  lineHeight: '16px',
                  padding: '1px 4px',
                  whiteSpace: 'nowrap',
                  display:
                    record?.campaigns?.[0]?.campaign_type_name != '满赠'
                      ? 'none'
                      : 'block',
                }}
              >
                满赠
              </span>
            </XlbTooltip>
            <span
              style={{
                color: '#fff',
                background: 'rgb(217, 3, 30)',
                borderRadius: '2px',
                fontSize: '12px',
                marginLeft: '5px',
                lineHeight: '16px',
                padding: '1px 4px',
                whiteSpace: 'nowrap',
                display: !record?.present ? 'none' : 'block',
              }}
            >
              赠品
            </span>
          </div>
        );
        break;
      case 'less_status':
        item.render = (value: any, record: any) => (
          <div
            className="info "
            style={{ color: lessStateSwitch(record).color }}
          >
            {value}
          </div>
        );
        break;
      case 'less_quantity':
        item.render = (value: any, record: any) =>
          record?.item_id && (
            <div className="info ">
              {info.state !== 'INIT'
                ? (
                    Number(record.quantity) - Number(record.actual_quantity)
                  ).toFixed(3)
                : '0.000'}
            </div>
          );
        break;
      case 'price':
      case 'basic_price':
      case 'original_price':
        item.render = (value: any, record: any) =>
          record?.item_id && (
            <div className="info ">
              {hasAuth(['门店订单/配送价', '查询'])
                ? Number(value || 0)?.toFixed(4)
                : '****'}
            </div>
          );
        break;
      case 'sale_price':
        item.render = (value: any, record: any) =>
          record?.item_id && (
            <div className="info ">
              {hasAuth(['门店订单/零售价', '查询'])
                ? Number(value)?.toFixed(4)
                : '****'}
            </div>
          );
        break;
      case 'volume':
        item.render = (value: any, record: any) => (
          <div className="info ">{Number(value || 0)?.toFixed(4)}</div>
        );
        break;
      case 'money':
        item.render = (value: any, record: any) =>
          record?.item_id && (
            <div className="info ">
              {hasAuth(['门店订单/配送价', '查询'])
                ? formatWithCommas(Number(value)?.toFixed(2))
                : '****'}
            </div>
          );

        break;
      case 'actual_quantity':
      case 'delivery_quantity':
      case 'actual_basic_quantity':
        item.render = (value: any, record: any) =>
          record?.item_id && (
            <div className="info ">{Number(value || 0)?.toFixed(3)}</div>
          );
        break;
      case 'basic_stock_quantity':
        item.render = (value: any, record: any) =>
          record?.item_id &&
          record._sum !== '合计' && (
            <div className="info ">
              {value
                ? toFixed(
                    safeMath.divide(value, record.delivery_ratio),
                    'QUANTITY',
                    true,
                  )
                : '——'}
            </div>
          );
        break;
      case 'stock_quantity':
        item.render = (value: any, record: any) =>
          record?.item_id &&
          record._sum !== '合计' && (
            <div className="info ">
              {value ? toFixed(value, 'QUANTITY') : '——'}
            </div>
          );
        break;
      case 'center_stock_quantity':
        item.render = (value: any, record: any) =>
          record._sum !== '合计' && (
            <div className="info ">
              {centerOpen
                ? value == null
                  ? '——'
                  : value
                    ? toFixed(value, 'QUANTITY')
                    : '0.000'
                : '***'}
              {/* {centerOpen
                ? value != null
                  ? toFixed(value, 'QUANTITY')
                  : '——'
                : '***'} */}
            </div>
          );
        break;
      case 'quantity':
        item.render = (value: any, record: any, index: any) =>
          record?._click ? (
            <XlbInput
              className="full-box"
              id={item.code + '-' + index['index']?.toString()}
              defaultValue={Number(value || 0)?.toFixed(3)}
              onFocus={(e) => e.target.select()}
              onPressEnter={() => onPressEnter(item.code, index['index'])}
              onBlur={(e) =>
                inputBlur(e.target.value, index['index'], item.code, record)
              }
              onChange={(e) => {
                setEdit(true);
                record.quantity = Number(e.target.value);
                record.basic_quantity = safeMath.multiply(
                  Number(e.target.value),
                  record.ratio,
                );
                record.actual_basic_quantity = safeMath.multiply(
                  Number(e.target.value),
                  record.delivery_ratio,
                );
                record.money = safeMath.multiply(
                  Number(e.target.value),
                  record.price,
                );
                record.volume = safeMath.multiply(
                  Number(e.target.value),
                  Number(record.basic_volume),
                );
                info.state === 'INIT' &&
                  (record.actual_quantity = Number(e.target.value));
              }}
            />
          ) : (
            record?.item_id && (
              <div className="info ">{Number(value || 0)?.toFixed(3)}</div>
            )
          );
        break;
      case 'item_spec':
        item.render = (value: any, record: any) => (
          <div className="info ">{record.purchase_spec}</div>
        );
        break;
      case 'memo':
        item.render = (value: any, record: any, index: any) => {
          const _index = index?.index;
          return record?._click ? (
            <XlbInput
              className="full-box"
              defaultValue={value}
              onFocus={(e) => e.target.select()}
              id={item.code + '-' + _index.toString()}
              onPressEnter={(e) => {
                onPressEnter(item.code, _index);
              }}
              onChange={(e) => {
                setEdit(true);
                record.memo = e.target.value;
                setRowData(rowData);
              }}
            />
          ) : (
            <div className="info ">{value}</div>
          );
        };
        break;
      case 'item_category':
        item.render = (value: any, record: any) => (
          <div className="info ">{record.item_category_name}</div>
        );
        break;
      case 'basic_out_quantity':
      case 'basic_purchase_quantity':
        item.render = (value: any, record: any) => (
          <div className="info ">
            {value ? Number(value / record.ratio).toFixed(3) || '' : '0.00'}
          </div>
        );
        break;
      case 'special_fid':
        item.render = (value: any, record: any, index: number) => {
          return record?.special_fid ? (
            <div className=" cursors">
              <span
                className="link cursors"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(
                    '/xlb_erp/goodsOrder/index',
                    {
                      tab: 'store_order_num',
                      jumpType: 'item',
                    },
                    'xlb_erp',
                    true,
                  );
                }}
              >
                {value}
              </span>
            </div>
          ) : null;
        };
        break;
    }
    return item;
  };

  //打印
  const printItem = async () => {
    const data = {
      fid: form.getFieldValue('fid'),
    };
    setIsLoading(true);
    const res = await print(data);
    setIsLoading(false);
    if (res.code === 0) {
      XlbPrintModal({
        data: res?.data,
        title: '打印详情',
      });
    }
  };
  const getAmount = async (store_id: number) => {
    if (!store_id) return;
    const res = await getStoreAmout({ store_id, freeze: true });
    res.code === 0 && setAmount(res.data);
  };

  const handleNum = (num1: any, num2: any) => {
    return num2 ? safeMath.add(num1, num2) : num1;
  };

  const handleQuantity = async () => {
    const demolition: any[] = [
      { type: '整件', type1: '品项' },
      { type: '整件', type1: '数量' },
    ];
    const nDemolition: any[] = [
      { type: '拆零', type1: '品项' },
      { type: '拆零', type1: '数量' },
    ];

    rowData.forEach((i: any) => {
      const item = {
        quantity: i.quantity || 0,
        actual_quantity: i.actual_quantity || 0,
        delivery_quantity: i.delivery_quantity || 0,
      };
      if (!i.demolition_state) {
        demolition[0].quantity =
          handleNum(demolition[0].quantity, item.quantity ? 1 : 0) || 0;
        demolition[0].actual_quantity =
          handleNum(
            demolition[0].actual_quantity,
            item.actual_quantity ? 1 : 0,
          ) || 0;
        demolition[0].delivery_quantity =
          handleNum(
            demolition[0].delivery_quantity,
            item.delivery_quantity ? 1 : 0,
          ) || 0;
        demolition[1].quantity =
          handleNum(demolition[1].quantity, item.quantity) || 0;
        demolition[1].actual_quantity =
          handleNum(demolition[1].actual_quantity, item.actual_quantity) || 0;
        demolition[1].delivery_quantity =
          handleNum(demolition[1].delivery_quantity, item.delivery_quantity) ||
          0;
      } else {
        nDemolition[0].quantity =
          handleNum(nDemolition[0].quantity, item.quantity ? 1 : 0) || 0;
        nDemolition[0].actual_quantity =
          handleNum(
            nDemolition[0].actual_quantity,
            item.actual_quantity ? 1 : 0,
          ) || 0;
        nDemolition[0].delivery_quantity =
          handleNum(
            nDemolition[0].delivery_quantity,
            item.delivery_quantity ? 1 : 0,
          ) || 0;
        nDemolition[1].quantity =
          handleNum(nDemolition[1].quantity, item.quantity) || 0;
        nDemolition[1].actual_quantity =
          handleNum(nDemolition[1].actual_quantity, item.actual_quantity) || 0;
        nDemolition[1].delivery_quantity =
          handleNum(nDemolition[1].delivery_quantity, item.delivery_quantity) ||
          0;
      }
    });
    // 根据is_force_delivery计算四个数量
    const selfSelectItemCount = rowData.filter(
      (item: any) => item?.item_id,
    )?.length;
    const selfSelectQuantity = rowData.reduce((total: number, item: any) => {
      if (item?.item_id) {
        return total + (item.quantity || 0);
      }
      return total;
    }, 0);

    await XlbTipsModal({
      title: '详情',
      width: 500,
      tips: (
        <>
          {/* 分割线 */}
          <div className={styles.modalDivider} />
          <div className={styles.modalItem}>
            <div>
              自选品项：<span>{selfSelectItemCount}</span>
            </div>
            <div>
              自选数量：<span>{selfSelectQuantity}</span>
            </div>
          </div>
          {deliveryparam.display_demolition_state ? (
            <XLBTable
              className={styles.modalTable}
              style={{ height: 184 }}
              dataSource={[...demolition, ...nDemolition]}
              columns={columns}
              hideOnRadioColumn
              hideOnSinglePage
            />
          ) : null}
        </>
      ),
      isConfirm: false,
    });
  };

  // 是否展示中心库存字段
  const showCenterStock = async (deliveryparam: any) => {
    try {
      // 查找是否存在 center_stock_quantity
      const centerStockQuantityExists = itemArr.some(
        (v) => v.code === 'center_stock_quantity',
      );
      // 如果 center_stock_quantity 存在且需要展示
      if (centerStockQuantityExists) {
        setdetailItemArr(
          itemArr.map((v) => {
            if (v.code === 'center_stock_quantity') {
              v.hidden = !deliveryparam?.display_center_stock_quantity;
            }
            return v;
          }),
        );
      }
    } catch (error) {
      console.error('Error fetching sale parameters:', error);
    }
  };

  //读取信息
  const readinfo = async (fid: any) => {
    setIsLoading(true);
    const res = await readInfo({ fid });
    setIsLoading(false);
    if (res.code === 0) {
      //   await getStockData(res.data.out_store_id)
      let money = 0;
      let actual_quantity = 0;
      let quantity = 0;
      let actual_basic_quantity = 0;
      const volume = 0;
      let price = 0;

      res.data.details.map((v: any) => {
        money += parseFloat(v.money);
        actual_quantity += parseFloat(v.actual_quantity);
        quantity += parseFloat(v.quantity);
        price += parseFloat(v.price);
        actual_basic_quantity += parseFloat(v.actual_basic_quantity);
        v.less_status =
          res.data.state !== 'INIT' ? lessStateSwitch(v).label : null;
      });
      setStoreId(res?.data?.store_id);
      setFid(fid);
      setInfo({
        ...info,
        state: res.data.state,
        value: res.data,
        type: 'STOREHOUSE_DELIVERY',
        delivery_state: res.data.delivery_state,
        submit_time: res.data.submit_time,
      });
      // showFidsList(res.data.ref_order_fids)
      const refOrderInfos = res.data?.ref_order_infos?.map((v) => v.fid);
      setFidsList(refOrderInfos);
      showFidsList(refOrderInfos.join(',') || '');
      setRowData(
        res.data.details?.map((v: any) => ({ ...v, short_row_id: uuidv4() })),
      );

      setPagin({
        ...pagin,
        total: res.data.details?.length,
      });
      form.setFieldsValue({
        ...res.data,
        type: 'STOREHOUSE_DELIVERY',
        audit_time: res.data.audit_time?.slice(0, 10),
        create_time: res.data.create_time?.slice(0, 10),
        update_time: res.data.update_time?.slice(0, 10),
        valid_date: res.data.valid_date
          ? dayjs(res.data.valid_date)
          : undefined,
        delivery_date: res.data.delivery_date
          ? dayjs(res.data.delivery_date)
          : undefined,
      });
      setAmount({
        balance: res.data.balance,
        credit_line: res.data.credit_line,
        available_balance: res.data.available_balance,
        frozen_money: res.data.frozen_money,
      });
      if (res.data.state == 'INIT' || res.data.state == 'PAID') {
        getAmount(res.data.store_id);
      }
      // changeItemArrFn(res.data.state, res.data.order_type, isPrePare)
    }
  };

  const showImage = async (row: any) => {
    const res = await getItemInfo({ id: row.item_id });
    if (res.code === 0) {
      setImgInfo({ ...res.data, item_brand_name: res.data?.item_brand?.name });
    }
    setImgModelVisible(true);
  };
  const getdeliveryparam = async () => {
    const res = await deliveryparamRead();
    if (res.code === 0) {
      setDeliveryparam(res.data);
      showCenterStock(res.data);
    }
  };

  const getCenterOpen = async () => {
    const res = await getCenterAble({});
    if (res.code == 0) {
      setCenterOpen(res.data?.request_order_show_center_stock);
    }
  };
  const openRefOrder = _.debounce(async (data) => {
    setVisible(true);
    setTableLoading(true);
    setFidDataList([]);
    const fids = data;
    const res = await XlbFetch.post(
      process.env.ERP_URL + '/erp/hxl.erp.requestorder.page',
      {
        fids: fids,
      },
    );
    if (res.code == 0) {
      setFidDataList(res?.data?.content);
    }
    setTableLoading(false);
  }, 50);
  const showFidsList = async (data: any) => {
    const firstOrderArr = data && data?.length > 0 ? data.split(',') : [];
    setFirstOrder(firstOrderArr?.length > 0 ? firstOrderArr[0] : '');
    setRemainingOrders(firstOrderArr.slice(1));
  };

  useEffect(() => {
    setRemainingCount(remainingOrders?.length);
  }, [remainingOrders]);

  useEffect(() => {
    setFid(record.fid);
    if (record.fid === 1) {
      setEdit(false);
      form.setFieldsValue({
        store_name: userInfo.store.enable_delivery_center
          ? ''
          : userInfo.store.store_name,
        store_id: userInfo.store.enable_delivery_center
          ? ''
          : userInfo.store.id,
        type: 'STOREHOUSE_DELIVERY',
      });
      if(!userInfo.store.enable_delivery_center){
        setStoreId(userInfo.store.id)
        form.setFieldsValue({
          org_name: userInfo.org_name,
          org_id: userInfo.org_id,
        })
      }
      if (!userInfo.store.enable_delivery_center && userInfo.upstream_store) {
        // getStockData(userInfo.upstream_store.id, true)
        getAmount(userInfo.store.id);
      }
      showFidsList('');
    } else {
      readinfo(record.fid);
    }
    getdeliveryparam();
    getCenterOpen();
    return () => {};
  }, []);
  useEffect(() => {
    footerData[0] = {};
    footerData[0]._sum = '合计';
    footerData[0]._index = '合计';
    footerData[0].money = (
      <div className="info ">
        {hasAuth(['门店订单/配送价', '查询']) ? (
          <XlbTooltip
            overlayStyle={{ whiteSpace: 'pre-wrap' }}
            title={`自选商品: ￥${formatWithCommas(
              Number(
                rowData.reduce(
                  (total, item) =>
                    item?.item_id ? total + Number(item.money || 0) : total,
                  0,
                ),
              )?.toFixed(2),
            )}`}
          >
            {formatWithCommas(
              Number(
                rowData.reduce(
                  (sum, v) => sum + Number(!v?.item_id ? 0 : v.money),
                  0,
                ) || 0,
              )?.toFixed(2),
            )}
          </XlbTooltip>
        ) : (
          '****'
        )}
      </div>
    );
    footerData[0].volume = (
      <div className="info ">
        {Number(
          rowData.reduce(
            (sum, v) => sum + Number(!v?.item_id ? 0 : v.volume),
            0,
          ) * 0.000001 || 0,
        )?.toFixed(4) + 'm³'}
      </div>
    );

    footerData[0].quantity = (
      <div
        className=" link cursor"
        onClick={(e) => {
          e.stopPropagation();
          handleQuantity();
        }}
      >
        {rowData.reduce(
          (sum, v) => sum + Number(!v?.item_id ? 0 : v.quantity),
          0,
        )}
      </div>
    );
    footerData[0].actual_quantity = rowData
      .reduce((sum, v) => sum + Number(!v?.item_id ? 0 : v.actual_quantity), 0)
      ?.toFixed(3);
    footerData[0].prepare_difference = rowData.reduce(
      (sum, v) => sum + Number(v?.prepare_difference || 0),
      0,
    );
    footerData[0].less_quantity = rowData.reduce(
      (sum, v) =>
        sum + Number(!v?.item_id ? 0 : v.quantity - v.actual_quantity),
      0,
    );
    footerData[0].price = rowData
      .reduce((sum, v) => sum + Number(!v?.item_id ? 0 : v.price), 0)
      ?.toFixed(4);
    footerData[0].actual_basic_quantity = rowData
      .reduce(
        (sum, v) => sum + Number(!v?.item_id ? 0 : v.actual_basic_quantity),
        0,
      )
      ?.toFixed(3);
    if (info.state === 'AUDIT') {
      footerData[0].delivery_quantity = rowData
        .reduce(
          (sum, v) => sum + Number(!v?.item_id ? 0 : v.delivery_quantity),
          0,
        )
        ?.toFixed(3);
      footerData[0].delivery_rate =
        (
          safeMath.divide(
            footerData[0].delivery_quantity,
            footerData[0].actual_quantity,
          ) * 100
        ).toFixed(2) + '%';
    }

    setFooterData([...footerData]);

    setPagin({
      ...pagin,
      total: rowData?.length,
    });
  }, [JSON.stringify(rowData)]);
  const checkData = () => {
    return {
      ...form.getFieldsValue(true),
      details: rowData,
      type: 'STOREHOUSE_DELIVERY',
      prepare_type_id: form.getFieldValue('prepare_type_id'),
      valid_date: form.getFieldValue('valid_date')
        ? dayjs(form.getFieldValue('valid_date')).format('YYYY-MM-DD HH:mm:ss')
        : null,
      delivery_date: form.getFieldValue('delivery_date')
        ? dayjs(form.getFieldValue('delivery_date')).format(
            'YYYY-MM-DD HH:mm:ss',
          )
        : null,
      store_id: Array.isArray(form.getFieldValue('store_id'))
        ? form.getFieldValue('store_id')[0]
        : (form.getFieldValue('store_id') ?? ''),
      memo: form.getFieldValue('all_memo'),
    };
  };

  // 保存 审核
  const afterSaveOrAudit = async (res: any) => {
    setIsLoading(false);
    if (res.code === 0) {
      XlbMessage.success('操作成功');
      setEdit(false);
      readinfo(res.data.fid);
      if (res.data?.state === 'INIT') {
        await lockDetail({
          fid: res.data.fid,
        });
      }
      if (res.data.owe_money) {
        //余额不足， 开启倒计时弹框
        XlbTipsModal({
          tips: `门店余额不足，还差${res.data.owe_money}元，充值后请到待付款订单中进行支付。(${deliveryparam.request_order_paid_duration}分钟内有效)`,
        });
      }
    }
  };

  const saveHandle = async () => {
    if (!rowData[0]?.item_id || rowData?.length == 0) {
      XlbTipsModal({
        tips: '请先添加商品!',
      });
      return false;
    }
    !rowData[rowData?.length - 1]?.item_id &&
      rowData.splice(rowData?.length - 1, 1);
    const data = checkData();
    setIsLoading(true);
    let res = fid === 1 ? await addInfo(data) : await updateInfo(data);
    await afterSaveOrAudit(res);
  };

  const auditHandle = async (data: any) => {
    if (!data?.prepare_type_id) {
      const p_res = await ErpRequest.post(
        `/bi/hxl.bi.store.category.forecast.find?storeId=${data?.store_id}`,
        {},
      );
      if (p_res.code === 0) {
        data.psd = p_res?.data?.psd;
      }
    }
    let res = await auditInfo({ ...data, enable_paid_state: true });
    await afterSaveOrAudit(res);
  };

  const auditCheck = async () => {
    if (!rowData[0]?.item_id || rowData?.length == 0) {
      XlbTipsModal({
        tips: '请先添加商品!',
      });
      return false;
    }
    !rowData[rowData?.length - 1]?.item_id &&
      rowData.splice(rowData?.length - 1, 1);
    if (rowData.every((v: any) => Number(v.quantity) == 0)) {
      XlbTipsModal({
        tips: '部分商品要货数量不合法!',
      });
      return;
    }
    const data = {
      ...checkData(),
      details: rowData.filter((v: any) => Number(v.quantity) !== 0),
    };
    setIsLoading(true);
    const res = await checkAuditInfo({
      ...data,
      enable_paid_state: true,
      item_ids: rowData.map((v: any) => v.item_id),
      check_repeat_item_remind: false,
    });
    if (res?.code === 0) {
      if (
        res.data?.can_adjust &&
        res.data?.details &&
        res.data?.details.length > 0
      ) {
        const resDetails = res.data?.details || [];
        const adjustedMap = new Map(
          resDetails.map((v: any) => [v.item_bar_code, v]),
        );
        const ml = data.details.filter((i: any) =>
          adjustedMap.has(i.item_bar_code),
        );
        const list = ml.map((item: any) => {
          const matched = adjustedMap.get(item.item_bar_code);
          return {
            ...item,
            ...matched
          };
        });
        XlbTipsModal({
          title: '以下商品缺货，审核后将自动调整数量',
          width: 800,
          tips: (
            <XlbTable
              style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
              columns={goodDataColumns.map(item => {
                if (item.name === '调整后数量') {
                  item.render = (value: any, row: any, index: any) => {
                    return <div style={{ color: 'red' }}>{row?.adjusted_quantity}</div>;
                  }
                }
                return item
              })}
              total={list?.length}
              dataSource={list}
              key={list?.length}
            ></XlbTable>
            // <div style={{ fontSize: '14px', margin: '0 40px' }}>
            //   {list.map((v: any, index: number) => (
            //     <div
            //       key={index}
            //       style={{ color: '#86909c', marginBottom: '6px' }}
            //     >
            //       <div>
            //         【{v.item_name}】，采购规格{v.item_spec}
            //       </div>
            //       <div>条码：{v.item_bar_code}</div>
            //       <div>
            //         ¥{v.price}/{v.unit}
            //       </div>
            //     </div>
            //   ))}
            // </div>
          ),
          isCancel: true,
          okText: '继续审核',
          onOk: () => {
            data.details.forEach((v: any) => {
              if (adjustedMap.has(v.item_bar_code)) {
                const adjusted: any = adjustedMap.get(v.item_bar_code);
                v.quantity = adjusted.adjusted_quantity;
                v.actual_quantity = adjusted.adjusted_quantity;
                v.basic_quantity = safeMath.multiply(
                  Number(adjusted.adjusted_quantity),
                  v.ratio,
                );
                v.actual_basic_quantity = safeMath.multiply(
                  Number(adjusted.adjusted_quantity),
                  v.delivery_ratio,
                );
                v.money = safeMath.multiply(
                  Number(adjusted.adjusted_quantity),
                  v.price,
                );
                v.volume = safeMath.multiply(
                  Number(adjusted.adjusted_quantity),
                  Number(v.basic_volume),
                );
              }
            });
            data.details = data.details.filter(
              (i: any) => Number(i.quantity) > 0,
            );
            auditHandle(data);
            return true;
          },
          onCancel: () => {
            setIsLoading(false);
          },
        });
      } else {
        if (res.data && res.data?.memo) {
          setIsLoading(false);
          XlbMessage.error(res.data?.memo);
          /**
           * res.data.type异常类型，后端返回如下
           * 0:商品不合法
           * 1:要货数量不合法
           * 2:配送日重复下单
           * 3:启用了bms但未绑定组织
           * 4:换算率不合法（换算率已更新）或者 数量和基本数量不合法（换算")
           * 5:商品全部无货,无法审核
           */
        } else {
          await auditHandle(data);
        }
      }
    } else {
      setIsLoading(false);
    }
  };

  const discountedProductsRef = useRef<any[]>([]);

  useEffect(() => {
    discountedProductsRef.current = discountedProductsList;
  }, [discountedProductsList]);
  // 批量添加
  const confirmAdd = (list: any, add_type = 'item') => {
    let filterArr = [...list];
    const ids = rowData.map((v) => v.item_id);
    const repeatArr = list?.filter((v) => ids.includes(v.id));
    const rName = [repeatArr.map((v) => `【${v.name}】`)];
    if (repeatArr.length) {
      XlbTipsModal({
        tips: '以下商品已存在，不允许重复添加，系统已自动过滤！',
        tipsList: rName,
      });
    }
    const spacialIDs = new Set(
      discountedProductsRef.current.map((product: any) => product.id),
    );
    const spacialList = list.filter((product: any) => {
      return (
        !spacialIDs.has(
          storeModal.modalState.modalType === 'import'
            ? product.item_id
            : product.id,
        ) &&
        product?.delivery_spacial_price_list?.length &&
        !product?.delivery_spacial_price_list.find(
          (spacial: any) => spacial.has_batch,
        )
      );
    });
    if (spacialList?.length) {
      XlbTipsModal({
        tips: (
          <div>
            <div>以下商品存在特价，请选择特价商品！</div>
            <div>{spacialList.map((v: any) => v.name || v.item_name)}</div>
          </div>
        ),
      });
      return false;
    }
    const ex_spacialIDs = spacialList.map((product: any) => product.id);
    filterArr = list.filter(
      (e: any) => !ids.includes(e.id) && !ex_spacialIDs.includes(e.id),
    );

    const newList = filterArr?.map((v) => {
      return {
        ...v,
        // 导入和批量添加的字段不通用
        _sum: '商品',
        short_row_id: uuidv4(),
        item_id: v.id,
        item_code: v.code,
        item_bar_code: v.bar_code,
        item_name: v.name,
        item_spec: v.purchase_spec,
        unit: v.delivery_unit,
        basic_unit: v.unit,
        quantity: 0, //要货数量
        memo: null,
        basic_quantity: 0,
        price: safeMath.multiply(v.basic_price, v.delivery_ratio), //单价（暂时）
        money: v.money ? Number(v.money).toFixed(2) : '0.00',
        ratio: v.delivery_ratio,
        less_quantity: Number(0).toFixed(3), //缺货数量
        actual_quantity: v.actual_quantity
          ? Number(v.actual_quantity).toFixed(3)
          : Number(0).toFixed(3), //预发数量
        actual_basic_quantity: v.actual_basic_quantity
          ? Number(v.actual_basic_quantity).toFixed(3)
          : 0, //基本预发数量
        basic_stock_quantity: v.hide_basic_stock_quantity
          ? null
          : v.basic_stock_quantity, //门店补货库存
        basic_volume: v.volume ? v.volume : 0,
        volume: v.volume ? v.volume * Number(v.quantity) || 0 : 0,
        _click: false,
        _edit: false,
      };
    });

    const flatt = flattenDiscountedProducts(
      newList,
      discountedProductsRef.current,
    );
    const flattNewList = newList
      .map((item: any) => {
        const matches = flatt.filter((bItem) => bItem.id === item.id);
        if (matches?.length > 0) {
          return matches;
        }
        return item;
      })
      .flat();
    // 最后过滤掉没有item_id的
    const mergeArr = [...rowData, ...flattNewList]?.filter((v) => v.item_id);
    newList.length > 0 ? setEdit(true) : setEdit(false);
    forbidenClickRowEvent.current = true;
    setRowData(
      mergeArr?.map((v, index) => ({ ...v, key: index + '_' + v?.item_id })),
    );
    setTimeout(() => {
      forbidenClickRowEvent.current = false;
    }, 0);
  };
  const flattenDiscountedProducts = (
    newPList: any[],
    productsList: any[],
  ): any[] => {
    const newListIds = new Set(newPList.map((product) => product.id));
    const filteredProducts = productsList.filter((product) =>
      newListIds.has(product.id),
    );
    if (filteredProducts.length) {
      const flattenedProducts = filteredProducts
        .map((product) => {
          const filteredPrices =
            product.delivery_spacial_price_list?.filter(
              (price: any) =>
                price.hasOwnProperty('quantity') &&
                price.quantity !== null &&
                price.quantity !== undefined &&
                !isNaN(price.quantity) &&
                price.quantity > 0,
            ) || [];

          return { ...product, delivery_spacial_price_list: filteredPrices };
        })
        .filter((product) => product.delivery_spacial_price_list.length > 0);
      if (flattenedProducts.length) {
        return flattenedProducts.flatMap((product) =>
          product.delivery_spacial_price_list.map((price) => ({
            ...product,
            item_id: product.id,
            item_code: product.code,
            item_bar_code: product.bar_code,
            item_name: product.name,
            item_spec: product.purchase_spec,
            unit: product.delivery_unit,
            basic_unit: product.unit,
            ratio: product.delivery_ratio,
            less_quantity: Number(0).toFixed(3), //缺货数量
            basic_stock_quantity: product.hide_basic_stock_quantity
              ? null
              : product.basic_stock_quantity, //门店补货库存
            memo: null,
            quantity: price.quantity || 0, //要货数量
            // price: price.special_price || 0,
            price: safeMath.multiply(price.basic_price, product.delivery_ratio), //单价（暂时）
            basic_price: price.basic_price,
            basic_quantity: safeMath.multiply(
              Number(price.quantity),
              Number(product.delivery_ratio),
            ),
            actual_basic_quantity: safeMath.multiply(
              Number(price.quantity),
              Number(product.delivery_ratio),
            ),
            money: safeMath.multiply(
              Number(price.quantity),
              Number(price.special_price),
            ),
            actual_quantity: Number(price.quantity),
            special_fid: price.special_fid,
            special_detail_num: price.special_detail_num,
            delivery_spacial_price_list: [price],
            initial_quantity: price.initial_quantity,
            activity_quantity: price.activity_quantity,
            remain_quantity: price.remain_quantity,
            base_price: price.base_price,
          })),
        );
      }
    }
    return [];
  };
  const beforeBack = async () => {
    const res = await unlockDetail({
      fid: fid,
    });
    if (res.code === 0) {
      onBack(false);
      dropScope('/xlb_erp/storeOrders/storeItem');
    }
    dropScope('/xlb_erp/storeOrders/storeItem');
  };

  const goBack = async () => {
    if (edit) {
      XlbTipsModal({
        tips: '单据未保存，是否确认返回？',
        onOkBeforeFunction: () => {
          beforeBack();
          return true;
        },
      });
    } else {
      beforeBack();
    }
  };

  const exportItem = async (e: any) => {
    setIsLoading(true);
    const data = { fid: form.getFieldValue('fid') };
    const res = await ErpRequest.post('/erp/hxl.erp.storeorder.export', data);
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };

  const payOrder = () => {
    XlbTipsModal({
      tips: '是否确认支付？',
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await payInfo({ fid });
        if (res.code === 0) {
          XlbMessage.success('操作成功');
          readinfo(fid);
        }
        return true;
      },
    });
  };
  const referenceRef = useRef<HTMLDivElement | null>(null);
  const [dialogWidth, setDialogWidth] = useState(180); // 默认值

  useEffect(() => {
    if (referenceRef.current) {
      const observer = new ResizeObserver(() => {
        const width = referenceRef.current?.offsetWidth - 118 || 180;
        setDialogWidth(width);
        console.log(width, 'width====>');
      });

      observer.observe(referenceRef.current);

      return () => observer.disconnect(); // 清理
    }
  }, []);
  return (
    <>
      <ConfigProvider
        theme={{
          token: {
            screenXLMin: 1280,
            screenXL: 1280,
          },
        }}
      >
        {/* <div className={styles.providerWrap}> */}
        {/* <div className={styles.innerProvider}> */}
        <div
          style={{
            height: 'calc(100vh - 120px)',
            display: 'flex',
            flexDirection: 'column',
            padding: 12,
          }}
          className={styles.form_container_wrapper_storeItem}
        >
          {/* 下游单据号弹窗 */}
          <XlbModal
            width={800}
            open={visible}
            title={'下游单据'}
            isCancel={true}
            keyboard={false}
            onOk={async () => {
              setVisible(false);
            }}
            onCancel={() => {
              setVisible(false);
            }}
          >
            <XLBTable
              isLoading={tableLoading}
              style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
              columns={[
                {
                  name: '序号',
                  code: '_index',
                  width: 80,
                },
                {
                  name: '下游单据号',
                  code: 'fid',
                  width: 150,
                },
                {
                  name: '统配单号',
                  code: 'relation_force_delivery_fid',
                  width: 150,
                  render: (text: any, record: any, index: any) => {
                    return <div>{text ? text : '-'}</div>;
                  },
                },
                {
                  name: '配送日期',
                  code: 'delivery_date',
                  width: 150,
                  features: { format: 'TIME' },
                },
                {
                  name: '配送状态',
                  code: 'delivery_state',
                  render: (text: any, record: any, index: any) => {
                    const item = deliveryState.find((v) => v.value === text);
                    return <div>{item ? item.label : ''}</div>;
                  },
                },
                {
                  name: '发货门店',
                  code: 'out_store_name',
                  width: 150,
                },
                {
                  name: '发货仓库',
                  code: 'storehouse_name',
                  width: 150,
                },
                {
                  name: '体积（cm³）',
                  code: 'volume',
                  width: 100,
                  render: (text: any, record: any, index: any) => {
                    return <div>{text * 1000000}</div>;
                  },
                },
                {
                  name: '商品物流模式',
                  code: 'item_logistics_mode',
                  width: 110,
                  features: { sortable: false },
                  align: 'left',
                  render: (text: any, record: any, index: any) => {
                    return (
                      <div className={``}>
                        {text
                          ? itemLogisticsMode?.find((v) => v.value == text)
                              ?.label
                          : ''}{' '}
                      </div>
                    );
                  },
                },
                {
                  name: '同步WMS',
                  code: 'has_wms_fid',
                  width: 100,
                  render: (text: any, record: any, index: any) => {
                    const showColor = text ? 'success' : 'danger';
                    return (
                      <div className={` ${showColor}`}>
                        {' '}
                        {text ? '是' : '否'}{' '}
                      </div>
                    );
                  },
                },
                {
                  name: '生成调出单',
                  code: 'has_out_order',
                  width: 110,
                  features: { sortable: false },
                  align: 'left',
                  render: (text: any, record: any, index: any) => {
                    const showColor = text == '是' ? 'success' : 'danger';
                    return (
                      <div className={` ${showColor}`}>
                        {' '}
                        {text == '是' ? '是' : '否'}{' '}
                      </div>
                    );
                  },
                },
                {
                  name: '生成缺货补货单',
                  code: 'replenish_fid',
                  width: 134,
                  features: { sortable: true },
                  align: 'left',
                  render: (text: any, record: any, index: any) => {
                    const showColor = text ? 'success' : 'danger';
                    return (
                      <div className={` ${showColor}`}>
                        {' '}
                        {text ? '是' : '否'}{' '}
                      </div>
                    );
                  },
                },
                {
                  name: '是否生成直配单',
                  code: 'has_direct_delivery_order',
                  width: 130,
                  features: { sortable: false },
                  align: 'left',
                  render: (text: any, record: any, index: any) => {
                    const showColor = text == '是' ? 'success' : 'danger';
                    return (
                      <div className={` ${showColor}`}>
                        {' '}
                        {text == '是' ? '是' : '否'}{' '}
                      </div>
                    );
                  },
                },
              ]}
              total={fidDataList?.length}
              dataSource={fidDataList}
              key={fidDataList?.length}
            ></XLBTable>
          </XlbModal>
          <XlbButton.Group>
            {hasAuth(['门店订单/仓配', '编辑']) && (
              <XlbButton
                label="保存"
                onClick={() => saveHandle()}
                type="primary"
                disabled={info.state !== 'INIT' || isLoading}
                icon={<XlbIcon size={16} name="baocun" />}
              />
            )}
            {hasAuth(['门店订单', '编辑']) && (
              <XlbButton
                label="确认支付"
                type="primary"
                onClick={() => payOrder()}
                disabled={info.state !== 'PAID' || isLoading}
                icon={<span className="icon iconfont icon-fukuan"></span>}
              />
            )}
            {hasAuth(['门店订单', '审核']) && (
              <XlbButton
                type="primary"
                label="审核"
                onClick={() => auditCheck()}
                disabled={info.state !== 'INIT' || fid === 1 || isLoading}
                icon={<XlbIcon size={16} name="shenhe" />}
              />
            )}
            {hasAuth(['门店订单', '导出']) && (
              <XlbDropdownButton
                label="业务操作"
                dropList={[
                  {
                    label: '导出',
                    disabled:
                      fid === 1 ||
                      !rowData?.length ||
                      !hasAuth(['门店订单', '导出']) ||
                      isLoading,
                  },
                  {
                    label: '打印',
                    disabled:
                      fid === 1 ||
                      !rowData?.length ||
                      !hasAuth(['门店订单', '打印']) ||
                      isLoading,
                  },
                ]}
                dropdownItemClick={(e, item: any) => {
                  switch (item?.label) {
                    case '导出':
                      exportItem(e);
                      break;
                    case '打印':
                      printItem();
                      break;
                  }
                }}
              />
            )}
            <XlbButton
              label="返回"
              type="primary"
              onClick={goBack}
              icon={<XlbIcon size={16} name="fanhui" />}
            />
          </XlbButton.Group>
          <XlbBasicForm
            colon
            form={form}
            autoComplete="off"
            layout="inline"
            initialValues={{
              flag: 1,
            }}
          >
            <XlbTabs
              defaultActiveKey="baseInfo"
              activeKey={baseInfoActiveKey}
              onChange={(e) => setBaseInfoActiveKey(e)}
              items={[
                {
                  label: '基本信息',
                  key: 'baseInfo',
                  children: (
                    <div
                      className={styles.form_container_transferDocument}
                      style={{ display: 'flex' }}
                    >
                      <div className="row-flex">
                        <div
                          className="row-flex"
                          style={{ flexWrap: 'wrap', flex: 1 }}
                        >
                          <Row gutter={12} wrap>
                            <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                              <XlbBasicForm.Item
                                label="补货门店"
                                name="store_id"
                              >
                                <XlbInputDialog
                                  dialogParams={{
                                    type: 'store',
                                    dataType: 'lists',
                                    data: {
                                      center_flag: false,
                                      status: true,
                                    },
                                  }}
                                  fieldNames={{
                                    idKey: 'id',
                                    nameKey: 'store_name',
                                  }}
                                  disabled={
                                    info.state !== 'INIT' ||
                                    (rowData?.length > 0 &&
                                      rowData?.some(
                                        (item: any) =>
                                          item?.code || item?.item_code,
                                      )) ||
                                    !hasAuth(['门店订单/仓配', '编辑'])
                                  }
                                  handleValueChange={(e, list: any) => {
                                    form.setFieldsValue({
                                      store_name: list[0].store_name,
                                      store_id: list[0].id,
                                      org_name: list[0].org_name,
                                      org_id: list[0].org_id,
                                      store_credit_line:
                                        list[0].enable_credit_line,
                                    });
                                    getAmount(list[0].id);
                                    setAdd(!list[0].id);
                                    setStoreId(list[0]?.id);
                                  }}
                                  width={dialogWidth}
                                ></XlbInputDialog>
                              </XlbBasicForm.Item>
                            </Col>
                            <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                              {enable_organization ? (
                                <XlbBasicForm.Item
                                  label="补货组织"
                                  name="org_name"
                                >
                                  <XlbInput
                                    disabled
                                    style={{ width: '100%' }}
                                  ></XlbInput>
                                </XlbBasicForm.Item>
                              ) : null}
                            </Col>
                            <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                              <div style={{ width: '100%' }} ref={referenceRef}>
                                <XlbBasicForm.Item label="单据号" name="fid">
                                  <XlbInput
                                    disabled
                                    style={{ width: '100%' }}
                                  ></XlbInput>
                                </XlbBasicForm.Item>
                              </div>
                            </Col>
                            <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                              <XlbBasicForm.Item
                                label="商品部门"
                                name="item_dept_names"
                              >
                                <XlbInput
                                  disabled
                                  style={{ width: '100%' }}
                                ></XlbInput>
                              </XlbBasicForm.Item>
                            </Col>
                            <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                              <XlbBasicForm.Item
                                label="有效日期"
                                name="valid_date"
                              >
                                <XlbDatePicker
                                  style={{ width: '100%' }}
                                  allowClear={false}
                                  format={'YYYY-MM-DD'}
                                  disabled
                                />
                              </XlbBasicForm.Item>
                            </Col>
                            <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                              <XlbBasicForm.Item
                                label="下游单据号"
                                name="ref_order_fids"
                              >
                                {/* 手写。。。 */}
                                <div
                                  onClick={() =>
                                    firstOrder && openRefOrder(fidsList)
                                  }
                                  onMouseEnter={() =>
                                    firstOrder && setHover(true)
                                  }
                                  onMouseLeave={() =>
                                    firstOrder && setHover(false)
                                  }
                                  style={{
                                    width: dialogWidth,
                                    height: 28,
                                    border: `1px solid ${
                                      firstOrder
                                        ? hover
                                          ? '#3D66FE'
                                          : '#ccc'
                                        : '#ddd'
                                    }`,
                                    background: firstOrder
                                      ? 'transparent'
                                      : '#f7f8fa',
                                    color: firstOrder ? 'inherit' : '#aaa',
                                    cursor: firstOrder
                                      ? 'pointer'
                                      : 'not-allowed',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    padding: '0 10px',
                                    borderRadius: '4px',
                                    transition: 'border 0.3s ease-in-out',
                                  }}
                                >
                                  <div
                                    style={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'space-between',
                                      gap: 5,
                                    }}
                                  >
                                    <XlbTooltip title={firstOrder}>
                                      <span
                                        style={{
                                          display: 'inline-block',
                                          width: dialogWidth - 15,
                                          overflow: 'hidden',
                                          textOverflow: 'ellipsis',
                                          whiteSpace: 'nowrap',
                                        }}
                                        className="link cursors"
                                      >
                                        {firstOrder}
                                      </span>
                                    </XlbTooltip>
                                    {remainingCount > 0 && (
                                      <XlbTooltip
                                        title={remainingOrders?.join(', ')}
                                      >
                                        <span
                                          style={{
                                            width: 32,
                                            height: 20,
                                            background: '#f2f3f5',
                                            lineHeight: '20px',
                                            color: '#86909c',
                                            textAlign: 'center',
                                            display: 'inline-block',
                                            borderRadius: '4px',
                                          }}
                                        >
                                          +{remainingCount}
                                        </span>
                                      </XlbTooltip>
                                    )}
                                  </div>
                                </div>
                              </XlbBasicForm.Item>
                            </Col>
                            <Col
                              xs={16}
                              sm={16}
                              md={16}
                              lg={16}
                              xl={12}
                              xxl={12}
                            >
                              <XlbBasicForm.Item
                                label="留言备注"
                                name="all_memo"
                              >
                                <XlbInput
                                  style={{
                                    width: '100%',
                                  }}
                                  disabled={
                                    info.state !== 'INIT' ||
                                    !hasAuth(['门店订单/仓配', '编辑'])
                                  }
                                />
                              </XlbBasicForm.Item>
                            </Col>
                          </Row>
                        </div>
                      </div>

                      {info?.state && (
                        <div
                          style={{
                            width: '150px',
                            flexBasis: '150px',
                            display: 'flex',
                            justifyContent: 'center',
                            marginLeft: 55,
                          }}
                        >
                          <img
                            src={orderStatusIcons[info?.state]}
                            width={86}
                            height={78}
                          />
                        </div>
                      )}
                    </div>
                  ),
                },
                {
                  label: '其他信息',
                  key: 'other',
                  children: (
                    <>
                      <div className={styles.form_container_transferDocument}>
                        <Row gutter={12} wrap>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item label="制单人" name="create_by">
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label="制单时间"
                              name="create_time"
                            >
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item label="审核人" name="audit_by">
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label="审核时间"
                              name="audit_time"
                            >
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label={'修改人'}
                              name={'update_by'}
                            >
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label={'修改时间'}
                              name={'update_time'}
                            >
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label={'作废人'}
                              name={'invalid_by'}
                            >
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>

                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label={'作废时间'}
                              name={'invalid_time'}
                            >
                              <XlbInput style={{ width: '100%' }} disabled />
                            </XlbBasicForm.Item>
                          </Col>
                        </Row>
                      </div>
                    </>
                  ),
                },
              ]}
            ></XlbTabs>
            {Object.keys(AmountInfo)?.length ? (
              <div className={styles.amount_box}>
                {!!AmountInfo.balance && (
                  <span>
                    门店余额：
                    {formatWithCommas(AmountInfo.balance?.toFixed(2)) || '0.00'}
                  </span>
                )}
                {!!AmountInfo.credit_line && (
                  <span>
                    授信额度：
                    {formatWithCommas(AmountInfo.credit_line?.toFixed(2)) ||
                      '0.00'}
                  </span>
                )}
                {!!AmountInfo.frozen_money && (
                  <span>
                    冻结金额：
                    {formatWithCommas(AmountInfo.frozen_money?.toFixed(2)) ||
                      '0.00'}
                  </span>
                )}
                {!!AmountInfo.available_balance && (
                  <span>
                    可用余额：
                    {formatWithCommas(
                      AmountInfo.available_balance?.toFixed(2),
                    ) || '0.00'}
                  </span>
                )}
              </div>
            ) : null}
          </XlbBasicForm>
          <XlbButton.Group>
            {hasAuth(['门店订单/仓配', '编辑']) && (
              <XlbButton
                label="批量添加"
                type="primary"
                style={{ marginBottom: 12 }}
                disabled={info.state !== 'INIT' || !storeId}
                onClick={async () => {
                  // handleDialogClick('item')
                  const list = await XlbBasicData({
                    type: 'goods',
                    url: '/erp/hxl.erp.storeorder.item.page',
                    isMultiple: true,
                    dataType: 'lists',
                    primaryKey: 'id',
                    resetForm: true,
                    nullable: false,
                    tableRowClick: (row, params) => {
                      if (
                        Array.isArray(row.delivery_spacial_price_list) &&
                        row.delivery_spacial_price_list?.length
                      ) {
                        if (!params?.selected) {
                          setDeliverySpacialRowData(row);
                          setSelectDiscVis(true);
                        }
                      }
                    },
                    data: {
                      type: 'STOREHOUSE_DELIVERY',
                      store_id: Array.isArray(form.getFieldValue('store_id'))
                        ? form.getFieldValue('store_id')[0]
                        : form.getFieldValue('store_id'),
                      prepare_type_id: form.getFieldValue('prepare_type_id'),
                    },
                  });
                  if (!list) return;
                  if (Array.isArray(list)) {
                    confirmAdd(list, 'item');
                  }
                }}
                icon={<XlbIcon size={16} name="jia" />}
              />
            )}
          </XlbButton.Group>
          <XlbShortTable
            isLoading={isLoading}
            showSearch
            key={`${baseInfoActiveKey}}`}
            style={{ flex: 1 }}
            url={'/erp/hxl.erp.storeorder.item.page'}
            data={{
              type: 'STOREHOUSE_DELIVERY',
              store_id: Array.isArray(form.getFieldValue('store_id'))
                ? form.getFieldValue('store_id')[0]
                : form.getFieldValue('store_id'),
              prepare_type_id: form.getFieldValue('prepare_type_id'),
            }}
            onChangeData={(data: any[], type: string) => {
              // console.log('data', data,type,rowData);
              if (info.state != 'INIT') {
                return;
              }
              const originIds = rowData.map((v) => v.item_id);
              const initialValues = Object.freeze({
                basic_stock_quantity: 0,
                memo: null,
              });
              const getInitials = (v: any) =>
                originIds.includes(v.item_id) ? {} : initialValues;

              setRowData(
                data.map((v, index) => {
                  const itemId = v.id || v.item_id;
                  const oldItem = rowData.find(
                    (item) => item.item_id === itemId,
                  );
                  if (oldItem) {
                    // 已存在，直接用老的
                    return oldItem;
                  }
                  // 新增，赋值
                  return {
                    ...v,
                    _sum: '商品',
                    // 新增时，用uuid赋值fid
                    short_row_id: uuidv4(),
                    item_id: itemId,
                    item_code: v.code || v.item_code,
                    item_bar_code: v.bar_code || v.item_bar_code,
                    item_name: v.name || v.item_name,
                    item_spec: v.purchase_spec,
                    unit: v.delivery_unit,
                    basic_unit: v.unit,
                    quantity: v.quantity || 0, //要货数量
                    memo: null,
                    basic_quantity: v.basic_quantity || 0,
                    price: safeMath.multiply(v.basic_price, v.delivery_ratio), //单价（暂时）
                    money: v.money ? Number(v.money).toFixed(2) : '0.00',
                    ratio: v.delivery_ratio,
                    less_quantity: Number(0).toFixed(3), //缺货数量
                    delivery_quantity: v.delivery_quantity || 0,
                    actual_quantity: v.actual_quantity
                      ? Number(v.actual_quantity).toFixed(3)
                      : Number(0).toFixed(3), //预发数量
                    actual_basic_quantity: v.actual_basic_quantity
                      ? Number(v.actual_basic_quantity).toFixed(3)
                      : 0, //基本预发数量
                    basic_stock_quantity: v.hide_basic_stock_quantity
                      ? null
                      : v.basic_stock_quantity, //门店补货库存
                    basic_volume: v.volume ? v.volume : 0,
                    volume: v.volume ? v.volume * Number(v.quantity) || 0 : 0,
                    _click: false,
                    _edit: false,
                    ...getInitials(v), // 初始化值
                  };
                }),
              );
              setEdit(type !== 'onChangeSorts');
            }}
            // 表格新增限制：存在新增商品时，禁止添加
            disabledAdd={rowData.some((v) => v._empty) || rowData?.length == 0}
            disabled={
              info.state !== 'INIT' ||
              !hasAuth(['门店订单/仓配', '编辑']) ||
              !form.getFieldValue('store_id')
            }
            columns={itemArr?.map((v) => InvoiceRender(v))}
            dataSource={rowData}
            footerDataSource={footerData}
            total={rowData?.length}
            selectMode="single"
            primaryKey="short_row_id"
            // popoverPrimaryKey="id"
          />
        </div>
        <SelectDiscountedProducts
          visible={selectDiscVis}
          setVisible={setSelectDiscVis}
          deliverySpacialRowData={deliverySpacialRowData}
          onOk={(obj: any) => {
            const { delivery_spacial_price_list } = obj;
            if (
              delivery_spacial_price_list.filter(
                (item: any) => item.quantity && item.quantity > 0,
              )?.length === 0
            ) {
              return;
            }
            if (discountedProductsList?.length === 0) {
              return setDiscountedProductsList([obj]);
            }
            const exists = discountedProductsList.some(
              (item: any) => item.id === obj.id,
            );
            if (exists) {
              return setDiscountedProductsList(
                discountedProductsList.map((item: any) =>
                  item.id === obj.id ? obj : item,
                ),
              );
            } else {
              return setDiscountedProductsList([
                ...discountedProductsList,
                obj,
              ]);
            }
          }}
        />
        <ImgModel
          visible={imgModelVisible}
          onCancel={() => setImgModelVisible(false)} //取消
          record={imgInfo}
        />
        {info.state === 'PAID' ? (
          <CountdownBox
            submit_time={info.submit_time}
            limit_time={deliveryparam.request_order_paid_duration}
          />
        ) : null}
        {/* </div> */}
        {/* </div> */}
      </ConfigProvider>
    </>
  );
};

export default DeliveryOrderItem;