import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { hasAuth, LStorage } from '@/utils';
import { isDateRangeExceeded } from '@/utils/format';
import { XlbMessage, XlbProPageContainer } from '@xlb/components';
import dayjs from 'dayjs';
import { type FC } from 'react';
import { statusList, tableColumn } from './data';
const ProForm: FC<{ title: string }> = () => {
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        formList: [
          {
            id: ErpFieldKeyMap?.erpStoreIds,
            label: '门店',
          },
          {
            id: ErpFieldKeyMap?.erpCenterStoreIdsMultiple,
            label: '实发配送中心',
            name: 'actual_store_ids',
          },
          {
            id: 'commonSelect',
            label: '门店状态',
            name: 'store_state',
            fieldProps: {
              mode: 'multiple',
              options: statusList,
            },
          },
          {
            id: ErpFieldKeyMap?.recordDates,
            label: '记录日期',
          },
        ],
        initialValues: {
          company_id: LStorage.get('userInfo')?.company_id,
          record_dates: [
            dayjs().format('YYYY-MM-DD'),
            dayjs().add(6, 'day').format('YYYY-MM-DD'),
          ],
        },
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.warehouse.store.relation.page',
        tableColumn: tableColumn,
        selectMode: 'single',
        keepDataSource: true,
        showColumnsSetting: true,
        immediatePost: false,
        prevPost: (data: any) => {
          if (isDateRangeExceeded(data?.record_dates)) {
            XlbMessage.error('时间跨度最长不超过3个月');
            return false;
          }
          return data;
        },
      }}
      exportFieldProps={{
        url: hasAuth(['仓店关系记录', '导出'])
          ? '/erp/hxl.erp.warehouse.store.relation.export'
          : '',
        fileName: '仓店关系记录.xlsx',
      }}
    />
  );
};

export default ProForm;
