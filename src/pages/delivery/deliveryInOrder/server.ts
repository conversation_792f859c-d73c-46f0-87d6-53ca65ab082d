import { XlbFetch as ErpRequest } from "@xlb/utils";
import {  LStorage } from '@/utils'
const params = {
  company_id : LStorage.get('userInfo')?.company_id,
  operator_store_id : LStorage.get('userInfo').store_id
}
//获取数据
export const getStoreDeliveryPrice = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storedeliveryprice.page',data)
}
//复制
export const copyStoreDeliveryPrice = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storedeliveryprice.copy',data)
}
//批量修改
export const batchUpdate = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storedeliveryprice.batchupdate',data)
}
 //更新
export const Update = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storedeliveryprice.update',data)
}
//获取修改记录
export const gethistory = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storedeliverypricelog.page',data)
}

export const  deleteItems =
async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.batchdelete',{...data,...params})
}



// 配送参数查询
export const deliveryparamRead = async () => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryparam.read',{ ...params })
}

export const batchOrderSave = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.batchsave', { ...data,...params })
}

//审核
export const batchAuditInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.batchaudit', { ...data,...params })
}

//反审核
export const unbatchAuditInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.tmp.reaudit', { ...data,...params })
}

//复制
export const copyInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.copy', { ...data,...params })
}



//冲红复制
export const RedCopy = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.reverse', { ...data,...params })
}

// 调出单详情打印
export const batchread = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.batchread', { ...data,...params })
}


// 合并打印
export const batchPrinter = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.batchprint', { ...data,...params })
}

// 刷新税率
export const refreshTaxRateInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.batchrefreshtaxrate', { ...data,...params })
}
// 刷新税率校验
export const refreshTaxRateCheck = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.batchrefreshtaxrate.valid', { ...data,...params })
}

//退货原因数据
export const getreasonListAll = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.reason.find', { ...data,...params })
}


//申请退货
export const Approvereturn = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.createstoreapplicationorder', { ...data,...params })
}

//上传
export const syncInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.amasynchlog.reupload', { ...data,...params  })
}

// 新增
export const errorInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.amasynchlog.errormsg.read', { data })
}


//一键签收
export const orderReceive = async (data: { fids: string[] }) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryincheckorder.receive', data)
}


export const getStock = async (data: { store_id: string | number; }) => {
  return await ErpRequest.post('/erp/hxl.erp.storehouse.store.find', data )
}