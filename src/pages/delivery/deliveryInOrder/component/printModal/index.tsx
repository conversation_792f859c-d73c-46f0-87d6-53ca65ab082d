import useDrag from '@/hooks/useDrag';
import NiceModal, { antdModal, useModal } from '@ebay/nice-modal-react';
import { Modal, Tabs } from 'antd';
import { useEffect, useState } from 'react';
const { TabPane } = Tabs;
const PrintMoreModal = NiceModal.create((data: any) => {
  const { src } = data;
  const modal = useModal();
  const [tabKey, setTabKey] = useState('0');

  const callback = (key: any) => {
    setTabKey(key);
  };
  useEffect(() => {
    if (!antdModal(modal).visible) return;
    useDrag('.ant-modal-header', '.ant-modal-content');
  }, [antdModal(modal).visible]);
  return (
    <Modal
      keyboard
      width={'50%'}
      title={`打印详情`}
      wrapClassName="xlbDialog"
      {...antdModal(modal)}
      footer={null}
    >
      <Tabs activeKey={tabKey} onChange={callback}>
        {src?.map((item, index) => {
          return (
            <TabPane tab={item.template_name} key={index}>
              <iframe width={'100%'} height={'600px'} src={item.url} />
            </TabPane>
          );
        })}
      </Tabs>
    </Modal>
  );
});

export default PrintMoreModal;
