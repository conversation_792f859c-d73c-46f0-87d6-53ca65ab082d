import { XlbFetch as ErpRequest } from '@xlb/utils';
import {  LStorage } from '@/utils';
const company_id = LStorage.get('userInfo')?.company_id
const operator_store_id= LStorage.get('userInfo')?.store_id

 
//  账号管理仓库查询
export const getStock = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storehouse.store.find', { ...data })
}

//  查询
export const getWholeSaleList = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.page', { ...data })
}
//  删除
export const deleteWholeSaleList = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.batchdelete', { ...data })
}

//  复制
export const copyWholeSale = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.copy', { ...data })
}

//  冲红复制
export const redcopyWholeSale = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.reverse', { ...data })
}
// 批量冲红
export const redcopyWholeBatch = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.batchreverse', { ...data })
}

//查询明细配送价
export const deliveryPrice = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.wholesaleprice', { ...data })
}

//新增
export const addWholeSale = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.save', { ...data })
}

//更新
export const updateWholeSale = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.update', { ...data })
}
//审核
export const auditeWholeSale = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.audit', { ...data })
}

//获取明细
export const getWholeSale = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.read', { ...data })
}

// 获取制单批发参数 -- 计算制单有效期
export const getBuyparam = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleparam.read', { ...data })
}
// 获取数据
export const getItemRequest = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.item.page', { ...data })
}
// 打印
export const print = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.print', { ...data })
}
//判读批发客户与门店是否有关联
export const relation = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.client.storerelationship', { ...data })
}
//判读批发客户与门店是否有关联
export const balanceRead = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.client.balance.read', { ...data })
}

//一键签收
export const orderReceive = async (data: { fids: string[] }) => {
  return await ErpRequest.post('/erp/hxl.erp.wholesaleorder.receive', { ...data })
}

//签收日期配置查询
export enum OrderType {
  DELIVERY_IN = 'DELIVERY_IN',
  WHOLESALE = 'WHOLESALE'
}
export interface OrderTypeRequest {
  order_type: OrderType
}
export const searchReceiveconfig = async (data: OrderTypeRequest) => {
  return await ErpRequest.post('/erp/hxl.erp.autoreceiveconfig.page', { ...data,company_id,operator_store_id })
}
//签收日期配置删除
export const deleteReceiveconfig = async (data: { ids: number[]; order_type: OrderType }) => {
  return await ErpRequest.post('/erp/hxl.erp.autoreceiveconfig.batchdelete', { ...data })
}
export interface ISaveReceiveconfig extends OrderTypeRequest {
  day: number
  org_ids: number[]
  store_ids: number[]
}

//签收日期配置新增
export const saveReceiveconfig = async (data: ISaveReceiveconfig) => {
  return await ErpRequest.post('/erp/hxl.erp.autoreceiveconfig.save', { ...data })
}

//签收日期配置更新
export interface UpdateReceiveconfig extends ISaveReceiveconfig {
  id: number
}
export type IUpdateReceiveconfig = Omit<UpdateReceiveconfig, 'order_type'>
export const updateReceiveconfig = async (data: {
  list: IUpdateReceiveconfig[]
  order_type: OrderType
}) => {
  return await ErpRequest.post('/erp/hxl.erp.autoreceiveconfig.batchupdate', {...data })
}
