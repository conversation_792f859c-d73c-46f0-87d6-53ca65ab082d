import { XlbFetch as ErpRequest } from '@xlb/utils';
import {
  deleteReceiveconfig,
  searchReceiveconfig,
  updateReceiveconfig,
  type IUpdateReceiveconfig,
  type OrderType,
} from './server';

import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbCheckbox,
  XlbIcon,
  XlbInputDialog,
  XlbInputNumber,
  XlbModal,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { message, Spin } from 'antd';
import { useEffect, useState } from 'react';
import addReceiveSettingModal from './addReceiveSettingModal';
import style from './index.less';
import { hasAuth } from '@/utils';

interface IProps {
  title: string;
  type: OrderType;
}
const receiveSettingModal = NiceModal.create((props: IProps) => {
  const { title, type } = props;
  const { visible, hide } = NiceModal.useModal();
  const [form] = XlbBasicForm.useForm();

  const [orgList, setOrgList] = useState<any[]>([]);
  // 获取二级组织
  const getOrgList = async () => {
    const res = await ErpRequest.post('/erp/hxl.erp.org.find', { level: 2 });
    if (res.code == 0) {
      setOrgList(
        res.data?.map((item: any) => ({ label: item.name, value: item.id })),
      );
    }
  };
  // 获取配置数据
  const [isLoading, setIsLoading] = useState(false);
  const [settingData, setSettingData] = useState<any[]>([]);
  const formatData = (dataList: any[]) => {
    dataList.map((item: any) => {
      form.setFieldsValue({
        [item.id]: {
          ...item,
          org_ids: item.organizations?.map((i: any) => i?.id),
          store_ids: item.stores?.map((i: any) => i?.id),
        },
      });
    });
  };
  const getSettinigData = async () => {
    setIsLoading(true);
    const res = await searchReceiveconfig({ order_type: type });
    if (res?.code === 0) {
      const dataList = res?.data?.content || [];
      setSettingData(dataList);
      formatData(dataList);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (visible) {
      form.resetFields();
      getOrgList();
      getSettinigData();
    }
  }, [visible]);

  // operate
  const addItem = () => {
    NiceModal.show(addReceiveSettingModal, {
      settingData,
      setSettingData,
      formatData,
      type,
    });
  };
  const deleteItem = () => {
    const chooseList = settingData?.filter((item) => item.check) || [];
    if (!chooseList.length) {
      return;
    }
    XlbTipsModal({
      title: '提示',
      width: 400,
      tips: `已选择${chooseList.length}条数据，确认删除？`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await deleteReceiveconfig({
          ids: chooseList.map((item) => item.id),
          order_type: type,
        });
        if (res.code === 0) {
          message.success('删除成功');
          setSettingData(settingData?.filter((item) => !item.check));
          return true;
        }
        return false;
      },
    });
  };
  const [confirmLoading, setConfirmLoading] = useState(false);
  const onOk = () => {
    form.validateFields().then(async () => {
      setConfirmLoading(true);
      const list: IUpdateReceiveconfig[] = [];
      settingData?.forEach((item) => {
        if (form.getFieldValue([item.id])) {
          list.push({
            day: form.getFieldValue([item.id, 'day']),
            id: item.id,
            org_ids: form.getFieldValue([item.id, 'org_ids']),
            store_ids: form.getFieldValue([item.id, 'store_ids']) || [],
          });
        }
      });
      const res = await updateReceiveconfig({ list, order_type: type });
      if (res.code === 0) {
        message.success(`${title}更新成功`);
        hide();
      }
      setConfirmLoading(false);
    });
  };
  return (
    <XlbModal
      title={title}
      keyboard={false}
      centered
      open={visible}
      maskClosable={false}
      confirmLoading={confirmLoading}
      isCancel
      onOk={onOk}
      onCancel={hide}
      width={900}
    >
      <div className={style.receiveContainer}>
        <div style={{ display: 'flex', marginBottom: 12 }}>
          {hasAuth(['调入单/配置', '编辑']) && (
            <XlbButton
              label="新增"
              type="primary"
              onClick={addItem}
              icon={<XlbIcon size={16} name="baocunxinzeng" />}
              style={{ marginRight: 12 }}
            />
          )}
          {hasAuth(['调入单/配置', '删除']) && (
            <XlbButton
              label="删除"
              type="primary"
              disabled={!settingData?.some((item) => item.check)}
              onClick={deleteItem}
              icon={<XlbIcon size={16} name="shanchu" />}
            />
          )}
        </div>
        <Spin spinning={isLoading}>
          <XlbBasicForm
            form={form}
            style={{ minHeight: 80 }}
            labelCol={{ span: 6 }}
            disabled={!hasAuth([`调入单/配置`, '编辑'])}
          >
            {settingData?.map((item) => (
              <div
                key={item.id}
                style={{ width: '100%', display: 'flex', overflow: 'hidden' }}
              >
                <XlbBasicForm.Item name={[item.id, 'check']}>
                  <XlbCheckbox
                    checked={item.check}
                    disabled={!hasAuth([`调入单/配置`, '删除'])}
                    onChange={(e) => {
                      setSettingData([
                        ...settingData?.map((i) => ({
                          ...i,
                          check: i.id === item.id ? e.target.checked : i.check,
                        })),
                      ]);
                    }}
                  />
                </XlbBasicForm.Item>
                <XlbBasicForm.Item
                  name={[item.id, 'org_ids']}
                  rules={[{ required: true, message: '请选择组织' }]}
                  label="组织"
                >
                  <XlbSelect
                    value={[item.id, 'org_ids']}
                    width={200}
                    mode="multiple"
                    allowClear
                    size="small"
                    placeholder="请选择"
                    options={orgList}
                    onChange={() =>
                      form.setFieldValue([item.id, 'store_ids'], [])
                    }
                  />
                </XlbBasicForm.Item>
                <XlbBasicForm.Item
                  noStyle
                  dependencies={[[item.id, 'org_ids']]}
                >
                  {({ getFieldValue }) => {
                    return (
                      <XlbBasicForm.Item
                        name={[item.id, 'store_ids']}
                        label="发货门店"
                      >
                        <XlbInputDialog
                          style={{ width: 200 }}
                          value={[item.id, 'store_ids']}
                          disabled={!hasAuth([`调入单/配置`, '编辑'])}
                          dialogParams={{
                            type: 'store',
                            dataType: 'lists',
                            isMultiple: true,
                            nullable: false,
                            data: {
                              org_ids: getFieldValue([item.id, 'org_ids']),
                              status: true,
                            },
                          }}
                          fieldNames={{
                            idKey: 'id',
                            nameKey: 'store_name',
                          }}
                        />
                      </XlbBasicForm.Item>
                    );
                  }}
                </XlbBasicForm.Item>
                <XlbBasicForm.Item
                  name={[item.id, 'day']}
                  rules={[{ required: true, message: '请输入天数' }]}
                  label="天数"
                >
                  <XlbInputNumber
                    width={100}
                    min={1}
                    max={99}
                    placeholder="请输入"
                    addonAfter="天"
                  />
                </XlbBasicForm.Item>
              </div>
            ))}
          </XlbBasicForm>
        </Spin>
      </div>
    </XlbModal>
  );
});
export default receiveSettingModal;