import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbInputDialog,
  XlbInputNumber,
  XlbModal,
  XlbSelect,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { useEffect, useState } from 'react';
import style from './index.less';
import { saveReceiveconfig, type OrderType } from './server';

interface IProps {
  settingData: any[];
  setSettingData: (data: any[]) => void;
  formatData: (dataList: any[]) => any;
  type: OrderType;
}
const addReceiveSettingModal = NiceModal.create((props: IProps) => {
  const { settingData, setSettingData, formatData, type } = props;
  const { visible, hide } = NiceModal.useModal();
  const [form] = XlbBasicForm.useForm();
  const [orgList, setOrgList] = useState<any[]>([]);

  // 获取二级组织
  const getOrgList = async () => {
    const res = await ErpRequest.post('/erp/hxl.erp.org.find', { level: 2 });
    if (res.code == 0) {
      setOrgList(
        res.data?.map((item: any) => ({ label: item.name, value: item.id })),
      );
    }
  };

  // operate
  const [confirmLoading, setConfirmLoading] = useState(false);
  const onOk = () => {
    form.validateFields().then(async () => {
      setConfirmLoading(true);
      const res = await saveReceiveconfig({
        ...form.getFieldsValue(),
        order_type: type,
      });
      if (res.code == 0) {
        setSettingData([...settingData, res.data]);
        formatData([res.data]);
        hide();
      }
      setConfirmLoading(false);
    });
  };
  useEffect(() => {
    if (visible) {
      getOrgList();
    } else {
      form.resetFields();
    }
  }, [visible]);
  return (
    <XlbModal
      title="新增配置"
      keyboard={false}
      centered
      open={visible}
      maskClosable={false}
      isCancel
      onOk={onOk}
      confirmLoading={confirmLoading}
      onCancel={hide}
      width={434}
    >
      <XlbBasicForm
        form={form}
        layout="inline"
        style={{ padding: '16px 0' }}
        className={style.addRecevieContainer}
      >
        <XlbBasicForm.Item
          name="org_ids"
          rules={[{ required: true, message: '请选择组织' }]}
          label="组织"
        >
          <XlbSelect
            value="org_ids"
            width={264}
            mode="multiple"
            allowClear
            size="small"
            placeholder="请选择"
            options={orgList}
            onChange={() => form.setFieldValue('store_ids', [])}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item noStyle dependencies={['org_ids']}>
          {({ getFieldValue }) => {
            return (
              <XlbBasicForm.Item name="store_ids" label="发货门店">
                <XlbInputDialog
                  style={{ width: 232 }}
                  value="store_ids"
                  dialogParams={{
                    type: 'store',
                    dataType: 'lists',
                    isMultiple: true,
                    nullable: false,
                    data: {
                      org_ids: getFieldValue('org_ids'),
                      status: true,
                    },
                  }}
                  fieldNames={{
                    idKey: 'id',
                    nameKey: 'store_name',
                  }}
                />
              </XlbBasicForm.Item>
            );
          }}
        </XlbBasicForm.Item>

        <XlbBasicForm.Item
          name="day"
          rules={[{ required: true, message: '请输入天数' }]}
          label="天数"
        >
          <XlbInputNumber
            width={264}
            min={1}
            max={99}
            placeholder="请输入"
            addonAfter="天"
          />
        </XlbBasicForm.Item>
      </XlbBasicForm>
    </XlbModal>
  );
});
export default addReceiveSettingModal;