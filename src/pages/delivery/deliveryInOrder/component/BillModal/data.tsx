import { columnWidthEnum } from '@/data/common/constant';
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';
//调出单
//调入状态
export const inState = [
  {
    label: '未调入',
    value: 'UNIN',
    type: 'danger',
  },
  {
    label: '部分调入',
    value: 'PARTIN',
    type: 'info',
  },
  {
    label: '全部调入',
    value: 'ALLIN',
    type: 'success',
  },
];
//调出单
export const deliveryForm: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '日期范围',
    name: 'audit_date',
    // showTime:true,
    resultFormat: 'YYYY-MM-DD',
    format: 'YYYY-MM-DD',
    allowClear: false
  },
  {
    label: '单据号',
    name: 'fid',
    code: 'fid',
    type: 'input',
    clear: true,
    check: true,
  },
  {
    label: '调入状态',
    name: 'delivery_in_states',
    type: 'select',
    clear: true,
    multiple: true,
    check: true,
    options: inState,
  },
  {
    label: '调入门店',
    name: 'in_store_ids',
    type: 'inputDialog',
    clear: false,
    check: true,
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    },
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isMultiple: false,
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '调出门店',
    name: 'store_ids',
    type: 'inputDialog',
    clear: true,
    check: true,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isMultiple: true,
      data: {
        enabled: true,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    },
  },
  {
    label: '调出仓库',
    name: 'storehouse_id',
    type: 'select',
    clear: true,
    check: true,
    options: [],
  },
];
export const deliveryTable: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
    render: (text, record) => {
      console.log(text, record);
      return '1';
    },
  },
  {
    name: '单据号',
    code: 'fid',
    width: columnWidthEnum.fid,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'in_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出仓库',
    code: 'storehouse_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '金额',
    code: 'money',
    width: 134,
    features: { sortable: true, format: 'MONEY' },
    align: 'right',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '商品数',
    code: 'item_count',
    width: 90,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '调入状态',
    code: 'in_state',
    width: 100,
    features: { sortable: true },
    lock: true,
    align: 'left',
    render: (text) => {
      const item = inState.find((t) => t.value === text);
      return (
        <span style={{ color: item?.type === 'danger' ? '#00b42b' : '' }}>
          {item?.label}
        </span>
      );
    },
  },
];