import {
  SearchFormType,
  SelectType,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbMessage,
  XlbModal,
  XlbPageContainer,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { LStorage } from '@xlb/utils';
import { DefaultOptionType } from 'antd/es/select';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { deliveryForm, deliveryTable, inState } from './data';
import NiceModal from '@ebay/nice-modal-react';
import PromiseModal from '@/components/promiseModal/PromiseModal';

const Index = (props: {
  options: (SelectType[] & DefaultOptionType[]) | undefined;
  visible: boolean | undefined;
  selected: any,
  hide: () => void;
  setData: (arg0: any[]) => void;
}) => {
  const { ToolBtn, SearchForm, Table } = XlbPageContainer;
  const [form] = XlbBasicForm.useForm<any>();
  const setSelectedRows = useRef([]);
  const [formnList] = useState<SearchFormType[]>(
    JSON.parse(JSON.stringify(deliveryForm)),
  );
  const [columns] = useState<XlbTableColumnProps<any>[]>(deliveryTable);
  const modal = NiceModal.useModal();

  formnList.forEach((_) => {
    if (_.name === 'storehouse_id') {
      _.options = props.options;
    }
  });
  columns.forEach((_) => {
    if (_.code === 'fid') {
      _.render = (text) => {
        return <div
          className="link overwidth"
          onClick={(e) => {
            e.stopPropagation();
            NiceModal.show(PromiseModal, {
              order_type: '调出单',
              order_fid: text,
            });
          }}
        >
          {text}
        </div>
      };
    }
    if (_.code === 'in_state') {
      _.render = (text) => {
        const item = inState.find((v) => v.value === text)
        return (
          <div className={`overwidth ${item ? item.type : ''}`}>{item ? item.label : ''}</div>
        )
      };
    }
  });

  const closeModal = () => {
    modal.resolve(false);
    modal.hide();
  };

  return (
    <>
      <XlbModal
        title={'调出单'}
        keyboard={false}
        open={modal.visible}
        maskClosable={false}
        onCancel={() => {
          form.resetFields();
          closeModal();
        }}
        onOk={() => {
          const list = setSelectedRows.current || [];
          if (list?.length === 0) {
            XlbMessage.warning('请选择单据');
            return;
          }
          if (list.every((v: any) => v?.in_state === 'ALLIN')) {
            XlbTipsModal({
              tips: `所选单据已全部调入，请重新选择！`,
            });
            return;
          }
          const store_id = list.every((v: any) => v?.store_id === list[0]?.store_id)
          const in_store_id = list.every((v: any) => v?.in_store_id === list[0]?.in_store_id)
          const storehouse_id = list.every((v: any) => v?.storehouse_id === list[0]?.storehouse_id)
          if (!storehouse_id || !in_store_id || !store_id) {
            XlbTipsModal({tips: '请选择调出门店、调出仓库、调入门店相同的单据进行调入！'})
            return
          }
          props.setData(list?.map((_: any) => _.fid));
          closeModal();
        }}
        width={'90%'}
        centered
      >
        <div style={{ height: 'calc(100vh - 280px)' }}>
          <XlbPageContainer
            tableColumn={columns}
            immediatePost={true}
            isShowTemplate={false}
            isOldBtn={true}
            // selectedRowKeys={props?.selected || []}
            prevPost={(pagin) => {
              const data = form?.getFieldsValue(true) || {};
              return {
                ...pagin,
                ...data,
                is_sync_scm: false,
                time_desc: 0,
                audit_date: [`${data?.audit_date?.[0]} 00:00:00`, `${data?.audit_date?.[1]} 23:59:59`],
              };
            }}
            url="/erp/hxl.erp.deliveryoutorder.pageforin"
          >
            <ToolBtn showColumnsSetting={false}>
              {({ fetchData, loading, selectRow }) => {
                setSelectedRows.current = selectRow;
                return (
                  <>
                    <XlbButton
                      loading={loading}
                      onClick={() => fetchData()}
                      type="primary"
                      icon={<span className="iconfont icon-sousuo" />}
                    >
                      查询
                    </XlbButton>
                  </>
                );
              }}
            </ToolBtn>
            <SearchForm>
              <XlbForm
                initialValues={{
                  audit_date: [
                    dayjs().format('YYYY-MM-DD'),
                    dayjs().format('YYYY-MM-DD'),
                  ],
                  in_store_ids: [LStorage.get('userInfo').store_id],
                  delivery_in_states: ['PARTIN', 'UNIN'],
                  company_id: LStorage.get('userInfo')?.company_id,
                  state: 'AUDIT',
                  operator_store_id: LStorage.get('userInfo').store_id,
                  is_sync_scm: false,
                  time_desc: 0,
                }}
                isHideDate={true}
                formList={formnList}
                form={form}
              ></XlbForm>
            </SearchForm>
            <Table selectMode="multiple" primaryKey="fid"></Table>
          </XlbPageContainer>
        </div>
      </XlbModal>
    </>
);
};
export default NiceModal.create(Index);