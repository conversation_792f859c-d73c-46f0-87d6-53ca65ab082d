import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { LStorage } from '@/utils';
import {
  DeleteOutlined,
  EditOutlined,
  FileAddOutlined,
} from '@ant-design/icons';
import {
  XlbBasicData,
  XlbButton,
  XlbCheckbox,
  XlbProForm,
  XlbTable,
  XlbTipsModal,
} from '@xlb/components';
import { safeMath, toFixed } from '@xlb/utils';
import { Checkbox, Form, Input, InputNumber, Modal, message } from 'antd';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { itemTableList } from './../../data';
import styles from './batchOrder.less';
// import XlbProgress from './progress'
import { cloneDeep } from 'lodash';
import { batchOrderSave, deliveryparamRead } from './../../server';
import dayjs from 'dayjs';
export const tableStyle = {
  '--header-row-height': '28px',
  '--row-height': '24px',
  '--header-bgcolor': '#fafafa',
  '--header-color': '#666',
  '--row-color': '#000',
  '--cell-padding': '0',
  '--highlight-bgcolor': '#E8F1FF',
  fontSize: '14px',
};
const BatchOrderIndex = (props: any) => {
  const formRef = useRef<any>(null);
  const { open, setOpen } = props;
  const [form] = Form.useForm();
  const [formUpdate] = Form.useForm();
  const [apiCalls, setApiCalls] = useState(0);
  const [rowData, setRowData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [itemArr, setItemArr] = useState<any>(
    JSON.parse(JSON.stringify(itemTableList)),
  );
  const [goodList, setGoodList] = useState<any>([]); //商品数组
  const [toOtherCenter, setToOtherCenter] = useState<boolean>(false); //是否支持跨配送中心调拨
  const [unCenterStoreApplication, setUnCenterStoreApplication] =
    useState(false);
  const { enable_organization } = useBaseParams((state) => state);
  const [selectedStoreList, setSelectedStoreList] = useState<string[]>([]); //已选择门店
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 });
  const [selectedGoodList, setSelectedGoodList] = useState<number[]>([]); //已选择商品
  const modalRef = useRef(null);

  //查询是否开启跨配送中心调拨
  const getdeliveryparam = async () => {
    const res = await deliveryparamRead();
    if (res?.code === 0) {
      setToOtherCenter(res.data.un_center_transform);
      setUnCenterStoreApplication(res.data.un_center_store_application);
    }
  };
  useEffect(() => {
    getdeliveryparam();
  }, []);
  const inputChange = (e: any, index: number, renderv: any, record: any) => {
    const list = JSON.parse(JSON.stringify(rowData));
    list[index][renderv.code] = Number(e.target.value);

    if (renderv.code?.endsWith('_delivery')) {
      const curItem =
        list[index].detail.find(
          (v: any) => `${v.id}_delivery` === renderv.code,
        ) || {};
      curItem.quantity = Number(e.target.value);
    }
    if (renderv.code?.endsWith('_price')) {
      const curItem =
        list[index].detail.find((v: any) => `${v.id}_price` === renderv.code) ||
        {};
      curItem.price = Number(e.target.value);
    }
    setRowData(list);
  };

  // 防抖
  const debounce = (fn: any, ms: number = 500) => {
    let timer: any;
    return function (...args: any) {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        fn(...args);
        timer = null;
      }, ms);
    };
  };

  const debounceTask = debounce(inputChange, 500);

  const inputOnblur = (
    e: any,
    value: any,
    record: any,
    index: number,
    f_id: number,
  ) => {
    const reg = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (value.code.endsWith('_delivery')) {
      if (!reg.test(e.target.value)) {
        message.warning('请输入数字');
        rowData[index][value.code] = '0.000';
        return setRowData(JSON.parse(JSON.stringify(rowData)));
      }
      rowData[index][value.code] = e.target.value
        ? Number(e.target.value).toFixed(3)
        : '0.000';
    }
    if (value.code.endsWith('_price')) {
      if (!reg.test(e.target.value) || e.target.value < 0) {
        message.warning('请输入>=0的数字');
        rowData[index][value.code] = '0.0000';
        return setRowData(JSON.parse(JSON.stringify(rowData)));
      }
      rowData[index][value.code] = e.target.value
        ? Number(e.target.value).toFixed(4)
        : '0.0000';
    }

    setRowData(JSON.parse(JSON.stringify(rowData)));
  };

  //操作删除行
  const showDeleteModal = (record: any, index: number) => {
    const store_index = rowData.findIndex(
      (store: any) => store.store_id === record.store_id,
    );
    rowData.splice(store_index, 1);
    // 店删光时品也要删光
    if (rowData?.length == 0) {
      setGoodList([]);
      setItemArr([itemArr[0], itemArr[itemArr.length - 1]]);
    }
    setRowData([...rowData]);
    setPagin({
      ...pagin,
      total: rowData.length,
    });
  };

  const storeStrongRender = (item: any) => {
    if (item.children && item.children.length) {
      item.children.map((v) => {
        switch (v?.code) {
          case 'index':
            v.render = (value: any, record: any, index: number) => {
              return record.tag === '合计' ? (
                <div className="info overwidth">{value}</div>
              ) : (
                <div className="info overwidth">
                  {(pagin.pageNum - 1) * pagin.pageSize + 1 + index}
                </div>
              );
            };
            break;

          case 'action':
            v.render = (value: any, record: any, index: number) => {
              return record.tag === '合计' ? null : (
                <div className="overwidth">
                  <DeleteOutlined
                    className="link cursors"
                    style={{ fontSize: '16px' }}
                    onClick={(e) => {
                      e.stopPropagation();
                      showDeleteModal(record, index);
                    }}
                  />
                </div>
              );
            };
            break;
          case 'store_type':
          case 'store_name':
            return (v.render = (value: any, record: any, index: number) => (
              <div className="info overwidth">{value}</div>
            ));
          default:
            if (v.children && v.children.length) {
              v.children?.map((it: any) => {
                switch (it?.code) {
                  case `${v.code}_delivery`:
                  case `${v.code}_price`:
                    it.render = (value: any, record: any, index: number) => {
                      return record.edit ? (
                        <Input
                          className="full-box"
                          autoComplete="off"
                          id={it.code + '-' + index.toString()}
                          defaultValue={Number(value || 0)}
                          style={{ textAlign: 'right' }}
                          onChange={(e) => debounceTask(e, index, it, record)}
                          onBlur={(e) =>
                            inputOnblur(e, it, record, index, v.code)
                          }
                          onFocus={(e) => e.target.select()}
                          // onClick={(e) => { e.stopPropagation(); }}
                        />
                      ) : (
                        <div
                          className="info overwidth"
                          style={{ textAlign: 'right' }}
                        >
                          {value ? value : '0'}
                        </div>
                      );
                    };
                    break;
                  case `${v.code}_stock`:
                    it.render = (value: any, record: any, index: number) => {
                      return record.tag === '合计' ? null : (
                        <div
                          className="info overwidth"
                          style={{ textAlign: 'right' }}
                        >
                          {/* {value ? value : '0'} */}
                          {toFixed(
                            safeMath.divide(value, record.ratio),
                            'QUANTITY',
                            true,
                          )}
                        </div>
                      );
                    };
                    break;
                }
              });
            }

            break;
        }
      });
    }
  };

  const onChange = (e: any, list: any, i: number) => {
    const key = Number(e.target.value);

    // 获取modal中的table
    const checkBoxs = modalRef?.current
      ?.querySelector('thead')
      .querySelector('.art-table-header-row')!
      .getElementsByClassName('art-table-header-cell');

    if (key === 9999 || key === 1111) {
      for (let k = 0; k < checkBoxs.length - 1; k++) {
        k > 0 &&
          (checkBoxs[k].querySelector('input[type="checkbox"]')!.checked =
            e.target.checked);
      }
      setSelectedGoodList(key === 9999 ? goodList.map((v) => v.id) : []);
    } else {
      setSelectedGoodList((prevList) => {
        const index = prevList.indexOf(key);
        if (index === -1) {
          return [...prevList, key];
        } else {
          const newList = [...prevList];
          newList.splice(index, 1);
          return newList;
        }
      });
    }
  };

  const handleAddClick = async () => {
    const list = await XlbBasicData({
      isMultiple: true,
      url: '/erp/hxl.erp.deliveryoutorder.batchsave.item.page',
      type: 'goods',
      dataType: 'lists',
      primaryKey: 'id',
      selectedList: rowData?.[0].detail.map((i: any) => ({
        ...i,
        id: i.item_id || i.id,
      })),
      fieldNames: {
        idKey: 'id',
        nameKey: 'name',
      },
      data: {
        in_store_ids: rowData?.map((i) => i?.store_id),
        store_id: formRef?.current?.getFieldValue('store_id')?.[0],
        storehouse_id: formRef?.current?.getFieldValue('storehouse_id'),
      },
    });
    if (list) {
      let goodlists = [];
      const listIds = list.map((t) => t.id);
      let newItemArr = cloneDeep(itemArr);
      // 删除没有选中的
      newItemArr = itemArr?.filter(
        (k: any, i: number) =>
          listIds?.includes(k.id) || listIds?.includes(k.item_id),
      );
      newItemArr = [itemArr[0], ...newItemArr, itemArr[itemArr?.length - 1]];
      // console.log(newItemArr, 'newItemArr')
      if (newItemArr.length && newItemArr.length > 2) {
        goodlists = JSON.parse(
          JSON.stringify(newItemArr.slice(1, newItemArr.length - 1)),
        );
        // console.log(goodList, 'goodListsss')
        setGoodList(goodlists);
      }
      // 跳过已有的
      for (let i = 0; i < list.length; i++) {
        const isExist = goodlists.some((item: any) => {
          return item.id == list[i].id;
        });
        if (isExist) continue;
        // 添加新的
        list[i].seleted = false;
        list[i][list[i].id + '_delivery'] = 0;
        list[i][list[i].id + '_price'] = 0;
        list[i][list[i].id + '_stock'] = list[i].basic_stock_quantity;
        newItemArr.splice(newItemArr.length - 1, 0, {
          title: (
            <span className={styles.checkText}>
              <input
                className={styles.diyCheckBox}
                type="checkbox"
                key={list[i].id}
                value={list[i].id}
                onChange={(e: any) => onChange(e, list, i)}
              />
              {list[i].name + '/' + list[i].code}
            </span>
          ),
          code: list[i].id,
          id: list[i].id,
          width: 300,
          align: 'center',
          children: [
            {
              title: `采购规格:${list[i].purchase_spec}`,
              code: list[i].id,
              width: 160,
              align: 'center',
              children: [
                {
                  title: `调出量(${list[i].delivery_unit})`,
                  code: `${list[i].id}_delivery`,
                  align: 'center',
                },
                {
                  title: `门店库存量(${list[i].delivery_unit})`,
                  code: `${list[i].id}_stock`,
                  align: 'center',
                },
                {
                  title: `单价`,
                  code: `${list[i].id}_price`,
                  align: 'center',
                },
              ],
            },
          ],
          obj: list[i],
        });
      }
      setItemArr([...newItemArr]);
      // console.log(goodList, 'goodList222')
      setGoodList(
        JSON.parse(JSON.stringify(newItemArr.slice(1, newItemArr.length - 1))),
      );

      // 若存在rowData.length
      if (rowData.length > 0) {
        // console.log(rowData, 'rowData 11', list)
        const arr = rowData;
        arr.map((v) => {
          // console.log(v, 'jjj')
          const listIdsT = list.map((t) => t.id);
          v.detail = v.detail?.filter(
            (k: any) =>
              listIdsT?.includes(k.id) || listIdsT?.includes(k.item_id),
          );
          // console.log(v.detail, 'v.detail')
          list.map((i: any) => {
            v[i.id + '_delivery'] = v?.quantity || 0;
            v[i.id + '_price'] =
              v?.price ||
              safeMath.multiply(i.basic_price_list[v?.id], i.delivery_ratio) ||
              0;
            v[i.id + '_stock'] = v.basic_stock_quantity || 0;
            // v.detail 原来的 list即将替换的
            if (
              !v.detail.find((s: any) => s.item_id === i.id || s.id === i.id)
            ) {
              // 此处只适用于添加
              v.detail.push({
                ...i,
                sale_price: i?.sale_price,
                account_method: i.account_method,
                item_id: i.id || i.item_id,
                item_code: i.code || i.item_code,
                item_bar_code: i.bar_code || i.item_bar_code,
                item_name: i.name || i.item_name,
                item_spec: i.purchase_spec || i.item_spec,
                unit: i.delivery_unit,
                quantity: i.quantity || 0,
                price:
                  i.price ||
                  safeMath.multiply(
                    i.basic_price_list[v?.id],
                    i.delivery_ratio,
                  ),
                money: i.money || 0,
                tax_rate: i.tax_rate_list[v?.id] || 0,
                tax_money: i.tax_money || 0,
                tare: i.tare || 0,
                basic_unit: i.unit,
                ratio: i.delivery_ratio,
                basic_quantity: i.basic_quantity || 0,
                basic_price: i.basic_price || 0,
                present_unit: i.present_unit ? i.present_unit : i.delivery_unit,
                present_ratio: i.present_ratio
                  ? i.present_ratio
                  : i.delivery_ratio,
                present_quantity: i.present_quantity || 0,
                producing_date: i?.producing_date || null,
                expire_date: i?.expire_date || null,
                batch_number: i?.batch_number || null,
                period: i.period
                  ? i.period
                  : i.expire_type === 1
                    ? i.expire_type_num + '天'
                    : i.expire_type_num + '月', //保质期
                basic_stock_quantity: i.basic_stock_quantity,
                basic_available_stock_quantity:
                  i.basic_available_stock_quantity,
                memo: i.memo || '',
                cost_price: i.cost_price,
                producing_date_flag: i.producing_date_flag, //是否可选生产日期
                batch_number_flag: i.batch_number_flag, //批次号
                expire_date_flag: i.expire_date_flag, //到期日期
                date_in_type: i.date_in_type, //日期录入规则
                delivery_unit: i.delivery_unit,
                delivery_ratio: i.delivery_ratio, //配送单位换算率
                purchase_unit: i.purchase_unit,
                purchase_ratio: i.purchase_ratio, //采购单位
                stock_unit: i.stock_unit,
                stock_ratio: i.stock_ratio, //库存单位
                wholesale_unit: i.wholesale_unit,
                wholesale_ratio: i.wholesale_ratio, //批发单位
                batch_unit: i.batch_unit || '',
                batch_ratio: i.batch_ratio || '', //批次单位
                units: Array.from(
                  new Set([
                    JSON.stringify({
                      name: i.delivery_unit,
                      ratio: i.delivery_ratio,
                    }),
                    JSON.stringify({
                      name: i.unit,
                      ratio: 1,
                    }),
                    JSON.stringify({
                      name: i.delivery_unit,
                      ratio: i.delivery_ratio,
                    }),
                    JSON.stringify({
                      name: i.purchase_unit,
                      ratio: i.purchase_ratio,
                    }),
                    JSON.stringify({
                      name: i.stock_unit,
                      ratio: i.stock_ratio,
                    }),
                    JSON.stringify({
                      name: i.wholesale_unit,
                      ratio: i.wholesale_ratio,
                    }),
                  ]),
                ),
                [i.id + '_delivery']: '0.000',
                [i.id + '_price']: '0.0000',
                [i.id + '_stock']: i?.basic_stock_quantity || 0,
              });
            }
          });
        });
        // console.log('arr:', arr)
        setRowData([...arr]);
      }
    }
  };

  const handleBatchStore = async () => {
    const list = await XlbBasicData({
      isMultiple: true,
      url: '/erp/hxl.erp.store.all.shortfind',
      selectedList: rowData?.map((v) => ({ ...v, id: v.store_id })),
      primaryKey: 'id',
      type: 'store',
      dataType: 'lists',
      data: {
        skip_filter: true,
        centerFlag: true,
        superiors:
          !formRef?.current?.getFieldValue('isCenter') && !toOtherCenter
            ? formRef?.current?.getFieldValue('deliveryCenterId')
            : null,
        center_flag: LStorage.get('userInfo').store.enable_delivery_center
          ? null
          : false,
        management_type:
          !unCenterStoreApplication &&
          formRef?.current?.getFieldValue('managementType') !== null &&
          !formRef?.current?.getFieldValue('isCenter')
            ? formRef?.current?.getFieldValue('managementType') == '0'
              ? '0'
              : '1'
            : null,
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'name',
      },
    });
    if (list) {
      // console.log(list, 'newList 1')
      const newList = list.map((v: any) => {
        return {
          ...v,
          store_id: v.id,
          store_name: v.store_name,
          store_type: v.store_group ? v.store_group.name : '',
          store_type_id: v.store_group ? v.store_group.id : '',
          detail: [],
        };
      });

      // const mergeArr = [...rowData, ...newList]
      newList.map((item, index) => {
        if (item.newRow) {
          newList.splice(index, 1);
        }
      });
      // console.log('mm:', newList)
      setRowData([...newList]);
    }
  };

  // 处理goodsList
  const getGoodList = (o_rowdata: any[]) => {
    const new_goodLists = [];

    if (o_rowdata && o_rowdata.length > 0) {
      if (o_rowdata[0].detail && o_rowdata[0].detail.length > 0) {
        // 处理成goodlist
        for (let i = 0; i < o_rowdata[0].detail.length; i++) {
          new_goodLists.push({
            title: (
              <span className={styles.checkText}>
                <input
                  type="checkbox"
                  className={styles.diyCheckBox}
                  disabled={
                    o_rowdata[0].detail[i].generate_replenishment_order_state
                  }
                  key={o_rowdata[0].detail[i].id}
                  value={o_rowdata[0].detail[i].id}
                  defaultChecked={
                    o_rowdata[0].detail[i].generate_replenishment_order_state
                  }
                  onChange={(e: any) => onChange(e, o_rowdata[0].detail, i)}
                ></input>
                {o_rowdata[0].detail[i].name +
                  '/' +
                  o_rowdata[0].detail[i].code}
              </span>
            ),
            code: o_rowdata[0].detail[i].id,
            id: o_rowdata[0].detail[i].id,
            width: 300,
            align: 'center',
            children: [
              {
                title: `采购规格:${o_rowdata[0].detail[i].purchase_spec}`,
                code: o_rowdata[0].detail[i].id,
                width: 160,
                align: 'center',
                children: [
                  {
                    title: `调出量(${o_rowdata[0].detail[i].delivery_unit})`,
                    code: `${o_rowdata[0].detail[i].id}_delivery`,
                    align: 'center',
                  },
                  {
                    title: `门店库存量(${o_rowdata[0].detail[i].delivery_unit})`,
                    code: `${o_rowdata[0].detail[i].id}_stock`,
                    align: 'center',
                  },
                  {
                    title: `单价`,
                    code: `${o_rowdata[0].detail[i].id}_price`,
                    align: 'center',
                  },
                ],
              },
            ],
            obj: o_rowdata[0].detail[i],
            selected: false,
          });
        }
      }
    }

    return new_goodLists;
  };

  // 删除
  const deleteGoods = () => {
    // console.log('deleteGoods', selectedGoodList, rowData)
    if (selectedGoodList.length === 0 && selectedStoreList.length === 0) {
      return message.warning('请先选择删除项！');
    }
    // 处理门店删除
    if (selectedStoreList.length > 0) {
      setRowData((prevRowData) => {
        const newRowData = prevRowData.filter(
          (item) => !selectedStoreList?.includes(item.id),
        );
        // console.log(newRowData, 'newRowData ww')
        if (!newRowData?.length) {
          // 店删光时品也要全删除
          setGoodList([]);
          setItemArr([itemArr[0], itemArr[itemArr.length - 1]]);
        }
        return newRowData;
      });
      setSelectedStoreList([]);
    }

    // 处理商品删除
    if (selectedGoodList.length > 0) {
      // 处理itemArr
      const newItemArr = [...itemArr];
      selectedGoodList.forEach((v: any) => {
        for (let i = 0; i < newItemArr.length; i++) {
          if (newItemArr[i].id && newItemArr[i].id === v) {
            newItemArr.splice(i, 1);
            break;
          }
        }
      });
      setGoodList(
        JSON.parse(JSON.stringify(newItemArr.slice(1, newItemArr.length - 1))),
      );
      setItemArr(newItemArr);

      // 处理rowData
      setRowData((prevRowData) => {
        const newRowData = prevRowData.map((v) => {
          const newV = { ...v };
          selectedGoodList.forEach((j) => {
            newV[j] = undefined;
          });
          if (newV.detail && newV.detail.length) {
            newV.detail = newV.detail.filter(
              (q) => !selectedGoodList?.includes(q.id),
            );
          }
          return newV;
        });
        return newRowData;
      });
      setSelectedGoodList([]);
    }
  };

  // 批量修改
  const batchUpdate = async () => {
    const bool = await XlbTipsModal({
      tips: (
        <>
          <div style={{ width: 'auto', fontSize: '16px' }} className="i-flex">
            <Form colon form={formUpdate} autoComplete="off" layout="inline">
              <Form.Item name="checkValues" initialValue={[]} label="修改内容">
                <XlbCheckbox.Group>
                  <XlbCheckbox value={'quantity'}>数量</XlbCheckbox>
                  <XlbCheckbox value={'price'}>价格</XlbCheckbox>
                </XlbCheckbox.Group>
              </Form.Item>
              <Form.Item name="quantity" initialValue={0} label="数量">
                <InputNumber
                  controls={false}
                  step={0.001}
                  min={0}
                  max={9999999}
                  size={'small'}
                  style={{ width: 100, height: 27, marginLeft: 10 }}
                />
              </Form.Item>
              <Form.Item name="price" initialValue={0} label="价格">
                <InputNumber
                  controls={false}
                  step={0.0001}
                  min={0}
                  max={9999999}
                  size={'small'}
                  style={{ width: 100, height: 27, marginLeft: 10 }}
                />
              </Form.Item>
            </Form>
          </div>
        </>
      ),
      isCancel: true,
      title: '批量设置',
      width: 360,
    });
    if (bool) {
      const obj: any = {};
      const { checkValues = [] } = formUpdate.getFieldsValue(true) || {};
      if (checkValues?.includes('quantity')) {
        selectedGoodList.forEach((i) => {
          obj[i + '_delivery'] = formUpdate.getFieldValue('quantity');
        });
      }
      if (checkValues?.includes('price')) {
        selectedGoodList.forEach((i) => {
          obj[i + '_price'] = formUpdate.getFieldValue('price');
        });
      }

      const arr = rowData.map((v) => {
        return {
          ...v,
          ...obj,
          edit: false,
          detail: v.detail.map((e) => ({
            ...e,
            quantity:
              selectedGoodList?.includes(e.id) &&
              checkValues?.includes('quantity')
                ? formUpdate.getFieldValue('quantity')
                : e.quantity,
            price:
              selectedGoodList?.includes(e.id) && checkValues?.includes('price')
                ? formUpdate.getFieldValue('price')
                : e.price,
            [e.id + '_delivery']:
              selectedGoodList?.includes(e.id) &&
              checkValues?.includes('quantity')
                ? formUpdate.getFieldValue('quantity')
                : e.quantity,
            [e.id + '_price']:
              selectedGoodList?.includes(e.id) && checkValues?.includes('price')
                ? formUpdate.getFieldValue('price')
                : e.price,
            [e.id + '_stock']: e.basic_stock_quantity,
          })),
        };
      });
      const new_goodLists = getGoodList(arr);
      const tmp_itemArr = [
        itemArr[0],
        ...new_goodLists,
        itemArr[itemArr.length - 1],
      ];
      setGoodList(new_goodLists);
      setItemArr(tmp_itemArr);
      setRowData(arr);
      formUpdate.resetFields();
    } else {
      formUpdate.resetFields();
    }
  };

  // 拆分数组
  const splitRowData = (rowList: any) => {
    const l: any = [];
    rowList.map((item: any) => {
      if (item.detail && item.detail.length) {
        item.detail.map((v: any) => {
          l.push({
            ...v,
            store_group_id: item.store_type_id,
            store_id: item.store_id,
            store_name: item.store_name,
            quantity: item[v.id + '_delivery'] || 0,
            price: item[v.id + '_price'] || 0,
            basic_stock_quantity: item[v.id + '_stock'] || 0,
            ratio: v.purchase_ratio,
          });
        });
      }
    });
    return l;
  };

  // 取消
  const onCancel = () => {
    formRef?.current?.resetFields();
    formUpdate.resetFields();
    setGoodList([]);
    setSelectedGoodList([]);
    setRowData([]);
    setItemArr(JSON.parse(JSON.stringify(itemTableList)));
    setOpen(false);
  };

  // 确定
  const onConfirm = async () => {
    const detailsData = splitRowData(rowData);
    // 加校验
    const { store_id, storehouse_id } = formRef?.current?.getFieldsValue(true);
    if (!store_id?.[0]) {
      message.warning(`调出门店id不能为空`);
      return;
    }
    if (!storehouse_id) {
      message.warning(`调出仓库id不能为空`);
      // message.warning({
      //   tips: `调出仓库id不能为空`,
      //   isCancel: false,
      // });
      return;
    }
    if (!detailsData?.length) {
      // message.warning({
      //   tips: `明细不能为空`,
      //   isCancel: false,
      // });
      return;
    }
    setIsLoading(true);
    const data = {
      ...formRef?.current?.getFieldsValue(true),
      store_id: store_id?.[0],
      payment_date: formRef?.current.getFieldValue('payment_date')
        ? dayjs(formRef?.current.getFieldValue('payment_date')).format(
            'YYYY-MM-DD',
          )
        : null,
      operate_date: formRef?.current.getFieldValue('operate_date')
        ? dayjs(formRef?.current.getFieldValue('operate_date')).format(
            'YYYY-MM-DD',
          )
        : null,
      details: detailsData,
    };

    const res = await batchOrderSave(data);
    setIsLoading(false);
    if (res.code === 0) {
      if (!res?.data?.length) {
        message.warning({
          tips: `生成调出单成功`,
          isCancel: false,
        });
      } else {
        message.warning({
          title: '批量制单结果',
          tips: (
            <div style={{ fontSize: '14px' }}>
              <p style={{ color: '#86909c', marginBottom: '10px' }}>
                由于部分商品不在对应门店的经营范围内，或数量设置为0，以下调出单有部分商品未被添加到调出单中：
              </p>
              <p>{res?.data?.join('、')}</p>
            </div>
          ),
          isCancel: false,
          okText: '知道了',
        });
      }
      onCancel();
    }
  };

  if (rowData?.length) {
    itemArr.map((v) => storeStrongRender(v));
  }
  itemArr[0].title = (
    <Checkbox
      disabled={
        !goodList?.length
        // goodList.filter((v) => v.obj.generate_replenishment_order_state).length === goodList.length
      }
      value={selectedGoodList.length === goodList.length ? 1111 : 9999}
      checked={goodList.length && selectedGoodList.length === goodList.length}
      onChange={(e: any) => onChange(e, goodList, null)}
    >
      商品名称/商品代码
    </Checkbox>
  );

  // const pipeline = useTablePipeline({ components: fusion })
  //   .input({ dataSource: rowData, columns: itemArr })
  //   .primaryKey('store_id')
  //   .use(
  //     features.columnResize({
  //       fallbackSize: 100,
  //     }),
  //   )
  //   .use(
  //     features.sort({
  //       mode: 'multiple',
  //       highlightColumnWhenActive: true,
  //     }),
  //   )
  //   .use(
  //     features.multiSelect({
  //       value: selectedStoreList as string[],
  //       highlightRowWhenSelected: true,
  //       checkboxColumn: { lock: true },
  //       clickArea: 'row',
  //       checkboxPlacement: 'start',
  //       onChange: (value, key) => {
  //         const data = pipeline.getDataSource();
  //         data.forEach((v) => {
  //           v.edit = v.store_id === key;
  //         });
  //         setRowData([...data]);
  //         setSelectedStoreList?.(value);
  //       },
  //     }),
  //   );
  // .use(features.rowGrouping({ defaultOpenAll: true }))
  return (
    <Modal
      open={open}
      centered
      width={'90%'}
      className={styles.dragModel}
      wrapClassName="xlbDialog"
      title={'批量制单'}
      confirmLoading={isLoading}
      onCancel={() => onCancel()}
      onOk={() => onConfirm()}
    >
      {/* <XlbProgress progress={progress} onCancel={setProgress} /> */}
      <div
        ref={modalRef}
        style={{ marginTop: '12px' }}
        className={styles.formWrapCus}
      >
        <XlbProForm
          formList={[
            {
              id: ErpFieldKeyMap?.erpCenterStoreIdForOrg,
              label: '调出门店',
              onChange(e, formT, l) {
                formT?.setFieldsValue({
                  store_name: l.map((v: any) => v.store_name).join(''),
                  // store_id: l?.length && l?.[0]?.id,
                  out_org_id: l?.[0]?.org_id,
                  out_org_name: l?.[0]?.org_name,
                  isCenter: l?.[0]?.enable_delivery_center,
                  deliveryCenterId: l?.[0]?.delivery_store?.id,
                  managementType: l?.[0]?.management_type,
                });
              },
            },
            {
              id: ErpFieldKeyMap?.orgName,
              label: '调出组织',
              name: 'out_org_name',
              disabled: true,
              hidden: !enable_organization,
            },
            {
              id: 'erpStorehouseIdDeliveryOrder',
              label: '调出仓库',
              fieldProps: {
                rootClassName: styles.myDropDown,
              },
            },
            {
              id: ErpFieldKeyMap?.deliveryDate,
              name: 'operate_date',
              label: '调出日期',
            },
            {
              id: ErpFieldKeyMap?.deliveryDate,
              name: 'payment_date',
              label: '付款日期',
            },
            {
              id: ErpFieldKeyMap.otherIncomeExpensesName,
              label: '留言备注',
              name: 'memo',
              fieldProps: {
                width: 624,
              },
            },
          ]}
          initialValues={{
            isCenter: LStorage.get('userInfo').store.enable_delivery_center,
            deliveryCenterId: LStorage.get('userInfo').store.upstream_center_id,
            store_name: LStorage.get('userInfo').store_name,
            store_id: [LStorage.get('userInfo').store_id],
            out_org_name:
              enable_organization && LStorage.get('userInfo').org_name,
            out_org_id: enable_organization && LStorage.get('userInfo').org_id,
            managementType: LStorage.get('userInfo').store.management_type,
          }}
          ref={formRef}
        />
        {/* btn */}
        <div className={'row-flex'} style={{ margin: '0 15px 8px 15px' }}>
          <XlbButton.Group>
            <XlbButton
              label="批量添加调入门店"
              type="primary"
              // onClick={() => handleDialogClick('门店')}
              onClick={() => handleBatchStore()}
              disabled={rowData?.some((i) => i?.detail?.length > 0)}
              preIcon={<FileAddOutlined />}
            />
            <XlbButton
              label="添加商品"
              type="primary"
              disabled={!rowData?.length}
              // onClick={() => handleDialogClick('添加')}
              onClick={() => handleAddClick()}
              preIcon={<FileAddOutlined />}
            />
            <XlbButton
              label="删除"
              type="primary"
              disabled={
                selectedGoodList.length === 0 && selectedStoreList.length === 0
              }
              onClick={() => deleteGoods()}
              preIcon={<DeleteOutlined />}
            />
            <XlbButton
              label="批量修改"
              type="primary"
              disabled={selectedGoodList.length === 0}
              onClick={() => batchUpdate()}
              preIcon={<EditOutlined />}
            />
          </XlbButton.Group>
        </div>
        {/* table */}
        <div className={rowData.length ? styles.table_box : ''}>
          <XlbTable
            selectMode="multiple"
            dataSource={rowData}
            columns={itemArr}
            isLoading={isLoading}
            className="jMuPhs"
            style={{
              overflow: 'auto',
              ...tableStyle,
              height: document.body.clientHeight - 120 - 200,
            }}
            emptyCellHeight={document.body.clientHeight - 120 - 325}
            getRowProps={(record, rowIndex) => {
              return {
                onClick(e) {
                  rowData.map((v) => (v.edit = false));
                  rowData[rowIndex].edit = true;
                  setRowData([...rowData]);
                },
              };
            }}
            footerDataSource={footerData}
            // {...pipeline.getProps()}
          />
        </div>
      </div>
    </Modal>
  );
};

export default BatchOrderIndex;