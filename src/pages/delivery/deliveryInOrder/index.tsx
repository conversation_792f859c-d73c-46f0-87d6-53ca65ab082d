import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { formatWithCommas } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import NiceModal from '@ebay/nice-modal-react';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbModal,
  XlbPageContainer,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbSelect,
  XlbTableColumnProps,
  XlbTipsModal,
  XlbTooltip,
} from '@xlb/components';
import { useNavigation } from '@xlb/max';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { useEffect, useRef, useState } from 'react';
import { BatchChangeTimeModal } from './component/BatchModal';
import receiveSettingModal from './component/receiveSettingModal/index';

import { timeTypeList } from '@/pages/delivery/deliveryInOrder/data';
import toFixed from '@/utils/toFixed';
import { wujieBus } from '@/wujie/utils';
import { history } from '@@/core/history';
import { cloneDeep } from 'lodash';
import {
  formList,
  invoiceStateList,
  Options6,
  RECEIVER_TYPE_OPTIONS,
  settlementStateList,
  stateList,
  tableList,
} from './data';
import DetailOrder from './item';
import {
  Approvereturn,
  batchAuditInfo,
  copyInfo,
  deleteItems,
  deliveryparamRead,
  getreasonListAll,
  orderReceive,
  syncInfo,
  unbatchAuditInfo,
} from './server';
import dayjs from 'dayjs';

const Index = () => {
  const record = history.location.state as any;
  const detailRef = useRef<XlbProPageModalRef>(null);
  const [form] = XlbBasicForm.useForm<any>();
  let fetchData = () => {};
  let setLoad = (v = false) => {};
  const [refundForm] = XlbBasicForm.useForm<any>();
  const rowData = useRef([]);
  const { enable_organization, enable_cargo_owner } = useBaseParams(
    (state) => state,
  );
  const paginInfo = useRef({});
  const paramsData = useRef<any>({ fid: 1 });
  const [refoundVisible, setRefoundVisible] = useState<boolean>(false); //退款弹框
  const [reasonList, setReasonlist] = useState([]); //退货原因
  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    cloneDeep(tableList),
  );
  const [mdLoading, setMdLoading] = useState<boolean>(false);

  const [is_audit_auto_out_order_auto_in, setis_audit_auto_out_order_auto_in] =
    useState<boolean>(false);
  const chooseList = useRef<any[]>([]);
  const { ToolBtn, SearchForm, Table } = XlbPageContainer;

  const batchTime = async (chooseList = []) => {
    await NiceModal.show(BatchChangeTimeModal, {
      idsList: chooseList,
      fetchData: fetchData,
      url: '/erp/hxl.erp.order.date.change',
      idKey: 'fid_list',
      timeKey: 'change_date',
      type: 'delivery_in',
    });
  };
  //#region render table fn
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'fid':
        item.render = (value: any, record: any, index: { index: any }) => {
          return (
            <div className="overwidth cursors">
              <span
                className="link cursors"
                style={{ color: record.reverse_fid ? '#FF0000' : '#3D66FE' }}
                onClick={(e) => {
                  e.stopPropagation();
                  paramsData.current = {
                    fid: record.fid,
                    index: index.index,
                    total: rowData.current.length,
                    allRow: rowData.current,
                  };
                  detailRef.current?.setOpen(true);
                }}
              >
                {value}
              </span>
            </div>
          );
        };
        break;
      case 'cost_money':
      case 'no_tax_cost_money':
      case 'tax_money':
        item.render = (value: any) => {
          return hasAuth(['调入单/成本价', '查询']) && value !== '****' ? (
            <div className="info overwidth">
              {formatWithCommas(toFixed(value, 'MONEY'))}
            </div>
          ) : (
            <div className="info overwidth">{'****'}</div>
          );
        };
        break;
      case 'purchase_money':
      case 'no_tax_purchase_money':
        item.render = (value: any) => {
          return hasAuth(['调入单/采购价', '查询']) && value !== '****' ? (
            <div className="info overwidth">{value === null ? '——' : formatWithCommas((Number(value) || 0).toFixed(4))}</div>
          ) : (
            <div className="info overwidth">{'****'}</div>
          )
        }
        break
      // 单据金额、单据金额去税---配送价
      case 'money':
      case 'no_tax_money':
        item.render = (value: any) => {
          return hasAuth(['调入单/配送价', '查询']) && value !== '****' ? (
            <div className="info overwidth">
              {formatWithCommas(toFixed(value, 'MONEY'))}
            </div>
          ) : (
            <div className="info overwidth">{'****'}</div>
          );
        };
        break;
      case 'state':
        item.render = (value: any) => {
          const item = stateList.find((v) => v.value === value);
          return (
            <div
              style={{ color: value === 'AUDIT' ? '#ff7d01' : '' }}
              className={`overwidth ${item ? item.type : ''}`}
            >
              {item ? item.label : ''}
            </div>
          );
        };
        break;
      case 'receive_type':
        item.render = (value: any) => {
          const item = RECEIVER_TYPE_OPTIONS.find((v) => v.value === value);
          return <div className={`overwidth `}>{item ? item.label : ''}</div>;
        };
        break;
      case 'license_type':
      case 'out_license_type':
        item.render = (value: any) => {
          const typeOfLicense: any = {
            COMPANY: '企业',
            PERSONAL: '个体',
          };
          return (
            <div className="info overwidth">
              {value ? typeOfLicense[value] : ''}
            </div>
          );
        };
        break;
      case 'check_state':
        item.render = (value: any) => {
          const item = Options6.find((v) => v.value === value);
          return (
            <div className={`overwidth ${item ? item.type : ''}`}>
              {item ? item.label : ''}
            </div>
          );
        };
        break;
      case 'order_type':
        item.render = (value: any, _record: any) => {
          return _record.index === '合计' ? null : value ===
            'WAREHOUSE_TO_STORE' ? (
            <div className="info overwidth">仓店配送</div>
          ) : value === 'STORE_TO_WAREHOUSE' ? (
            <div className="info overwidth">店仓反配</div>
          ) : value === 'STORE_TO_STORE' ? (
            <div className="info overwidth">店间调拨</div>
          ) : value === 'WAREHOUSE_TO_WAREHOUSE' ? (
            <div className="info overwidth">仓间调拨</div>
          ) : null;
        };
        break;
      case 'invoice_state':
        item.render = (value: any) => {
          const _item = invoiceStateList.find((v) => v.value === value);
          return (
            <div className={`overwidth ${_item ? _item.type : ''}`}>
              {_item ? _item.label : ''}
            </div>
          );
        };
        break;
      case 'settlement_state':
        item.render = (value: any, record: any) => {
          const item = settlementStateList.find((v) => v.value === value);
          return record.in_center_flag ? (
            <div className={`overwidth ${item ? item.type : ''}`}>
              {item ? item.label : ''}
            </div>
          ) : (
            <div className="info overwidth">{'——'}</div>
          );
        };
        break;
      case 'delivery_out_order_fids_str':
      case 'memo':
        item.render = (value: any) => {
          return (
            <XlbTooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div className="info overwidth"> {value}</div>
            </XlbTooltip>
          );
        };
        break;
      case 'print_count':
        item.render = (value: any) => (
          <div className="info overwidth">{value || 0}</div>
        );
        break;
      case 'ama_order_fid':
        item.render = (value: any, record: any) =>
          value ? (
            <div className="info overwidth"> {value}</div>
          ) : (
            <div
              className="link cursors"
              onClick={(e) => {
                if (record.state === 'INIT') return;
                e.stopPropagation();
                uploadInfo(record.fid);
              }}
            >
              {record.state === 'INIT' ? null : '上传'}
            </div>
          );
        break;
      // case 'last_in_time':
      // case 'create_time':
      // case 'audit_time':
      //   item.render = (value: any) => {
      //     console.log(value, 11111);
      //     return (
      //       <div className="info overwidth"> {dateManipulation(value || '')}</div>
      //     );
      //   }
      //   break;
    }
    return item;
  };
  //#region render table
  //#region  删除 btn
  const deleteItem = async (data = [], fids = []) => {
    const errFid = data.filter((_: any) => _?.state !== 'INIT');
    if (errFid.length > 0) {
      XlbTipsModal({
        tips: '只能删除单据状态为制单的单据!',
      });
      return;
    }
    await XlbTipsModal({
      tips: `已选择${fids.length}张单据，是否确认删除!`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await deleteItems({ fids });
        if (res.code === 0) {
          XlbMessage.success('操作成功');
          fetchData();
          return true;
        }
      },
    });
  };
  //#endregion  删除
  // #region 导出
  const exportItem = async (e: any) => {
    const formData = form.getFieldsValue(true);
    const { time_type, create_date, both_reversed = [{}], ...rest } = formData;
    const item = both_reversed[0];
    const both_reversed_obj = {
      both_reversed: item?.value ? (item?.itemKey ? true : false) : undefined,
    };
    const data = {
      ...both_reversed_obj,
      [time_type]: create_date,
      ...rest,
    };
    setLoad(true);
    const res = await ErpRequest.post('/erp/hxl.erp.deliveryoutorder.export', {
      ...data,
    });
    if (res.code === 0) {
      XlbMessage.success('导出受理成功，请前往下载中心查看');
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
    }
    setLoad(false);
  };
  //#region 批量审核
  // 审核
  const batchAudit = async (data = [], fids = []) => {
    const errFid = data.filter((_: any) => _?.state !== 'INIT');

    if (errFid.length > 0) {
      XlbTipsModal({
        tips: (
          <>
            <div>下列单据为审核状态，不能再次审核!</div>
            {errFid.map((_: any) => {
              return <div key={_.fid}>{_.fid}</div>;
            })}
          </>
        ),
      });
      return;
    }

    await XlbTipsModal({
      tips: `已选择${fids.length}张单据，是否确认审核!
    `,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await batchAuditInfo({ fids });
        if (res.code === 0) {
          XlbMessage.success('操作成功');
          fetchData();
          return true;
        }
      },
    });
  };
  //#region 申请退货
  const approveItem = async (fids = []) => {
    if (fids.length > 1) {
      XlbTipsModal({
        tips: '申请退货不支持批量操作!',
      });
    } else if (fids.length === 1) {
      setRefoundVisible(true);
      const data = {
        pageSize: 200,
        pageNum: 0,
        type: 'RETURN_REQUEST',
      };
      setLoad(true);
      const res = await getreasonListAll(data);
      if (res.code === 0) {
        setReasonlist(res.data);
      }
      setLoad(false);
    }
  };
  // const onValuesChange = (changedValues: any) => {
  //   if (changedValues.hasOwnProperty('store_ids') && !changedValues.store_ids) {
  //     setTimeout(()=>{
  //       form.setFieldsValue({
  //         store_ids: LStorage.get('userInfo')?.store?.id ? [LStorage.get('userInfo')?.store?.id] : undefined
  //       })
  //     }, 300)
  //   }
  // };
  //查询是否开启跨配送中心调拨
  const getdeliveryparam = async () => {
    const res = await deliveryparamRead();
    if (res?.code === 0 && !res.data.enable_in_check_order) {
      const index = itemArr.findIndex((v: any) => v.label === '确认状态');
      if (index !== -1) {
        itemArr.splice(index, 1);
        setItemArr([...itemArr]);
      }
    }
    if (res?.code === 0 && res.data.audit_auto_out_order_auto_in)
      setis_audit_auto_out_order_auto_in(true);
  };
  //#region 复制
  const copyItem = async (arr = [], fids = []) => {
    // 复制不支持批量操作
    if (fids.length > 1) {
      XlbTipsModal({
        tips: '复制不支持批量操作!',
      });
      return;
    } else {
      await XlbTipsModal({
        tips: `是否确认复制单据"${fids.join(',')}"?`,
        isCancel: true,
        onOkBeforeFunction: async () => {
          const res = await copyInfo({ fid: fids[0] });
          if (res.code === 0) {
            XlbMessage.success('操作成功');
            fetchData();
            return true;
          }
        },
      });
    }
  };
  const receive = (data = []) => {
    if (data.some((_: any) => _.state === 'INIT')) {
      XlbTipsModal({
        title: '提示',
        width: 510,
        tips: (
          <>
            {data
              .filter((_: any) => _.state === 'INIT')
              .map((_: any) => _.fid)
              .join('、')}
            <br />
            状态为制单，无法一键签收，请重新选择
          </>
        ),
      });
      return;
    }

    if (data.some((_: any) => _.state === 'ALLCHECK')) {
      XlbTipsModal({
        title: '提示',
        width: 510,
        tips: (
          <>
            {data
              .filter((_: any) => _.state === 'ALLCHECK')
              .map((_: any) => _.fid)
              .join('、')}
            <br />
            确认状态为全部确认，无法一键签收，请重新选择
          </>
        ),
      });
      return;
    }

    const reverseList = data
      .filter((item: any) => item.reverse_fid)
      .map((item: any) => item.fid);

    if (reverseList.length) {
      XlbTipsModal({
        title: '提示',
        width: 510,
        tips: (
          <>
            {reverseList.map((_) => _.fid).join('、')}
            <br />
            为冲红调入单，不允许操作
          </>
        ),
      });
      return;
    }

    const reversedList = data
      .filter((item: any) => item.reversed)
      .map((item: any) => item.fid);
    if (reversedList?.length) {
      XlbTipsModal({
        title: '提示',
        width: 510,
        tips: (
          <>
            {reversedList.join('、')}
            <br />
            调入单已被冲红，不能签收
          </>
        ),
      });
      return;
    }

    if (data.length > 1) {
      XlbTipsModal({
        title: '提示',
        width: 510,
        tips: <>勾选{data.length}条调入单，是否一键签收,</>,
        onOk: async () => {
          const res = await orderReceive({ fids: data.map((_: any) => _.fid) });
          if (res?.code === 0) {
            fetchData();
            XlbMessage.success('一键签收成功');
          }
        },
      });
      return;
    }
    const { navigate } = useNavigation();
    navigate(
      '/xlb_erp/deliveryConfirm/item',
      {
        fid: data?.[0]?.fid,
        from: 'deliveryInOrder',
        fetchData: () => fetchData(),
      },
      'xlb_erp',
      true,
    );
  };

  // #region 取消退款
  const handleCancelapprove = async () => {
    refundForm.resetFields();
    setRefoundVisible(false);
  };
  // 反审核
  const getBatchReAudit = async (chooseList: any[]) => {
    const errFid: any = [];
    chooseList?.forEach((j) => {
      if (j?.state !== 'AUDIT') errFid?.push(j);
    });
    if (errFid?.length > 0) {
      XlbTipsModal({
        tips: (
          <>
            <div style={{ color: 'red' }}>下列单据非审核状态，不能反审核!</div>
            {errFid.map((_) => {
              return <div key={_.fid}>{_.fid}</div>;
            })}
          </>
        ),
      });
      return;
    }

    const bool = await XlbTipsModal({
      tips: `已选择${chooseList?.length}张单据，是否确认反审核!`,
    });
    if (bool) {
      setLoad(true);
      const res = await unbatchAuditInfo({
        fids: chooseList.map((_) => _.fid),
      });
      if (res.code === 0) {
        fetchData();
        XlbTipsModal({
          tips: `反审核成功${chooseList?.length}张`,
        });
      }
      setLoad(false);
    }
  };
  //#region 退货
  const handleOkapprove = async () => {
    setLoad(true);
    setMdLoading(true);
    const res = await Approvereturn({
      fid: chooseList.current[0],
      reason: refundForm.getFieldValue('reason'),
    });
    setLoad(false);
    setMdLoading(false);
    handleCancelapprove();
    if (res.code === 0) {
      XlbTipsModal({
        tips: (
          <div>
            <div>已生成门店申请-退货申请单{res.data?.fid}</div>
            {res.data?.messages?.length > 0 ? (
              <>
                <div style={{ color: 'green' }}>
                  以下商品禁止退仓，系统已自动过滤！
                </div>
                <div>{res.data?.messages}</div>
              </>
            ) : null}
          </div>
        ),
      });
      fetchData();
    }
  };

  const prevPost = async (pagin = paginInfo.current) => {
    try {
      await form.validateFields();
    } catch (error) {
      return false;
    }
    const formData = form.getFieldsValue(true);
    const both_reversed = formData.both_reversed || [{}];
    const { time_type, create_date, ...rest } = formData;
    const item = both_reversed[0];
    const both_reversed_obj = {
      both_reversed: item?.value ? !!item?.itemKey : null,
    };
    const time_obj: any = {};
    time_obj[time_type] = create_date;
    const time_type_list = timeTypeList.map((_) => _.value);
    time_type_list.forEach((_) => {
      if (_ !== rest.time_type) {
        rest[_] = undefined;
      }
    });

    const { storehouse_id } = rest;
    const storehouse_id_obj = {
      storehouse_id: Array.isArray(storehouse_id)
        ? storehouse_id[0]
        : storehouse_id,
    };
    const params = {
      ...pagin,
      ...rest,
      ...storehouse_id_obj,
      ...both_reversed_obj,
      ...time_obj,
      storehouse_id_options: undefined,
    };
    // 存储表单数据
    LStorage.set('deliveryInOrder', { ...params });
    return params;
  };
  const exportItemDetail = async (e: any) => {
    const data = prevPost();
    setLoad(true);
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliveryinorder.detail.export.sum',
      {
        ...data,
        page_number: data.pageNum - 1,
        page_size: data.pageSize,
        operator_store_id: LStorage.get('userInfo').store_id,
      },
    );
    if (res?.code === 0) {
      XlbMessage.success('导出受理成功，请前往下载中心查看');
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
    }
    setLoad(false);
  };
  const uploadInfo = async (fid: any) => {
    setLoad(true);
    const res = await syncInfo({ fid: fid });
    setLoad(false);
    if (res.code === 0) {
      if (res.data) {
        XlbMessage.error(res.data);
      } else {
        XlbMessage.success('上传成功！');
        fetchData();
      }
    }
  };
  // itemArr.forEach((_) => tableRender(_));
  const getOrgList = async () => {
    formList.find((i) => i.name === 'org_ids')!.hidden = !enable_organization;
    formList.find((i) => i.name === 'out_org_ids')!.hidden =
      !enable_organization;
  };

  useEffect(() => {
    getOrgList();
  }, [enable_organization]);

  useEffect(() => {
    getdeliveryparam();
  }, []);

  useEffect(() => {
    if (record?.change_tab) {
      // getStockData(record.store_ids[0])
      form.resetFields();
      form.setFieldsValue({
        time_desc: 2,
        time_type: 'create_date',
        ...record,
        create_date: [
          dayjs(record.create_date[0]),
          dayjs(record.create_date[1]),
        ],
        page_number: 1,
        page_size: 200,
      });
      fetchData();
      record.change_tab = false;
    }
  }, [record]);

  //#region HTML
  return (
    <>
      <XlbModal
        title="请输入"
        open={refoundVisible}
        width={308}
        isCancel={true}
        onOk={handleOkapprove}
        confirmLoading={mdLoading}
        maskClosable={false}
        centered
        onCancel={() => {
          handleCancelapprove();
        }}
      >
        <div style={{ padding: '12px 0' }}>
          <XlbBasicForm autoComplete="off" layout="inline" form={refundForm}>
            <span style={{ color: 'red', margin: '0 0 10px 22px' }}>
              是否确认申请退货？
            </span>
            <XlbBasicForm.Item label="退货申请原因：" name="reason">
              <XlbSelect style={{ width: 140 }}>
                {reasonList?.map((v: any, i: number) => {
                  return (
                    <XlbSelect.Option key={i} value={v.name}>
                      {v.name}
                    </XlbSelect.Option>
                  );
                })}
              </XlbSelect>
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </div>
      </XlbModal>
      <XlbProPageModal
        ref={detailRef}
        Content={({ onClose }) => {
          return (
            <>
              <section>
                <DetailOrder
                  parentRef={detailRef}
                  closeModal={(reload: boolean = false) => {
                    detailRef.current?.setOpen(false);
                    if (reload) {
                      fetchData?.();
                    }
                  }}
                  data={paramsData}
                ></DetailOrder>
              </section>
            </>
          );
        }}
      >
        <div></div>
      </XlbProPageModal>
      <XlbPageContainer
        url={'/erp/hxl.erp.deliveryinorder.page'}
        tableColumn={itemArr.map((item) => tableRender(item))}
        footerDataSource={(data) => {
          const footerData = [
            {
              _index: '合计',
              money: hasAuth(['调出单/配送价', '查询']) && data?.money !== '****'
                ? data?.money?.toFixed(2) || '0.00'
                : '****',
              quantity: data?.quantity?.toFixed(3) || '0.000',
              item_count: data.item_count || 0,
              tax_money: hasAuth(['调出单/成本价', '查询']) && data?.tax_money !== '****'
                ? data?.tax_money?.toFixed(2) || '0.00'
                : '****',
              no_tax_money: hasAuth(['调出单/配送价', '查询']) && data?.no_tax_money !== '****'
                ? data?.no_tax_money?.toFixed(2) || '0.00'
                : '****',
              cost_money: hasAuth(['调出单/配送价', '查询']) && data?.cost_money !== '****'
                ? data?.cost_money?.toFixed(2) || '0.00'
                : '****',
              no_tax_cost_money: hasAuth(['调出单/配送价', '查询']) && data?.no_tax_cost_money !== '****'
                ? data?.no_tax_cost_money?.toFixed(2) || '0.00'
                : '****',
              purchase_money: hasAuth(['调出单/配送价', '查询']) && data?.purchase_money !== '****'
                ? data?.purchase_money?.toFixed(4) || '0.00'
                : '****',
              no_tax_purchase_money: hasAuth(['调出单/配送价', '查询']) && data?.no_tax_purchase_money !== '****'
                ? data?.no_tax_purchase_money?.toFixed(4) || '0.00'
                : '****',
            },
          ];
          return footerData;
        }}
        prevPost={(pagin) => {
          paginInfo.current = pagin;
          return prevPost(pagin);
        }}
        immediatePost={false}
      >
        <ToolBtn>
          {(context: ContextState) => {
            const {
              loading,
              setLoading,
              fetchData: _fetchData,
              dataSource,
            } = context;
            fetchData = _fetchData;
            setLoad = setLoading;
            rowData.current = dataSource;
            const selectRow: any[] | undefined = context.selectRow;
            const selectRowKeys: string[] = (context.selectRow || []).map(
              (_) => _.fid,
            );
            chooseList.current = selectRowKeys;

            return (
              <XlbButton.Group>
                <XlbButton
                  label="查询"
                  type="primary"
                  loading={loading}
                  onClick={() => _fetchData()}
                  icon={<XlbIcon name="sousuo" />}
                />
                {hasAuth(['调入单', '编辑']) && !enable_cargo_owner ? (
                  <XlbButton
                    label="新增"
                    type="primary"
                    loading={loading}
                    onClick={() => {
                      if (
                        is_audit_auto_out_order_auto_in &&
                        enable_organization
                      ) {
                        XlbMessage.info(
                          '仅可手动创建调出单，不可手动创建调入单',
                        );
                        return;
                      }
                      detailRef.current?.setOpen(true);
                      paramsData.current = {
                        fid: 1,
                      };
                    }}
                    icon={<XlbIcon name="jia" />}
                  />
                ) : null}

                {hasAuth(['调入单', '审核']) ? (
                  <XlbButton
                    label="批量审核"
                    type="primary"
                    disabled={!selectRowKeys.length || loading}
                    onClick={() =>
                      batchAudit(selectRow as [], selectRowKeys as [])
                    }
                    icon={<XlbIcon name="shenhe" />}
                  />
                ) : null}
                {hasAuth(['调入单', '删除']) ? (
                  <XlbButton
                    label="删除"
                    type="primary"
                    disabled={!selectRowKeys?.length || loading}
                    onClick={() =>
                      deleteItem(selectRow as [], selectRowKeys as [])
                    }
                    icon={<XlbIcon name="shanchu" />}
                  />
                ) : null}
                {hasAuth(['调入单', '导出']) ? (
                  <XlbDropdownButton
                    label="导出"
                    dropList={[
                      {
                        label: '导出',
                        disabled: !rowData.current.length || loading,
                      },
                      {
                        label: '明细导出',
                        disabled: !rowData.current.length || loading,
                      },
                    ]}
                    dropdownItemClick={(value: any, item: any, e) => {
                      if (item?.label === '导出') {
                        exportItem(e);
                      } else if (item?.label === '明细导出') {
                        exportItemDetail(e);
                      }
                    }}
                  />
                ) : null}

                {hasAuth(['调入单', '编辑']) && !enable_cargo_owner ? (
                  <XlbButton
                    label="复制"
                    type="primary"
                    disabled={!selectRowKeys.length || loading}
                    onClick={() =>
                      copyItem(selectRow as [], selectRowKeys as [])
                    }
                    icon={<XlbIcon name="fuzhi" />}
                  />
                ) : null}

                {hasAuth(['调入单', '编辑']) ? (
                  <XlbButton
                    label="申请退货"
                    type="primary"
                    disabled={!selectRowKeys.length || loading}
                    onClick={() => approveItem(selectRowKeys as [])}
                    icon={<XlbIcon name="shenqingtuihuo" />}
                  />
                ) : null}

                {LStorage.get('userInfo')?.company_id === 67800 &&
                hasAuth(['调入单', '反审核']) ? (
                  <XlbButton
                    label="批量反审核"
                    onClick={() => {
                      getBatchReAudit(selectRow as []);
                    }}
                    icon={<XlbIcon name="shenhe" />}
                    loading={!selectRowKeys.length || loading}
                  />
                ) : null}
                {hasAuth(['调入单/日期修改', '编辑']) ? (
                  <XlbButton
                    label="日期修改"
                    type="primary"
                    disabled={!selectRowKeys.length || loading}
                    onClick={() => batchTime(selectRowKeys)}
                    icon={<span className="iconfont icon-xiugai1" />}
                  />
                ) : null}
                {hasAuth(['调入单/一键签收', '查询']) ? (
                  <XlbButton
                    label="一键签收"
                    type="primary"
                    disabled={!selectRow?.length || loading}
                    onClick={() => receive(selectRow)}
                    icon={<span className="iconfont icon-shengchengXXdan" />}
                  />
                ) : null}
                {hasAuth(['调入单/配置', '查询']) ? (
                  <XlbButton
                    label="配置"
                    type="primary"
                    disabled={loading}
                    onClick={() =>
                      NiceModal.show(receiveSettingModal, {
                        title: '调入单自动签收配置',
                        type: 'DELIVERY_IN',
                      })
                    }
                    icon={<span className="iconfont icon-jichupeizhi" />}
                  />
                ) : null}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm
            formList={formList}
            form={form}
            isHideDate={true}
            initialValues={{
              store_ids: LStorage.get('userInfo')?.store?.id
                ? [LStorage.get('userInfo')?.store?.id]
                : undefined,
              time_type: 'create_date',
              time_desc: 0,
              create_date: [
                dayjs().format('YYYY-MM-DD 00:00:00'),
                dayjs().format('YYYY-MM-DD 23:59:59'),
              ],
            }}
            // getFormRecord={() => refresh()}
            // onValuesChange={onValuesChange}
          />
        </SearchForm>
        <Table selectMode="multiple" primaryKey={'fid'}></Table>
      </XlbPageContainer>
    </>
  );
};

export default Index;