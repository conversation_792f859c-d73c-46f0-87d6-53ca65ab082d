import { XlbFetch as ErpRequest } from '@xlb/utils';
import {  LStorage } from '@/utils'
const params = {
  company_id : LStorage.get('userInfo')?.company_id,
  operator_store_id : LStorage.get('userInfo').store_id
}
//获取数据
export const getDeliveryInOrder = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.page', { ...data,...params })
}

//新增
export const addInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.save', { ...data,...params })
}

//删除
export const deleteInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.batchdelete', { ...data,...params })
}

//复制
export const copyInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.copy', { ...data,...params })
}

//冲红复制
export const RedCopy = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.reverse', { ...data,...params })
}

//申请退货
export const Approvereturn = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.createstoreapplicationorder', { ...data,...params })
}

//读取
export const readInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.read', { ...data,...params })
}

//更新
export const updateInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.update', { ...data,...params })
}

//审核
export const auditInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.audit', { ...data,...params })
}
//反审核
export const reauditRequest = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.reaudit', { ...data,...params })
}

//调入商品分页查询
export const getInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.item.page', { ...data,...params })
}

//  账号管理仓库查询
export const getStock = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storehouse.store.find', { ...data,...params })
}

//库存明细分页查询
export const getStockDetail = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockdetail.page', { ...data,...params })
}

// 配送参数查询
export const deliveryparamRead = async () => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryparam.read',{...params})
}
// 调出单生成调入单
export const deliveryout = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.createbydeliveryoutorder', { ...data,...params })
}
// 打印
export const print = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.print', { ...data,...params })
}

//退货原因数据
export const getreasonListAll = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.reason.find', { ...data,...params })
}

// 批量审核
export const batchAudit = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.batchaudit', { ...data,...params })
}

// 批量反审核
export const batchReAudit = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.reaudit', { ...data,...params })
}

//一键签收
export const orderReceive = async (data: { fids: string[] }) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryincheckorder.receive', { ...data,...params })
}


// 调入单 导出文件
export const exportFile = async (data: { fids: string[] }) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryinorder.export', { ...data,...params })
}
