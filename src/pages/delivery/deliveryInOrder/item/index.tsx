import { useBaseParams } from '@/hooks/useBaseParams';
import { formatWith<PERSON>om<PERSON>, hasAuth, sortTypeSwitch } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import { LStorage } from '@/utils/storage';
import toFixed from '@/utils/toFixed';
import { SearchOutlined } from '@ant-design/icons';
import {
  XlbBadge,
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbColumns,
  XlbDatePicker,
  XlbDropdownButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbMessage,
  XlbModal,
  XlbPrintModal,
  XlbSelect,
  XlbShortTable,
  XlbTable,
  XlbTabs,
  XlbTipsModal,
  XlbTooltip,
  XlbUploadFile,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { useEffect, useRef, useState } from 'react';
import BillModal from './../component/BillModal';
import { itemTableList_all, itemTableListDetail } from './../data';
import styles from './item.less';
import {
  addInfo,
  auditInfo,
  deliveryout,
  deliveryparamRead,
  getDeliveryInOrder,
  getStock,
  print,
  readInfo,
  reauditRequest,
  updateInfo,
} from './server';
// import SignaturePrintingModal from './attachmentOfReceiptForm';
import { orderStatusIcons } from '@/components/data';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import classnames from 'classnames';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';

const { Option } = XlbSelect;

const DeliveryInOrderItem = (props: any) => {
  const [fileList, setFileList] = useState<any[]>([]);
  const forbidenClickRowEvent = useRef(false);
  const [uploadFileModalVisible, setUploadFileModalVisible] = useState(false);

  const [rowData, setRowData] = useState<any[]>([]);
  const [summaryData, setSummaryData] = useState<any[]>([]); //汇总表数据
  const [chooseList, setChooseList] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [keepData, setKeepData] = useState(false);

  const [unCenterStoreApplication, setUnCenterStoreApplication] =
    useState(false);
  const [managementType, setManagementType] = useState(null);
  const [enableDeliveryCenter, setEnableDeliveryCenter] = useState(null); //是否是配送中心门店
  const [itemArr, setItemArr] = useState<any[]>(
    JSON.parse(JSON.stringify(itemTableList_all)),
  );
  const [itemArrdetail, setdetailItemArr] = useState<any[]>(
    JSON.parse(JSON.stringify(itemTableListDetail)),
  );
  // const [signatureVisible, setSignatureVisible] = useState<boolean>(false);
  // const signatureData = useRef([]);
  // const [pagin, setPagin] = useState({
  //   pageSize: 200000,
  //   pageNum: 1,
  //   total: 0,
  // });

  const [isFold, setIsFold] = useState<boolean>(false);
  const formBox = useRef<HTMLDivElement | null>(null);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [stockList, setStockList] = useState<any[]>([]);
  // const [form] = Form.useForm()
  const [form] = XlbBasicForm.useForm();
  const shortTableEditable = XlbBasicForm.useWatch('out_store_id', form);
  const [formDataSource, setFormDataSource] = useState<any>(false);
  const [fid, setFid] = useState<any>();
  const [info, setInfo] = useState({ state: 'INIT', fid: null });
  const [batchLoding, setBatchLoding] = useState<boolean>(false);
  const [allRow, setAllRow] = useState<any>({}); //主页查询数据
  const [nextLoding, setNextLoding] = useState<boolean>(false); //下一单
  const [lastLoding, setLastLoding] = useState<boolean>(false); //上一单
  const [tabsKey, setTabsKey] = useState<string>('detailTab'); //明细汇总切换
  const [tableKey, setTableKey] = useState<any>('baseInfo');
  const [wmsFid, setWmsFid] = useState<any>(null);
  const [fileInfo, setFileInfo] = useState<any[]>([]);
  const [edit, setEdit] = useState<boolean>(false); //触发表格编辑
  const [centerStore, setCenterStore] = useState({
    isCenter: '',
    deliveryCenterId: '',
  }); //记录调入门店是否为配送中心,上游配送中心id
  const [outStore, setOutStore] = useState({ isCenter: '' }); //记录调出门店是否为配送中心,上游配送中心id
  const [toOtherCenter, setToOtherCenter] = useState<boolean>(false); //是否支持跨配送中心调拨
  const [audit, setAudit] = useState<boolean>(true); //新增保存后审核可用
  const [outOrder, setOutOrder] = useState<boolean>(false);

  const [errNames, setErrNames] = useState<any>([]);
  const [WmsInfo, setWmsInfo] = useState<any>({});
  const [sortType, setSortType] = useState<{ order: string; code: string }>({
    order: '',
    code: '',
  });
  const [reload, setReload] = useState<boolean>(false);
  const { enable_organization } = useBaseParams((state) => state);

  const handleUid = () => {
    let uid: any = {
      key: '',
      name: '',
    };
    switch (tabsKey) {
      case 'detailTab':
        uid.key = '/erp/hxl.erp.deliveryinorder.read-detail-columns';
        uid.name = '/erp/hxl.erp.deliveryinorder.read-detail-columns-name';
        break;
      case 'totalTab':
        uid.key = '/erp/hxl.erp.deliveryinorder.read-total-columns';
        uid.name = '/erp/hxl.erp.deliveryinorder.read-total-columns-name';
        break;

      default:
        uid.key = '/erp/hxl.erp.deliveryinorder.read-detail-columns';
        uid.name = '/erp/hxl.erp.deliveryinorder.read-detail-columns-name';
    }
    return uid;
  };

  const InvoiceRender = (item: any) => {
    switch (item.code) {
      case 'item_name':
        item.render = (value: any, record: any, index: number) => {
          return (
            !record._empty && (
              <div className="overwidth">
                <XlbTooltip title={value}>
                  <div
                    style={{
                      maxWidth: '80%',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {value}
                  </div>
                </XlbTooltip>
                {record?.campaigns?.[0]?.campaign_type_name === '满赠' && (
                  <XlbTooltip title="该商品参与满赠活动,数量满足后自动添加赠品">
                    <span
                      style={{
                        color: '#fff',
                        background: 'rgb(133, 3, 255)',
                        borderRadius: '2px',
                        fontSize: '12px',
                        marginLeft: '5px',
                        lineHeight: '16px',
                        padding: '1px 4px',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      满赠
                    </span>
                  </XlbTooltip>
                )}
                {record?.present && (
                  <span
                    style={{
                      color: '#fff',
                      background: 'rgb(217, 3, 30)',
                      borderRadius: '2px',
                      fontSize: '12px',
                      marginLeft: '5px',
                      lineHeight: '16px',
                      padding: '1px 4px',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    赠品
                  </span>
                )}
              </div>
            )
          );
        };
        break;
      case 'basic_stock_quantity':
        item.render = (value: any, record: any) => {
          return record._empty ? null : (
            <div className="overwidth">
              {toFixed(
                safeMath.divide(record.value, record.ratio),
                'QUANTITY',
                true,
              )}
            </div>
          );
        };
        break;
      case 'purchase_money':
      case 'no_tax_purchase_money':
        item.render = (value: any) => {
          return (
            <div className="info overwidth">
              {hasAuth(['调入单/采购价', '查询']) && value !== '****'
                ? value === null
                  ? '——' : (Number(value || 0) || 0).toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      // 零售单价---零售价
      case 'sale_price':
        item.render = (value: any, record: any, { index = 0 }) => {
          return (
            <div className="info overwidth">
              {record._empty
                ? null
                : hasAuth(['调入单/零售价', '查询']) && value !== '****'
                  ? (Number(value || 0) || 0).toFixed(4)
                  : '****'}
            </div>
          );
        };
        break;
      case 'present_unit':
      case 'unit':
        item.render = (value: any, record: any, index: any) => {
          return record?._click &&
            record?.item_name &&
            tabsKey === 'detailTab' &&
            !outOrder &&
            !formDataSource &&
            !wmsFid ? (
            <XlbSelect
              style={{ width: '100%' }}
              defaultValue={value}
              onClick={(e) => {
                e?.stopPropagation();
              }}
              onChange={(e) => ratioChangeByUnit(e, item.code, index.index)}
            >
              {record?.units?.map((v: any, i: any) => {
                return JSON.parse(v).name ? (
                  <Option key={i} value={JSON.parse(v).name}>
                    {JSON.parse(v).name}
                  </Option>
                ) : null;
              })}
            </XlbSelect>
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'ratio': //#region 换算率
      case 'basic_quantity': //基本数量
        item.render = (value: any, record: any, { index = 0 }) => {
          return record?._click &&
            record?.item_name &&
            tabsKey === 'detailTab' &&
            record.account_method === '中心手工批次' &&
            centerStore.isCenter &&
            !outOrder &&
            !formDataSource &&
            !wmsFid ? (
            <XlbInput
              key={record[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index.toString()}
              defaultValue={(Number(value || 0) ||0 ).toFixed(3)}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onFocus={(e) => e.target.select()}
              onChange={(e) => inputChange(e, index, item.code, record.ratio)}
              onBlur={(e) => inputBlur(e, index, item.code, record.ratio)}
              style={{ textAlign: 'right' }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index);
              }}
            />
          ) : (
            <div className="info overwidth">
              {(Number(value || 0) || 0).toFixed(3)}
            </div>
          );
        };
        break;
      //#region数量 赠品数量
      case 'quantity': //数量
      case 'present_quantity': //赠品数量
        item.render = (value: any, record: any, { index = 0 }) => {
          return record._click &&
            record?.item_name &&
            tabsKey === 'detailTab' &&
            !formDataSource &&
            !wmsFid ? (
            <XlbInput
              key={record[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index.toString()}
              defaultValue={(Number(value || 0) || 0).toFixed(3)}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onFocus={(e) => e.target.select()}
              onChange={(e) => inputChange(e, index, item.code, record.ratio)}
              onBlur={(e) => inputBlur(e, index, item.code, record.ratio)}
              style={{ textAlign: 'right' }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index);
              }}
            />
          ) : (
            <div className="info overwidth">
              {(Number(value || 0) || 0).toFixed(3)}
            </div>
          );
        };
        break;
      // 税费、成本、成本去税---成本价
      case 'cost_money':
      case 'no_tax_cost_money':
      case 'tax_money':
        item.render = (value: any, record: any, { index = 0 }) => {
          return (
            <div className="info overwidth">
              {hasAuth(['调入单/成本价', '查询']) && value !== '****'
                ? (Number(value || 0) || 0).toFixed(2)
                : '****'}
            </div>
          );
        };
        break;

      //单价、金额、基本单价---配送价
      case 'money': //金额(含税)
        item.render = (value: any, record: any, { index = 0 }) => {
          return hasAuth(['调入单/配送价', '编辑']) &&
            record._click &&
            record?.item_name &&
            tabsKey === 'detailTab' &&
            !outOrder &&
            !formDataSource &&
            !wmsFid ? (
            <XlbInput
              key={record[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index.toString()}
              defaultValue={(Number(value || 0) || 0).toFixed(2)}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onFocus={(e) => e.target.select()}
              onChange={(e) => inputChange(e, index, item.code, record.ratio)}
              onBlur={(e) => inputBlur(e, index, item.code, record.ratio)}
              style={{ textAlign: 'right' }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index);
              }}
            />
          ) : (
            <div className="info overwidth">
              {hasAuth(['调入单/配送价', '查询']) && value !== '****'
                ? formatWithCommas((Number(value || 0) || 0).toFixed(2))
                : '****'}
            </div>
          );
        };
        break;
      case 'price': //单价(含税)
      case 'basic_price': //基本单价(含税)
        item.render = (value: any, record: any, { index = 0 }) => {
          return hasAuth(['调入单/配送价', '编辑']) &&
            record._click &&
            tabsKey === 'detailTab' &&
            record?.item_name &&
            !outOrder &&
            !formDataSource &&
            !wmsFid ? (
            <XlbInput
              key={record[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index.toString()}
              defaultValue={(Number(value) || 0).toFixed(4)}
              onFocus={(e) => e.target.select()}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onChange={(e) => inputChange(e, index, item.code, record.ratio)}
              onBlur={(e) => inputBlur(e, index, item.code, record.ratio)}
              style={{ textAlign: 'right' }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index);
              }}
            />
          ) : (
            <div className="info overwidth">
              {record._empty
                ? null
                : hasAuth(['调入单/配送价', '查询']) && value !== '****'
                  ? (Number(value || 0) || 0).toFixed(4)
                  : '****'}
            </div>
          );
        };
        break;
      case 'producing_date':
        item.render = (value: any, record: any, { index = 0 }) => {
          return record._click && record.producing_date_flag && !formDataSource && !wmsFid ? (
            <XlbDatePicker
              defaultValue={value ? dayjs(value) : undefined}
              onChange={(e) => inputChange(e, index, item.code, record.ratio)}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'expire_date':
        item.render = (value: any, record: any, { index = 0 }) => {
          return record._click && record.expire_date_flag && !formDataSource && !wmsFid ? (
            <XlbDatePicker
              defaultValue={value ? dayjs(value) : undefined}
              onChange={(e) => inputChange(e, index, item.code, record.ratio)}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'batch_number':
        item.render = (value: any, record: any, { index = 0 }) => {
          const reg = /[\W]/g;
          return record._click && record.batch_number_flag && !formDataSource && !wmsFid ? (
            <XlbInput
              key={record[item.code]}
              style={{ width: '120px' }}
              autoComplete={'off'}
              maxLength={20}
              onFocus={(e) => e.target.select()}
              defaultValue={value}
              size="small"
              onBlur={(e) => {
                rowData[index].batch_number = e.target.value.replace(reg, '');
                setRowData([...rowData]);
              }}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'memo':
        item.render = (value: any, record: any, { index = 0 }) => {
          return record._click &&
            tabsKey === 'detailTab' &&
            record?.item_name &&
            !formDataSource &&
            !wmsFid ? (
            <XlbInput
              key={record[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index.toString()}
              defaultValue={value}
              onFocus={(e) => e.target.select()}
              onChange={(e) => (rowData[index].memo = e.target.value)}
              onBlur={(e) => inputBlur(e, index, item.code, record.ratio)}
              style={{ textAlign: 'left' }}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index);
              }}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
    }
    return item;
  };
  //回车事件
  const onPressEnter = (code: any, index: any) => {
    Promise.resolve()
      .then(() => {
        rowData[index].edit = false;
        index + 1 === rowData.length
          ? (rowData[0].edit = true)
          : (rowData[index + 1].edit = true);
        setRowData(JSON.parse(JSON.stringify(rowData)));
      })
      .then(() => {
        const inputBox =
          index + 1 === rowData.length
            ? document.getElementById(code + '-' + (0).toString())
            : document.getElementById(code + '-' + (index + 1).toString());
        inputBox?.focus();
      });
  };
  //快捷添加行上、下、回车 键盘事件

  //单位改变换算率
  const ratioChangeByUnit = (value: any, key: any, index: any) => {
    //触发编辑
    setEdit(true);
    rowData[index][key] = value;
    //切换【单位】计算单价=基本单价*切换后单位的换算率；计算金额=单价*数量 ；计算基本数量=数量*切换后单位的换算率
    if (key === 'unit') {
      rowData[index].units.forEach((v: any, i: any) => {
        JSON.parse(v).name === value
          ? (rowData[index].ratio = JSON.parse(v).ratio)
          : null;
      });
      rowData[index].price = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].ratio,
      );
      rowData[index].money = safeMath.multiply(
        rowData[index].price,
        rowData[index].quantity,
      );
      rowData[index].basic_quantity = safeMath.multiply(
        rowData[index].quantity,
        rowData[index].ratio,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
      rowData[index].present_unit === rowData[index]?.unit &&
        (rowData[index].present_ratio = rowData[index].ratio);
    }
    if (key === 'present_unit') {
      rowData[index].units.forEach((v: any, i: any) => {
        JSON.parse(v).name === value
          ? (rowData[index].present_ratio = JSON.parse(v).ratio)
          : null;
      });
      rowData[index].present_unit === rowData[index].unit &&
        (rowData[index].present_ratio = rowData[index].ratio);
    }
    setRowData([...rowData]);
  };
  //输入框改变
  const inputChange = (e: any, index: any, key: any, ratio: any) => {
    //记录编辑
    setEdit(true);
    if (key === 'producing_date' || key === 'expire_date') {
      rowData[index][key] = e ? dayjs(e._d).format('YYYY-MM-DD') : null;
    }
    const regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (key === 'quantity' && regPos.test(e.target.value)) {
      if (
        !outOrder ||
        (outOrder &&
          safeMath.multiply(Number(e.target.value), ratio) <=
            rowData[index].available_basic_quantity)
      ) {
        //编辑【数量】,计算基本数量,金额（含税）;
        rowData[index][key] = Number(e.target.value);
        rowData[index].basic_quantity = safeMath.multiply(
          e.target.value,
          ratio,
        );
        rowData[index].money = safeMath.multiply(
          rowData[index].price,
          rowData[index][key],
        );
        rowData[index].tax_money = safeMath.minus(
          rowData[index].money,
          safeMath.divide(
            rowData[index].money,
            safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
          ),
        );
      }
    }
    if (key === 'price' && regPos.test(e.target.value) && e.target.value >= 0) {
      //编辑【单价（含税）】 计算金额（含税）=单价（含税）*数量；计算基本单价（含税）=单价（含税）/ 换算率；
      rowData[index][key] = Number(e.target.value);
      rowData[index].money = safeMath.multiply(
        rowData[index][key],
        rowData[index].quantity,
      );
      rowData[index].basic_price = safeMath.divide(
        rowData[index][key],
        rowData[index].ratio,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
    }
    if (key === 'money' && regPos.test(e.target.value) && e.target.value >= 0) {
      //编辑【金额（含税）】 计算税费=金额(含税)*销项税率*0.01  计算单价含税
      rowData[index][key] = Number(e.target.value);
      if (rowData[index].quantity != 0) {
        rowData[index].price = safeMath.divide(
          rowData[index].money,
          rowData[index].quantity,
        );
        rowData[index].basic_price = safeMath.divide(
          rowData[index].money,
          rowData[index].basic_quantity,
        );
      }
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
    }
    if (key === 'ratio' && regPos.test(e.target.value) && e.target.value >= 0) {
      //编辑【换算率】 计算基本单价（含税）, 基本数量；
      rowData[index][key] = Number(e.target.value);
      rowData[index].basic_quantity = safeMath.multiply(
        rowData[index].ratio,
        rowData[index].quantity,
      );
      rowData[index].money = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].basic_quantity,
      );
      rowData[index].present_unit === rowData[index].unit &&
        (rowData[index].present_ratio = rowData[index].ratio);
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
      if (rowData[index].quantity !== 0) {
        rowData[index].price = safeMath.divide(
          rowData[index].money,
          rowData[index].quantity,
        );
      }
    }
    if (key === 'basic_quantity' && regPos.test(e.target.value)) {
      //编辑【基本数量】计算换算率
      rowData[index][key] = Number(e.target.value);
      if (Number(e.target.value) === 0) {
        rowData[index].quantity = 0;
      }
      rowData[index].money = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].basic_quantity,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
      if (rowData[index].quantity != 0) {
        rowData[index].ratio = safeMath.divide(
          rowData[index].basic_quantity,
          rowData[index].quantity,
        );
        rowData[index].price = safeMath.divide(
          rowData[index].money,
          rowData[index].quantity,
        );
      }
      rowData[index].present_unit === rowData[index].unit &&
        (rowData[index].present_ratio = rowData[index].ratio);
    }
    if (
      key === 'basic_price' &&
      regPos.test(e.target.value) &&
      e.target.value >= 0
    ) {
      //编辑【基本单价（含税）】计算金额（含税）=基本单价*基本数量；计算单价=基本单价*换算率；
      rowData[index][key] = Number(e.target.value);
      rowData[index].price = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].ratio,
      );
      rowData[index].money = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].basic_quantity,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
    }
    if (key === 'present_quantity' && regPos.test(e.target.value)) {
      if (
        !outOrder ||
        (outOrder &&
          safeMath.multiply(
            Number(e.target.value),
            rowData[index].present_ratio,
          ) <= rowData[index].available_basic_present_quantity)
      ) {
        rowData[index][key] = Number(e.target.value);
      }
    }
    // debounce(setData)
  };
  //失去焦点
  const inputBlur = (e: any, index: any, key: any, ratio: any) => {
    const regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (
      key === 'quantity' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      XlbTipsModal({ tips: '数量请输入>=0的数字' });
      rowData[index][key] = 0;
      return;
    }
    if (
      key === 'quantity' &&
      outOrder &&
      safeMath.multiply(Number(e.target.value), ratio) >
        rowData[index].available_basic_quantity
    ) {
      XlbTipsModal({
        tips: `商品【${rowData[index].item_name}】待收数量为${safeMath.divide(
          rowData[index].available_basic_quantity,
          ratio,
        )}，已超量！`,
      });
      rowData[index][key] = safeMath.divide(
        rowData[index].available_basic_quantity,
        ratio,
      );
      return;
    }
    if (
      key === 'price' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      XlbTipsModal({ tips: '单价（含税）请输入>=0的数字' });
      rowData[index][key] = 0;
      return;
    }
    if (
      key === 'money' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      XlbTipsModal({ tips: '金额（含税）请输入>=0的数字' });
      rowData[index][key] = 0;
      return;
    }
    if (
      key === 'ratio' &&
      (!regPos.test(e.target.value) || e.target.value <= 0)
    ) {
      XlbTipsModal({ tips: '换算率请输入>0的数字' });
      rowData[index][key] = 0;
      return;
    }
    if (key === 'basic_quantity' && !regPos.test(e.target.value)) {
      XlbTipsModal({ tips: '基本数量请输入数字' });
      rowData[index][key] = 0;
      return;
    }
    if (key === 'basic_quantity' && !Number(rowData[index].quantity)) {
      XlbTipsModal({ tips: '数量为0,请先输入数量' });
      rowData[index][key] = 0;
      return;
    }
    if (
      key === 'basic_price' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      XlbTipsModal({ tips: '基本单价（含税）请输入>=0的数字' });
      rowData[index][key] = 0;
      return;
    }
    if (key === 'present_quantity' && !regPos.test(e.target.value)) {
      XlbTipsModal({ tips: '赠品数量请输入数字' });
      rowData[index][key] = 0;
      return;
    }
    if (
      key === 'present_quantity' &&
      outOrder &&
      safeMath.multiply(Number(e.target.value), rowData[index].present_ratio) >
        rowData[index].available_basic_present_quantity
    ) {
      XlbTipsModal({
        tips: `商品【${rowData[index].item_name}】赠品待收数量为${safeMath.divide(
          rowData[index].available_basic_present_quantity,
          rowData[index].present_ratio,
        )}，已超量！`,
      });
      rowData[index][key] = safeMath.divide(
        rowData[index].available_basic_present_quantity,
        rowData[index].present_ratio,
      );
      return;
    }
    if (
      safeMath.multiply(
        rowData[index].present_quantity,
        rowData[index].quantity,
      ) < 0
    ) {
      XlbTipsModal({
        tips: `商品【${rowData[index].item_name}】数量与赠品数量必须同为正数或负数！`,
      });
      rowData[index].present_quantity = 0;
      rowData[index].quantity = 0;
      return;
    }
    if (key === 'memo' && e.target.value.length > 200) {
      XlbTipsModal({ tips: '备注长度应<=200' });
      rowData[index][key] = e.target.value.substring(0, 200);
      return;
    }
    setRowData([...rowData]);
  };
  //#region 查询门店下仓库
  const getStockData = async (id: any) => {
    const res = await getStock({ store_id: id });
    if (res?.code === 0) {
      const labArr = res?.data?.map((item: any) => ({
        label: item.name,
        value: item.id,
        default_flag: item.default_flag,
      }));
      const _lab = labArr.filter((item: any) => item.default_flag);
      if (_lab.length !== 0) {
        form.setFieldsValue({ storehouse_id: _lab[0].value });
      } else {
        form.setFieldsValue({ storehouse_id: labArr[0]?.value });
      }
      setStockList(labArr);
    }
  };
  //#region 单位下拉框转换
  const setUnits = (v: any) => {
    if (!v) return;
    const unit = { name: v.unit, ratio: v.ratio };
    const basic_unit = {
      name: v.basic_unit,
      ratio: v.basic_unit === v.unit ? unit.ratio : 1,
    };
    const delivery_unit = {
      name: v.delivery_unit,
      ratio: v.delivery_unit === v.unit ? unit.ratio : v.delivery_ratio,
    };
    const purchase_unit = {
      name: v.purchase_unit,
      ratio: v.purchase_unit === v.unit ? unit.ratio : v.purchase_ratio,
    };
    const stock_unit = {
      name: v.stock_unit,
      ratio: v.stock_unit === v.unit ? unit.ratio : v.stock_ratio,
    };
    const wholesale_unit = {
      name: v.wholesale_unit,
      ratio: v.wholesale_unit === v.unit ? unit.ratio : v.wholesale_ratio,
    };

    v.units = Array.from(
      new Set([
        JSON.stringify(basic_unit),
        JSON.stringify(delivery_unit),
        JSON.stringify(purchase_unit),
        JSON.stringify(stock_unit),
        JSON.stringify(wholesale_unit),
        JSON.stringify(unit),
      ]),
    );
  };
  //#region 读取信息
  const readinfo = async (
    _fid: any,
    fids: any = [],
    summary: boolean = tabsKey !== 'detailTab',
  ) => {
    setEdit(_fid === 'handleBill');
    setAudit(_fid === 'handleBill');
    setIsLoading(true);
    const res =
      _fid === 'handleBill'
        ? await deliveryout({ fids: fids })
        : await readInfo({
            fid: _fid,
            summary: summary,
            orders: sortType.code
              ? [
                  {
                    direction: sortType.order.toUpperCase(),
                    property: sortTypeSwitch(sortType.code),
                  },
                ]
              : undefined,
          });
    if (res?.code === 0) {
      await getStockData(res?.data?.store_id);
      setCenterStore({
        isCenter: res?.data?.in_center_flag,
        deliveryCenterId: '',
      });
      setOutStore({ isCenter: res?.data?.out_center_flag });
      setManagementType(res?.data?.management_type);
      setEnableDeliveryCenter(res?.data?.in_center_flag);
      res?.data?.details.forEach((v: any) => {
        v.tax_rate = v.tax_rate ? v.tax_rate : 0;
        v.present_quantity = v.present_quantity ? v.present_quantity : 0;
        v.basic_stock_quantity = v.basic_stock_quantity
          ? v.basic_stock_quantity
          : 0;
        v.basic_available_stock_quantity = v.basic_available_stock_quantity
          ? v.basic_available_stock_quantity
          : 0;
        setUnits(v);
      });
      setInfo({ state: res?.data?.state, fid: res?.data?.fid });
      setFileList(res?.data?.files);
      setWmsInfo(res.data?.wms_info ? res.data?.wms_info : {});
      setWmsFid(res.data?.wms_fid ? res.data?.wms_fid : null);
      if (tabsKey === 'detailTab') {
        setRowData(
          res?.data?.details?.map((t: any) => ({
            ...t,
            short_row_id: uuidv4(),
          })),
        );
      } else {
        setSummaryData(
          res.data?.summary_details?.map((t: any) => ({
            ...t,
            short_row_id: uuidv4(),
          })),
        );
      }
      form.setFieldsValue({
        ...res.data,
        out_store_name: res?.data?.out_store_name,
        out_store_id: res?.data?.out_store_id,
        store_name: res?.data?.store_name,
        store_id: res?.data?.store_id,
        storehouse_id: res?.data?.storehouse_id,
        item_dept_names: res?.data?.item_dept_names,
        delivery_out_order_fids: res?.data?.delivery_out_order_fids,
        memo: res?.data?.memo,
        operate_date: res?.data?.operate_date
          ? dayjs(res?.data?.operate_date)
          : dayjs(),
        payment_date: res?.data?.payment_date
          ? dayjs(res?.data?.payment_date)
          : null,
        create_time: res?.data?.create_time,
        audit_time: res?.data?.audit_time,
        updata_time: res?.data?.updata_time,
      });
      setFormDataSource(res?.data?.data_source === 'WAREHOUSE_TRANSFER')
    }
    setIsLoading(false);
  };

  //查询是否开启跨配送中心调拨
  const getdeliveryparam = async () => {
    const res = await deliveryparamRead();
    if (res?.code === 0) {
      setToOtherCenter(res?.data?.un_center_transform);
      setUnCenterStoreApplication(res?.data?.un_center_store_application);
    }
  };
  const getArrayChild = (v: any) => {
    const res = Object.prototype.toString.call(v).slice(8, -1).toLowerCase();
    return res === 'array' ? v[0] : v;
  };
  useEffect(() => {
    const record = props.data.current;
    const { index, ...rest } = record;
    setFid(record.fid);
    setAllRow({ ...rest, index: index });
    if (record.index === 0) setLastLoding(true);
    if (record.index === record.total - 1) setNextLoding(true);
    if (record.fid === 1) {
      setRowData([{ _empty: true,  short_row_id: uuidv4() }]);
      setCenterStore({
        isCenter: LStorage.get('userInfo').store.enable_delivery_center,
        deliveryCenterId: LStorage.get('userInfo').store.upstream_center_id,
      });
      getStockData(LStorage.get('userInfo').store_id);
      form.setFieldsValue({
        // store_name: LStorage.get('userInfo').store_name,
        store_id: [LStorage.get('userInfo').store_id],
        org_id: enable_organization && LStorage.get('userInfo').org_id,
        org_name: enable_organization && LStorage.get('userInfo').org_name,
        operate_date: dayjs(),
        // payment_date: dayjs()
      });
      setManagementType(LStorage.get('userInfo').store.management_type);
      setEnableDeliveryCenter(
        LStorage.get('userInfo').store.enable_delivery_center,
      );
    } else {
      // readinfo(record.fid)
    }
    getdeliveryparam();
    // itemArr.map((v) => InvoiceRender(v));
    // itemArrdetail.map((v) => InvoiceRender(v));
  }, []);

  useEffect(() => {
    const rec = props.data.current || {};
    if (tabsKey === 'totalTab') {
      // 商品汇总状态下
      if (rec?.fid !== 1 || form.getFieldValue('fid')) {
        // 有fid时调接口
        readinfo(form.getFieldValue('fid') || rec?.fid, [], true);
      }
      // itemArr.map((v) => InvoiceRender(v));
      if (rowData[rowData.length - 1]?._empty) {
        rowData.splice(rowData.length - 1, 1);
      }
      setRowData([...rowData]);
    } else {
      // 商品明细时候调用
      if (rec?.fid !== 1 || form.getFieldValue('fid')) {
        // 有fid时调接口
        readinfo(form.getFieldValue('fid') || rec?.fid, [], false);
      }
    }
  }, [JSON.stringify(tabsKey)]);

  useEffect(() => {
    // 获取表单字段值
    const outStoreId = form.getFieldValue('out_store_id');
    const storeId = form.getFieldValue('store_id');
    const storehouseId = form.getFieldValue('storehouse_id');
    const deliveryOutOrderFids = form.getFieldValue('delivery_out_order_fids');

    // 处理 setBatchLoding 逻辑
    if (outStoreId && storeId && storehouseId && info.state === 'INIT') {
      setBatchLoding(false);
    } else {
      setBatchLoding(true);
    }

    // 处理 setOutOrder 逻辑
    if (deliveryOutOrderFids?.[0]) {
      setOutOrder(true);
    } else {
      setOutOrder(false);
    }
  }, [form.getFieldsValue()]);

  useEffect(() => {
    // 设置合计行
    footerData[0] = {};
    footerData[0]._index = '合计';
    footerData[0].money = hasAuth(['调入单/配送价', '查询'])
      ? rowData
          .reduce(
            (sum, v) => safeMath.add(sum, Number(v?._empty ? 0 : v.money) || 0),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].quantity = rowData
      .reduce(
        (sum, v) => safeMath.add(sum, Number(v?._empty ? 0 : v.quantity) || 0),
        0,
      )
      .toFixed(3);
    footerData[0].tax_money = hasAuth(['调入单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) => safeMath.add(sum, Number(v?._empty ? 0 : v.tax_money) || 0),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].basic_quantity = rowData
      .reduce(
        (sum, v) => safeMath.add(sum, Number(v?._empty ? 0 : v.basic_quantity) || 0),
        0,
      )
      .toFixed(3);
    footerData[0].present_quantity = rowData
      .reduce(
        (sum, v) =>
          safeMath.add(sum, Number(v?._empty ? 0 : v.present_quantity) || 0),
        0,
      )
      .toFixed(3);
    footerData[0].cost_money = hasAuth(['调入单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) => safeMath.add(sum, Number(v?._empty ? 0 : v.cost_money) || 0),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].no_tax_cost_money = hasAuth(['调入单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) =>
              safeMath.add(sum, Number(v?._empty ? 0 : v.no_tax_cost_money) || 0),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].purchase_money = hasAuth(['调入单/采购价', '查询'])
      ? rowData
          .reduce(
            (sum, v) =>
              safeMath.add(sum, Number(v?.newRow ? 0 : v.purchase_money) || 0),
            0,
          )
          .toFixed(4)
      : '****';
    footerData[0].no_tax_purchase_money = hasAuth(['调入单/采购价', '查询'])
      ? rowData
          .reduce(
            (sum, v) =>
              safeMath.add(
                sum,
                Number(v?.newRow ? 0 : v.no_tax_purchase_money) || 0,
              ),
            0,
          )
          .toFixed(4)
      : '****';
    setFooterData([...footerData]);
    // setPagin({
    //   ...pagin,
    //   pageSize: rowData.length || 200,
    //   total: rowData[rowData.length - 1]?._empty
    //     ? rowData.length - 1
    //     : rowData.length,
    // });
    if (
      rowData.length &&
      rowData[0]._empty &&
      form.getFieldValue('delivery_out_order_fids') &&
      form.getFieldValue('delivery_out_order_fids')[0]
    ) {
      allClear();
    }
  }, [JSON.stringify(rowData)]);
  useEffect(() => {
    if (fid !== 1 && sortType.code !== '') {
      readinfo(form.getFieldValue('fid'));
    }
  }, [sortType]);
  //数据校验
  const errListFun = (data: any) => {
    function isNumber(value: string | number) {
      if (typeof value === 'number') {
        return !isNaN(value) && isFinite(value);
      }
      if (typeof value === 'string') {
        value = value.trim();
        if (value === '') return false;
        const num = Number(value);
        return !isNaN(num) && isFinite(num);
      }
      return false;
    }

    const regPos = {
      test: (e) => isNumber(e),
    };
    //  /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;

    const Arow = data.filter((_: any) => _.item_name);
    Arow.forEach((v: any, i: any) => {
      if (!regPos.test(v.quantity) || v?.quantity < 0)
        errNames.push(`第${i + 1}行,【${v.item_name}】数量请输入>=0的数字!`);
      if (!(regPos.test(v.ratio) || v.ratio < 0 || v.ratio > 999999999.999))
        errNames.push(
          `第${i + 1}行,【${v.item_name}】换算率请输入>=0并且<=999999999.999的数字!`,
        );
      if (!regPos.test(v.price) || v.price < 0)
        errNames.push(
          `第${i + 1}行,【${v.item_name}】单价（含税）请输入>=0的数字!`,
        );
      if (!regPos.test(v.money))
        errNames.push(`第${i + 1}行,【${v.item_name}】金额（含税）请输入数字!`);
      if (!regPos.test(v.basic_quantity)) {
        errNames.push(`第${i + 1}行,【${v.item_name}】基本数量请输入数字`);
      }
      if (!regPos.test(v.basic_price) || v.basic_price < 0)
        errNames.push(
          `第${i + 1}行,【${v.item_name}】基本单价（含税）请输入>=0的数字!`,
        );
      if (!regPos.test(v.present_quantity))
        errNames.push(`第${i + 1}行,【${v.item_name}】赠品数量请输入数字`);
      if (
        regPos.test(v.present_quantity) &&
        regPos.test(v.quantity) &&
        safeMath.multiply(v.present_quantity, v.quantity) < 0
      )
        errNames.push(
          `第${i + 1}行,【${v.item_name}】数量与赠品数量必须同为正数或负数！`,
        );
      if (v.memo?.length > 200)
        errNames.push(`第${i + 1}行,【${v.item_name}】备注长度应<=200!`);
    });
    Arow.forEach((v) => {
      v.basic_price = (Number(v.basic_price || 0) || 0).toFixed(8);
      v.quantity = (Number(v.quantity || 0) || 0).toFixed(8);
      v.basic_quantity = (Number(v.basic_quantity || 0) || 0).toFixed(8);
    });
    if (errNames.length) {
      XlbTipsModal({
        tips: (
          <>
            <div style={{ color: 'red' }}>以下数据编辑存在问题！</div>
            {errNames.map((_) => (
              <div key={_}>{_}</div>
            ))}
          </>
        ),
      });
      setErrNames([]);
      return;
    }
  };

  // 保存
  const saveOrder = async () => {
    if (formDataSource) {
      XlbTipsModal({tips: 'wms产生单据不允许操作'})
      return
    }
    if (!form.getFieldValue('out_store_id')) {
      XlbTipsModal({
        tips: '请先选择调出门店',
      });
      return;
    }

    if (
      rowData?.length === 0 ||
      rowData?.every(({ item_name }) => !item_name)
    ) {
      XlbTipsModal({
        tips: '请先添加商品明细',
      });
      return;
    }
    errListFun(rowData);
    if (errNames.length) return;
    const submitData = rowData.filter((_) => _.item_name);
    submitData.map(
      (v: any) =>
        (v.basic_present_quantity = safeMath.multiply(
          v.present_quantity,
          v.present_ratio,
        )),
    );
    const data = {
      out_org_id: form.getFieldValue('out_org_id'),
      org_id: form.getFieldValue('org_id'),
      memo: form.getFieldValue('memo'),
      fid: form.getFieldValue('fid'),
      operate_date: dayjs(form.getFieldValue('operate_date')).format(
        'YYYY-MM-DD',
      ),
      payment_date: form.getFieldValue('payment_date')
        ? dayjs(form.getFieldValue('payment_date')).format('YYYY-MM-DD')
        : null,
      out_store_id: getArrayChild(form.getFieldValue('out_store_id')),
      store_id: getArrayChild(form.getFieldValue('store_id')),
      storehouse_id: form.getFieldValue('storehouse_id'),
      delivery_out_order_fids: form.getFieldValue('delivery_out_order_fids'),
      details: submitData,
      files: fileList,
    };
    if (fid === 1) {
      getData(1);
      setIsLoading(true);
      const res = await addInfo(data);
      setIsLoading(false);
      if (res?.code === 0) {
        readinfo(res?.data?.fid);
        setEdit(false);
        setReload(true);
        setFid(res?.data?.fid);
        XlbMessage.success('保存成功');
        return;
      }
    } else {
      setIsLoading(true);
      const res = await updateInfo(data);
      setIsLoading(false);
      if (res?.code === 0) {
        readinfo(res?.data?.fid);
        setEdit(false);
        setReload(true);
        XlbMessage.success('保存成功');
        return;
      }
    }
  };
  /**
   * @function 获取数据
   */
  const checkData = (page_Num: number) => {
    const _formData: any = LStorage.get('deliveryInOrder');
    // const time_type = _formData.time_type;
    const data: any = { ..._formData };
    data.page_number = page_Num - 1;
    // data.page_size = pagin.pageSize;
    // data.create_date =
    //   time_type === 'create_date' ? _formData.create_date : undefined;
    // data.audit_date =
    //   time_type === 'audit_date' ? _formData.create_date : undefined;
    // data.state = _formData.state;
    // data.settlement_state = _formData.settlement_state;
    // data.store_ids = _formData.store_names ? _formData.store_ids : undefined;
    // data.storehouse_id = _formData.storehouse_id
    //   ? _formData.storehouse_id
    //   : undefined;
    // data.out_store_ids = _formData.out_store_names
    //   ? _formData.out_store_ids
    //   : undefined;
    // data.item_ids = _formData.item_names ? _formData.item_ids : undefined;
    // data.fid = _formData.fid;
    return data;
  };
  const getData = async (page_Num: number) => {
    setIsLoading(true);
    const data = checkData(page_Num);
    const res = await getDeliveryInOrder({ ...data });
    if (res?.code === 0) {
      setAllRow({
        allRow: res?.data?.content,
        total: res?.data?.total_elements,
        index: 0,
      });
      setLastLoding(true);
      setNextLoding(false);
    }
    setIsLoading(false);
  };
  const reauditItem = async () => {
    if (formDataSource) {
      XlbTipsModal({tips: 'wms产生单据不允许操作'})
      return
    }
    const data = {
      fids: [form.getFieldValue('fid')],
    };
    setIsLoading(true);
    const res = await reauditRequest(data);
    if (res?.code === 0) {
      readinfo(form.getFieldValue('fid'));
      setEdit(false);
      setReload(true);
      XlbMessage.success('反审核成功');
    }
    setIsLoading(false);
  };
  //审核
  const auditItem = async () => {
    if (rowData[rowData.length - 1]?._empty) {
      rowData.splice(rowData.length - 1, 1);
    }
    if (formDataSource) {
      XlbTipsModal({tips: 'wms产生单据不允许操作'})
      return
    }
    //判断是否添加商品
    if (rowData.length === 0) {
      XlbTipsModal({
        tips: `请先添加商品！`,
      });
      return;
    }
    // 判断商品数量
    if (
      !(Number(footerData[0].quantity) + Number(footerData[0].present_quantity))
    ) {
      XlbTipsModal({
        tips: `数量、赠品数量都为0，无法审核！`,
      });
      return;
    }
    //判断商品是否选择明细
    const errs: any = [];
    rowData.forEach((item, index) => {
      item.basic_present_quantity = safeMath.multiply(
        item.present_quantity,
        item.present_ratio,
      );
      if (
        centerStore.isCenter &&
        item.producing_date_flag &&
        !item.producing_date
      ) {
        errs.push(
          `第${index + 1}行,【${rowData[index].item_name}】未录入生产日期`,
        );
      }
      if (centerStore.isCenter && item.expire_date_flag && !item.expire_date) {
        errs.push(
          `第${index + 1}行,【${rowData[index].item_name}】未录入到期日期`,
        );
      }
    });
    if (errs.length > 0) {
      XlbTipsModal({
        tips: (
          <>
            <div style={{ color: 'red' }}>以下数据编辑存在问题！</div>
            {errs.map((_) => (
              <div key={_}>{_}</div>
            ))}
          </>
        ),
      });
      return;
    }
    errListFun(rowData);
    if (errNames.length) return;
    const data = {
      memo: form.getFieldValue('memo'),
      fid: form.getFieldValue('fid'),
      operate_date: dayjs(form.getFieldValue('operate_date')).format(
        'YYYY-MM-DD',
      ),
      payment_date: form.getFieldValue('payment_date')
        ? dayjs(form.getFieldValue('payment_date')).format('YYYY-MM-DD')
        : null,
      out_store_id: getArrayChild(form.getFieldValue('out_store_id')), //调出
      store_id: getArrayChild(form.getFieldValue('store_id')), //调入
      storehouse_id: form.getFieldValue('storehouse_id'),
      delivery_out_order_fids: form.getFieldValue('delivery_out_order_fids'),
      details: rowData,
      files: fileList,
    };
    setIsLoading(true);
    const res = await auditInfo(data);
    setIsLoading(false);
    if (res?.code === 0) {
      readinfo(res?.data?.fid);
      setEdit(false);
      setReload(true);
      XlbMessage.success('操作成功');
      return;
    }
  };
  // 导入

  const importItem = async () => {
    const res = await XlbImportModal({
      templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.deliveryinorder.template.download`,
      importUrl: `${process.env.ERP_URL}/erp/hxl.erp.deliveryinorder.import`,
      templateName: '下载导入模板',
      params: {
        store_id: form.getFieldValue('store_id'),
        out_store_id: form.getFieldValue('out_store_id'),
        storehouse_id: form.getFieldValue('storehouse_id'),
      },
      callback: (res) => {
        if (res?.code === 0) {
          addItemWithType(res?.data?.details || [], 'import');
        }
      },
    });
  };

  //导出
  const exportItem = async (e) => {
    if (edit) return XlbMessage.warning('请先保存！');
    setIsLoading(true);
    const data = {
      fid: form.getFieldValue('fid'),
      summary: tabsKey === 'totalTab',
      orders: sortType.code
        ? [
            {
              direction: sortType.order.toUpperCase(),
              property: sortTypeSwitch(sortType.code),
            },
          ]
        : null,
    };
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliveryinorder.detail.export',
      { ...data },
    );
    if (res?.code === 0) {
      XlbMessage.success('导出受理成功，请前往下载中心查看');
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
    }
    setIsLoading(false);
  };
  //返回前判断保存状态
  const goBack = async () => {
    if (edit) {
      await XlbTipsModal({
        tips: '单据未保存，是否确认返回？',
        isCancel: true,
        onOkBeforeFunction: () => {
          // props.parentRef.current.close();
          props?.closeModal();
          return true;
        },
      });
      return false;
    }
    // props.parentRef.current.close();
    props?.closeModal(reload);
  };
  //分页切换事件
  // const pageChange = (p: number) => {
  //   setPagin({
  //     ...pagin,
  //     pageNum: p,
  //   });
  // };

  //打印
  const printItem = async () => {
    const data = {
      fid: form.getFieldValue('fid'),
      orders: sortType.code
        ? [
            {
              direction: sortType.order.toUpperCase(),
              property: sortTypeSwitch(sortType.code),
            },
          ]
        : null,
    };
    setIsLoading(true);
    const res = await print(data);
    setIsLoading(false);
    if (res?.code === 0) {
      // signatureData.current = res.data;
      // printDom(res.data);
      XlbPrintModal({ data: res.data });
      // setSignatureVisible(true);
      // NiceModal.show(PrintMoreModal, { src: res.data });
      // window.open(res.data, "_blank", "toolbar=no, menubar=no, scrollbars=no, resizable=no,location=no, status=no")
    }
  };
  // const printDom = async (data: any[]) => {
  //   const url = data[0];
  //   await XlbTipsModal({
  //     width: 900,
  //     title: '打印详情',
  //     tips: (
  //       <>
  //         <h3 style={{ padding: 12 }}>{url?.template_name}</h3>
  //         <div style={{ height: '600px', overflow: 'hidden' }}>
  //           <iframe
  //             style={{ width: '100%', height: '100%' }}
  //             src={url?.url || ''}
  //           ></iframe>
  //         </div>
  //       </>
  //     ),
  //   });
  // };

  const addItemWithType = (list: any = [], modalType = 'items') => {
    const ids = rowData.map((v) => v.item_id);
    let repeatArr: Array<any> = [];
    let newArr: Array<any> = [];
    if (centerStore.isCenter) {
      repeatArr = list.filter(
        (v: any) =>
          ids.includes(modalType === 'import' ? v.item_id : v.id) &&
          !(
            v.account_method === '中心手工批次' ||
            (v.account_method === '移动加权平均' &&
              (v.expire_date_flag || v.producing_date_flag))
          ),
      );
      newArr = list.filter(
        (item: any) =>
          !ids.includes(modalType === 'import' ? item.item_id : item.id) ||
          item.account_method === '中心手工批次' ||
          (item.account_method === '移动加权平均' &&
            (item.expire_date_flag || item.producing_date_flag)),
      );
    } else {
      repeatArr = list.filter((v: any) =>
        ids.includes(modalType === 'import' ? v.item_id : v.id),
      );
      newArr = list.filter(
        (item: any) =>
          !ids.includes(modalType === 'import' ? item.item_id : item.id),
      );
    }
    const rName = [
      repeatArr
        .map((v: any) => `【${modalType === 'import' ? v.item_name : v.name}】`)
        .join('、'),
    ];
    if (repeatArr.length) {
      XlbTipsModal({
        tips: '以下商品已存在，不允许重复添加，系统已自动过滤！',
        tipsList: rName,
      });
    }
    const newList = newArr.map((v: any) => {
      return {
        sale_price: v?.sale_price,
        account_method: v.account_method,
        item_id: v.id || v.item_id,
        item_code: v.code || v.item_code,
        item_bar_code: v.bar_code || v.item_bar_code,
        item_name: v.name || v.item_name,
        item_spec: v.purchase_spec || v.item_spec,
        unit: modalType === 'import' ? v.unit : v.delivery_unit,
        quantity: v.quantity || 0,
        price: v.price || safeMath.multiply(v.basic_price, v.delivery_ratio),
        money: v.money || 0,
        tax_rate: v.tax_rate,
        tax_money: v.tax_money || 0,
        basic_unit: modalType === 'import' ? v.basic_unit : v.unit,
        ratio: Number(modalType === 'import' ? v.ratio : v.delivery_ratio),
        basic_quantity: v.basic_quantity || 0,
        basic_price: v.basic_price,
        present_unit: v.present_unit ? v.present_unit : v.delivery_unit,
        present_ratio: v.present_ratio ? v.present_ratio : v.delivery_ratio,
        present_quantity: v.present_quantity || 0,
        producing_date: v?.producing_date || null,
        expire_date: v?.expire_date || null,
        batch_number: v?.batch_number || null,
        item_category_name: v.item_category_name, //保质期规则
        period: v.period
          ? v.period
          : v.expire_type === 1
            ? v.expire_type_num + '天'
            : v.expire_type_num + '月', //保质期
        basic_stock_quantity: v.basic_stock_quantity,
        memo: v.memo || '',
        cost_price: v.cost_price,
        producing_date_flag: v.producing_date_flag, //是否可选生产日期
        batch_number_flag: v.batch_number_flag, //批次号
        expire_date_flag: v.expire_date_flag, //到期日期
        date_in_type: v.date_in_type, //日期录入规则
        delivery_unit: v.delivery_unit,
        delivery_ratio: v.delivery_ratio, //配送单位换算率
        purchase_unit: v.purchase_unit,
        purchase_ratio: v.purchase_ratio, //采购单位
        stock_unit: v.stock_unit,
        stock_ratio: v.stock_ratio, //库存单位
        wholesale_unit: v.wholesale_unit,
        wholesale_ratio: v.wholesale_ratio, //批发单位
        units: Array.from(
          new Set([
            JSON.stringify(
              modalType === 'import'
                ? {
                    name: v.basic_unit,
                    ratio: v.unit === v.basic_unit ? v.ratio : 1,
                  }
                : {
                    name: v.delivery_unit,
                    ratio:
                      modalType === 'import'
                        ? v.unit === v.delivery_unit
                          ? v.ratio
                          : v.delivery_ratio
                        : v.delivery_ratio,
                  },
            ),
            JSON.stringify({
              name: v.unit,
              ratio: modalType === 'import' ? v.ratio : 1,
            }),
            JSON.stringify({
              name: v.delivery_unit,
              ratio:
                modalType === 'import'
                  ? v.unit === v.delivery_unit
                    ? v.ratio
                    : v.delivery_ratio
                  : v.delivery_ratio,
            }),
            JSON.stringify({
              name: v.purchase_unit,
              ratio:
                modalType === 'import'
                  ? v.unit === v.purchase_unit
                    ? v.ratio
                    : v.purchase_ratio
                  : v.purchase_ratio,
            }),
            JSON.stringify({
              name: v.stock_unit,
              ratio:
                modalType === 'import'
                  ? v.unit === v.stock_unit
                    ? v.ratio
                    : v.stock_ratio
                  : v.stock_ratio,
            }),
            JSON.stringify({
              name: v.wholesale_unit,
              ratio:
                modalType === 'import'
                  ? v.unit === v.wholesale_unit
                    ? v.ratio
                    : v.wholesale_ratio
                  : v.wholesale_ratio,
            }),
          ]),
        ),
      };
    });
    newList.length > 0 ? setEdit(true) : setEdit(false);
    const mergeArr = [...rowData, ...newList];
    mergeArr.forEach((item, index) => {
      item.short_row_id = uuidv4();
      if (item._empty) {
        mergeArr.splice(index, 1);
      }
    });
    forbidenClickRowEvent.current = true;
    setChooseList([])
    setRowData([...mergeArr]);
    setTimeout(() => {
      forbidenClickRowEvent.current = false;
    }, 0);
    // if (modalType === 'import' && newList.length) {
    //   XlbMessage.success('导入成功,共导入' + newList.length + '条数据')
    // }
  };

  // 批量添加
  const handleBatchAdd = async () => {
    const { out_store_id, store_id, storehouse_id } = form.getFieldsValue(true);
    if (!getArrayChild(out_store_id)) {
      XlbMessage.warning('调出门店不能为空!');
      return;
    }
    if (getArrayChild(out_store_id) === getArrayChild(store_id)) {
      XlbMessage.warning('调出门店与调入门店不可相同!');
      return;
    }
    const result = await XlbBasicData({
      type: 'batchAddGoods',
      data: {
        company_id: LStorage.get('userInfo')?.company_id,
        operator_store_id: LStorage.get('userInfo').store_id,
        store_id: getArrayChild(store_id),
        out_store_id: getArrayChild(out_store_id),
        storehouse_id,
        status: 1,
        supplierSwitch: false,
      },
      isMultiple: true,
      dataType: 'lists',
      primaryKey: 'id',
      resetForm: true,
      nullable: false,
    });
    if (result) {
      result?.forEach((_) => (_.item_name = _.name));
      addItemWithType(result);
    }
  };
  //#region 单据弹框确定

  //单据取消事件
  const allClear = () => {
    setEdit(true);
    setRowData([]);
    form.setFieldsValue({
      out_store_name: '',
      out_store_id: '',
      store_name: LStorage.get('userInfo').store_name,
      store_id: LStorage.get('userInfo').store_id,
      storehouse_id: '',
      item_dept_names: '',
      memo: '',
      delivery_out_order_fids: [],
      operate_date: dayjs(),
      // payment_date: dayjs()
    });
    getStockData(LStorage.get('userInfo').store_id);
  };

  const openUpload = async () => {
    if (formDataSource) {
      XlbTipsModal({tips: 'wms产生单据不允许操作'})
      return
    }
    setFileInfo([...fileList]);
    setUploadFileModalVisible(true);
  };

  const billVisible = () => {
    NiceModal.show(BillModal, {
      options: stockList,
      selected: form.getFieldValue('delivery_out_order_fids') || [],
      setData: (e: any) => {
        form.setFieldValue('delivery_out_order_fids', e);
        readinfo('handleBill', e);
      },
    });
  };

  // #region  HTML
  return (
    <div
      style={{
        padding: 12,
        height: 'calc(100vh - 120px)',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* 打印 */}
      {/*<SignaturePrintingModal*/}
      {/*  open={signatureVisible}*/}
      {/*  handleCancel={() => {*/}
      {/*    setSignatureVisible(false);*/}
      {/*  }}*/}
      {/*  setPrintModal={() => {*/}
      {/*    setSignatureVisible(false);*/}
      {/*  }}*/}
      {/*  rowData={signatureData.current}*/}
      {/*/>*/}

      <div className={'button_box row-flex'}>
        <div
          style={{ width: '90%', height: '100%' }}
          className="row-flex v-flex"
        >
          <XlbButton.Group>
            {hasAuth(['调入单', '编辑']) ? (
              <XlbButton
                type="primary"
                label="保存"
                disabled={info.state !== 'INIT' || isLoading || !!wmsFid}
                onClick={saveOrder}
                icon={<XlbIcon name="baocun" />}
              />
            ) : null}
            {hasAuth(['调入单', '审核']) ? (
              <XlbButton
                type="primary"
                label="审核"
                disabled={info.state !== 'INIT' || audit || isLoading}
                onClick={auditItem}
                icon={<XlbIcon name="shenhe" />}
              />
            ) : null}
            {hasAuth(['调入单', '编辑']) ? (
              <XlbBadge
                className={styles.badge}
                count={fileList.length}
                offset={[-6, 6]}
                size="small"
              >
                <XlbButton
                  type="primary"
                  label="附件"
                  disabled={
                    (info.state !== 'INIT' && fileList.length === 0) || !!wmsFid
                  }
                  onClick={openUpload}
                  icon={<XlbIcon name="fujian" />}
                />
              </XlbBadge>
            ) : null}
            {!hasAuth(['调入单', '导出']) &&
            !hasAuth(['调入单', '打印']) ? null : (
              <XlbDropdownButton
                label="业务操作"
                dropList={[
                  {
                    label: '导出',
                    disabled:
                      isLoading || !hasAuth(['调入单', '导出']) || fid === 1,
                  },
                  {
                    label: '打印',
                    disabled:
                      fid === 1 || isLoading || !hasAuth(['调入单', '打印']),
                  },
                ]}
                dropdownItemClick={(value, opt = {}, e) => {
                  if (opt.label === '导出') {
                    exportItem(e);
                  } else {
                    printItem();
                  }
                }}
              />
            )}
            <XlbDropdownButton
              label="常用操作"
              dropList={[
                {
                  label: '上一单',
                  value: '上一单',
                  disabled: lastLoding || fid === 1,
                },
                {
                  label: '下一单',
                  value: '下一单',
                  disabled: nextLoding || fid === 1,
                },
              ]}
              dropdownItemClick={(value: number, opt = {}) => {
                switch (opt.label) {
                  case '上一单': // 上一单
                    allRow.index = Math.max(0, (allRow.index -= 1));
                    setLastLoding(allRow.index === 0);
                    setNextLoding(allRow.index === allRow.total - 1);
                    readinfo(allRow.allRow[allRow.index].fid);

                    break;
                  case '下一单': // 下一单
                    if (allRow.index !== allRow.total - 1) {
                      allRow.index++;
                    }
                    setLastLoding(allRow.index === 0);
                    setNextLoding(allRow.index === allRow.total - 1);
                    readinfo(allRow.allRow[allRow.index].fid);
                    break;
                }
              }}
            />
            {LStorage.get('userInfo').tel === '15151864744' ? (
              <XlbButton
                type="primary"
                label="反审核"
                disabled={info.state !== 'AUDIT' || isLoading}
                onClick={reauditItem}
                icon={<XlbIcon name="fanshenhe" />}
              />
            ) : null}
            <XlbButton
              type="primary"
              label="返回"
              onClick={goBack}
              icon={<XlbIcon name="fanhui" />}
            />
          </XlbButton.Group>
        </div>
        <div
          style={{
            width: '10%',
            height: '28px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            columnGap: 8,
          }}
        >
          <XlbTooltip title={!isFold ? '收起' : '展开'}>
            <XlbIcon
              data-type={'顶层展开收起'}
              onClick={() => setIsFold(!isFold)}
              name="shouqi"
              size={20}
              className={classnames('xlb-columns-main-btn', {
                'xlb-columns-fold': !isFold,
                'xlb-columns-expand-btn-dev': true,
              })}
            />
          </XlbTooltip>
          <XlbColumns
            isFold={isFold}
            isFoldChange={setIsFold}
            url={handleUid()?.key}
            // originColumns={oldArr()}
            // ts-ignore
            originColumns={
              tabsKey === 'detailTab' ? itemTableListDetail : itemTableList_all
            }
            value={tabsKey === 'detailTab' ? itemArrdetail : itemArr}
            onChange={(e) => {
              if (tabsKey === 'detailTab') {
                setdetailItemArr(e);
              } else {
                setItemArr(e);
              }
            }}
            name={handleUid()?.name}
          />
        </div>
      </div>
      {isFold ? null : (
        <div ref={formBox}>
          {/* new form */}
          <XlbBasicForm
            colon
            form={form}
            autoComplete="off"
            layout="inline"
            className={styles.contractTab}
          >
            <XlbTabs
              defaultActiveKey={'baseInfo'}
              onChange={(key: string) => setTableKey(key)}
              items={[
                {
                  label: '基本信息',
                  key: 'baseInfo',
                  children: (
                    <div style={{ marginTop: 12 }} className="row-flex">
                      <div
                        className="row-flex"
                        style={{ flex: 1, flexWrap: 'wrap' }}
                      >
                        <XlbBasicForm.Item label="调出门店" name="out_store_id">
                          <XlbInputDialog
                            allowClear={false}
                            dialogParams={{
                              type: 'store',
                              dataType: 'lists',
                              isMultiple: false,
                              url: '/erp-mdm/hxl.erp.store.all.shortfind',
                              // showDialogByDisabled: getFieldValue('org_ids')?.length,
                              data: {
                                skip_filter: true,
                                centerFlag: true,
                                superiors:
                                  !centerStore.isCenter && !toOtherCenter
                                    ? centerStore.deliveryCenterId
                                    : null,
                                management_type:
                                  !unCenterStoreApplication &&
                                  managementType !== null &&
                                  !enableDeliveryCenter
                                    ? managementType === '0'
                                      ? '0'
                                      : '1'
                                    : null,
                                supplierSwitch: false,
                                status: true,
                                operator_store_id:
                                  LStorage.get('userInfo').store_id,
                              },
                            }}
                            disabled={
                              (fid !== 1 && info.state !== 'INIT') ||
                              (fid !== 1 &&
                                info.state === 'INIT' &&
                                rowData.length !== 0 &&
                                !rowData[0]?._empty) ||
                              (fid === 1 &&
                                rowData.length !== 0 &&
                                !rowData[0]?._empty) ||
                              !!wmsFid || formDataSource
                            }
                            fieldNames={{
                              idKey: 'id',
                              nameKey: 'store_name',
                            }}
                            handleValueChange={(_: string[], list: any[]) => {
                              const item = list?.[0] || {};
                              form.setFieldValue(
                                'out_org_name',
                                item?.org_name,
                              );
                              form.setFieldValue('out_org_id', item?.org_id);
                              setOutStore({
                                isCenter: item?.enable_delivery_center || false,
                              });
                              setEdit(true);
                            }}
                            width={180}
                          />
                        </XlbBasicForm.Item>
                        {enable_organization ? (
                          <XlbBasicForm.Item
                            label="调出组织"
                            name="out_org_name"
                          >
                            <XlbInput disabled style={{ width: '180px' }} />
                          </XlbBasicForm.Item>
                        ) : null}
                        <XlbBasicForm.Item label="调入门店" name="store_id">
                          <XlbInputDialog
                            allowClear={false}
                            dialogParams={{
                              type: 'store',
                              dataType: 'lists',
                              isMultiple: false,
                              // showDialogByDisabled: getFieldValue('org_ids')?.length,
                              // initCustomValue: '所有门店',
                              data: {
                                storeStatus: false,
                                centerFlag: true,
                                supplierSwitch: false,
                                status: true,
                                operator_store_id:
                                  LStorage.get('userInfo').store_id,
                              },
                            }}
                            disabled={
                              (fid !== 1 && info.state !== 'INIT') ||
                              (fid !== 1 &&
                                info.state === 'INIT' &&
                                rowData.length !== 0 &&
                                !rowData[0]?._empty) ||
                              (fid === 1 &&
                                rowData.length !== 0 &&
                                !rowData[0]?._empty) ||
                              !!wmsFid || formDataSource
                            }
                            fieldNames={{
                              idKey: 'id',
                              nameKey: 'store_name',
                            }}
                            handleValueChange={(_: string[], list: any[]) => {
                              const item = list[0] || {};
                              setCenterStore({
                                isCenter: item?.enable_delivery_center,
                                deliveryCenterId: item?.delivery_store?.id,
                              });
                              form.setFieldsValue({
                                org_id: item?.org_id,
                                org_name: item?.org_name,
                                // store_id: list[0].id,
                                // store_name: list[0].store_name,
                                out_org_id: '',
                                out_org_name: '',
                                // out_store_name: '',
                                out_store_id: '',
                              });
                              setEnableDeliveryCenter(
                                item?.enable_delivery_center,
                              );
                              setManagementType(item?.management_type);
                              getStockData(item?.id);
                              setEdit(true);
                            }}
                            width={180}
                          />
                        </XlbBasicForm.Item>
                        {enable_organization ? (
                          <XlbBasicForm.Item label="调入组织" name="org_name">
                            <XlbInput disabled style={{ width: '180px' }} />
                          </XlbBasicForm.Item>
                        ) : null}
                        <XlbBasicForm.Item
                          label="调入仓库"
                          name="storehouse_id"
                        >
                          <XlbSelect
                            style={{ width: 180 }}
                            disabled={
                              (fid !== 1 && info.state !== 'INIT') ||
                              (fid !== 1 &&
                                info.state === 'INIT' &&
                                rowData.length !== 0 &&
                                !rowData[0]?._empty &&
                                form.getFieldValue('delivery_out_order_fids')
                                  ?.length === 0) ||
                              (fid === 1 &&
                                rowData.length !== 0 &&
                                !rowData[0]?._empty &&
                                form.getFieldValue('delivery_out_order_fids')
                                  ?.length === 0) ||
                              !!wmsFid || formDataSource
                            }
                            onChange={(value) => {
                              setEdit(true);
                            }}
                          >
                            {stockList.map((v, i) => {
                              return (
                                <Option key={i} value={v.value}>
                                  {v.label}
                                </Option>
                              );
                            })}
                          </XlbSelect>
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="单据号" name="fid">
                          <XlbInput
                            size="small"
                            style={{ width: '180px' }}
                            disabled
                            // suffix={
                            //   <SearchOutlined style={{ color: '#F5F5F5' }} />
                            // }
                          />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item
                          label="商品部门"
                          name="item_dept_names"
                        >
                          <XlbInput
                            disabled
                            size="small"
                            // suffix={
                            //   <SearchOutlined style={{ color: '#F5F5F5' }} />
                            // }
                            style={{ width: '180px' }}
                          />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="调入日期" name="operate_date">
                          <XlbDatePicker
                            style={{ width: '180px' }}
                            disabled={
                              info.state !== 'INIT' ||
                              !hasAuth(['调入单', '编辑']) ||
                              !!wmsFid || formDataSource
                            }
                          />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="付款日期" name="payment_date">
                          <XlbDatePicker
                            style={{ width: '180px' }}
                            disabled={
                              info.state !== 'INIT' ||
                              !hasAuth(['调入单', '编辑']) ||
                              !!wmsFid || formDataSource
                            }
                            onChange={() => setEdit(true)}
                          />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item
                          label="调出单"
                          name="delivery_out_order_fids"
                        >
                          <XlbInput
                            style={{ width: '180px' }}
                            allowClear={fid === 1}
                            onFocus={(e) => e.target.blur()}
                            disabled={fid !== 1 || !!wmsFid || formDataSource}
                            suffix={
                              fid !== 1 ? null : (
                                <span
                                  style={{
                                    display: 'inline-block',
                                  }}
                                  onClick={() => {
                                    if (fid !== 1) return;
                                    billVisible();
                                  }}
                                >
                                  <XlbIcon
                                    name="sousuo"
                                    size={14}
                                    color="#c9cdd4"
                                  />
                                </span>
                              )
                            }
                            onClick={() => billVisible()}
                            onChange={allClear}
                          />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="留言备注" name="memo">
                          <XlbInput
                            style={{ width: '480px' }}
                            maxLength={200}
                            disabled={
                              info.state !== 'INIT' ||
                              !hasAuth(['调入单', '编辑']) ||
                              !!wmsFid || formDataSource
                            }
                            onChange={(e) => {
                              setEdit(true);
                            }}
                          />
                        </XlbBasicForm.Item>
                      </div>
                      <div>
                        {info?.state && (
                          <div
                            style={{
                              width: '150px',
                              flexBasis: '150px',
                              display: 'flex',
                              justifyContent: 'center',
                            }}
                          >
                            <img
                              src={orderStatusIcons[info?.state]}
                              width={86}
                              height={78}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  ),
                },
                {
                  label: '其他信息',
                  key: 'otherInfo',
                  children: (
                    <>
                      <div style={{ marginTop: 12 }} className="row-flex">
                        <XlbBasicForm.Item label="制单人" name="create_by">
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="制单时间" name="create_time">
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="审核人" name="audit_by">
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="审核时间" name="audit_time">
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                      </div>
                      <div className="row-flex">
                        <XlbBasicForm.Item label={'修改人'} name={'update_by'}>
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item
                          label={'修改时间'}
                          name={'update_time'}
                        >
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                        {Object.keys(WmsInfo)?.length ? (
                          <div className={styles.data_box}>
                            <span>总件数：</span>
                            {WmsInfo.total_quantity}
                            <span>件数：</span>
                            {WmsInfo.quantity}
                            <span>拆零件数：</span>
                            {WmsInfo.box_quantity}
                          </div>
                        ) : null}
                      </div>
                    </>
                  ),
                },
              ]}
            ></XlbTabs>
          </XlbBasicForm>
        </div>
      )}
      <div
        className={'button_box row-flex'}
        style={{ padding: !isFold ? 0 : '12px 0 0' }}
      >
        <XlbButton.Group>
          {hasAuth(['调入单', '编辑']) && (
            <XlbButton
              type="primary"
              label="批量添加"
              disabled={
                batchLoding ||
                !!(outStore.isCenter && centerStore.isCenter) ||
                (form.getFieldValue('delivery_out_order_fids') || [])?.length ||
                !!wmsFid
              }
              onClick={() => {
                if (formDataSource) {
                  XlbTipsModal({tips: 'wms产生单据不允许操作'})
                  return
                }
                handleBatchAdd();
              }}
              icon={<XlbIcon name="jia" />}
            />
          )}
          {hasAuth(['调入单', '导入']) && (
            <XlbButton
              type="primary"
              label="导入"
              disabled={
                batchLoding ||
                (form.getFieldValue('delivery_out_order_fids') || []).length ||
                !!wmsFid
              }
              onClick={() => {
                if (formDataSource) {
                  XlbTipsModal({ tips: 'wms产生单据不允许操作' });
                  return;
                }
                importItem();
              }}
              icon={<XlbIcon name="daoru" />}
            />
          )}
        </XlbButton.Group>
      </div>
      <div style={{ position: 'relative' }}>
        <XlbTabs
          defaultActiveKey={'detailTab'}
          onChange={(key) => setTabsKey(key)}
          onClick={() => {
            if (tabsKey === 'totalTab' && edit) {
              XlbMessage.warning('请保存商品明细表内容后查看商品汇总！');
            }
          }}
          items={[
            {
              label: '商品明细',
              key: 'detailTab',
            },
            {
              label: '商品汇总',
              key: 'totalTab',
              disabled: edit,
            },
          ]}
        />
        <div
          style={{ visibility: !edit ? 'hidden' : 'visible' }}
          className={styles.disbt}
          onClick={() =>
            XlbMessage.warning('请保存商品明细表内容后查看商品汇总！')
          }
        />
      </div>
      {tabsKey === 'detailTab' && (
        <XlbShortTable
          style={{ flex: 1 }}
          showSearch
          url={'/erp/hxl.erp.deliveryinorder.item.page'}
          disabled={
            formDataSource ||
            !shortTableEditable ||
            info.state !== 'INIT' ||
            !!wmsFid ||
            !hasAuth(['调入单', '编辑']) ||
            !!(outStore.isCenter && centerStore.isCenter)
          }
          disabledAdd={rowData.some((v) => v._empty) || outOrder}
          data={{
            store_id: form?.getFieldValue('store_id')?.toString(),
            company_id: LStorage.get('userInfo')?.company_id,
            operator_store_id: LStorage.get('userInfo').store_id,
            storehouse_id: form.getFieldValue('storehouse_id')?.toString(),
            out_store_id: form.getFieldValue('out_store_id')?.toString(),
          }}
          // tableKey={tableKey}
          isLoading={isLoading}
          placeholder="请输入关键字后按Enter进行搜索"
          onChangeData={(newDataSourcee, optionType) => {
            console.log(newDataSourcee, optionType, 'newDataSourcee');
            setChooseList([]);
            if (optionType === 'onAdd' || optionType === 'onChangeSorts') {
              setRowData(
                newDataSourcee.map((t) => ({ ...t, short_row_id: uuidv4() })),
              );
            }
            if (optionType === 'onDelete') {
              setEdit(true);
              if (newDataSourcee?.length !== 0) {
                setRowData(newDataSourcee);
                return;
              }
              setRowData([{ _empty: true, short_row_id: uuidv4() }]);
            }
          }}
          afterPopupSelect={(oldArr) => {
            addItemWithType(oldArr);
            return oldArr;
          }}
          isFold={isFold}
          // keepDataSource={keepData}
          selectMode="single"
          footerDataSource={[...footerData]}
          key={`${tableKey}${isFold}`}
          selectedRowKeys={chooseList}
          onSelectRow={(selectedRowKeys: any) => {
            setChooseList(selectedRowKeys);
          }}
          popoverPrimaryKey="id"
          // repeatKey="name"
          total={rowData?.length}
          dataSource={rowData}
          columns={itemArrdetail.map((v: any) => InvoiceRender(v))}
          primaryKey="short_row_id"
        />
      )}
      {tabsKey === 'totalTab' && (
        <XlbTable
          tableKey={tableKey}
          isLoading={isLoading}
          style={{ flex: 1 }}
          list={rowData}
          isFold={isFold}
          selectMode="single"
          key={`${tableKey}${isFold}`}
          footerDataSource={footerData}
          dataSource={summaryData}
          columns={itemArr.map((v: any) => InvoiceRender(v))}
          primaryKey="short_row_id"
        />
      )}

      <XlbModal
        title={'上传附件'}
        open={uploadFileModalVisible}
        centered
        onOk={() => {
          setFileList([...fileInfo]);
          setUploadFileModalVisible(false);
          setFileInfo([]);
        }}
        onCancel={() => setUploadFileModalVisible(false)}
      >
        <div style={{ padding: '12px' }}>
          <XlbUploadFile
            accept={'image'}
            fileList={fileInfo}
            onChange={(e = []) => {
              if (e) {
                setFileInfo([...e]);
              } else {
                setFileInfo([]);
              }
            }}
            listType={'picture'}
            maxCount={9}
            data={{ fid: form.getFieldValue('fid') }}
            action={`${process.env.ERP_URL}/erp/hxl.erp.deliveryinorder.file.upload`}
          ></XlbUploadFile>
          <span
            style={{
              fontSize: 14,
              color: ' rgb(134, 144, 156)',
              marginLeft: 8,
              wordBreak: 'break-all',
            }}
          >
            (支持上传PNG,JPG,JPEG,png,jpeg,webp,jpg,gif,bmp格式，最多9个)
          </span>
        </div>
      </XlbModal>
    </div>
  );
};

export default DeliveryInOrderItem;