import useDrag from '@/hooks/useDrag';
import { LStorage } from '@/utils/storage';
import { Button, Modal } from 'antd';
import './index.less';

const SignaturePrintingModal = (props: any) => {
  const { open, setPrintModal, handleCancel, closable, rowData } = props;
  console.log(rowData, 'rowDatarowDatarowDatarowDatarowData');
  useDrag('.ant-modal-header', '.ant-modal-content');
  const userInfo = LStorage.get('userInfo');
  const today = new Date();
  const day = today.getDate(); // 获取当前日期
  const month = today.getMonth() + 1; // 获取当前月份（注意：月份是从0开始计算的，所以要加1）
  const year = today.getFullYear(); // 获取当前年份

  const currentTime = year + '年' + month + '月' + day + '日';

  const printTable = () => {
    const newWindow: any = window.open();
    newWindow.document.body.innerHTML =
      document?.getElementById('printTable')?.innerHTML;
    const style = newWindow.document.createElement('style');
    style.type = 'text/css';
    // @media print {
    //   @page {
    //     size: 24.1in 9.31in;
    //     margin: 0;
    //   }
    // }
    style.innerHTML = `
      @media print {
          @page {
            size: 241mm 93mm landscape;
            margin: 0;
            margin-top: 20px;
            padding: 20px;
            overflow: hidden;
          }
          .print-image {
            border: none;
            text-align: center;
          }
          .page-break{
            break-before: page;
          }
          /* 隐藏页头 */
          #header {
            display: none !important;
          }

          /* 隐藏页脚 */
          #footer {
            display: none !important;
          }
        }
        body {
          width: 950px;
          height: 260px;
          margin: 0;
          padding: 20px;
          overflow: hidden;
        }
        .formInformation {
          display: flex;
          flex-wrap: wrap;
        }
        .titleInformation {
          width: 230px;
          font-size: 14px;
        }
        .tabularInformation {
          width: 100%;
          margin-top: 20px;
        }
        table {
          table-layout: fixed;
          width: 95%;
          border: 1px solid #000;
          border-collapse: collapse;
          font-size: 16px;
        }
        table td,
        table th {
          border: 1px solid #000;
          text-align: center;
        }
        table td {
          height: 40px;
        }
        .print-image {
          border: none;
          text-align: center;
        }
      `;
    newWindow.document.getElementsByTagName('head').item(0).appendChild(style);
    // newWindow.document.write(table)
    newWindow.print();
  };

  const calculateTotal = (list: any) => {
    const total = list.reduce(
      (acc: any, item: any) => acc + Number(item.quantity),
      0,
    );
    return total;
  };

  return (
    <Modal
      title={'调出单详情'}
      open={open}
      width={1200}
      wrapClassName="xlbDialog"
      centered
      maskClosable={false}
      closable={closable}
      style={{ maxHeight: 550, padding: '10px' }}
      onCancel={() => setPrintModal(false)}
      footer={[
        <Button
          key="2"
          type="default"
          onClick={() => {
            handleCancel();
          }}
        >
          {'取消'}
        </Button>,
        <Button key="3" type="primary" onClick={printTable}>
          打印
        </Button>,
      ]}
    >
      <div style={{ padding: '10px' }} className="printTable" id="printTable">
        <div>
          {rowData?.length > 0 &&
            rowData.map((item: any, index: number) => {
              const list = rowData[index].details;
              const totalQuantity = calculateTotal(list);
              return (
                <>
                  <h1
                    style={{
                      textAlign: 'center',
                      fontWeight: 'bold',
                      marginTop: '10px',
                    }}
                  >
                    调出单详情
                  </h1>
                  <div key={index}>
                    <div className="formInformation">
                      <div className="titleInformation">
                        调入门店：{item?.in_store_name}
                      </div>
                      <div className="titleInformation">
                        调出门店：{item?.store_name}
                      </div>
                      <div className="titleInformation">
                        调出仓库：{item?.storehouse_name}
                      </div>
                      <div className="titleInformation">
                        调出单号：{item?.fid}
                      </div>
                      <div className="titleInformation">
                        调出日期：{item?.operate_date}
                      </div>
                      <div className="titleInformation">
                        留言备注：{item?.memo}
                      </div>
                    </div>
                    <div
                      className={'tabularInformation'}
                      style={{ marginTop: '20px' }}
                    >
                      <table id="myTable">
                        <thead>
                          <tr>
                            <th style={{ width: '60px' }}>序号</th>
                            <th style={{ width: '180px' }}>商品名称</th>
                            <th style={{ width: '100px' }}>商品代码</th>
                            <th style={{ width: '100px' }}>商品条码</th>
                            <th style={{ width: '100px' }}>采购规格</th>
                            <th style={{ width: '60px' }}>单位</th>
                            <th style={{ width: '60px' }}>数量</th>
                          </tr>
                        </thead>
                        <tbody>
                          {rowData[index].details?.length > 0 &&
                            rowData[index].details.map(
                              (details: any, index: number) => {
                                const serialNumber = index + 1;
                                return (
                                  <tr key={details.item_name + index}>
                                    <td style={{ width: '60px' }}>
                                      {serialNumber}
                                    </td>
                                    <td>{details.item_name}</td>
                                    <td>{details.item_code}</td>
                                    <td>{details.item_bar_code}</td>
                                    <td>{details.item_spec}</td>
                                    <td style={{ width: '60px' }}>
                                      {details.unit}
                                    </td>
                                    <td style={{ width: '60px' }}>
                                      {details.quantity}
                                    </td>
                                  </tr>
                                );
                              },
                            )}

                          <tr>
                            <th style={{ width: '60px' }}>合计</th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th style={{ width: '60px' }}></th>
                            <th style={{ width: '60px' }}>{totalQuantity}</th>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div
                      className="bottomInformation"
                      style={{
                        marginTop: '10px',
                        marginBottom: '10px',
                        width: '95%',
                        height: '20px',
                      }}
                    >
                      <div
                        style={{
                          textAlign: 'left',
                          float: 'left',
                          width: '55%',
                        }}
                      >
                        打印人：{userInfo?.name}
                      </div>
                      <div
                        style={{
                          textAlign: 'right',
                          float: 'right',
                          width: '45%',
                        }}
                      >
                        打印时间：{currentTime}
                      </div>
                    </div>
                  </div>
                  <div className="page-break"></div>
                </>
              );
            })}
        </div>
      </div>
    </Modal>
  );
};

export default SignaturePrintingModal;
