import { useBaseParams } from '@/hooks/useBaseParams';
import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import type { XlbTableColumnProps } from '@xlb/components';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbBlueBar,
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbMessage,
  XlbSelect,
  XlbTable,
  XlbTimePicker,
  XlbTipsModal,
} from '@xlb/components';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { cycleDay, itemTableList, ruleOptions } from './data';
// import Api from './server';
import { exportPage } from '@/services/system';
import { Col, ConfigProvider, message, Row } from 'antd';
import styles from './index.less';
import Api from './server';
type InfoProps = {
  onBack: any;
  record: any;
};

const Item = (props: InfoProps) => {
  const { onBack, record } = props;
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [chooseList, setChooseList] = useState<any[]>([]);
  const [rowData, setRowData] = useState<any[]>([]);

  //表单
  const [form] = XlbBasicForm.useForm();
  const [preId, setId] = useState(-1);

  // 列定制
  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(itemTableList)),
  );

  const [hidden, setHidden] = useState(false);

  const { enable_organization } = useBaseParams((state) => state);

  //读取
  const readInfo = async (id: any) => {
    const res = await Api.readItem({ id: id });
    if (res.code === 0) {
      setRowData(res.data.stores);
      setHidden(res.data.rule === 'NUMBER');
      form.setFieldsValue({
        ...res.data,
        time: res.data.time ? moment(res.data.time, 'HH:mm') : undefined,
        current_type: res.data.current_type
          ? res.data.current_type === 'ODD'
            ? '奇数'
            : '偶数'
          : null,
      });
    }
  };

  useEffect(() => {
    itemArr.find((i) => i.code === 'org_name')!.hidden = !enable_organization;
    setItemArr([...itemArr]);
  }, [enable_organization]);

  useEffect(() => {
    setId(record.id);
    form.setFieldsValue({
      time: moment('12:00', 'HH:mm'),
    });
    if (record.id > 0) {
      readInfo(record.id);
    }
  }, []);
  useEffect(() => {
    form.setFieldsValue({
      cycle_day: form.getFieldValue('rule') === 'CYCLE' && 'D+1',
      init_type: form.getFieldValue('rule') !== 'CYCLE' && 'ODD',
    });
  }, [form.getFieldValue('rule')]);

  // 保存
  const saveStore = async () => {
    const validateRes = await form?.validateFields();
    if (!validateRes) {
      return;
    }
    if (!rowData?.length) {
      message.error('请添加门店');
      return;
    }
    const data = {
      // ...form.getFieldsValue(true),
      name: form.getFieldValue('name'),
      time: form.getFieldValue('time')
        ? form.getFieldValue('time').format('HH:mm')
        : '',
      store_ids: rowData?.map((item: any) => item.id),
      rule: form.getFieldValue('rule'),
      cycle_day:
        form.getFieldValue('rule') === 'CYCLE'
          ? form.getFieldValue('cycle_day')
          : '',
      init_type:
        form.getFieldValue('rule') === 'CYCLE'
          ? undefined
          : form.getFieldValue('init_type'),
      current_type:
        form.getFieldValue('rule') === 'CYCLE'
          ? undefined
          : form.getFieldValue('current_type')
            ? form.getFieldValue('current_type') === '奇数'
              ? 'ODD'
              : 'EVEN'
            : '',
    };

    setisLoading(true);
    const res =
      preId == -1
        ? await Api.addItem(data)
        : await Api.updateItem({ ...data, id: preId });
    setisLoading(false);

    if (res.code === 0) {
      message.success('保存成功');
      onBack();
      // back('/xlb_erp/storeDeliveryDay/index')
    }
  };

  //添加
  const addGoods = async () => {
    const bool = await XlbBasicData({
      isMultiple: true,
      dataType: 'lists',
      type: 'store',
      idsKey: 'ids',
      // idsKey: 'id',
      selectedList: rowData,
      data: {
        center_flag: false,
        status: true,
      },
    });
    if (bool) {
      const List = bool.map(
        (v: any) =>
          (v = {
            ...v,
            store_group_name: v?.store_group?.name,
            store_group_id: v?.store_group?.id,
          }),
      );
      setRowData(List);
    }
  };

  //删除
  const deleteGoods = async () => {
    const code = await XlbTipsModal({
      isConfirm: true,
      isCancel: true,
      tips: `是否删除选中的${chooseList.length}个门店?`,
    });
    if (code) {
      chooseList.map((item: any) => {
        // 如果是删除有id的，item是number类型， 如果是刚新增的，item是字符串类型的下标
        const i =
          typeof item === 'number'
            ? rowData.findIndex((e) => e.id === item)
            : Number(item);
        rowData.splice(i, 1);
      });
      setChooseList([]);
    }
  };

  //导入
  const imports = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.storedeliveryday.store.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.storecodetemplate.download`,
      templateName: '下载模版',
      callback: (res) => {
        if (res?.data?.state) {
          let repeatArr: any[] = [];
          setRowData((prev: any) => {
            const code = prev.map((v: any) => v.store_code);
            repeatArr = res?.data?.stores?.filter((v: any) =>
              code.includes(v.store_code),
            );
            const newArr = res?.data?.stores?.filter(
              (v: any) => !code.includes(v.store_code),
            );
            return [...prev, ...newArr];
          });
          if (repeatArr.length) {
            XlbTipsModal({
              isConfirm: true,
              isCancel: false,
              tips: '以下门店已存在，系统已自动过滤！',
              tipsList: [
                repeatArr.map((v: any) => `【${v.store_name}】`).join('、'),
              ],
            });
          }
          res?.data?.success_num > repeatArr?.length &&
            XlbMessage.success(
              '导入成功,共导入' +
                (res?.data?.success_num - repeatArr?.length) +
                '条数据',
            );
        }
      },
    });
  };
  //导出
  const exportItem = async () => {
    const data = {
      id: record?.id,
    };
    setisLoading(true);

    const res = await exportPage(
      '/erp/hxl.erp.storedeliveryday.store.export',
      data,
      { responseType: 'blob' },
    );
    setisLoading(false);
    const download = new Download();
    download.filename = '门店配送日门店导出.xlsx';
    download.xlsx(res?.data);
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          screenXLMin: 1280,
          screenXL: 1280,
        },
      }}
    >
      <div
        style={{
          padding: 12,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
        }}
        className={styles.form_container_wrapper}
      >
        <XlbButton.Group>
          {hasAuth(['门店配送日', '编辑']) && (
            <XlbButton
              label="保存"
              type="primary"
              loading={isLoading}
              onClick={saveStore}
              icon={<XlbIcon name="baocun" />}
            />
          )}
          <XlbButton
            label="返回"
            type="primary"
            onClick={onBack}
            icon={<XlbIcon name="fanhui" />}
          />
        </XlbButton.Group>
        <XlbBlueBar title={'配送日详情'} hasMargin={true} />
        <div className={styles.form_container_storeDeliveryDay}>
          <XlbBasicForm
            autoComplete="off"
            layout="horizontal"
            colon={true}
            form={form}
            onValuesChange={(e: Object) => {
              const changeKey = Object.keys(e)[0];
              if (changeKey === 'rule') {
                form.getFieldValue('rule') === 'CYCLE'
                  ? setHidden(false)
                  : setHidden(true);
              }
            }}
          >
            <Row gutter={12} wrap>
              <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                <XlbBasicForm.Item
                  label="配送日名称"
                  name="name"
                  rules={[{ required: true, message: '请输入配送日名称' }]}
                >
                  <XlbInput style={{ width: '100%' }} allowClear />
                </XlbBasicForm.Item>
              </Col>
              <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                <XlbBasicForm.Item
                  label="配送日规则"
                  name="rule"
                  initialValue={'CYCLE'}
                >
                  <XlbSelect
                    style={{ width: '100%' }}
                    options={ruleOptions}
                    allowClear={false}
                    // onChange={handleChangeRule}
                  />
                </XlbBasicForm.Item>
              </Col>
              <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                <XlbBasicForm.Item label="配送日时间" name="time">
                  <XlbTimePicker
                    style={{ width: '100%' }}
                    format={'HH:mm'}
                    allowClear={false}
                  />
                </XlbBasicForm.Item>
              </Col>
              {!hidden && (
                <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                  <XlbBasicForm.Item label="周期天数" name="cycle_day">
                    <XlbSelect
                      style={{ width: '100%' }}
                      options={cycleDay}
                      allowClear
                    />
                  </XlbBasicForm.Item>
                </Col>
              )}

              <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                {hidden && (
                  <XlbBasicForm.Item label="初始配送日" name="init_type">
                    <XlbSelect
                      style={{ width: '100%' }}
                      allowClear={false}
                      options={[
                        { value: 'ODD', label: '奇数' },
                        { value: 'EVEN', label: '偶数' },
                      ]}
                      // allowClear
                    />
                  </XlbBasicForm.Item>
                )}
              </Col>
              <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                {hidden && (
                  <XlbBasicForm.Item label="当前配送日" name="current_type">
                    <XlbInput style={{ width: '100%' }} readOnly />
                  </XlbBasicForm.Item>
                )}
              </Col>
            </Row>
          </XlbBasicForm>
        </div>
        <XlbBlueBar title={'门店详情'} hasMargin={true} />
        <XlbButton.Group>
          {hasAuth(['门店配送日', '编辑']) ? (
            <XlbButton
              type="primary"
              label="添加"
              onClick={addGoods}
              icon={<XlbIcon name="jia" />}
            />
          ) : null}
          {hasAuth(['门店配送日', '编辑']) ? (
            <XlbButton
              label="删除"
              onClick={deleteGoods}
              type="primary"
              disabled={!chooseList.length}
              icon={<XlbIcon name="shanchu" />}
            />
          ) : null}
          {hasAuth(['门店配送日', '导入']) ? (
            <XlbButton
              label="导入"
              type="primary"
              // disabled={!(preId > 0)}
              onClick={imports}
              icon={<XlbIcon name="daoru" />}
            />
          ) : null}
          {hasAuth(['门店配送日', '导出']) ? (
            <XlbButton
              label="导出"
              type="primary"
              disabled={!rowData.length || preId === -1}
              onClick={exportItem}
              icon={<XlbIcon name="daochu" />}
            />
          ) : null}
        </XlbButton.Group>

        <XlbTable
          isLoading={isLoading}
          primaryKey="id"
          // key={rowData?.length}
          columns={itemArr}
          style={{ flex: 1, marginTop: 5 }}
          dataSource={rowData}
          selectMode="multiple"
          total={rowData.length}
          showSearch
          // pageSize={pageSize}
          useVirtual={true}
          selectedRowKeys={chooseList}
          onSelectRow={(selectedRowKeys: any) => setChooseList(selectedRowKeys)}
          // onPaginChange={(page, pageSize) => setPageSize(pageSize)}
        />
      </div>
    </ConfigProvider>
  );
};

export default Item;