import { Image, Space, Spin } from 'antd';
import { useEffect, useState } from 'react';
import {
  addPrepareType,
  editPrepareType,
  getPrepareType,
  getRangePsd,
} from '../server';
// import { useKeepAliveRefresh } from '@/hooks'
import banner from '@/assets/images/workbenchBanner.png';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { useIRouter } from '@/wujie/utils';
import {
  XlbBaseUpload,
  XlbBasicForm,
  XlbBlueBar,
  XlbButton,
  XlbCheckbox,
  XlbDatePicker,
  XlbIcon,
  XlbInput,
  XlbInputNumber,
  XlbMessage,
  XlbRadio,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { useNavigation } from '@xlb/max';
import { XlbFetch } from '@xlb/utils';
import dayjs from 'dayjs';
import styles from './index.less';
const Item = (props: any) => {
  const { onBack, record } = props;
  const { enable_organization } = useBaseParams((state) => state);
  const [form] = XlbBasicForm.useForm();
  const { navigate } = useIRouter();
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [isCheck, setIsCheck] = useState<boolean>(false);
  const [bType, setBType] = useState<string>(''); //备货法
  const [orgList, setOrgList] = useState<any>([]);
  const [psdValue, setPsdValue] = useState<any>(null);
  const [spinLoding, setSpinLoading] = useState<boolean>(false);
  const [factoryGateFiles, setFactoryGateFiles] = useState<any>({}); // 门头照片
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);
  const saveOrder = async () => {
    try {
      await form.validateFields();
    } catch (err: any) {
      throw err;
    }
    const data = form.getFieldsValue(true);
    if (!data?.prepare_type_files) {
      XlbTipsModal('请上传banner图片');
      return;
    }
    const params = {
      // enable: isCheck,
      ...data,
      query_start_date: dayjs(form.getFieldValue('query_time')[0])?.format(
        'YYYY-MM-DD',
      ),
      query_end_date: dayjs(form.getFieldValue('query_time')[1])?.format(
        'YYYY-MM-DD',
      ),
      sale_start_date: dayjs(form.getFieldValue('sale_time')[0])?.format(
        'YYYY-MM-DD',
      ),
      sale_end_date: dayjs(form.getFieldValue('sale_time')[1])?.format(
        'YYYY-MM-DD',
      ),
      order_valid_date: form.getFieldValue('order_valid_date')
        ? dayjs(form.getFieldValue('order_valid_date'))?.format('YYYY-MM-DD')
        : '',
      start_date: dayjs(form.getFieldValue('prepare_time')[0])?.format(
        'YYYY-MM-DD',
      ),
      end_date: dayjs(form.getFieldValue('prepare_time')[1])?.format(
        'YYYY-MM-DD',
      ),
      prepare_type_files: factoryGateFiles,
    };
    setisLoading(true);
    const res = data.id
      ? await editPrepareType({
          id: data?.id,
          ...params,
        })
      : await addPrepareType(params);
    setisLoading(false);
    if (res?.code === 0) {
      XlbMessage.success('保存成功');
      onBack?.(true);
    }
  };

  const getData = async (name: string) => {
    const res = await getPrepareType({
      keyword: name,
    });
    if (res.code === 0 && res.data.length >= 1) {
      // 从res.data里面匹配元素name = name的元素
      const matchedElement =
        res.data.find((item: any) => item.name === name) || {};
      if (!matchedElement) return;
      form.setFieldsValue({
        ...matchedElement,
        query_time: [
          dayjs(matchedElement?.query_start_time),
          dayjs(matchedElement?.query_end_time),
        ],
        prepare_time: [
          dayjs(matchedElement?.start_time),
          dayjs(matchedElement?.end_time),
        ],
        sale_time: [
          dayjs(matchedElement?.sale_start_time),
          dayjs(matchedElement?.sale_end_time),
        ],
        order_valid_date: matchedElement?.order_valid_date
          ? dayjs(matchedElement?.order_valid_date)
          : '',
      });
      setFactoryGateFiles(res?.data?.[0]?.prepare_type_files);
      setBType(res?.data?.[0]?.solution);
      // setIsCheck(matchedElement.enable);
      form.setFieldValue('enable', matchedElement.enable);
      if (
        matchedElement?.query_start_time &&
        matchedElement?.query_end_time &&
        matchedElement?.org_id
      )
        getPsd();
    }
  };

  const getPsd = async (
    date = [
      form.getFieldValue('query_time')[0],
      form.getFieldValue('query_time')[1],
    ],
    orgId = form.getFieldValue('org_id'),
  ) => {
    setSpinLoading(true);
    const res = await getRangePsd({
      start_date: dayjs(date[0])?.format('YYYYMMDD'),
      end_date: dayjs(date[1])?.format('YYYYMMDD'),
      org_level_two_id: orgId,
    });

    if (res.code === 0) {
      setSpinLoading(false);
      setPsdValue(res?.data?.psd);
    }
  };

  const getOrgList = async () => {
    if (enable_organization) {
      const res = await XlbFetch.post('/erp/hxl.erp.org.find', {
        data: { level: 2 },
      });
      if (res.code === 0) {
        const org_list = res.data?.map((i: any) => ({
          level: i.level,
          value: i.id,
          label: i.name,
        }));
        setOrgList(org_list);
      }
    }
  };
  useEffect(() => {
    getOrgList();
  }, [enable_organization]);

  useEffect(() => {
    // const record = history.location.state as any;
    if (record.id > 0) {
      // 用record传过来的数据赋值的话日期渲染会有问题
      getData(record.name).then();
    }
  }, []);

  return (
    <div
      style={{
        padding: 10,
        height: 'calc(100vh - 110px)',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <XlbButton.Group>
        {hasAuth(['备货类型设置', '编辑']) ? (
          <XlbButton
            key={'保存'}
            loading={isLoading}
            type={'primary'}
            label={'保存'}
            icon={<XlbIcon name={'baocun'} />}
            onClick={saveOrder}
          />
        ) : null}
        <XlbButton
          key={'返回'}
          type={'primary'}
          label={'返回'}
          icon={<XlbIcon name={'fanhui'} />}
          onClick={() => onBack?.(false)}
        />
      </XlbButton.Group>
      <XlbBlueBar title={'基本信息'} hasMargin />
      <div className={styles.form_container}>
        <XlbBasicForm
          colon
          form={form}
          autoComplete="off"
          labelCol={{ style: { width: 160 } }}
          className={'baseInfoForm'}
          layout="horizontal"
          initialValues={{
            enable: true,
          }}
        >
          <XlbBasicForm.Item
            label="备货类型名称"
            name="name"
            rules={[{ required: true, message: '备货类型名称不能为空' }]}
          >
            <XlbInput
              style={{ width: 260 }}
              size="small"
              maxLength={20}
              placeholder="请输入"
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item label="销量取值范围" required>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <XlbBasicForm.Item
                name="query_time"
                noStyle
                rules={[{ required: true, message: '销量取值范围不能为空' }]}
              >
                <XlbDatePicker.RangePicker
                  style={{ width: 260 }}
                  format={'YYYY-MM-DD'}
                  disabledDate={(current) => {
                    return current && current >= dayjs().startOf('day');
                  }}
                  onChange={(e: any) => {
                    if (e && e?.length === 2)
                      getPsd([
                        dayjs(e[0]).format('YYYY-MM-DD'),
                        dayjs(e[1]).format('YYYY-MM-DD'),
                      ]);
                  }}
                />
              </XlbBasicForm.Item>
              <div style={{ marginLeft: 10 }}>
                <span>平均PSD： {spinLoding ? <Spin /> : psdValue}元</span>
              </div>
            </div>
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="备货日期"
            name="prepare_time"
            rules={[{ required: true, message: '备货日期不能为空' }]}
          >
            <XlbDatePicker.RangePicker
              style={{ width: 260 }}
              format={'YYYY-MM-DD'}
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="销售日期"
            name="sale_time"
            rules={[{ required: true, message: '销售日期不能为空' }]}
          >
            <XlbDatePicker.RangePicker
              style={{ width: 260 }}
              format={'YYYY-MM-DD'}
              disabledDate={(current) => {
                // 获取今天的开始时间
                const today = dayjs().startOf('day');
                // 计算三个月后的日期
                const threeMonthsLater = dayjs().add(3, 'months').endOf('day');
                // 禁用今天之前的日期和三个月之后的日期
                return (
                  current && (current < today || current > threeMonthsLater)
                );
              }}
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item label="备注" name="memo">
            <XlbInput
              style={{ width: 260 }}
              size="small"
              maxLength={20}
              placeholder="请输入"
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="启用"
            name="enable"
            valuePropName="checked" // 绑定 checked 而非 value
          >
            <XlbCheckbox />
          </XlbBasicForm.Item>
          {/*<XlbBasicForm.Item label="启用" name="enable">*/}
          {/*  <XlbCheckbox*/}
          {/*    checked={isCheck}*/}
          {/*    onChange={(e) => {*/}
          {/*      setIsCheck(e.target.checked);*/}
          {/*      form.setFieldValue('enable', e.target.checked);*/}
          {/*    }}*/}
          {/*  />*/}
          {/*</XlbBasicForm.Item>*/}
          <XlbBasicForm.Item
            label="组织"
            name="org_id"
            rules={[{ required: true, message: '组织不能为空' }]}
          >
            <XlbSelect
              style={{ width: 260 }}
              options={orgList}
              allowClear
              placeholder="请选择"
              onChange={(e) => {
                getPsd(undefined, e);
              }}
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="订单有效期"
            name="order_valid_date"
            rules={[{ required: true, message: '订单有效期不能为空' }]}
          >
            <XlbDatePicker style={{ width: 260 }} format={'YYYY-MM-DD'} />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="备货法"
            name="solution"
            rules={[{ required: true, message: '备货法不能为空' }]}
          >
            <XlbRadio.Group
              value={bType}
              onChange={(e) => {
                console.log('eee:', e);
                setBType(e.target.value);
                form.setFieldsValue({
                  solution: e.target.value,
                  multiple_factor: null,
                });
              }}
            >
              <Space direction="horizontal">
                <XlbRadio value={'MULTIPLE'}>倍数备货法</XlbRadio>
                <XlbRadio value={'ESTIMATED'}>预估备货法</XlbRadio>
              </Space>
            </XlbRadio.Group>
          </XlbBasicForm.Item>
          {bType === 'MULTIPLE' && (
            <div className={styles.description}>
              <div>
                倍数备货法：根据“销量取值范围”的平均PSD，设置一个倍数系统，
              </div>
              <div>得出门店建议备货量，预估到门店维度</div>
            </div>
          )}
          {bType === 'ESTIMATED' && (
            <div className={styles.description}>
              <div>
                预估备货法：根据“销量取值范围”的平均PSD，BI系统预测备货预估金额，
              </div>
              <div>预估到一二级分类及店铺维度，指导门店下备货单</div>
            </div>
          )}
          {bType === 'MULTIPLE' && (
            <XlbBasicForm.Item
              label="倍数系数"
              name="multiple_factor"
              rules={[
                { required: true, message: '备货系数不能为空' },
                {
                  pattern: /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/,
                  message: '倍数系数只能输入数字',
                },
              ]}
            >
              <XlbInputNumber
                style={{ width: 260 }}
                size="small"
                maxLength={20}
                max={999.999}
                min={0.001}
              />
            </XlbBasicForm.Item>
          )}
          <XlbBasicForm.Item
            label="备货订货上限限制逻辑"
            name="item_prepare_control_policy"
            rules={[
              { required: true, message: '备货订货上限限制逻辑不能为空' },
            ]}
            initialValue={'ONCE_ALLOW'}
          >
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <XlbBasicForm.Item name="item_prepare_control_policy" noStyle>
                <XlbRadio.Group
                  onChange={(e) => {
                    form.setFieldsValue({
                      item_prepare_control_policy: e.target.value,
                    });
                  }}
                >
                  <Space direction="horizontal">
                    <XlbRadio value={'ONCE_ALLOW'}>允许一次性下完</XlbRadio>
                    <XlbRadio value={'DAILY_AVERAGE'}>
                      限制每一天下单数量
                    </XlbRadio>
                  </Space>
                </XlbRadio.Group>
              </XlbBasicForm.Item>
              <div style={{ marginLeft: 10 }}>
                <span
                  className={'link'}
                  onClick={() => {
                    // go('/xlb_erp/goodsOrder/index', {
                    //   tab: 'store_order_num'
                    // })
                    const { navigate } = useNavigation();
                    navigate(
                      '/xlb_erp/goodsOrder/index',
                      {
                        tab: 'store_order_num',
                      },
                      'xlb_erp',
                      true,
                    );
                  }}
                >
                  去设置备货订货上限
                </span>
              </div>
            </div>
          </XlbBasicForm.Item>
          <div className={styles.description}>
            <div>
              允许一次性下完：A门店a商品设置50个上限，不限制每天可以下单的个数，
            </div>
            <div>累计不超过50个的任意数量</div>
            <div>
              限制每一天下单数量：A门店a商品设置50个上限，限制每天可以下单的个数，总数不可以超过50
            </div>
          </div>
          <XlbBasicForm.Item
            rules={[{ required: true, message: '请添加banner图片' }]}
            name="prepare_type_files"
            label="banner图片"
          >
            <div className={styles.imgTextList}>
              <XlbBaseUpload
                accept={'image'}
                action="/erp/hxl.erp.file.upload"
                showIndex={false}
                fileList={factoryGateFiles}
                maxCount={1}
                deleteByServer={true}
                listType="picture"
                data={{
                  refType: 'PREPARE_TYPE_BANNER',
                  id: form.getFieldValue('id')
                    ? form.getFieldValue('id')
                    : void 0,
                  fid: form.getFieldValue('id')
                    ? form.getFieldValue('id')
                    : void 0,
                  refId: form.getFieldValue('id')
                    ? form.getFieldValue('id')
                    : 'TEMP',
                }} // 传参
                onChange={(e: any) => {
                  setFactoryGateFiles(e);
                }}
              />
            </div>
          </XlbBasicForm.Item>
          <div style={{ marginBottom: 20 }} className={styles.description}>
            <div>尺寸：702*120px</div>
            <div>
              该图片会显示在新零帮APP-工作台上方，
              <span
                className="link cursors"
                onClick={() => setPreviewVisible(true)}
              >
                查看示例图
              </span>
              。建议请公司设计师输出高质量的图片
            </div>
          </div>
          <Image
            style={{ display: 'none' }}
            preview={{
              visible: previewVisible,
              onVisibleChange: (visible) => setPreviewVisible(visible),
              maskClosable: true,
            }}
            width={200}
            src={banner}
          />
        </XlbBasicForm>
      </div>
    </div>
  );
};

export default Item;