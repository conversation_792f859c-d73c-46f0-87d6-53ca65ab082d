import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { formatWithCommas } from '@/utils/kit';
import { wujieBus } from '@/wujie/utils';
import { SearchOutlined, UploadOutlined } from '@ant-design/icons';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbColumns,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbTable,
  XlbTableColumnProps,
  XlbTooltip,
} from '@xlb/components';
import { XlbFetch as ErpRequest, LStorage } from '@xlb/utils';
import { Tabs } from 'antd';
import classnames from 'classnames';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import { useEffect, useState } from 'react';
import DeliveryOrderItem from './components/deliveryOrder';
import {
  djTotalArr,
  formList,
  goodsTotalArr,
  storeTotalArr,
  storegoodsTotalArr,
} from './data';
import {
  getdjtotal,
  getgoodsway,
  getstoregoodstotal,
  getstoretotal,
} from './server';
const Goodsway = () => {
  const { TabPane } = Tabs;
  const [isFold, setIsFold] = useState<boolean>(true);
  const [tabList, setTabList] = useState<XlbTableColumnProps<any>[]>(
    cloneDeep(goodsTotalArr),
  );
  const { enable_cargo_owner } = useBaseParams((state) => state);
  const [isTabksKeyChange, setIsTabksKeyChange] = useState<boolean>(false);
  const { enable_organization } = useBaseParams((state) => state);
  const [form] = XlbBasicForm.useForm();
  const [tabKey, setTabKey] = useState('sphz');
  const [rowData, setRowData] = useState<any[]>([]);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [sortType, setSortType] = useState<{ order: string; code: string }>({
    order: '',
    code: '',
  });
  const [useGoodsTotalRowData, setUseGoodsTotalRowData] = useState<[]>([]);
  const [useStoreTotalArrRowData, setUseStoreTotalArrRowData] = useState<[]>(
    [],
  );
  const [useStoregoodsTotalRowData, setUseStoregoodsTotalRowData] = useState<
    []
  >([]);
  const [useDjTotalRowData, setUseDjTotalRowData] = useState<[]>([]);

  const [useGoodsTotalFooterData, setUseGoodsTotalFooterData] = useState<[]>(
    [],
  );
  const [useStoreTotalFooterData, setUseStoreTotalFooterData] = useState<[]>(
    [],
  );
  const [useStoregoodsTotalFooterData, setUseStoregoodsTotalFooterData] =
    useState<[]>([]);
  const [useDjTotalFooterData, setUseDjTotalFooterData] = useState<[]>([]);
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useGoodsTotalPagin, setUseGoodsTotalPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useStoreTotalPagin, setUseStoreTotalPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useStoregoodsTotalPagin, setUseStoregoodsTotalPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [useDjTotalPagin, setUseDjTotalPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const { compactDatePicker } = form.getFieldsValue(true);
  const [isLoading, setisLoading] = useState<boolean>(false);

  const fIdClick = (data: any) => {
    NiceModal.show(NiceModal.create(DeliveryOrderItem), data);
  };
  const [formLists, setSearchFormLists] = useState(cloneDeep(formList));
  //表格数据
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'out_money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {hasAuth(['商品在途/配送价', '查询'])
                ? formatWithCommas(parseFloat(value)?.toFixed(2))
                : '****'}
            </div>
          );
        };
        break;

      case 'in_money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {hasAuth(['商品在途/配送价', '查询'])
                ? formatWithCommas(parseFloat(value)?.toFixed(2))
                : '****'}
            </div>
          );
        };
        break;

      case 'delivery_out_fid':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div
              className="link overwidth"
              onClick={() => fIdClick({ orderId: value })}
            >
              {value}
            </div>
          );
        };
        break;
      case 'on_way_money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {hasAuth(['商品在途/配送价', '查询'])
                ? formatWithCommas(parseFloat(value)?.toFixed(2))
                : '****'}
            </div>
          );
        };
        break;
    }
  };
  //   const getOrgList = async () => {
  //     if (enable_organization) {
  //       const res = await ErpRequest.post('/erp/hxl.erp.org.find', {});
  //       if (res.code == 0) {
  //         const org_list = res.data.map((i: any) => ({
  //           value: i.id,
  //           label: i.name,
  //         }));
  //         formLists.find((i: any) => i.name === 'out_org_ids')!.options =
  //           org_list;
  //         formLists.find((i: any) => i.name === 'in_org_ids')!.options = org_list;
  //       }
  //     }
  //     setSearchFormLists([...formLists]);
  //   };
  //   useEffect(() => {
  //     getOrgList();
  //   }, []);
  const onValuesChange = (e: any) => {
    if (e?.out_org_ids) {
      if (e?.out_org_ids?.length) {
        form.setFieldsValue({
          out_store_ids: [],
        });
      }
    }
    if (e?.in_org_ids) {
      if (e?.in_org_ids?.length) {
        form.setFieldsValue({
          in_store_ids: [],
        });
      }
    }
  };
  const changeKey = (key: string) => {
    let tableArr: any = [];
    setIsTabksKeyChange(true);
    switch (key) {
      case 'sphz':
        tableArr = goodsTotalArr;
        setRowData([...useGoodsTotalRowData]);
        setFooterData([...useGoodsTotalFooterData]);
        setPagin(useGoodsTotalPagin);
        break;
      case 'mdhz':
        tableArr = storeTotalArr.filter((v) => {
          if (!enable_cargo_owner) {
            return v.code !== 'out_org_name';
          }
          return v;
        });
        setRowData([...useStoreTotalArrRowData]);
        setFooterData([...useStoreTotalFooterData]);
        setPagin(useStoreTotalPagin);
        break;
      case 'mdsphz':
        tableArr = storegoodsTotalArr.filter((v) => {
          if (!enable_cargo_owner) {
            return v.code !== 'out_org_name';
          }
          return v;
        });
        setRowData([...useStoregoodsTotalRowData]);
        setFooterData([...useStoregoodsTotalFooterData]);
        setPagin(useStoregoodsTotalPagin);
        break;
      case 'djhz':
        tableArr = djTotalArr.filter((v) => {
          if (!enable_cargo_owner) {
            return v.code !== 'out_org_name';
          }
          return v;
        });
        setRowData([...useDjTotalRowData]);
        setFooterData([...useDjTotalFooterData]);
        setPagin(useDjTotalPagin);
        break;
    }
    if (key == 'djhz') {
      formLists.find((v) => v.label === '供应商')!.hidden = true;
    } else {
      formLists.find((v) => v.label === '供应商')!.hidden = false;
    }
    setSearchFormLists([...formLists]);
    setTabList([...tableArr]);
  };
  const checkData = (page_number: number = 1, page_size: number = 200) => {
    const data = {
      page_number: page_number - 1,
      page_size: page_size,
      ...form.getFieldsValue(),
      audit_date: [
        form.getFieldValue('compactDatePicker')?.[0] + ' 00:00:00',
        form.getFieldValue('compactDatePicker')?.[1] + ' 23:59:59',
      ],
      out_store_ids: form.getFieldValue('out_store_ids')
        ? form.getFieldValue('out_store_ids')
        : [],
      in_store_ids: form.getFieldValue('in_store_ids')
        ? form.getFieldValue('in_store_ids')
        : [],
      item_ids: form.getFieldValue('item_ids')
        ? form.getFieldValue('item_ids')
        : [],
      item_category_ids: form.getFieldValue('item_category_ids')
        ? form.getFieldValue('item_category_ids')
        : [],
      supplier_ids: form.getFieldValue('supplier_ids')
        ? form.getFieldValue('supplier_ids')
        : [],
      delivery_out_fid: form.getFieldValue('delivery_out_fid'),
      out_org_ids: form.getFieldValue('out_org_ids'),
      in_org_ids: form.getFieldValue('in_org_ids'),
      query_item_on_way_not_equal_zero: form
        .getFieldValue('checkValue')
        ?.includes('query_item_on_way_not_equal_zero')
        ? true
        : '',
    };
    data.orders =
      sortType.code && !isTabksKeyChange
        ? [
            {
              direction: sortType.order.toUpperCase(),
              property:
                sortType.code === 'supplier_name'
                  ? 'supplier_id'
                  : sortType.code,
            },
          ]
        : null;
    return data;
  };
  const handleUid = () => {
    let uid: any = {
      key: '',
      name: '',
    };
    switch (tabKey) {
      case 'sphz':
        uid.key =
          'hxl.erp.deliveryreport.itemonway.itemsummary.sphz.page-columns';
        uid.name =
          'hxl.erp.deliveryreport.itemonway.itemsummary.sphz.page-columns-name';
        break;
      case 'mdhz':
        uid.key =
          'hxl.erp.deliveryreport.itemonway.itemsummary.mdhz.page-columns';
        uid.name =
          'hxl.erp.deliveryreport.itemonway.itemsummary.mdhz.page-columns-name';
        break;
      case 'mdsphz':
        uid.key =
          'hxl.erp.deliveryreport.itemonway.itemsummary.mdsphz.page-columns';
        uid.name =
          'hxl.erp.deliveryreport.itemonway.itemsummary.mdsphz.page-columns-name';
        break;
      case 'djhz':
        uid.key =
          'hxl.erp.deliveryreport.itemonway.itemsummary.djhz.page-columns';
        uid.name =
          'hxl.erp.deliveryreport.itemonway.itemsummary.djhz.page-columns-name';
        break;

      default:
        uid.key =
          'hxl.erp.deliveryreport.itemonway.itemsummary.sphz.page-columns';
        uid.name =
          'hxl.erp.deliveryreport.itemonway.itemsummary.sphz.page-columns-name';
    }
    return uid;
  };
  const oldArr = () => {
    let tableArr: any = [];
    switch (tabKey) {
      case 'sphz':
        tableArr = cloneDeep(goodsTotalArr);
        break;
      case 'mdhz':
        tableArr = cloneDeep(
          storeTotalArr.filter((v) => {
            if (!enable_cargo_owner) {
              return v.code !== 'out_org_name';
            }
            return v;
          }),
        );
        break;
      case 'mdsphz':
        tableArr = cloneDeep(
          storegoodsTotalArr.filter((v) => {
            if (!enable_cargo_owner) {
              return v.code !== 'out_org_name';
            }
            return v;
          }),
        );
        break;
      case 'djhz':
        tableArr = cloneDeep(
          djTotalArr.filter((v) => {
            if (!enable_cargo_owner) {
              return v.code !== 'out_org_name';
            }
            return v;
          }),
        );
        break;
      default:
        tableArr = cloneDeep(goodsTotalArr);
    }
    return tableArr;
  };
  // 获取数据
  const getData = async (page_number: number = 1, page_size: number = 200) => {
    const data = checkData(page_number, page_size);
    data.supplier_ids =
      tabKey == 'djhz'
        ? []
        : form.getFieldValue('supplier_ids')
          ? form.getFieldValue('supplier_ids')
          : [];
    setisLoading(true);
    let res = null;
    switch (tabKey) {
      case 'sphz':
        res = await getgoodsway(data);
        break;
      case 'mdhz':
        res = await getstoretotal(data);
        break;
      case 'mdsphz':
        res = await getstoregoodstotal(data);
        break;
      case 'djhz':
        res = await getdjtotal(data);
        break;
    }
    setisLoading(false);
    if (res.code == '0') {
      setRowData(res.data.content || []);
      //合计行
      const footDatas: any = res.data;
      const data: any = [
        {
          _index: '合计',
          out_quantity: footDatas.out_quantity_total || '0.000',
          out_basic_quantity: footDatas.basic_out_quantity_total || '0.000',
          out_money: hasAuth(['商品在途/配送价', '查询'])
            ? footDatas.out_money_total?.toFixed(2) || '0.00'
            : '****',
          in_quantity: footDatas.in_quantity_total || '0.000',
          in_basic_quantity: footDatas.basic_in_quantity_total || '0.000',
          in_money: hasAuth(['商品在途/配送价', '查询'])
            ? footDatas.in_money_total?.toFixed(2) || '0.00'
            : '****',
          on_way_quantity: footDatas.on_way_quantity_total || '0.000',
          on_way_basic_quantity:
            footDatas.basic_on_way_quantity_total || '0.000',
          on_way_money: hasAuth(['商品在途/配送价', '查询'])
            ? footDatas.on_way_money_total?.toFixed(2) || '0.00'
            : '****',
        },
      ];
      setFooterData(data);
      const paginData = {
        pageSize: page_size,
        pageNum: page_number,
        total: res.data.total_elements,
      };
      setPagin(paginData);
      switch (tabKey) {
        case 'sphz':
          setUseGoodsTotalRowData(res.data.content || []);
          setUseGoodsTotalFooterData(data);
          setUseGoodsTotalPagin(paginData);
          break;
        case 'mdhz':
          setUseStoreTotalArrRowData(res.data.content || []);
          setUseStoreTotalFooterData(data);
          setUseStoreTotalPagin(paginData);
          break;
        case 'mdsphz':
          setUseStoregoodsTotalRowData(res.data.content || []);
          setUseStoregoodsTotalFooterData(data);
          setUseStoregoodsTotalPagin(paginData);
          break;
        case 'djhz':
          setUseDjTotalRowData(res.data.content || []);
          setUseDjTotalFooterData(data);
          setUseDjTotalPagin(paginData);
          break;
      }
    }
  };
  // 导出
  const exportItem = async (e: any) => {
    setisLoading(true);
    const data = checkData();
    data.page_size = 10000;
    let res = null;
    switch (tabKey) {
      case 'sphz':
        res = await ErpRequest.post(
          '/erp/hxl.erp.deliveryreport.itemonway.itemsummary.export',
          data,
        );
        break;
      case 'mdhz':
        res = await ErpRequest.post(
          '/erp/hxl.erp.deliveryreport.itemonway.storesummary.export',
          data,
        );
        break;
      case 'mdsphz':
        res = await ErpRequest.post(
          '/erp/hxl.erp.deliveryreport.itemonway.storeitemsummary.export',
          data,
        );
        break;
      case 'djhz':
        res = await ErpRequest.post(
          '/erp/hxl.erp.deliveryreport.itemonway.ordersummary.export',
          data,
        );
        break;
    }
    setisLoading(false);
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };
  const initData = async () => {
    const center = LStorage.get('userInfo').store.enable_delivery_center;
    form.setFieldsValue({
      in_store_names: center ? '' : LStorage.get('userInfo').store_name,
      in_store_ids: center ? '' : [LStorage.get('userInfo').store_id],
      out_store_names: center ? LStorage.get('userInfo').store_name : '',
      out_store_ids: center ? [LStorage.get('userInfo').store_id] : '',
      compactDatePicker: [
        dayjs().format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
    });
  };
  useEffect(() => {
    initData();
  }, []);
  useEffect(() => {
    sortType.code !== '' && getData(1);
  }, [sortType]);
  tabList.map((v) => tableRender(v));
  return (
    <XlbPageContainer>
      <div
        className={'button_box row-flex'}
        style={{ marginBottom: '10px', padding: '0 16px' }}
      >
        <div style={{ width: '90%' }} className="row-flex">
          <XlbButton.Group>
            {hasAuth(['商品在途', '查询']) && (
              <XlbButton
                label={'查询'}
                disabled={isLoading}
                type="primary"
                onClick={() => getData()}
                icon={<SearchOutlined />}
              />
            )}
            {hasAuth(['商品在途', '导出']) && (
              <XlbButton
                type="primary"
                label={'导出'}
                disabled={!rowData.length || isLoading}
                onClick={(e: any) => exportItem(e)}
                icon={<UploadOutlined />}
              />
            )}
          </XlbButton.Group>
        </div>
        <div
          style={{
            width: '10%',
            height: '28px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            columnGap: 8,
          }}
        >
          <XlbTooltip title={isFold ? '收起' : '展开'}>
            <XlbIcon
              data-type={'顶层展开收起'}
              onClick={() => setIsFold(!isFold)}
              name="shouqi"
              size={20}
              className={classnames('xlb-columns-main-btn', {
                'xlb-columns-fold': !isFold,
                'xlb-columns-expand-btn-dev': true,
              })}
            />
          </XlbTooltip>
          <XlbColumns
            isFold={isFold}
            isFoldChange={setIsFold}
            url={handleUid()?.key}
            originColumns={oldArr()}
            value={tabList}
            onChange={setTabList}
            name={handleUid()?.name}
          />
        </div>
      </div>
      <div
        style={{
          display: isFold ? 'block' : 'none',
        }}
        className={'form_header_box'}
      >
        <XlbForm
          formList={formLists?.filter((v) => {
            if (
              !enable_organization &&
              ['out_org_ids', 'in_org_ids'].includes(v.name)
            ) {
              return false;
            } else {
              return true;
            }
          })}
          form={form}
          isHideDate={true}
          onValuesChange={onValuesChange}
        />
      </div>
      <div>
        <Tabs
          defaultActiveKey={tabKey}
          style={{ paddingLeft: '16px' }}
          activeKey={tabKey}
          onTabClick={(e) => {
            setTabKey(e);
            changeKey(e);
          }}
        >
          <TabPane tab="商品汇总" key={'sphz'}></TabPane>
          <TabPane tab="门店汇总" key={'mdhz'}></TabPane>
          <TabPane tab="门店-商品汇总" key={'mdsphz'}></TabPane>
          <TabPane tab="单据汇总" key={'djhz'}></TabPane>
        </Tabs>
      </div>
      <XlbTable
        tableKey={tabKey}
        key={`${isFold?.toString()}_${tabKey}`}
        columns={tabList}
        isLoading={isLoading}
        style={{ flex: 1, margin: '0 16px' }}
        pagin={pagin}
        pageSize={pagin?.pageSize}
        pageNum={pagin?.pageNum}
        total={pagin?.total}
        dataSource={rowData}
        isFold={isFold}
        onPaginChange={(page_number: number, page_size: number) => {
          getData(page_number, page_size);
        }}
        footerDataSource={footerData}
        onChangeSorts={(e) => {
          setSortType(e);
          setIsTabksKeyChange(false);
        }}
      />
    </XlbPageContainer>
  );
};
export default Goodsway;
