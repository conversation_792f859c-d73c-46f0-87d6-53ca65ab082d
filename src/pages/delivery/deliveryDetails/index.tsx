import PromiseModal from '@/components/promiseModal/PromiseModal';
import { useBaseParams } from '@/hooks/useBaseParams';
import useDownload from '@/hooks/useDownload';
import { formatWithCommas, hasAuth } from '@/utils/kit';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  ContextState,
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbMessage,
  XlbPageContainer,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import type { FC } from 'react';
import { useEffect, useState } from 'react';
import { formList, tableColumn } from './data';
import './index.less';
import { exportAll } from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;
const DeliveryDetails: FC = () => {
  const { downByProgress } = useDownload();
  const [form] = XlbBasicForm.useForm();
  const [footerDataSource, setFooterDataSource] = useState<any>([]);
  const [formLists, setFormLists] = useState<SearchFormType[]>(
    cloneDeep(formList),
  );
  const { enable_organization, enable_cargo_owner } = useBaseParams(
    (state) => state,
  );

  const checkData = () => {
    const formData = form.getFieldsValue();
    const { start_time, end_time, panelValue, compactDatePicker } =
      form.getFieldsValue(true);
    const data = {
      ...formData,
      stop_purchase: panelValue?.find((v: any) => v.value == 'stopPurchase')
        ?.itemKey,
      stop_sale: panelValue?.find((v: any) => v.value == 'stopSale')?.itemKey,
      audit_date:
        form.getFieldValue('time_type') === 'audit_date'
          ? compactDatePicker
          : null,
      create_date:
        form.getFieldValue('time_type') === 'create_date'
          ? compactDatePicker
          : null,
      effect_date:
        form.getFieldValue('time_type') === 'effect_date'
          ? compactDatePicker
          : null,
    };
    return data;
  };
  // 请求前置处理
  const prevPost = () => {
    const values = checkData();
    return values;
  };

  const afterPost = (data) => {
    setFooterDataSource([
      {
        _index: '合计',
        money: hasAuth(['配送商品明细/配送价', '查询'])
          ? data?.all_out_money_total?.toFixed(2) || '0.00'
          : '****',
        no_tax_money: hasAuth(['配送商品明细/配送价', '查询'])
          ? data?.all_out_no_tax_money_total?.toFixed(2) || '0.00'
          : '****',
        quantity: data?.all_out_quantity_total?.toFixed(3) || '0.000',
        basic_quantity: data?.basic_quantity_total?.toFixed(3) || '0.000',
        cost_money: hasAuth(['配送商品明细/成本价', '查询'])
          ? data?.cost_money_total?.toFixed(2) || '0.00'
          : '****',
        present_quantity: data?.present_quantity_total?.toFixed(3) || '0.000',
        profit: hasAuth(['配送商品明细/毛利', '查询'])
          ? data?.profit_total?.toFixed(2) || '0.00'
          : '****',
        tax_money: hasAuth(['配送商品明细/配送价', '查询'])
          ? data?.tax_money_total?.toFixed(2) || '0.00'
          : '****',
      },
    ]);
  };

  const getOrgList = async () => {
    formLists.find((i) => i.name === 'out_org_ids')!.hidden =
      !enable_organization;
    formLists.find((i) => i.name === 'in_org_ids')!.hidden =
      !enable_organization;
    if (enable_organization) {
      const res = await XlbFetch.post('/erp/hxl.erp.org.find', {});
      if (res?.code == 0) {
        const org_list = res.data.map((i: any) => ({
          value: i.id,
          label: i.name,
        }));
        formLists.find((i) => i.name === 'out_org_ids')!.options = org_list;
        formLists.find((i) => i.name === 'in_org_ids')!.options = org_list;
      }
    }
    setFormLists([...formLists]);
  };

  useEffect(() => {
    getOrgList();

    return () => {
      // setFormLists()
    };
  }, [enable_organization]);
  useEffect(() => {
    form.setFieldsValue({
      time_desc: 0,
      time_type: 'audit_date',
      compactDatePicker: [
        moment().format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD'),
      ],
    });
  }, []);

  const exportItem = async (e: React.MouseEvent) => {
    console.log('e', e);
    const values = checkData();
    const data = { ...values };
    const res = await exportAll({
      ...data,
    });
    wujieBus?.$emit('xlb_erp-event', {
      code: 'downloadEnd',
      target: e,
    });
    XlbMessage.success('导出受理成功，请前往下载中心查看');
  };

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'fid':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div
              className="link overwidth"
              onClick={(e) => {
                e.stopPropagation();
                NiceModal.show(PromiseModal, {
                  order_type: record.type,
                  order_fid: record.fid,
                  isShowFooter: false,
                });
              }}
            >
              {value}
            </div>
          );
        };
        break;
      case 'producing_date':
      case 'expire_date':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {value ? value?.substring(0, 10) : ''}
            </div>
          );
        };
        break;
      // item.render = (value: any, record: any, index: number) => {
      //   return <div className="info overwidth">{dateManipulation(value)}</div>
      // }
      // break
      case 'quantity':
      case 'present_quantity':
      case 'basic_quantity':
        item.render = (value: any, record: any) => {
          return <div>{formatWithCommas(Number(value || 0).toFixed(3))}</div>;
        };
        break;
      case 'tax_rate':
        item.render = (value: any, record: any) => {
          return <div>{formatWithCommas(Number(value || 0).toFixed(2))}%</div>;
        };
        break;
      case 'no_tax_price':
      case 'price':
      case 'no_tax_money':
      case 'money':
      case 'tax_money':
      case 'basic_price':
        // 配送金额,查询权限
        item.render = (value: any, record: any) => {
          return (
            <div>
              {hasAuth(['配送商品明细/配送价', '查询'])
                ? formatWithCommas(Number(value || 0).toFixed(2))
                : '****'}
            </div>
          );
        };
        break;
      case 'cost_price':
      case 'cost_money':
      case 'no_tax_cost_money':
        // 配送金额,查询权限
        item.render = (value: any, record: any) => {
          return (
            <div>
              {hasAuth(['配送商品明细/成本价', '查询'])
                ? formatWithCommas(Number(value || 0).toFixed(2))
                : '****'}
            </div>
          );
        };
        break;
      case 'profit':
        // 配送金额,查询权限
        item.render = (value: any, record: any) => {
          return (
            <div>
              {hasAuth(['配送商品明细/毛利', '查询'])
                ? formatWithCommas(Number(value || 0).toFixed(2))
                : '****'}
            </div>
          );
        };
        break;
      default:
        item.render = (value: any) => <div>{value}</div>;
        break;
    }
  };

  const filteredColumns = tableColumn.filter(
    (v) =>
      enable_cargo_owner || (v.name !== '调入组织' && v.name !== '调出组织'),
  );
  filteredColumns.forEach((v) => tableRender(v));

  let refresh: Function;
  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.delivery.itemdetail.find'}
      tableColumn={filteredColumns}
      prevPost={prevPost}
      afterPost={afterPost}
    >
      <SearchForm>
        <XlbForm
          formList={formLists}
          form={form}
          isHideDate={true}
          getFormRecord={() => refresh()}
        />
      </SearchForm>

      <ToolBtn withHLine={false}>
        {({ dataSource, fetchData, loading, selectRow }: ContextState<any>) => {
          refresh = fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['配送商品明细', '查询']) && (
                <XlbButton
                  type="primary"
                  label="查询"
                  loading={loading}
                  onClick={() => fetchData()}
                  icon={<span className="iconfont icon-sousuo" />}
                />
              )}
              {hasAuth(['配送商品明细', '导出']) && (
                <XlbButton
                  type="primary"
                  label="导出"
                  disabled={!dataSource?.length || loading}
                  onClick={(e) => exportItem(e)}
                  icon={<span className="iconfont icon-sousuo" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table
        key="id"
        keepDataSource={false}
        footerDataSource={footerDataSource}
        selectMode="single"
      />
    </XlbPageContainer>
  );
};
export default DeliveryDetails;