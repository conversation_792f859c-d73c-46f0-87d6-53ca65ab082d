import type {
  SearchFormType,
  XlbTableColumnProps,
} from '@xlb/components';

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: 160,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '单据类型',
    code: 'type',
    width: 100,
    align: 'left',
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 180,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '制单人',
    code: 'create_by',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    hiddenInXlbColumns: true,
    width: 140,
    align: 'left',
  },
  {
    name: '调出门店代码',
    code: 'out_store_number',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '调出门店名称',
    code: 'out_store_name',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '调入组织',
    code: 'in_org_name',
    hiddenInXlbColumns: true,
    width: 140,
    align: 'left',
  },
  {
    name: '调入门店代码',
    code: 'in_store_number',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '调入门店名称',
    code: 'in_store_name',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '规格',
    code: 'item_spec',
    width: 80,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'delivery_unit',
    width: 120,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '数量',
    code: 'quantity',
    width: 120,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '单价(去税)',
    code: 'no_tax_price',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '单价',
    code: 'price',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额(去税)',
    code: 'no_tax_money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额',
    code: 'money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '税率',
    code: 'tax_rate',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '税额',
    code: 'tax_money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '赠品单位',
    code: 'present_unit',
    width: 100,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 120,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '基本单位',
    code: 'unit',
    width: 100,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 120,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '成本单价',
    code: 'cost_price',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '成本金额',
    code: 'cost_money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '成本金额(去税)',
    code: 'no_tax_cost_money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: 180,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '有效天数',
    code: 'item_valid_period',
    width: 120,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '过期日期',
    code: 'expire_date',
    width: 180,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '毛利金额',
    code: 'profit',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
];
export const formList: SearchFormType[] = [
  {
    width: 392,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
  },
  {
    label: '单据类型',
    name: 'order_type',
    type: 'select',
    allowClear: true,
    placeholder: '',
    initialValue: '调出单',
    options: [
      {
        label: '调出单',
        value: '调出单',
      },
      {
        label: '调入单',
        value: '调入单',
      },
    ],
  },
  {
    label: '调出组织',
    name: 'out_org_ids',
    type: 'select',
    multiple: true,
    allowClear: true,
    hidden: true,
    options: [],
  },
  {
    label: '调出门店',
    name: 'out_store_ids',
    type: 'inputDialog',
    allowClear: true,
    placeholder: '',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        status: true,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '调入组织',
    name: 'in_org_ids',
    type: 'select',
    multiple: true,
    allowClear: true,
    hidden: true,
    options: [],
  },
  {
    label: '调入门店',
    name: 'in_store_ids',
    type: 'inputDialog',
    allowClear: true,
    placeholder: '',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        status: true,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      isMultiple: true,
    },
    allowClear: true,
  },
  {
    label: '商品类别',
    name: 'category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    },
  },
  {
    className: 'inputPanelValue',
    type: 'inputPanel',
    name: 'panelValue',
    label: '其他条件',
    placeholder: '停购/停售',
    // label: " ",
    // colon: false,
    allowClear: true,
    items: [
      {
        label: '仅显示',
        key: 'true',
      },
      {
        label: '不显示',
        key: 'false',
      },
    ],
    options: [
      { label: '停购', value: 'stopPurchase' },
      { label: '停售', value: 'stopSale' },
    ],
    width: 205,
  },
];
