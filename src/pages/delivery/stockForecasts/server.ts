import { XlbFetch as ErpRequest, LStorage } from '@xlb/utils';

export default {
  // 获取门店管理数据
  getInvoicesAll: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.store.page', data);
  },

  // 查询详情
  getQuery: async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.preparepredictioncoefficient.read',
      data,
    );
  },

  // 批量修改
  batchUpdate: async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.preparepredictioncoefficient.batchupdate',
      data,
    );
  },

  // 保存
  saveUpdate: async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.preparepredictioncoefficient.update',
      data,
    );
  },

  // 获取门店分组
  getstoreGrop: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storegroup.find', data);
  },

  // 门店分组删除
  getstoregroupDelete: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storegroup.delete', data);
  },

  // 门店编辑
  getStoregroupUpdate: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storegroup.update', data);
  },
  // 门店保存
  getStoregroupSave: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storegroup.save', data);
  },

  // 获取分类数据
  getClassList: async () => {
    const hadUpdate = LStorage.get('CACHE_CATEGORY_UPDATE');
    if (!hadUpdate && LStorage.get('CACHE_CATEGORY_DATA')) {
      return new Promise((resolve) => {
        const res = JSON.parse(LStorage.get('CACHE_CATEGORY_DATA') || '{}');
        resolve(res);
      });
    } else {
      const res = await ErpRequest.post('/erp/hxl.erp.category.find', {
        company_id: LStorage.get('userInfo')?.company_id,
        operator_store_id: LStorage.get('userInfo').store_id,
      });
      LStorage.set('CACHE_CATEGORY_DATA', JSON.stringify(res));
      LStorage.set('CACHE_CATEGORY_UPDATE', '');
      return res;
    }
  },
};