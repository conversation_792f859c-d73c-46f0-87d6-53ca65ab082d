import { hasAuth } from '@/utils/kit';
import { history } from '@@/core/history';
import { EditOutlined } from '@ant-design/icons';
import { message } from 'antd';
import { FC, useRef, useState } from 'react';
import BatchChange from './component/batchChange';
import { tableColumn } from './data';
import Api from './server';
// import useKeepAliveRefresh from '@/hooks/useKeepAliveRefresh';
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import StockForecastsDetail from '@/pages/delivery/stockForecasts/item';
import {
  type ContextState,
  XlbBasicForm,
  XlbButton,
  XlbInput,
  XlbProPageContainer,
  XlbProPageModal,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { DataType } from '@xlb/components/dist/components/XlbTree/type';

const Index: FC = () => {
  const record = history.location.state as any;
  const fetchFresh = useRef<any>();
  const treeInstanceConfig = useRef<any>(null);
  const [batchVisible, setBatchVisible] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [treeForm] = XlbBasicForm.useForm();

  const getData = async () => {
    fetchFresh.current?.();
  };
  // const { go } = useKeepAliveRefresh({ refresh: getData });
  const batchChange = async () => {
    setBatchVisible(true);
  };

  // const getIds = (data: any) => {
  //   const result: Array<string> = [];
  //   for (let i = 0; i < data.length; i++) {
  //     result.push(data[i].id);
  //     if (data[i].children && data[i].children.length > 0) {
  //       result.push(...getIds(data[i].children));
  //     }
  //   }
  //   return result;
  // };

  //获取分类ID
  const treeCallBack = (e: any) => {
    console.log(e, 1212);
    if (e?.id === 0) {
      return { store_group_ids: [] };
    }
  };
  const onMenuClick = (type: string, data: any) => {
    if (!hasAuth(['门店管理', '编辑']))
      return message.error('当前节点不可操作');
    if (type === 'add' && data.level === 3)
      return message.error('三级分类不能新增下级分类');
    if (
      (type === 'delete' || type === 'edit') &&
      (data.level === 0 || !data.level)
    )
      return message.error('当前节点不可操作');
    if (type === 'delete') {
      XlbTipsModal({
        tips: `是否要删除分类${data.name}`,
        isCancel: true,
        onOkBeforeFunction: async () => {
          const res = await Api.getstoregroupDelete({ id: data.id });
          if (res.code === 0) {
            message.success('操作成功');
            treeInstanceConfig?.current?.refresh();
            return true;
          }
        },
      });
    } else {
      treeForm.setFieldsValue({
        parent_id: type === 'edit' ? data.parent_id : data.id,
        name: type === 'edit' ? data.name : '',
      });
      XlbTipsModal({
        tips: (
          <XlbBasicForm
            autoComplete="off"
            colon={true}
            form={treeForm}
            labelCol={{ span: 6 }}
          >
            <XlbBasicForm.Item
              label="上级分类"
              name="parent_id"
              rules={[{ required: true, message: '请输入分类名称' }]}
            >
              <XlbSelect
                showSearch
                placeholder="请选择上级分类"
                style={{ width: 260 }}
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option!.children as unknown as string)
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
              >
                {treeData?.map((item) => (
                  <XlbSelect.Option key={item.key || item.id} value={item.id}>
                    {item.name}
                  </XlbSelect.Option>
                ))}
              </XlbSelect>
            </XlbBasicForm.Item>
            <XlbBasicForm.Item
              label="分类名称"
              name="name"
              rules={[{ required: true, message: '请输入分类名称' }]}
            >
              <XlbInput
                style={{ width: 260 }}
                size="small"
                placeholder="请输入分类名称"
                maxLength={30}
              />
            </XlbBasicForm.Item>
          </XlbBasicForm>
        ),
        destroyOnClose: true,
        isCancel: true,
        onCancel: () => treeForm.resetFields(),
        onOkBeforeFunction: async () => {
          try {
            await treeForm.validateFields();
          } catch (err: any) {
            return false;
          }
          const params =
            type === 'add'
              ? {
                  ...data,
                  name: data.category_name,
                  parent_id: data.id,
                  level: data.level + 1,
                  ...treeForm.getFieldsValue(),
                }
              : { ...data, ...treeForm.getFieldsValue() };
          // const params = { ...treeForm.getFieldsValue() }
          const res =
            type === 'add'
              ? await Api.getStoregroupSave(params)
              : await Api.getStoregroupUpdate({ ...params, id: data.id });
          if (res.code === 0) {
            message.success('操作成功');
            treeForm.resetFields();
            // console.log(refreshTreeData?.(), 222)
            treeInstanceConfig?.current?.refresh();
            return true;
          }
        },
      });
    }
  };

  return (
    <>
      <XlbProPageContainer
        searchFieldProps={{
          formList: [
            {
              label: '门店名称',
              name: 'keyword',
              id: ErpFieldKeyMap?.erpKeyword,
            },
          ],
          initialValues: {
            deletion: record?.deletion || '',
          },
        }}
        extra={({ fetchData, loading }: ContextState) => {
          fetchFresh.current = fetchData;
          return (
            <>
              {hasAuth(['备货预测系数', '编辑']) && (
                <XlbButton
                  label="批量修改"
                  type="primary"
                  loading={loading}
                  onClick={batchChange}
                  icon={<EditOutlined />}
                />
              )}
            </>
          );
        }}
        treeFieldProps={{
          leftUrl: '/erp/hxl.erp.storegroup.find',
          dataType: DataType.LISTS,
          leftKey: 'store_group_ids',
          isTreeSelectWithChild: true,
          requestParams: {
            query_stores: true,
          },
          // menu: () => {
          //   return [
          //     {
          //       name: '新增分类',
          //       key: 'add',
          //     },
          //     {
          //       name: '编辑分类',
          //       key: 'edit',
          //     },
          //     {
          //       name: '删除分类',
          //       key: 'delete',
          //     },
          //   ];
          // },
          afterPost(data) {
            setTreeData(data);
            return data;
          },
          onSelect: treeCallBack,
          onMenuClick: onMenuClick,
          onExpose: (e) => {
            treeInstanceConfig.current = e;
          },
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.store.page',
          tableColumn: () =>
            tableColumn.map((item) => {
              if (item.code === 'store_name') {
                return {
                  ...item,
                  render: (text: any, record: any) => {
                    return (
                      <XlbProPageModal
                        Content={({ onClose }) => (
                          <StockForecastsDetail
                            rowData={record}
                            onClose={onClose}
                          />
                        )}
                      >
                        <XlbButton type={'link'}>{text}</XlbButton>
                      </XlbProPageModal>
                    );
                  },
                };
              }
              return item;
            }),
          selectMode: 'single',
          showColumnsSetting: true,
          changeColumnAndResetDataSource: true,
          immediatePost: true,
        }}
      />
      <BatchChange
        visible={batchVisible}
        handleCancel={() => setBatchVisible(false)}
        getData={getData}
      />
    </>
  );
};
export default Index;
