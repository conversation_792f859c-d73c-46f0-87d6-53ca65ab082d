import { useBaseParams } from '@/hooks/useBaseParams';
import { exportPage } from '@/services/system';
import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbBlueBar,
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbMessage,
  XlbSelect,
  XlbTable,
  XlbTableColumnProps,
  XlbTimePicker,
  XlbTipsModal,
} from '@xlb/components';
import { safeMath } from '@xlb/utils';
import { Col, ConfigProvider, Row } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { itemTableList, orderingCircle, ruleOptions } from '../data';
import Api from '../server';
import styles from './index.less';
const Item = (props) => {
  const { onBack, record } = props;
  const [orderingCircles, setOrderingCircles] = useState<any[]>(
    orderingCircle?.WEEK,
  );
  const [info, setInfo] = useState<any>({});

  const [isLoading, setisLoading] = useState<boolean>(false);
  const [chooseList, setChooseList] = useState<any[]>([]);
  const [rowData, setRowData] = useState<any[]>([]);

  //表单
  const [form] = XlbBasicForm.useForm();
  const [preId, setId] = useState(-1);
  const [pageSize, setPageSize] = useState(200);

  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(itemTableList)),
  );

  const { enable_organization } = useBaseParams((state) => state);

  const handleChangeRule = (v: any) => {
    setOrderingCircles(orderingCircle[v as keyof typeof orderingCircle]);
    form?.setFieldValue('rule_values', undefined);
  };
  //读取
  const readInfo = async (id: any) => {
    const res = await Api.readItem({ id: id });
    if (res.code === 0) {
      const { type, stores, rule_values } = res?.data;
      setInfo(res?.data);
      setRowData(stores);
      setOrderingCircles(orderingCircle[type as keyof typeof orderingCircle]);
      form.setFieldsValue({
        ...res.data,
        rule_values: rule_values
          ?.split(',')
          ?.map((i: string) => parseInt(i))
          ?.filter(Boolean),
        time:
          res?.data?.time_begin && res?.data?.time_end
            ? [
                dayjs(res?.data?.time_begin?.slice(11), 'HH:mm:ss').format(
                  'HH:mm:ss',
                ),
                dayjs(res?.data?.time_end?.slice(11), 'HH:mm:ss').format(
                  'HH:mm:ss',
                ),
              ]
            : undefined,
      });
    }
  };

  useEffect(() => {
    itemArr.find((i) => i.code === 'org_name')!.hidden = !enable_organization;
    setItemArr([...itemArr]);
  }, [enable_organization]);

  useEffect(() => {
    setId(record?.id);
    if (record?.id == -1) {
      form.setFieldsValue({
        time: [
          dayjs().startOf('day').format('HH:mm:ss'),
          dayjs().endOf('day').format('HH:mm:ss'),
        ],
        type: 'WEEK',
      });
    }
    if (record?.id > 0) {
      readInfo(record.id);
    }
  }, []);

  //添加
  const addGoods = async () => {
    const bool = await XlbBasicData({
      isMultiple: true,
      dataType: 'lists',
      type: 'store',
      selectedList: rowData,
      data: {
        center_flag: false,
        status: true,
      },
    });
    if (bool) {
      const List = bool.map(
        (v: any) =>
          (v = {
            ...v,
            store_group_name: v?.store_group?.name,
            store_group_id: v?.store_group?.id,
          }),
      );
      setRowData(List);
    }
  };
  //删除
  const deleteGoods = async () => {
    const code = await XlbTipsModal({
      isConfirm: true,
      isCancel: true,
      tips: `是否删除选中的${chooseList.length}个门店?`,
    });
    if (code) {
      chooseList.map((item: any) => {
        // 如果是删除有id的，item是number类型， 如果是刚新增的，item是字符串类型的下标
        const i =
          typeof item === 'number'
            ? rowData.findIndex((e) => e.id === item)
            : Number(item);
        rowData.splice(i, 1);
      });
      setChooseList([]);
    }
  };
  //导入
  const imports = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.ordertimerule.store.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.storecodetemplate.download`,
      templateName: '下载模版',
      callback: (res) => {
        if (res?.data?.state) {
          let repeatArr: any[] = [];
          setRowData((prev: any) => {
            const code = prev.map((v: any) => v.store_code);
            repeatArr = res?.data?.stores?.filter((v: any) =>
              code.includes(v.store_code),
            );
            const newArr = res?.data?.stores?.filter(
              (v: any) => !code.includes(v.store_code),
            );
            return [...prev, ...newArr];
          });
          if (repeatArr.length) {
            XlbTipsModal({
              isConfirm: true,
              isCancel: false,
              tips: '以下门店已存在，系统已自动过滤！',
              tipsList: [
                repeatArr.map((v: any) => `【${v.store_name}】`).join('、'),
              ],
            });
          }
          res?.data?.success_num > repeatArr?.length &&
            XlbMessage.success(
              '导入成功,共导入' +
                (res?.data?.success_num - repeatArr?.length) +
                '条数据',
            );
        }
      },
    });
  };
  //导出
  const exportItem = async () => {
    const data = {
      id: record?.id,
    };
    setisLoading(true);
    const res = await exportPage(
      '/erp/hxl.erp.ordertimerule.store.export',
      data,
      { responseType: 'blob' },
    );
    const download = new Download();
    download.filename = '门店下单日门店导出.xlsx';
    download.xlsx(res);
    setisLoading(false);
  };
  // 保存
  const saveStore = async () => {
    const validateRes = await form?.validateFields();
    if (!validateRes) {
      return;
    }
    const start_day =
      preId == -1
        ? dayjs().format('YYYY-MM-DD')
        : dayjs(info?.time_begin)?.format('YYYY-MM-DD');
    const time_begin = `${start_day} ${form?.getFieldValue('time')[0]}`;
    const end_day =
      preId == -1
        ? dayjs().format('YYYY-MM-DD')
        : dayjs(info?.time_end)?.format('YYYY-MM-DD');
    const time_end = `${end_day} ${form?.getFieldValue('time')[1]}`;
    const order_types = form?.getFieldValue('order_types');
    const data = {
      ...form.getFieldsValue(true),
      rule_values: form?.getFieldValue('rule_values')?.join(','),
      time_begin,
      time_end,
      order_type: order_types?.length
        ? order_types.reduce((pre: any, cur: any) => safeMath.add(pre, cur), 0)
        : 0,
      store_ids: rowData?.map((item: any) => item.id),
    };
    delete data?.time;
    setisLoading(true);
    const res =
      preId == -1
        ? await Api.addItem(data)
        : await Api.updateItem({ ...data, id: preId });
    setisLoading(false);
    if (res.code === 0) {
      XlbMessage.success('保存成功');
      onBack();
    }
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          screenXLMin: 1280,
          screenXL: 1280,
        },
      }}
    >
      <div
        style={{
          padding: 10,
          height: 'calc(100vh - 120px)',
          display: 'flex',
          flexDirection: 'column',
        }}
        className={styles.form_container_wrapper}
      >
        <XlbButton.Group>
          {hasAuth(['门店下单日', '编辑']) && (
            <XlbButton
              label="保存"
              type="primary"
              loading={isLoading}
              onClick={saveStore}
              icon={<XlbIcon name="baocun" />}
            />
          )}
          <XlbButton
            label="返回"
            type="primary"
            onClick={() => {
              onBack();
            }}
            icon={<XlbIcon name="fanhui" />}
          />
        </XlbButton.Group>
        <XlbBlueBar title={'下单日详情'} hasMargin={true} />
        <div className={styles.form_container_storeOrderingDate}>
          <XlbBasicForm
            autoComplete="off"
            layout="horizontal"
            colon={true}
            form={form}
          >
            <Row gutter={12} wrap>
              <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                <XlbBasicForm.Item
                  label="下单日名称："
                  name="name"
                  rules={[{ required: true, message: '请输入下单日名称' }]}
                >
                  <XlbInput
                    style={{ width: '100%' }}
                    maxLength={20}
                    allowClear
                  />
                </XlbBasicForm.Item>
              </Col>
              <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                <XlbBasicForm.Item
                  label="下单日规则："
                  name="type"
                  rules={[{ required: true, message: '请选择下单日规则' }]}
                >
                  <XlbSelect
                    style={{ width: '100%' }}
                    options={ruleOptions}
                    allowClear
                    onChange={handleChangeRule}
                  />
                </XlbBasicForm.Item>
              </Col>
              <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                <XlbBasicForm.Item
                  label="下单日周期："
                  name="rule_values"
                  rules={[{ required: true, message: '请选择下单日周期' }]}
                >
                  <XlbSelect
                    style={{ width: '100%' }}
                    mode="multiple"
                    maxTagCount="responsive"
                    options={orderingCircles}
                    allowClear
                  />
                </XlbBasicForm.Item>
              </Col>
              <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                <XlbBasicForm.Item
                  label="下单日时段"
                  name="time"
                  rules={[{ required: true, message: '请选择下单日时段' }]}
                >
                  <XlbTimePicker.RangePicker
                    style={{ width: '100%' }}
                    format="HH:mm:ss"
                    allowClear
                  />
                </XlbBasicForm.Item>
              </Col>
              <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                <XlbBasicForm.Item
                  label="单据类型"
                  name="order_types"
                  initialValue={[1, 2, 4, 8]}
                >
                  <XlbSelect
                    style={{ width: '100%' }}
                    mode="multiple"
                    options={[
                      { label: '门店叫货', value: 1 },
                      { label: '统配订单', value: 2 },
                      { label: '预定单', value: 4 },
                      { label: '直配订单', value: 8 },
                    ]}
                    allowClear
                  ></XlbSelect>
                  {/* <XlbCheckbox.Group style={{ width: '250%' }}>
                  <XlbCheckbox value={1}>门店叫货</XlbCheckbox>
                  <XlbCheckbox value={2}>统配订单</XlbCheckbox>
                  <XlbCheckbox value={4}>预定单</XlbCheckbox>
                  <XlbCheckbox value={8}>直配订单</XlbCheckbox>
                </XlbCheckbox.Group> */}
                </XlbBasicForm.Item>
              </Col>
            </Row>
          </XlbBasicForm>
        </div>
        <XlbBlueBar title={'门店详情'} hasMargin={true} />
        <XlbButton.Group>
          {hasAuth(['门店下单日', '编辑']) ? (
            <XlbButton
              label="添加"
              onClick={addGoods}
              type="primary"
              icon={<XlbIcon name="jia" />}
            />
          ) : null}
          {hasAuth(['门店下单日', '编辑']) ? (
            <XlbButton
              label="删除"
              onClick={deleteGoods}
              type="primary"
              disabled={!chooseList.length}
              icon={<XlbIcon name="shanchu" />}
            />
          ) : null}
          {hasAuth(['门店下单日', '导入']) ? (
            <XlbButton
              label="导入"
              type="primary"
              onClick={imports}
              icon={<XlbIcon name="daoru" />}
            />
          ) : null}
          {hasAuth(['门店下单日', '导出']) ? (
            <XlbButton
              label="导出"
              type="primary"
              disabled={!rowData.length || preId == -1}
              onClick={exportItem}
              icon={<XlbIcon name="daochu" />}
            />
          ) : null}
        </XlbButton.Group>
        <XlbTable
          isLoading={isLoading}
          key={rowData?.length}
          columns={itemArr}
          components={{
            EmptyContent: () => null,
          }}
          style={{ flex: 1, marginTop: 5 }}
          dataSource={rowData}
          selectMode="multiple"
          total={rowData.length}
          showSearch
          pageSize={pageSize}
          useVirtual={true}
          selectedRowKeys={chooseList}
          onSelectRow={(selectedRowKeys) => setChooseList(selectedRowKeys)}
          onPaginChange={(page, pageSize) => setPageSize(pageSize)}
        />
      </div>
    </ConfigProvider>
  );
};

export default Item;