import type { SearchFormType, XlbTableColumnProps } from '@xlb/components';
import { selectType } from './types';

const columnWidthEnum = {
  tel: 120,
  fid: 160,
  TIME: 120,
  DATE: 100,
  ITEM_CODE: 124,
  ITEM_BAR_CODE: 124,
  SHORTHAND_CODE: 110,
  STORE_NAME: 140,
  MEMO: 140,
  STOP_SALE: 90,
  ORDER_STATE: 90,
  INDEX: 50,
  ITEM_SPEC: 110,
  BY: 110,
  ORDER_FID: 140,
};

export const unitTypes: selectType[] = [
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
];

interface SummaryType extends selectType {
  code: string;
}
export const formList: SearchFormType[] = [
  {
    width: 372,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
  },
  {
    label: '调出组织',
    name: 'out_org_ids',
    type: 'select',
    multiple: true,
    clear: true,
    // hidden: !enable_organization,
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.org.find',
      responseTrans: {
        label: 'name',
        value: 'id',
      },
    },
  },
  {
    label: '调出门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        status: true,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '发货仓库',
    name: 'storehouse_id',
    type: 'select',
    clear: true,
    dropdownMatchSelectWidth: 240,
    options: [],
    // dependencies: ['store_ids'],
    // linkId: 'storehouse'
  },
  {
    label: '调入组织',
    name: 'in_org_ids',
    clear: true,
    type: 'select',
    multiple: true,
    // hidden: !enable_organization,
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.org.find',
      responseTrans: {
        label: 'name',
        value: 'id',
      },
    },
  },
  {
    label: '调入门店',
    name: 'in_store_ids',
    type: 'inputDialog',
    clear: true,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        status: true,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dependencies: ['summary_types'],
    allowClear: true,
    dialogParams: (data) => {
      console.log('🚀 ~ data:', data);
      return {
        type: 'goods',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
      };
    },
  },
  {
    label: '查询单位',
    name: 'unit_type',
    type: 'select',
    clear: true,
    options: unitTypes,
  },
  {
    label: '统配原因',
    name: 'reason_name',
    type: 'select',
    clear: true,
    selectRequestParams: {
      url: '/erp/hxl.erp.reason.find',
      postParams: {
        type: 'FROCE_DELIVER',
      },
      responseTrans: {
        label: 'name',
        value: 'name',
      },
    },
  },
  // {
  //   label: '汇总条件',
  //   name: 'summary_types',
  //   type: 'select',
  //   multiple: true,
  //   clear: true,
  //   options: summaryTypes,
  // },
  {
    // className: 'inputPanelValue',
    type: 'inputPanel',
    name: 'panelValue',
    label: '其他条件',
    placeholder: '统配数量为0商品',
    // label: " ",
    // colon: false,
    allowClear: true,
    items: [
      {
        label: '仅显示',
        key: 'true',
      },
      {
        label: '不显示',
        key: 'false',
      },
    ],
    options: [
      { label: '统配数量为0商品', value: 'filter_zero_force_delivery' },
    ],
    width: 205,
  },
  {
    label: '汇总条件',
    name: 'summary_types',
    type: 'summaryTypes',
    clear: true,
    check: true,
    options: [
      {
        label: '调出门店',
        value: 'DELIVERY_STORE',
      },
      {
        label: '调入门店',
        value: 'STORE',
      },
      {
        label: '统配日期',
        value: 'DELIVERY_DATE',
      },
      {
        label: '审核日期',
        value: 'AUDIT_DATE',
      },
      {
        label: '商品档案',
        value: 'ITEM',
      },
      {
        label: '单据',
        value: 'ORDER',
      },
      {
        label: '统配类型',
        value: 'FORCE_TYPE',
      },
      {
        label: '生产日期',
        value: 'PRODUCING_DATE',
      },
    ],
  },
];
export const summaryTypes: SummaryType[] = [
  {
    label: '调出门店',
    value: 'DELIVERY_STORE',
    code: 'delivery_store_name',
  },
  {
    label: '调入门店',
    value: 'STORE',
    code: 'store_name',
  },
  {
    label: '统配日期',
    value: 'DELIVERY_DATE',
    code: 'delivery_date',
  },
];

export const enableItem: selectType[] = [
  { label: '启用', value: true },
  { label: '禁用', value: false },
];

export const filterArr = [
  {
    label: '过滤未调出的门店补货单数据',
    value: 'filter_not_out_store_request_order',
  },
];
// 调出门店
export const DELIVERY_STORE: XlbTableColumnProps<any>[] = [
  {
    name: '调出组织',
    code: 'out_org_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'delivery_store_name',
    width: columnWidthEnum.STORE_NAME,
    align: 'left',
  },
];
export const STORE: XlbTableColumnProps<any>[] = [
  {
    name: '调入组织',
    code: 'in_org_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    align: 'left',
  },
];
export const DELIVERY_DATE: XlbTableColumnProps<any>[] = [
  {
    name: '统配日期',
    code: 'delivery_date',
    width: 160,
    align: 'left',
    features: { format: 'TIME' },
  },
];
export const AUDIT_DATE: XlbTableColumnProps<any>[] = [
  {
    name: '审核日期',
    code: 'audit_date',
    width: columnWidthEnum.STORE_NAME,
    align: 'left',
    features: { format: 'TIME' },
  },
];
export const ITEM: XlbTableColumnProps<any>[] = [
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left',
  },
];
export const ORDER: XlbTableColumnProps<any>[] = [
  {
    name: '统配单号',
    code: 'fid',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '关联单号',
    code: 'relation_fid',
    width: 160,
    features: { sortable: true },
  },
];
export const FORCE_TYPE: XlbTableColumnProps<any>[] = [
  {
    name: '统配类型',
    code: 'force_delivery',
    width: 160,
    features: { sortable: true },
  },
];
export const PRODUCING_DATE: XlbTableColumnProps<any>[] = [
  {
    name: '生产日期',
    code: 'producing_date',
    width: 160,
    features: { sortable: true, format: 'TIME' },
  },
];
export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '统配门店数量',
    code: 'force_store_quantity',
    width: 140,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '统配数量',
    code: 'force_delivery_quantity',
    width: 110,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '统配基本数量',
    code: 'basic_force_delivery_quantity',
    width: 120,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '统配金额',
    code: 'force_delivery_money',
    width: 110,
    features: { sortable: true, format: 'MONEY' },
    align: 'right',
  },
];
