import { columnWidthEnum } from '@/data/common/constant';
import { useBaseParams } from '@/hooks/useBaseParams';
import useDownload from '@/hooks/useDownload';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { wujieBus } from '@/wujie/utils';
import { history } from '@@/core/history';
import {
  ContextState,
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbForm,
  XlbIcon,
  XlbInputDialogByRead,
  XlbMessage,
  XlbPageContainer,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { message, Tooltip } from 'antd';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { InputDialogColumns, Options1, Options3 } from '../data';
import Item from '../item/index';
import Api from '../server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const deliveryPriceMange = () => {
  const [form] = XlbBasicForm.useForm();
  const [isLoading, setisLoading] = useState<boolean>(false);
  const { downByProgress } = useDownload();
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 });
  const { enable_organization } = useBaseParams((state) => state);
  let refresh = () => {};
  const selectRowRef = useRef<any>(null);
  const formRef = useRef<any>(null);
  const dataSourceRef = useRef<any>(null);
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbPageContainerRef>(null);
  const [record, setRecord] = useState<any>({});
  const formList: SearchFormType[] = [
    {
      width: 372,
      type: 'compactDatePicker',
      label: '日期选择',
      name: 'compactDatePicker',
      allowClear: false,
      // disabled: true,
      // format: "YYYY-MM-DD HH:mm:ss",
      // @ts-ignore
    },
    {
      label: '时间类型',
      name: 'time_type',
      type: 'select',
      clear: false,
      check: true,
      options: Options3,
    },
    {
      label: '单据状态',
      name: 'state',
      type: 'select',
      clear: true,
      check: true,
      options: Options1,
    },
    {
      label: '单据号',
      name: 'fid',
      type: 'input',
      clear: true,
      check: true,
    },
    {
      label: '应用组织',
      name: 'org_ids',
      type: 'inputDialog',
      treeModalConfig: {
        title: '选择组织',
        url: '/erp/hxl.erp.org.tree',
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
      },
      hidden: !enable_organization,
    },
    {
      label: '应用门店',
      name: 'store_ids',
      type: 'inputDialog',
      dependencies: ['org_ids'],
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
      rules: [{ required: true, message: '应用门店必填' }],
      dialogParams: (form) => {
        const data = {
          org_ids: enable_organization && form?.org_ids ? form?.org_ids : [],
          filter_org_levels: [1, 3],
        };
        if (!form?.org_ids) {
          delete data.org_ids;
        }
        return {
          type: 'store',
          dataType: 'lists',
          isLeftColumn: true,
          isMultiple: true,
          data: {
            ...data,
          },
        };
      },
    },
    {
      label: '商品档案',
      name: 'item_ids',
      type: 'inputDialog',
      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isMultiple: true,
      },
    },
    {
      label: '制单人',
      name: 'create_by',
      type: 'input',
      check: true,
      clear: true,
    },
    {
      label: '审核人',
      name: 'audit_by',
      type: 'input',
      check: true,
      clear: true,
    },
  ];
  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
    },
    {
      name: '单据号',
      code: 'fid',
      width: columnWidthEnum.fid,
      features: { sortable: true },
      align: 'left',
      render: (value: any, record: any, index: any) => {
        return (
          <>
            <div className="cursors">
              <span
                className="link cursors"
                onClick={(e) => {
                  e.stopPropagation();
                  setRecord(record);
                  pageModalRef.current?.setOpen(true);
                }}
              >
                {value}
              </span>
            </div>
          </>
        );
      },
    },
    {
      name: '应用门店',
      code: 'stores',
      width: 160,
      features: { sortable: true },
      align: 'left',
      render: (value: any) => {
        return (
          <div style={{ display: 'flex' }}>
            <div style={{ padding: 0 }}>{value?.[0]?.store_name}</div>
            {value?.length > 1 && (
              <a
                className="abutten"
                onClick={(e) => {
                  e.stopPropagation();
                  onClickStore(value);
                }}
              >
                ({value?.length})
              </a>
            )}
          </div>
        );
      },
    },
    {
      name: '单据状态',
      code: 'state',
      width: columnWidthEnum.ORDER_STATE,
      features: { sortable: true },
      align: 'left',
      render: (value: any) => {
        const item = Options1.find((v) => v.value === value);
        return (
          <div className={`${item ? item.type : ''}`}>
            {item ? item.label : ''}
          </div>
        );
      },
    },
    {
      name: '应用状态',
      code: 'supply_state',
      width: 100,
      features: { sortable: true },
      align: 'left',
      render: (value: any) => {
        return (
          <div className={`${value === 'UN_SUPPLY' ? '' : 'success'}`}>
            {value === 'UN_SUPPLY' ? '未应用' : '已应用'}
          </div>
        );
      },
    },
    {
      name: '应用时间',
      code: 'supply_date',
      width: 90,
      features: { sortable: true, format: 'TIME' },
      align: 'left',
      // render: (value: any) => {
      //   return <div> {value?.slice(0, 10)}</div>;
      // },
    },
    {
      name: '制单人',
      code: 'create_by',
      width: columnWidthEnum.BY,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '审核人',
      code: 'audit_by',
      width: columnWidthEnum.BY,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '处理人',
      code: 'handle_by',
      width: columnWidthEnum.BY,
      features: { sortable: true },
      align: 'left',
    },
    {
      name: '制单时间',
      code: 'create_time',
      width: columnWidthEnum.TIME,
      features: { sortable: true, format: 'TIME' },
      align: 'left',
    },
    {
      name: '审核时间',
      code: 'audit_time',
      width: columnWidthEnum.TIME,
      features: { sortable: true, format: 'TIME' },
      align: 'left',
    },
    {
      name: '处理时间',
      code: 'handle_time',
      width: columnWidthEnum.TIME,
      features: { sortable: true, format: 'TIME' },
      align: 'left',
    },
    {
      name: '留言备注',
      code: 'memo',
      width: columnWidthEnum.MEMO,
      features: { sortable: true },
      align: 'left',
      render: (value: any, record: any) => {
        return (
          <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
            <div className="info overwidth"> {value}</div>
          </Tooltip>
        );
      },
    },
    {
      name: '失效日期',
      code: 'invalid_time',
      width: columnWidthEnum.MEMO,
      features: { sortable: true, format: 'TIME' },
      align: 'left',
    },
    {
      name: '失效人',
      code: 'invalid_by',
      width: columnWidthEnum.MEMO,
      features: { sortable: true },
      align: 'left',
    },
  ];
  const setJudgment = () => {
    return (
      !form.getFieldValue('store_ids') &&
      !form.getFieldValue('org_ids') &&
      !form.getFieldValue('item_ids')
    );
  };
  const prevPost = async () => {
    if (setJudgment()) {
      message.warning('应用门店不能为空');
      return false;
    }
    return getParams(pagin?.pageNum);
  };
  const onClickStore = async (data: any[]) => {
    await XlbInputDialogByRead({
      maskClosable: true,
      columns: InputDialogColumns, // 表头信息
      title: '应用门店',
      selectedList: data, // 源数据 对象数组  selectedList.length > 0 时，则不查询接口
    });
  };

  const getParams = (pageNum: number) => {
    // const { compactDatePicker } = form.getFieldsValue(true);
    const compactDatePicker = [
      form.getFieldValue('compactDatePicker')?.[0] + ' 00:00:00',
      form.getFieldValue('compactDatePicker')?.[1] + ' 23:59:59',
    ];

    const data = {
      ...form.getFieldsValue(),
      store_ids: form.getFieldValue('store_ids')
        ? form.getFieldValue('store_ids')
        : null,
      audit_date:
        form.getFieldValue('time_type') === 'audit_date'
          ? compactDatePicker
          : null,
      create_date:
        form.getFieldValue('time_type') === 'create_date'
          ? compactDatePicker
          : null,
      handle_date:
        form.getFieldValue('time_type') === 'handle_date'
          ? compactDatePicker
          : null,
      org_ids: form.getFieldValue('org_ids')
        ? form.getFieldValue('org_ids')
        : [],
      item_ids: form.getFieldValue('item_ids')
        ? form.getFieldValue('item_ids')
        : null,
      page_size: pagin.pageSize,
      page_number: pageNum,
    };
    return data;
  };

  const exportItem = async (e: any) => {
    const data = getParams(1);
    setisLoading(true);
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.export',
      data,
    );
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success(res.data);
    }
    setisLoading(false);
  };
  // 导出明细
  const exportItemDetail = async (e: any) => {
    const chooseListFids = selectRowRef?.current?.map((v) => v.fid);
    const data = {
      fids: chooseListFids,
    };
    setisLoading(true);
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.detailbatch.export',
      data,
    );
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success(res.data);
    }
    setisLoading(false);
  };
  // 初始化表单数据
  const getFormData = () => {
    const formData = LStorage.get('deliveryPriceMange');
    if (formData) {
      form.setFieldsValue({
        ...formData,
        start_time: moment(formData.start_time),
        end_time: moment(formData.end_time),
      });
    } else {
      form.setFieldsValue({
        time_desc: 0,
        time_type: 'create_date',
        start_time: moment(),
        end_time: moment(),
      });
    }
  };
  //删除
  const deleteItem = async () => {
    let errFid: any = [];
    dataSourceRef?.current?.map((v) => {
      selectRowRef?.current?.map((j) => {
        v.fid === j && v.state !== 'INIT' && errFid.push(j);
      });
    });
    if (errFid.length > 0) {
      XlbTipsModal({
        tips: '只能删除单据状态为制单的单据!',
      });
      return;
    }
    await XlbTipsModal({
      tips: `已选择${selectRowRef?.current?.length}张单据，是否确认删除!`,
      onOkBeforeFunction: async () => {
        const deleteFids = selectRowRef?.current?.map((v) => v.fid);
        const res = await Api.deleteInfo({ fids: deleteFids });
        if (res.code === 0) {
          XlbTipsModal({
            tips: `已删除${deleteFids.length}张单据!`,
          });
          refresh();
        }
        return true;
      },
    });
  };

  //复制
  const copyItem = async () => {
    if (selectRowRef?.current?.length > 1) {
      XlbTipsModal({
        tips: '复制不支持批量操作!',
      });
      return;
    } else if (selectRowRef?.current?.length === 1) {
      console.log(selectRowRef, 'selectRowRef======>');

      await XlbTipsModal({
        tips: `是否确认复制单据"${selectRowRef?.current?.[0].fid}"?`,
        onOkBeforeFunction: async () => {
          const res = await Api.copyInfo({
            fid: selectRowRef?.current?.[0]?.fid,
          });
          if (res.code === 0) {
            XlbTipsModal({
              tips: `已生成新的单据"${res.data.fid}"!`,
            });
            refresh();
          }
          return true;
        },
      });
    }
  };
  const onValuesChange = (changedValues: any, allValues: any) => {
    if (Object.keys(changedValues).includes('org_ids')) {
      form.setFieldsValue({
        store_ids: [],
      });
    }
  };
  useEffect(() => {
    form.setFieldsValue({
      time_type: 'create_date',
      compactDatePicker: [
        moment().format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD'),
      ],
    });
  }, []);
  useEffect(() => {
    getFormData();
    const params = history.location.state as any;
    if (params && params.refreshPage) {
      refresh();
    }
  }, []);

  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={(back: boolean) => {
                  if (back) {
                    pageConatainerRef?.current?.pageContainerRef?.current?.fetchData?.();
                  }
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbPageContainer
        ref={pageConatainerRef}
        url={'/erp/hxl.erp.deliverypriceadjust.page'}
        tableColumn={tableList}
        prevPost={prevPost}
        immediatePost={false}
      >
        <ToolBtn showColumnsSetting>
          {({
            fetchData,
            loading,
            dataSource,
            requestForm,
            selectRowKeys,
            selectRow,
            form,
          }: ContextState<any>) => {
            refresh = fetchData;
            selectRowRef.current = selectRow;
            dataSourceRef.current = dataSource;
            formRef.current = form;
            return (
              <XlbButton.Group>
                {hasAuth(['配送价调整', '查询']) && (
                  <XlbButton
                    label="查询"
                    type="primary"
                    disabled={isLoading}
                    onClick={() => {
                      fetchData();
                    }}
                    icon={<XlbIcon name="sousuo" />}
                  />
                )}
                {hasAuth(['配送价调整', '编辑']) && (
                  <XlbButton
                    label="新增"
                    type="primary"
                    disabled={isLoading}
                    onClick={() => {
                      setRecord({ fid: 1 });
                      pageModalRef.current?.setOpen(true);
                    }}
                    icon={<XlbIcon name="jia" />}
                  />
                )}
                {hasAuth(['配送价调整', '删除']) && (
                  <XlbButton
                    label="删除"
                    type="primary"
                    disabled={!selectRowKeys?.length || isLoading}
                    onClick={deleteItem}
                    icon={<XlbIcon name={'shanchu'} />}
                  />
                )}
                {hasAuth(['配送价调整', '导出']) && (
                  <XlbDropdownButton
                    label={'导出'}
                    style={{ marginRight: '8px' }}
                    dropList={[
                      {
                        label: '导出',
                        disabled: !dataSource?.length,
                      },
                      {
                        label: '导出明细',
                        disabled: !selectRowKeys?.length,
                      },
                    ]}
                    dropdownItemClick={(index, item, e) => {
                      if (item?.label == '导出') {
                        exportItem(e);
                      } else if (item?.label == '导出明细') {
                        exportItemDetail(e);
                      }
                    }}
                  />
                )}
                {hasAuth(['配送价调整/复制', '编辑']) && (
                  <XlbButton
                    label="复制"
                    type="primary"
                    disabled={!selectRowKeys?.length || isLoading}
                    onClick={copyItem}
                    icon={<XlbIcon name="fuzhi" />}
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm
            formList={formList}
            form={form}
            isHideDate={true}
            getFormRecord={() => refresh()}
            onValuesChange={onValuesChange}
          />
        </SearchForm>
        <Table key="fid" selectMode="multiple" />
      </XlbPageContainer>
    </>
  );
};
export default deliveryPriceMange;