import { XlbFetch as ErpRequest } from '@xlb/utils';
export default class Api {
  //获取数据
  static getAllList = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliverypriceadjust.page', data);
  };
  //新增
  static addInfo = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.campaign.save', data);
  };
  //删除
  static deleteInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.campaign.batchdelete',
      data,
    );
  };
  //读取
  static readInfo = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.campaign.read', data);
  };
  //审核
  static auditInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.campaign.audit',
      data,
    );
  };
  //更新
  static updateInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.campaign.update',
      data,
    );
  };
  //通过
  static handleInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.campaign.handle',
      data,
    );
  };
  //拒绝
  static refuseInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.handlerefuse',
      data,
    );
  };
  //终止
  static abortInfo = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.handleabort',
      data,
    );
  };
  //查询中心配送门店
  static getCenterStore = async () => {
    return await ErpRequest.post('/erp/hxl.erp.store.center.find');
  };
  // 获取配送价
  static getGoodsPrice = async (data: any) => {
    return await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.deliveryprice.page',
      data,
    );
  };

  // 复制
  static copyInfo = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliverypriceadjust.copy', data);
  };

  // 获取模板数据
  static getNotice = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.noticetemplate.find', data);
  };

  // 获取模板内容
  static getNoticeContent = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.noticetemplate.read', data);
  };

}