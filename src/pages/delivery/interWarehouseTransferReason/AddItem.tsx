import NiceModal from '@ebay/nice-modal-react';
import { XlbBasicForm, XlbForm, XlbMessage, XlbModal } from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { useEffect, useState } from 'react';
const AddItem = (props: any) => {
  const { fetchData, record, fetchTreeData, level } = props;
  const [form] = XlbBasicForm.useForm();
  const modal = NiceModal.useModal();
  const [reasonList, setReasonList] = useState([]);
  const handleOk = async (values: any) => {
    const data = {
      ...values,
      id: level == 1 ? record?.level_one_id : record?.level_two_id,
    };
    const res = await XlbFetch.post(
      record?.level_one_id || record?.level_two_id
        ? '/erp/hxl.erp.warehousetransferreason.update'
        : '/erp/hxl.erp.warehousetransferreason.save',
      data,
    );
    if (res?.code == 0) {
      modal.hide();
      fetchData();
      fetchTreeData();
      XlbMessage.success('操作成功');
    }
  };
  const getReasonList = async () => {
    const data = {
      level: 1,
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.warehousetransferreason.find',
      data,
    );
    if (res?.code == 0) {
      setReasonList(
        res.data
          ?.filter((item) => item.level == 1)
          ?.map((v) => {
            return {
              ...v,
              label: v.name,
              value: v.id,
            };
          }),
      );
    }
  };
  const readInfo = async (record: any) => {
    const data = {
      id: level == 1 ? record?.level_one_id : record?.level_two_id,
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.warehousetransferreason.read',
      data,
    );
    if (res?.code == 0) {
      form?.setFieldsValue({
        parent_id: res.data.parent_id,
        name: res.data.name,
        enabled: res.data.enabled,
      });
    }
  };
  useEffect(() => {
    if (modal.visible) {
      getReasonList();
      if (record?.level_one_id || record?.level_two_id) {
        readInfo(record);
      }
    }
  }, [modal.visible]);
  return (
    <XlbModal
      width={450}
      open={modal.visible}
      title={
        record?.level_one_id || record?.level_two_id
          ? '编辑调拨原因'
          : '新增调拨原因'
      }
      isCancel={true}
      onOk={async () => {
        handleOk(form?.getFieldsValue(true));
      }}
      onCancel={() => {
        form.resetFields();
        modal.resolve(false);
        modal.hide();
      }}
    >
      <div style={{ marginLeft: '12px' }}>
        <XlbForm
          form={form}
          isHideDate={true}
          style={{ marginTop: '12px' }}
          initialValues={{
            enabled: true,
          }}
          formList={[
            {
              label: '上级调拨原因',
              name: 'parent_id',
              type: 'select',
              width: 180,
              showSearch: true,
              placeholder: '请选择',
              disabled: record?.level_one_id || record?.level_two_id,
              options: reasonList,
            },
            {
              label: '调拨原因名称',
              name: 'name',
              type: 'input',
              width: 180,
              placeholder: '请输入',
              rules: [{ required: true, message: '请输入调拨原因' }],
            },
            {
              label: '状态',
              name: 'enabled',
              type: 'radio',
              rules: [{ required: true, message: '请选择状态' }],
              options: [
                {
                  label: '启用',
                  value: true,
                },
                {
                  label: '停用',
                  value: false,
                },
              ],
            },
          ]}
        />
      </div>
    </XlbModal>
  );
};
export default NiceModal.create(AddItem);
