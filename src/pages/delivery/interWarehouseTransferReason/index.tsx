import { hasAuth } from '@/utils/kit';
import NiceModal from '@ebay/nice-modal-react';
import type { ExposeConfig } from '@xlb/components';
import {
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbLayout,
  XlbPageContainer,
  XlbTableColumnProps,
  XlbTree,
} from '@xlb/components';

import AddItem from './AddItem';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const interWarehouseTransferReason = () => {
  let treeInstanceConfig: ExposeConfig | null = null;
  let refresh = () => {};
  const [form] = XlbBasicForm.useForm();
  const treeCallBack = (node) => {
    form.setFieldsValue({
      id: node.id,
    });
    refresh();
  };
  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 80,
      align: 'center',
    },
    {
      name: '一级调拨原因',
      code: 'level_one_name',
      width: 160,
      align: 'left',
      features: { sortable: true },
      render: (text, record, index) => {
        return (
          <span className="link" onClick={() => add(record, 1)}>
            {text}
          </span>
        );
      },
    },
    {
      name: '二级调拨原因',
      code: 'level_two_name',
      width: 160,
      features: { sortable: true },
      align: 'left',
      render: (text, record, index) => {
        return (
          <span className="link" onClick={() => add(record, 2)}>
            {text}
          </span>
        );
      },
    },
    {
      name: '最近更新人',
      code: 'update_by',
      features: { sortable: true },
      width: 160,
      align: 'left',
    },
    {
      name: '最近更新时间',
      code: 'update_time',
      features: { sortable: true, format: 'TIME' },
      width: 160,
      align: 'left',
    },
  ];
  const formList: SearchFormType[] = [
    {
      label: '关键字',
      name: 'keyword',
      type: 'input',
      allowClear: true,
      check: true,
    },
  ];
  const prevPost = () => {
    return {
      ...form.getFieldsValue(true),
    };
  };
  const add = (record?, level?: number) => {
    NiceModal.show(AddItem, {
      fetchData: refresh,
      fetchTreeData: treeInstanceConfig?.refresh,
      record: record,
      level: level,
    });
  };
  return (
    <XlbLayout
      leftNode={
        <>
          <div
            style={{
              backgroundColor: '#FFF',
              height: '100%',
              overflow: 'auto',
            }}
          >
            <XlbTree
              url="/erp/hxl.erp.warehousetransferreason.find"
              type="base"
              onExpose={(config) => {
                treeInstanceConfig = config;
              }}
              dataType={'lists'}
              // topLevelTreeDisabled
              onSelect={(node) => treeCallBack(node)}
              // fieldName={{ parent_id: 'parent_id', id: 'id' }}
            />
          </div>
        </>
      }
      rightNode={
        <XlbPageContainer
          url={'/erp/hxl.erp.warehousetransferreason.find.level'}
          tableColumn={tableList}
          prevPost={prevPost}
          immediatePost={true}
        >
          <ToolBtn showColumnsSetting>
            {({
              fetchData,
              loading,
              dataSource = [],
              selectRowKeys = [],
              selectRow = [],
              requestForm,
            }) => {
              refresh = fetchData;
              return (
                <XlbButton.Group>
                  <XlbButton
                    key="query"
                    label="查询"
                    type="primary"
                    disabled={loading}
                    onClick={() => {
                      fetchData();
                    }}
                    icon={<span className="iconfont icon-sousuo" />}
                  />
                  {hasAuth(['仓间调拨原因', '编辑']) && (
                    <XlbButton
                      label="新增"
                      type="primary"
                      onClick={() => add()}
                      icon={<XlbIcon name="jia" />}
                    />
                  )}
                </XlbButton.Group>
              );
            }}
          </ToolBtn>
          <SearchForm>
            <XlbForm formList={formList} form={form} isHideDate={true} />
          </SearchForm>
          <Table keepDataSource={false} primaryKey="id" selectMode="single" />
        </XlbPageContainer>
      }
    ></XlbLayout>
  );
};
export default interWarehouseTransferReason;
