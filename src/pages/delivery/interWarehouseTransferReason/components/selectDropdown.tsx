import { PlusOutlined } from '@ant-design/icons';
import { XlbButton, XlbIcon, XlbSelect } from '@xlb/components';
import { Divider, Tree } from 'antd';
import React, { useMemo, useState } from 'react';

type TreeNode = {
  title: string;
  key: string;
  children?: TreeNode[];
};

const defaultTreeData: TreeNode[] = [
  {
    title: '一级调拨原因',
    key: '1',
    children: [
      { title: '二级调拨原因1', key: '1-1' },
      { title: '二级调拨原因2', key: '1-2' },
    ],
  },
  {
    title: '一级调拨原因2',
    key: '2',
    children: [
      { title: '二级调拨原因3', key: '2-1' },
      { title: '二级调拨原因4', key: '2-2' },
    ],
  },
];

interface ReasonSelectProps {
  value?: string;
  onChange?: (val: string) => void;
  treeData?: TreeNode[];
  onAddReason?: () => void;
  placeholder?: string;
  style?: React.CSSProperties;
}

const ReasonSelect: React.FC<ReasonSelectProps> = ({
  value,
  onChange,
  treeData = defaultTreeData,
  onAddReason,
  placeholder = '请选择调拨原因',
  style = { width: 300 },
}) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  const filteredTreeData = useMemo(() => {
    if (!searchValue) return treeData;

    const match = (node: TreeNode): TreeNode | null => {
      const isMatched = node.title
        .toLowerCase()
        .includes(searchValue.toLowerCase());

      if (isMatched) {
        // 如果自己匹配到了，返回不带 children 的节点
        return { ...node, children: undefined };
      }

      if (node.children) {
        const matchedChildren = node.children
          .map(match)
          .filter((n): n is TreeNode => !!n);
        if (matchedChildren.length) {
          return { ...node, children: matchedChildren };
        }
      }

      return null;
    };

    return treeData.map(match).filter((n): n is TreeNode => !!n);
  }, [searchValue, treeData]);

  const handleTreeSelect = (_: any, info: any) => {
    onChange?.(info.node.title);
    setDropdownVisible(false);
    setSearchValue(''); // 选完后清空搜索
  };

  const renderDropdown = () => {
    const isSearching = searchValue.length > 0
    return (
      <>
        {filteredTreeData.length > 0 ? (
          <Tree
            treeData={filteredTreeData}
            defaultExpandAll
            onSelect={handleTreeSelect}
            selectable
          />
        ) : isSearching ? (
          <div style={{ padding: '16px', textAlign: 'center', color: '#999' }}>
            {/* 缺省图或提示信息 */}
            <div>暂无匹配结果</div>
          </div>
        ) : null}
        <Divider style={{ margin: '4px 0' }} />
        <div
          style={{
            padding: '8px 12px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: 2,
            textAlign: 'center',
          }}
        >
          <XlbIcon
            color="#3d66fe"
            style={{ marginBottom: 2 }}
            name="jia"
          ></XlbIcon>
          <XlbButton
            type="link"
            icon={<PlusOutlined />}
            onClick={() => {
              onAddReason?.();
            }}
          >
            添加原因
          </XlbButton>
        </div>
      </>
    );
  };

  return (
    <XlbSelect
      open={dropdownVisible}
      onDropdownVisibleChange={setDropdownVisible}
      value={value}
      placeholder={placeholder}
      allowClear
      dropdownRender={renderDropdown}
      showSearch
      style={style}
      onClear={() => {
        onChange?.('');
        setSearchValue('');
      }}
      onSearch={setSearchValue}
      filterOption={false} // 关闭默认过滤
    />
  );
};

export default ReasonSelect;
