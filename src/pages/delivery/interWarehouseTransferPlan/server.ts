import { XlbFetch } from '@xlb/utils';

// 新增
export const save = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.warehousetransferplan.save', data);
};
// 编辑
export const update = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.warehousetransferplan.update', data);
};
// 查看
export const read = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.warehousetransferplan.read', data);
};
// 批量删除
export const batchDelete = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.warehousetransferplan.batchdelete', data);
};
// 审核
export const audit = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.warehousetransferplan.audit', data);
};
// 批量审核
export const batchAudit = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.warehousetransferplan.batchaudit', data);
};
// 批量处理
export const batchhandle = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.requisitionorder.batchhandle', data);
};
// 处理
export const handle = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.requisitionorder.handle', data);
};
// 批量批复
export const batchapprove = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.requisitionorder.batchapprove',
    data,
  );
};
// 反审核
export const unaudit = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.requisitionorder.reaudit', data);
};
// 批复
export const approve = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.requisitionorder.approve', data);
};
// 复制
export const copy = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.warehousetransferplan.copy', data);
};
// 作废
export const invalid = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.warehousetransferplan.batchinvalid', data);
};
// 列表导出
export const exportData = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.warehousetransferplan.export', data);
};
// 详情导出
export const detailexport = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.warehousetransferplan.detail.export',
    data,
  );
};
// 明细导出
export const detailbatchexport = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.warehousetransferplan.detail.batchexport',
    data,
  );
};
// 打印
export const print = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.requisitionorder.print', data);
};
// 审核前检查
export const check = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.requisitionorder.check', data);
};
// oa同意
export const approvalorderComplete = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.warehousetransferplan.approvalorder.complete', data);
};
// oa驳回
export const approvalorderReject = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.warehousetransferplan.approvalorder.reject', data);
};