import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

export const monthList = [
  {
    label: '1月',
    value: '1',
  },
  {
    label: '2月',
    value: '2',
  },
  {
    label: '3月',
    value: '3',
  },
  {
    label: '4月',
    value: '4',
  },
  {
    label: '5月',
    value: '5',
  },
  {
    label: '6月',
    value: '6',
  },
  {
    label: '7月',
    value: '7',
  },
  {
    label: '8月',
    value: '8',
  },
  {
    label: '9月',
    value: '9',
  },
  {
    label: '10月',
    value: '10',
  },
  {
    label: '11月',
    value: '11',
  },
  {
    label: '12月',
    value: '12',
  },
];

export const stateOptions = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
  {
    label: '审批中',
    value: 'APPROVE_STATUS_ING',
    type: 'warning',
  },
  // {
  //   label: '处理通过',
  //   value: 'PASS',
  //   type: 'success',
  // },
  {
    label: '审批通过',
    value: 'APPROVE_STATUS_AGREE',
    type: 'success',
  },
  {
    label: '审批驳回',
    value: 'APPROVE_STATUS_REFUSE', // 枚举修改
    type: 'danger',
  },
];
export const tableStateOptions = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
  {
    label: '审批中',
    value: 'APPROVE_STATUS_ING',
    type: 'warning',
  },
  {
    label: '作废',
    value: 'INVALID',
    type: 'danger',
  },
  {
    label: '审批通过',
    value: 'APPROVE_STATUS_AGREE',
    type: 'success',
  },
  {
    label: '审批驳回',
    value: 'APPROVE_STATUS_REFUSE', // 枚举修改
    type: 'danger',
  },
  {
    label: '审批撤回',
    value: 'APPROVE_STATUS_CANCEL', // 枚举修改
    type: 'danger',
  },
];
export const formList: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
  },
  {
    type: 'select',
    name: 'state',
    label: '单据状态',
    options: stateOptions,
  },
  {
    type: 'input',
    name: 'fid',
    label: '单据号',
    tooltip: '单据号不受其他查询条件限制',
  },
  {
    type: 'select',
    name: 'time_type',
    label: '时间类型',
    allowClear: false,
    options: [
      //制单时间、审核时间、审批时间、作废时间
      {
        label: '制单时间',
        value: 'create_date',
      },
      {
        label: '审核时间',
        value: 'audit_date',
      },
      {
        label: '审批时间',
        value: 'approve_date',
      },
      {
        label: '作废时间',
        value: 'invalid_date',
      },
    ],
  },
  {
    label: '调入组织',
    name: 'in_org_ids',
    type: 'select',
    multiple: true,
    clear: true,
    options: [],
    // @ts-ignore
    onChange: (e: any, formData: any) => {
      formData.setFieldsValue({
        store_ids: [],
      });
    },
    selectRequestParams: (params: Record<string, any>) => {
      return {
        url: '/erp-mdm/hxl.erp.org.find',
        responseTrans(data) {
          const options = data.map((item: any) => {
            const obj = {
              label: item.name,
              value: item.id,
            };
            return obj;
          });
          return options;
        },
      };
    },
  },
  {
    label: '调入门店',
    name: 'in_store_ids',
    type: 'inputDialog',
    dependencies: ['in_org_ids'],
    dialogParams: (params: any) => {
      return {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
          org_ids: params?.in_org_ids || [],
        },
      };
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '调出门店',
    name: 'out_store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '计划类型',
    name: 'plan_type',
    type: 'select',
    allowClear: true,
    options: [
      {
        label: '计划调拨',
        value: 'PLAN_TRANSFER',
      },
      {
        label: '临时调拨',
        value: 'TEMP_TRANSFER',
      },
    ],
  },
  {
    label: '计划月份',
    name: 'plan_month',
    type: 'select',
    allowClear: true,
    hidden: true,
    options: monthList,
  },
  {
    label: '一级调拨原因',
    name: 'parent_reason_ids',
    type: 'select',
    allowClear: true,
    multiple: true,
    onChange: (e: any, formData: any) => {
      formData.setFieldsValue({
        reason_ids: [],
      });
    },
    selectRequestParams: (params: Record<string, any>) => {
      return {
        url: '/erp/hxl.erp.warehousetransferreason.find',
        postParams: {
          level: 1,
        },
        responseTrans(data) {
          const options = data.map((item: any) => {
            const obj = {
              label: item.name,
              value: item.id,
            };
            return obj;
          });
          return options;
        },
      };
    },
  },
  {
    label: '二级调拨原因',
    name: 'reason_ids',
    type: 'select',
    multiple: true,
    allowClear: true,
    dependencies: ['parent_reason_ids'],
    selectRequestParams: (params: Record<string, any>) => {
      console.log('🚀 ~ params:', params);
      if (!params?.parent_reason_ids || !params?.parent_reason_ids?.length) {
        return;
      }
      return {
        url: '/erp/hxl.erp.warehousetransferreason.find',
        postParams: {
          level: 2,
          parent_ids: params?.parent_reason_ids,
        },
        responseTrans(data) {
          const options = data.map((item: any) => {
            const obj = {
              label: item.name,
              value: item.id,
            };
            return obj;
          });
          return options;
        },
      };
    },
  },
];

export const tableColumn = [
  {
    name: '序号',
    code: '_index',
    width: 65,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: 200,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '调拨类型',
    code: 'plan_type',
    width: 100,
    features: { sortable: true },
    align: 'left',
    render: (t) => t === 'PLAN_TRANSFER' ? '计划调拨' : '临时调拨',
  },
  {
    name: '调入组织',
    code: 'in_org_id',
    width: 100,
    features: { sortable: true },
    align: 'left',
    render: (text: any, record: any) => {
      return record.in_org_name;
    },
  },
  {
    name: '调入门店',
    code: 'in_store_id',
    width: 100,
    features: { sortable: true },
    align: 'left',
    render: (text: any, record: any) => {
      return record.in_store_name;
    },
  },
  {
    name: '计划月份',
    code: 'plan_month',
    width: 100,
    features: { sortable: true },
    align: 'left',
    render: (text: any, record: any) => {
      return monthList.find((item: any) => item.value === text)?.label || '';
    },
  },
  {
    name: '调出门店数量',
    code: 'out_store_count',
    width: 133,
    features: { format: 'QUANTITY' },
    align: 'right',
    // render: (text: any, record: any) => {
    //   return record.out_store_name;
    // },
  },
  {
    name: '商品数',
    code: 'item_count',
    width: 100,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  // {
  //   name: '件数',
  //   code: 'quantity1',
  //   width: 100,
  //   features: {
  //     sortable: true,
  //     Tooltip: '调拨计划单包含的所有商品总件数，取基本单位',
  //   },
  //   align: 'right',
  // },
  {
    name: '单据状态',
    code: 'state',
    width: 100,
    features: {
      sortable: true,
    },
    align: 'left',
    render: (value: any) => {
      const item = tableStateOptions.find((item: any) => item.value === value);
      return (
        <div className={`${item ? item?.type : ''}`}>
          {item ? item?.label : ''}
        </div>
      );
    },
  },
  // {
  //   name: '供应商起订量',
  //   code: 'supplier_initial_quantity',
  //   width: 140,
  //   features: { sortable: true },
  //   align: 'right',
  // },
  // {
  //   name: '单品起订量',
  //   code: 'item_initial_quantity',
  //   width: 100,
  //   features: { sortable: true },
  //   align: 'right',
  // },
  {
    name: '月度调拨量(预估)',
    code: 'quantity',
    width: 160,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '实际调拨数量',
    code: 'actual_quantity',
    width: 140,
    features: { format: 'QUANTITY' },
    align: 'right',
    render: (text: any) => {
      return text || 0;
    },
  },
  {
    name: '调拨费用',
    code: 'money',
    width: 100,
    features: { format: 'MONEY' },
    align: 'right',
  },
  {
    name: '制单人',
    code: 'create_by',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '审批人',
    code: 'approve_by',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '作废人',
    code: 'invalid_by',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 150,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 150,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '审批时间',
    code: 'approve_time',
    width: 150,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '作废时间',
    code: 'invalid_time',
    width: 150,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '作废原因',
    code: 'invalid_reason',
    width: 150,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '留言备注',
    code: 'memo',
    width: 150,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '关联仓间调拨单',
    code: 'transfer_order_fids',
    width: 150,
    // features: { sortable: true },
    align: 'left',
    render: (t: any) => typeof t === 'string' ? t : Array.isArray(t) ? t?.join(',') : ''
  },
];

//商品明细
export const itemTableListDetail: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '操作',
    code: '_operator',
    align: 'center',
    width: 80,
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品一级分类',
    code: 'one_item_category_name',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '供应商名称',
    code: 'supplier_name',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '供应商起订量',
    code: 'supplier_initial_quantity',
    width: 140,
    features: { sortable: true },
    align: 'right',
    render: (t: any) => (Number(t || 0) || 0).toFixed(3)
  },
  {
    name: '单品起订量',
    code: 'item_initial_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
    render: (t: any) => (Number(t || 0) || 0).toFixed(3)
  },
  {
    name: '单位',
    code: 'unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '月度调拨量(预估)',
    code: 'quantity',
    width: 146,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '实际调拨量',
    code: 'actual_quantity',
    width: 110,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
    render: (text: any) => {
      return text || 0;
    },
  },
  {
    name: '调拨费用',
    code: 'money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  // {
  //   name: '一级调拨原因',
  //   code: 'first_reason_id',
  //   width: 160,
  //   features: { sortable: true },
  //   align: 'left',
  // },
  {
    name: '调拨原因',
    code: 'reason_id',
    width: 240,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '备注',
    code: 'memo',
    width: 160,
    features: { sortable: true },
    align: 'left',
  },
];