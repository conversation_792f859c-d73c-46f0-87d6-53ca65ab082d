import NiceModal, { useModal } from '@ebay/nice-modal-react'
import { ModalProps } from 'antd'
import React, { FC, useState } from 'react'
import { XlbBaseUpload, XlbBasicForm, XlbInput, XlbMessage, XlbModal } from '@xlb/components'
import styles from '../index.less'
import { LStorage } from '@/utils'
import { invalid, detailexport, approvalorderComplete, approvalorderReject } from '../server';

interface ApprovalModalProps extends ModalProps {
  type: any
  fid: any
}

const { TextArea } = XlbInput

const ApprovalModal: FC<ApprovalModalProps> = ({ type, fid }) => {
  const modal = useModal()
  const [form] = XlbBasicForm.useForm()
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const handleAuditOk = async () => {
    const comment = form.getFieldValue('comment')
    if (type !== 1 && !comment) {  // 业务希望同意的时候，审批意见不是必填，驳回的时候必填
      return XlbMessage.error('请输入审核意见')
    }
    setIsLoading(true)
    const uesrInfo = LStorage.get('userInfo') || {}
    const serv = type === 1 ? approvalorderComplete : approvalorderReject
    const params = form.getFieldsValue()
    const res = await serv({
      fid,
      ...params,
      company_id: uesrInfo.company_id
    })
    setIsLoading(false)
    if (res?.code === 0) {
      modal.resolve(true)
      form.setFieldsValue({ comment: '', files: [] })
      modal.hide()
    }
  }

  return (
    <XlbModal
      title="审批"
      bordered={false}
      open={modal.visible}
      width={430}
      wrapClassName="xlbDialog"
      onOk={handleAuditOk}
      maskClosable={false}
      isCancel={true}
      cancelText={'取消'}
      okText={type === 1 ? '同意' : '驳回'}
      onCancel={() => {
        form.setFieldsValue({ comment: '', files: [] })
        modal.resolve(false)
        modal.hide()
      }}
      confirmLoading={isLoading}
    >
      <div style={{ padding: '16px 0' }}>
        <XlbBasicForm autoComplete="off" layout="vertical" colon={false} form={form}>
          <XlbBasicForm.Item
            label="审核意见："
            name="comment"
            className={styles.txta}
            required={type !== 1} // 业务希望同意的时候，审批意见不是必填，驳回的时候必填
          >
            <TextArea maxLength={500} style={{ height: 100, resize: 'none' }}/>
          </XlbBasicForm.Item>
          {/*<XlbBasicForm.Item*/}
          {/*  name={'images'}*/}
          {/*  label=""*/}
          {/*>*/}
          {/*  <XlbUploadFileWithForm*/}
          {/*    accept={['image', 'file']}*/}
          {/*    listType="text"*/}
          {/*    maxCount={1}*/}
          {/*    name={''}*/}
          {/*    action={`/erp/hxl.erp.file.upload`}*/}
          {/*    data={{*/}
          {/*      refType: FilesType.LABOUR_BUSINESS_LICENSE,*/}
          {/*      fid*/}
          {/*    }}*/}
          {/*    deleteByServer={false}*/}
          {/*    onChange={(response) => {*/}
          {/*      // response![0]?.url && getOcr(response![0]?.url, type)*/}
          {/*    }}*/}
          {/*  />*/}
          {/*</XlbBasicForm.Item>*/}
          <XlbBasicForm.Item
            name={'files'}
            label=""
            normalize={(value: any) => {
              return value?.map((t: any) => t?.response?.data)
            }}
          >
            <XlbBaseUpload
              mode={'primaryButton'}
              listType="text"
              buttonModal={false}
              multiple={true}
              className={styles.uploadContetn}
              uploadText={'附件'}
              data={{ fid }}
              accept={['file', 'media']}
              subTitle={''}
              // fileList={files}
              action={`/erp/hxl.erp.storeitemattr.file.upload`}
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      </div>
    </XlbModal>
  )
}
export default NiceModal.create(ApprovalModal)