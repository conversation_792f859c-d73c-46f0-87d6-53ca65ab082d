import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbInputDialog, XlbInputNumber, XlbMessage,
  XlbModal,
  XlbTable, XlbTipsModal,
} from '@xlb/components';
import IconFont from '@xlb/components/dist/components/icon';
import { useEffect, useRef, useState } from 'react';
import { read } from '../server';
import { XlbFetch, XlbFetch as ErpRequest } from '@xlb/utils';

interface IProps {
  fid?: number|string,
  // item_id: number;
  // store_id: number;
  // storehouse_id: number;
}

const GenerateInterOrders = (props: IProps) => {
  const { fid } = props;
  const modal = NiceModal.useModal();
  const [detail, setDetail] = useState<any>(null);
  const [dataSource, setDataSource] = useState([]);
  const [allData, setAllData] = useState([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [planFidRules, setPlanFidRules] = useState<boolean>(false);
  const [form] = XlbBasicForm.useForm();
  const [invalidForm] = XlbBasicForm.useForm();
  const minimumNumber = useRef<any>(null)


  const getInStoreOrgSaleParam = async (data: any) => {
    const res = await XlbFetch.post('/erp/hxl.erp.deliveryparam.read', {})
    if (res?.code === 0) {
      const res2 = await XlbFetch.post('/erp-mdm/hxl.erp.org.find', {})
      const warehouse_transfer_check_plan_quantity_org_ids = res?.data?.warehouse_transfer_check_plan_quantity_org_ids
      const result = res2?.data.filter((item: any) =>
        warehouse_transfer_check_plan_quantity_org_ids?.includes(item.parent_id)
      )
      const orgList = result?.map((v: any) => v.id)
      if (orgList?.includes(data?.in_org_id)) {
        setPlanFidRules(true);
      } else {
        setPlanFidRules(false);
      }
    }
  }

  const getData = async () => {
    setLoading(true);
    const { code, data } = await read({
      fid
    });
    if (code === 0) {
      const dt = data?.details?.map((v: any) => ({
        ...v,
        _custemId: `${v?.out_store_id}-${v?.item_id}`,
        quantity: v?.balance_quantity || 0,
      })) || []
      setDataSource(dt);
      setAllData(dt)
      setDetail(data)
      minimumNumber.current = dt.length > 0
        ? Math.min(...dt.map((item: any) => item?.balance_quantity))
        : null;
      await getInStoreOrgSaleParam(data)
    }
    setLoading(false);
  };

  const filterByIds = (outStoreIds: any, itemIds: any) => {
    return allData.filter((item: any) => {
      const matchOutStore = !outStoreIds?.length || outStoreIds.includes(item.out_store_id);
      const matchItemId = !itemIds?.length || itemIds.includes(item.item_id);
      return matchOutStore && matchItemId;
    });
  };

  const filterData = () => {
    const fv = form.getFieldsValue();
    if (!fv?.store_ids?.length && !fv?.item_ids?.length) {
      setDataSource([...allData]);
    } else {
      setDataSource(filterByIds(fv?.store_ids||[], fv?.item_ids||[]));
    }
  };

  useEffect(() => {
    if (modal.visible) {
      getData();
    }
  }, [modal.visible]);

  useEffect(() => {
    // if (dataSource.length) {
    const map = new Map();
    allData?.forEach((item: any) => {
      map.set(item._custemId, item);
    });
    dataSource?.forEach((item: any) => {
      map.set(item._custemId, item);
    });
    setAllData(Array.from(map.values()));
    // }
  }, [dataSource]);

  return (
    <XlbModal
      title="商品数量限制"
      open={modal.visible}
      bodyStyle={{ paddingTop: 16 }}
      isCancel={true}
      confirmLoading={loading}
      onCancel={() => {
        modal.hide();
        setDataSource([]);
      }}
      onOk={async () => {
        const map = new Map();
        allData.forEach((item: any) => {
          map.set(item._custemId, item);
        });
        dataSource.forEach((item: any) => {
          map.set(item._custemId, item);
        });
        const params = {...detail, details: Array.from(map.values())}
        const invalidData = params?.details?.filter((item: any) => (item?.quantity || 0) <= 0) || []
        if (invalidData?.length) {
          XlbTipsModal({
            tips: (
              <div>
                {
                  invalidData?.map((item: any) => (
                    <div key={item.id}>
                      <div>【{item.item_name}】申请数量不能为0</div>
                    </div>
                  ))
                }
              </div>
             )
          })
          return
        }
        setLoading(true);
        const res = await ErpRequest.post('/erp/hxl.erp.warehousetransferplan.warehousetransferorder.save',  {...params})
        setLoading(false);
        if (res.code === 0) {
          modal.hide();
          XlbMessage.success(`已成功生成${res?.data}条仓间调拨单！`)
          setDataSource([]);
        }
      }}
      width={830}
    >
      <XlbButton.Group>
        <XlbButton
          type="primary"
          onClick={() => {
            filterData();
          }}
          icon={<XlbIcon size={16} name="sousuo" />}
        >
          查询
        </XlbButton>
        <XlbButton
          label="批量修改"
          type="primary"
          disabled={loading || !dataSource?.length}
          onClick={async () => {
            invalidForm.resetFields();
            const flag = await XlbTipsModal({
              title: '批量修改',
              isCancel: true,
              width: 380,
              tips: (
                <div style={{ margin: '20px 0 6px 12px' }}>
                  <XlbBasicForm form={invalidForm}>
                    <XlbBasicForm.Item
                      name="applications"
                      label="生成申请数量"
                      rules={[{
                        validator: (_, value) => {
                          if (!value && value !== 0) {
                            return Promise.reject(new Error('请输入生成申请数量'))
                          }
                          return Promise.resolve()
                        }
                      }]}
                    >
                      <XlbInputNumber
                        style={{ width: 180 }}
                        step={0.001}
                        min={0}
                        max={9999999}
                        precision={3}
                      />
                    </XlbBasicForm.Item>
                  </XlbBasicForm>
                </div>
              ),
              onOkBeforeFunction: async () => {
                try {
                  await invalidForm.validateFields();
                } catch (err: any) {
                  return false;
                }
                return true;
              },
            });
            if (flag) {
              const applications = invalidForm.getFieldValue('applications');
              if (planFidRules && applications && applications > minimumNumber.current) {
                XlbMessage.info(`生成申请数量不能大于剩余数量${minimumNumber.current}`);
                return
              }
              setDataSource((prev: any ) => {
                return prev.map((v: any) => ({
                  ...v,
                  quantity: applications
                }));
              });
            }
          }}
          icon={<IconFont name="xiugai" color="currentColor" size={16} />}
        />
      </XlbButton.Group>
      <XlbBasicForm style={{ marginTop: 16 }} form={form}>
        <XlbBasicForm.Item label="调出门店" name="store_ids">
          <XlbInputDialog
            dialogParams={{
              type: 'store',
              isMultiple: true,
              data: {
                center_flag: true,
                enable_organization: false,
              },
            }}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_name',
            }}
            width={156}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item name="item_ids" label="商品档案">
          <XlbInputDialog
            fieldNames={{
              idKey: 'id',
              nameKey: 'name',
            }}
            dialogParams={{
              type: 'goods',
              dataType: 'lists',
              isMultiple: true,
            }}
            width={168}
          ></XlbInputDialog>
        </XlbBasicForm.Item>
      </XlbBasicForm>
      <XlbTable
        isLoading={loading}
        key={fid}
        style={{ marginTop: 10, maxHeight: 400 }}
        columns={[
          {
            name: '序号',
            code: '_index',
            width: 60,
            align: 'center',
          },
          {
            name: '调出门店',
            code: 'out_store_name',
            width: 120,
            align: 'center',
          },
          {
            name: '商品一级分类',
            code: 'one_item_category_name',
            width: 120,
            align: 'center',
          },
          {
            name: '商品代码',
            code: 'item_code',
            width: 120,
            align: 'center',
          },
          {
            name: '商品名称',
            code: 'item_name',
            width: 120,
            align: 'center',
          },
          {
            name: '生成申请数量',
            code: 'quantity',
            width: 120,
            align: 'center',
            render: (value, record,  index) => {
              return (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <XlbInputNumber
                    style={{ width: 120 }}
                    step={0.001}
                    min={0}
                    max={9999999}
                    precision={3}
                    value={value}
                    // onBlur={(e) => {
                    //   console.log(e.target.value, 11);
                    //   console.log(isNumber(e.target.value));
                    // }}
                    onChange={(v) => {
                      let va = v
                      if (planFidRules && v && v > minimumNumber.current) {
                        va = record?.balance_quantity || 0
                        XlbMessage.info(`生成申请数量不能大于剩余数量${minimumNumber.current}`);
                      }
                      setDataSource((prev) => {
                        const newData = [...prev];
                        newData[index.index].quantity = va;
                        return newData;
                      });
                    }}
                  />
                </div>
              );
            },
          },
          {
            name: '剩余数量',
            code: 'balance_quantity',
            width: 120,
            align: 'center',
            features: { format: 'QUANTITY' }
            // render: (value, record) => {
            //   return (record.transfer_quantity - record.out_quantity) || 0
            // },
          },
        ]}
        total={dataSource.length}
        dataSource={dataSource}
        rowKey="id"
      />
    </XlbModal>
  );
};

export default NiceModal.create(GenerateInterOrders);