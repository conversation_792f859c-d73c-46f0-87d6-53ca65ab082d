import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth, LStorage } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbForm,
  XlbIcon,
  XlbInput,
  XlbMessage,
  XlbPageContainer,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTipsModal,
} from '@xlb/components';
import { history } from '@@/core/history'
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import GenerateInterOrders from './components/generateInterOrders';
import { formList, tableColumn } from './data';
import Item from './item';
import {
  batchAudit, batchDelete,
  copy,
  detailbatchexport,
  exportData,
  invalid,
} from './server';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { ExceptionOutlined } from '@ant-design/icons';

const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const InterWarehouseTransferPlan = () => {
  const { enable_organization } = useBaseParams((state) => state);
  const pageConatainerRef = useRef<XlbPageContainerRef>(null);
  const [record, setRecord] = useState<any>({});
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const [form] = XlbBasicForm.useForm<any>();
  const [invalidReasonForm] = XlbBasicForm.useForm<any>();
  const [form_list, setFormList] = useState(formList)

  const checkData = () => {
    const formData = form.getFieldsValue();
    const { compactDatePicker } = form.getFieldsValue(true);
    const data = {
      ...formData,
      audit_date:
        form.getFieldValue('time_type') === 'audit_date'
          ? compactDatePicker
          : null,
      approve_date:
        form.getFieldValue('time_type') === 'approve_date'
          ? compactDatePicker
          : null,
      create_date:
        form.getFieldValue('time_type') === 'create_date'
          ? compactDatePicker
          : null,
      invalid_date:
        form.getFieldValue('time_type') === 'invalid_date'
          ? compactDatePicker
          : null,
    };
    return data;
  };
  const prevPost = () => {
    return checkData();
  };
  const handleBatchDelete = async (ids: any, selectRow: any, setLoading: any) => {
    // 只删除制单状态的单据
    const initRow = selectRow.filter((v: any) => v.state !== 'INIT');
    if (initRow?.length > 0) {
      XlbMessage.error('仅支持制单状态的单据');
      return;
    }
    await XlbTipsModal({
      title: '提示',
      tips: '已选择' + ids?.length + '条数据，确定删除吗？',
      onOk: async () => {
        setLoading(true)
        const res = await batchDelete({ fids: ids });
        setLoading(false)
        if (res?.code === 0) {
          XlbMessage.success('删除成功');
          pageConatainerRef?.current?.fetchData?.();
        }
      },
    });
  };
  // cons  // 确认作废
  const handleSureCancel = async (selectRow: any, setLoading: any) => {
    // const initRow = selectRow.filter((v: any) => v.state !== 'APPROVE_STATUS_AGREE');
    // if (initRow?.length > 0) {
    //   XlbMessage.error('仅支持审核通过状态的单据');
    //   return;
    // }
    const flag = await XlbTipsModal({
      tips: (
        <span style={{ wordBreak: 'break-all' }}>
          是否确认作废单据{selectRow?.map((v: any) => v?.fid).join(',')}？
        </span>
      ),
    });
    invalidReasonForm.resetFields();
    if (!flag) return;
    await XlbTipsModal({
      title: '作废原因',
      isCancel: true,
      width: 380,
      tips: (
        <div>
          <XlbBasicForm form={invalidReasonForm}>
            <XlbBasicForm.Item name={'invalid_reason'} label={''}>
              <XlbInput.TextArea
                maxLength={50}
                placeholder={'请输入作废原因'}
                style={{
                  width: 348,
                  height: 115,
                  resize: 'none',
                  marginTop: 10,
                }}
              ></XlbInput.TextArea>
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </div>
      ),
      onOk: async () => {
        setLoading(true)
        const res = await invalid({
          fids: selectRow?.map((k: any) => k.fid),
          invalid_reason: invalidReasonForm.getFieldValue('invalid_reason'),
        });
        setLoading(false)
        if (res?.code === 0 && !res?.data?.tips?.length) {
          XlbMessage.success(`已作废${res?.data?.success_num}张单据！`);
        } else if (res?.code === 0 && res?.data?.tips?.length) {
          XlbTipsModal({
            isCancel: false,
            isConfirm: true,
            tips: `已作废${res?.data?.success_num}张单据，以下单据不允许作废，系统自动过滤！`,
            tipsList: res?.data?.tips,
          });
        }
        pageConatainerRef?.current?.fetchData?.();
      },
    });
  };
  const handleExport = async (ids: any, e: any, type: string, setLoading: any) => {
    setLoading(true)
    const data = prevPost();
    let res: any;
    if (type === 'detail') {
      res = await detailbatchexport({ fids: ids });
    } else {
      res = await exportData(data);
    }
    setLoading(false)
    if (res?.code === 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success('导出成功');
    }
  };
  const handleCopy = async (selectRow: any, setLoading: any) => {
    await XlbTipsModal({
      title: '提示',
      isCancel: true,
      tips: '是否确认复制单据' + selectRow?.[0]?.fid + '？',
      onOk: async () => {
        setLoading(true)
        const res = await copy({ fid: selectRow?.[0]?.fid });
        setLoading(false)
        if (res?.code === 0) {
          pageConatainerRef?.current?.fetchData?.();
          XlbTipsModal({
            title: '提示',
            tips: `已生成新的单据${res?.data?.fid}`,
          });
        }
      },
    });
  };
  const handleAudit = async (ids: any, selectRow: any, setLoading: any) => {
    const initRow = selectRow?.filter((v: any) => v.state === 'INIT');
    if (initRow?.length !== ids?.length) {
      XlbMessage.error('仅支持制单状态的单据');
      return;
    }
    await XlbTipsModal({
      title: '提示',
      tips: '是否确认审核' + ids?.length + '条数据？',
      onOk: async () => {
        setLoading(true)
        const res = await batchAudit({ fids: ids });
        setLoading(false)
        if (res?.code === 0) {
          XlbMessage.success('审核成功');
        }
        pageConatainerRef?.current?.fetchData?.();
      },
    });
  };

  useEffect(() => {
    const pageRecord = history.location.state as any
    if (pageRecord?.source === 'oa') {
      setRecord({ fid: pageRecord?.business_key, isOA: pageRecord?.source === 'oa' });
      pageModalRef.current?.setOpen(true);
    }
  }, []);

  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={(back: boolean, isOA: boolean) => {
                  if (back) {
                    pageConatainerRef?.current?.fetchData?.();
                  }
                  // if (isOA) {
                  //   window.$wujie?.bus.$emit('wujie-router-jump', {
                  //     url: 'xlb_oa+approvalHandle+index',
                  //     params: {},
                  //     appType: 'xlb_oa',
                  //   });
                  // }
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbPageContainer
        ref={pageConatainerRef}
        tableColumn={tableColumn
          ?.filter((v: any) => {
            if (v.code === 'org_id') {
              return enable_organization;
            }
            return true;
          })
          .map((v: any) => {
            if (v.code === 'fid') {
              return {
                ...v,
                render(text: any, record: any, index: any) {
                  return (
                    <div
                      className="link"
                      onClick={() => {
                        setRecord({ fid: text });
                        pageModalRef.current?.setOpen(true);
                      }}
                    >
                      {text}
                    </div>
                  );
                },
              };
            }
            return v;
          })}
        url={'/erp/hxl.erp.warehousetransferplan.page'}
        prevPost={prevPost}
        footerDataSource={(data) => {
          return [
            {
              _index: '合计',
              item_count: Number(data?.item_count || 0) || 0,
              quantity: Number(data?.quantity || 0) || 0,
              // supplier_initial_quantity: Number(data?.total_supplier_initial_quantity || 0) || 0,
              // item_initial_quantity: Number(data?.total_item_initial_quantity || 0) || 0,
              // transfer_quantity: Number(data?.total_transfer_quantity || 0)?.toFixed(3),
              actual_quantity: Number(data?.actual_quantity || 0) || 0,
              money: Number(data?.money || 0) || 0
            },
          ];
        }}
      >
        <ToolBtn showColumnsSetting>
          {({ fetchData, loading, setLoading, dataSource, selectRow, selectRowKeys }) => {
            return (
              <XlbButton.Group>
                {hasAuth(['仓间调拨计划单', '查询']) && (
                  <XlbButton
                    label="查询"
                    type="primary"
                    disabled={loading}
                    onClick={() => {
                      fetchData();
                    }}
                    icon={<XlbIcon name={'sousuo'} />}
                  />
                )}
                {hasAuth(['仓间调拨计划单', '编辑']) && (
                  <XlbButton
                    label="新增"
                    type="primary"
                    disabled={loading}
                    onClick={() => {
                      setRecord({ fid: 1 });
                      pageModalRef.current?.setOpen(true);
                    }}
                    icon={<XlbIcon name={'jia'} />}
                  />
                )}
                {hasAuth(['仓间调拨计划单', '删除']) && (
                  <XlbButton
                    label="删除"
                    type="primary"
                    disabled={loading || !selectRow?.length}
                    onClick={() => {
                      handleBatchDelete(selectRowKeys, selectRow, setLoading);
                    }}
                    icon={<XlbIcon name={'shanchu'} />}
                  />
                )}
                {hasAuth(['仓间调拨计划单', '导出']) && (
                  <XlbDropdownButton
                    label="导出"
                    disabled={loading}
                    dropList={[
                      {
                        label: '导出',
                        disabled: !dataSource?.length,
                      },
                      {
                        label: '导出明细',
                        disabled: !selectRow?.length,
                      },
                    ]}
                    dropdownItemClick={(value: number, item, e) => {
                      switch (item?.label) {
                        case '导出':
                          handleExport(selectRowKeys, e, 'all', setLoading);
                          break;
                        case '导出明细':
                          handleExport(selectRowKeys, e, 'detail', setLoading);
                          break;
                      }
                    }}
                  />
                )}
                {hasAuth(['仓间调拨计划单', '作废']) && (
                  <XlbButton
                    label="作废"
                    type="primary"
                    disabled={loading || !selectRow?.length}
                    icon={<ExceptionOutlined />}
                    onClick={() => {
                      handleSureCancel(selectRow, setLoading);
                    }}
                  />
                )}
                {hasAuth(['仓间调拨计划单', '编辑']) && (
                  <XlbButton
                    label="复制"
                    type="primary"
                    disabled={loading || selectRow?.length !== 1}
                    onClick={() => {
                      handleCopy(selectRow, setLoading);
                    }}
                    icon={<XlbIcon name={'fuzhi'} />}
                  />
                )}
                {hasAuth(['仓间调拨计划单', '审核']) && (
                  <XlbButton
                    label="批量审核"
                    type="primary"
                    disabled={loading || !selectRow?.length}
                    onClick={() => {
                      handleAudit(selectRowKeys, selectRow, setLoading);
                    }}
                    icon={<XlbIcon name={'shenhe'} />}
                  />
                )}
                {hasAuth(['仓间调拨计划单', '编辑']) && (
                  <XlbButton
                    label="生成仓间调拨单"
                    type="primary"
                    disabled={loading || selectRow?.length !== 1 || selectRow?.[0]?.state !== 'APPROVE_STATUS_AGREE'}
                    icon={<XlbIcon name={'shenqingtuihuo'} />}
                    onClick={() => {
                      setLoading?.(true)
                      ErpRequest.post('/erp/hxl.erp.warehousetransferplan.warehousetransferorder.check', { fid: selectRow?.[0]?.fid, company_id: LStorage.get('userInfo')?.company_id, }).then((res: any) => {
                        if (res?.code === 0) {
                          NiceModal.show(GenerateInterOrders, {fid: selectRow?.[0]?.fid});
                        }
                      }).finally(() => {
                        setLoading?.(false)
                      })
                    }}
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm
            formList={form_list?.filter((v: any) => {
              if (v.name === 'in_org_ids') {
                return enable_organization;
              }
              return true;
            })}
            form={form}
            isHideDate={true}
            initialValues={{
              time_type: 'create_date',
              compactDatePicker: [
                dayjs().format('YYYY-MM-DD'),
                dayjs().format('YYYY-MM-DD'),
              ],
            }}
            onValuesChange={(values) => {
              if (values.hasOwnProperty('plan_type')) {
                form_list.find((v: any) => v.name === 'plan_month')!.hidden = values.plan_type !== 'PLAN_TRANSFER';
                setFormList([ ...form_list ]);
              }
            }}
          />
        </SearchForm>
        <Table key={'fid'} primaryKey={'fid'} selectMode="multiple" style={{ flex: 1 }}></Table>
      </XlbPageContainer>
    </>
  );
};
export default InterWarehouseTransferPlan;