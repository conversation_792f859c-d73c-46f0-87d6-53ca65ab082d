import { orderStatusIcons } from '@/components/data';
import XlbApprovalProcessModal from '@/components/XlbApprovalProcessModal';
import ApprovalModal from '@/pages/delivery/interWarehouseTransferPlan/components/approvalModal';
import GenerateInterOrders from '@/pages/delivery/interWarehouseTransferPlan/components/generateInterOrders';
import { hasAuth, LStorage } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbCascader,
  XlbCheckbox,
  XlbDropdownButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbInputNumber,
  XlbMessage,
  XlbSelect,
  XlbTable,
  XlbTableColumnProps,
  XlbTabs,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { Col, ConfigProvider, Row } from 'antd';
import { cloneDeep, isNumber } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { itemTableListDetail, monthList, tableStateOptions } from './data';
import styles from './index.less';
import { audit, detailexport, invalid, read, save, update } from './server';
import { useBaseParams } from '@/hooks/useBaseParams';
import { ExceptionOutlined } from '@ant-design/icons';

const Item = (props: any) => {
  const [info, setInfo] = useState({ state: 'INIT' });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [fid, setFid] = useState<any>(1);
  const [rowData, setRowData] = useState<any[]>([]);
  const [form] = XlbBasicForm.useForm();
  const [invalidReasonForm] = XlbBasicForm.useForm();
  const { record, onBack } = props;
  const { enable_cargo_owner } = useBaseParams((state) => state)
  const [itemArrdetail, setItemArrdetail] = useState<
    XlbTableColumnProps<any>[]
  >(cloneDeep(itemTableListDetail));
  const [baseInfoActiveKey, setBaseInfoActiveKey] = useState('baseInfo');
  const [edit, setEdit] = useState(false);
  const [pageRefresh, setPageRefresh] = useState(false);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 });
  const [orgList, setOrgList] = useState<any>([]);
  const [chooseList, setChooseList] = useState<any>([]);
  const [isOA, setOA] = useState<boolean>(record.isOA);
  const [tableDisable, setTableDisable] = useState<boolean>(true);
  const [reasonOptions, setReasonOptions] = useState<any>([]);
  const [dialogWidth, setDialogWidth] = useState(180); // 默认值
  const referenceRef = useRef<HTMLDivElement | null>(null);

  const itemListRef = useRef<any>(null);

  const inputChange = (e: any, code: string, record: any, index: any) => {
    const i = rowData.findIndex(
      (item) => item._custem_id === record._custem_id,
    );
    setRowData((pre) => {
      pre[i][code] = e;
      return [...pre];
    });
  };

  const footerSet = () => {
    footerData[0] = {};
    footerData[0]._index = '合计';
    footerData[0].supplier_initial_quantity = hasAuth([
      '仓间调拨计划单',
      '查询',
    ])
      ? rowData
          .reduce(
            (sum: number, v: any) =>
              sum + Number(v?.supplier_initial_quantity || 0),
            0,
          )
          .toFixed(3)
      : '****';
    footerData[0].item_initial_quantity = rowData
      .reduce(
        (sum: number, v: any) => sum + Number(v?.item_initial_quantity || 0),
        0,
      )
      .toFixed(3);
    footerData[0].quantity = rowData
      .reduce((sum: number, v: any) => sum + Number(v?.quantity || 0), 0)
      .toFixed(3);
    footerData[0].money = hasAuth(['仓间调拨计划单', '查询'])
      ? rowData
          .reduce((sum: number, v: any) => sum + Number(v?.money || 0), 0)
          .toFixed(2)
      : '****';
    setFooterData([...footerData]);
  };

  const removeDuplicateData = (array1: any, array2: any) => {
    const idSet = new Set(array1.map((item: any) => item._custem_id));
    const rawDuplicates = array2.filter((item: any) =>
      idSet.has(item._custem_id),
    );
    const seen = new Set();
    const duplicates = rawDuplicates.filter((item: any) => {
      if (seen.has(item._custem_id)) return false;
      seen.add(item._custem_id);
      return true;
    });
    const uniqueFromArray2 = array2.filter(
      (item: any) => !idSet.has(item._custem_id),
    );
    const mergedArray = [...array1, ...uniqueFromArray2];
    if (duplicates.length > 0) {
      XlbTipsModal({
        tips: '已将重复数据过滤！',
      });
    }
    return mergedArray;
  };
  // 批量添加
  const confirmAdd = async (itemList: any[], type?: string) => {
    setIsLoading(true);
    setEdit(true);
    await Promise.resolve();
    setTimeout(async () => {
      let mergeData: any[] = [];
      const storeList = itemList?.[0]?.supplier_stores || [];
      itemList?.forEach((item: any) => {
        const ls = type === 'import' ? item?.supplier_stores || [] : storeList;
        ls?.forEach((store: any) => {
          // 参考仓间调拨
          const basic_unit = {
            label: item.basic_unit,
            value: item.basic_unit,
            ratio: 1,
          };
          // const wholesale_unit = { label: item?.wholesale_unit, value: item?.wholesale_unit, ratio: item?.wholesale_ratio }
          const delivery_unit = {
            label: item?.delivery_unit,
            value: item?.delivery_unit,
            ratio: item?.delivery_ratio,
          };
          const purchase_unit = {
            label: item?.purchase_unit,
            value: item?.purchase_unit,
            ratio: item?.purchase_ratio,
          };
          // const stock_unit = { label: item?.stock_unit, value: item?.stock_unit, ratio: item?.stock_ratio }
          const units = Array.from(
            new Set([
              basic_unit,
              // wholesale_unit,
              delivery_unit,
              purchase_unit,
              // stock_unit
            ]),
          );
          mergeData.push({
            ...store,
            ...item,
            item_id: item?.id,
            item_code: item?.code,
            item_name: item?.name,
            item_spec: item?.purchase_spec,
            basic_unit: item?.basic_unit ? item?.basic_unit : item?.unit,
            unit: item?.purchase_unit,
            ratio: item?.purchase_ratio,
            units: Array.from(new Map(units.map((t) => [t.label, t])).values()),
            _custem_id: `${store.out_store_id}_${item.id}`,
            _click: false,
            _edit: false,
          });
        });
      });
      mergeData = removeDuplicateData(rowData || [], mergeData);
      mergeData.sort((a, b) => a._custem_id.localeCompare(b._custem_id));
      console.log(mergeData, 'MERGEDATA');
      setRowData([...mergeData]);
      setPagin({ ...pagin, total: mergeData?.length });
      setIsLoading(false);
    }, 0);
  };
  const tableRender = (item: any) => {
    switch (item.code) {
      case '_operator':
        item.render = (value: any, record: any) => {
          return (
            <XlbIcon
              name="shanchu"
              style={{ cursor: 'pointer' }}
              size={16}
              onClick={(e) => {
                e.stopPropagation();
                if (info.state !== 'INIT') {
                  return;
                }
                const i = rowData.findIndex(
                  (item) => item._custem_id === record._custem_id,
                );
                rowData.splice(i, 1);
                setRowData([...rowData]);
                setEdit(true);
                if (chooseList?.length) {
                  const ci = chooseList.findIndex(
                    (id: any) => id === record._custem_id,
                  );
                  if (ci !== -1) {
                    chooseList.splice(ci, 1);
                    setChooseList([...chooseList]);
                  }
                }
              }}
            />
          );
        };
        break;
      case 'quantity':
      case 'money':
        item.render = (value: any, record: any, index: any) => {
          return record?._custemEdit && info.state === 'INIT' ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <XlbInputNumber
                style={{ width: 80 }}
                value={value}
                min={0}
                max={9999999}
                precision={item.code === 'money' ? 2 : 3}
                onBlur={(e) => {
                  let v: any = e.target.value;
                  if (/^-?(?:\d+\.?\d*|\.\d+)$/.test(v) && Number(v)) {
                    v = Number(v).toFixed(item.code === 'money' ? 2 : 3);
                    if (v > 9999999) v = 9999999;
                    if (v < 0) v = 0;
                    inputChange(Number(v), item.code, record, index['index']);
                    setEdit(true);
                  }
                }}
                // onChange={(e) => {
                //   inputChange(e, item.code, record, index['index'])
                //   setEdit(true);
                // }}
              />
            </div>
          ) : isNumber(value) ? (
            Number(value || 0)?.toFixed(item.code === 'money' ? 2 : 3)
          ) : (
            ''
          );
        };
        break;
      case 'unit':
        item.render = (value: any, record: any, index: any) => {
          return record?._custemEdit && info.state === 'INIT' ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <XlbSelect
                size="small"
                value={value}
                allowClear={false}
                style={{ width: '100%' }}
                // disabled={!hasAuth(['供应商管理', '编辑'])}
                onChange={(e, option: any) => {
                  setEdit(true);
                  const dt = rowData.find(
                    (item) => item._custem_id === record._custem_id,
                  );
                  // const dt = rowData[index.index];
                  if (e) {
                    dt.unit = option?.value;
                    dt.ratio = option?.ratio;
                  } else {
                    dt.unit = null;
                    dt.ratio = null;
                  }
                }}
                options={record?.units}
              />
            </div>
          ) : (
            value || ''
          );
        };
        break;
      case 'reason_id':
        item.render = (value: any, record: any, index: any) => {
          return record?._custemEdit && info.state === 'INIT' ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <XlbCascader
                defaultValue={
                  record.first_reason_id && record.reason_id
                    ? [record.first_reason_id, record.reason_id]
                    : []
                }
                style={{ width: '100%', padding: '2px 0', height: 32 }}
                options={reasonOptions}
                popupClassName={styles.cascSty}
                onChange={(value: any, options: any) => {
                  setEdit(true);
                  // const dt = rowData[index.index];
                  const dt = rowData.find(
                    (item) => item._custem_id === record._custem_id,
                  );
                  if (value) {
                    dt.first_reason_id = options?.[0]?.id;
                    dt.first_reason_name = options?.[0]?.name;
                    dt.reason_id = options?.[1]?.id;
                    dt.reason_name = options?.[1]?.name;
                  } else {
                    dt.first_reason_id = null;
                    dt.first_reason_name = null;
                    dt.reason_id = null;
                    dt.reason_name = null;
                  }
                  setRowData([...rowData]);
                }}
              />
            </div>
          ) : (
            <div className="info">
              {record.first_reason_name && record.reason_name
                ? `${record.first_reason_name} / ${record.reason_name}`
                : ''}
            </div>
          );
        };
        break;
      case 'memo':
        item.render = (value: any, record: any, index: any) => {
          if (record?._custemEdit && info.state === 'INIT') {
            return (
              <XlbInput
                maxLength={30}
                defaultValue={value}
                onBlur={(e) => {
                  setEdit(true);
                  inputChange(
                    e.target.value,
                    item.code,
                    record,
                    index['index'],
                  );
                }}
              />
            );
          } else {
            return <div className="info">{value}</div>;
          }
        };
        break;
    }
    return item;
  };

  const readinfo = async (fid: any) => {
    setIsLoading(true);
    const res = await read({ fid });
    setIsLoading(false);
    if (res.code === 0) {
      setEdit(false);
      form.setFieldsValue({ ...res.data });
      setFid(res?.data?.fid);
      setInfo({ state: res?.data?.state });
      setRowData(
        res?.data?.details?.map((v: any, index: any) => {
          const unit = { label: v.unit, value: v.unit, ratio: v.ratio };
          const basic_unit = {
            label: v?.basic_unit,
            value: v?.basic_unit,
            ratio: 1,
          };
          // const wholesale_unit = {
          //   label: v?.wholesale_unit,
          //   value: v?.wholesale_unit,
          //   ratio: v?.wholesale_ratio,
          // };
          const delivery_unit = {
            label: v?.delivery_unit,
            value: v?.delivery_unit,
            ratio: v?.delivery_ratio,
          };
          const purchase_unit = {
            label: v?.purchase_unit,
            value: v?.purchase_unit,
            ratio: v?.purchase_ratio,
          };
          // const stock_unit = {
          //   label: v?.stock_unit,
          //   value: v?.stock_unit,
          //   ratio: v?.stock_ratio,
          // };
          const units = Array.from(
            new Set([
              unit,
              basic_unit,
              // wholesale_unit,
              delivery_unit,
              purchase_unit,
              // stock_unit,
            ]),
          );
          return {
            ...v,
            _custem_id: v.out_store_id + '_' + v?.item_id,
            units: Array.from(new Map(units.map((t) => [t.label, t])).values()),
          };
        }),
      );
      setPagin({ ...pagin, total: res?.data?.details?.length });
    }
  };

  const getOrgList = async (list: any) => {
    const data = {
      store_id: Array.isArray(list) ? list[0].id : list,
    };
    // 组织接口
    const res = await ErpRequest.post(
      '/erp/hxl.erp.delivery.cargo.owner.org.find',
      { ...data },
    );
    if (res.code === 0) {
      const org_list = res.data.map((v: any) => ({
        ...v,
        label: v.name,
        value: v.id,
      }));
      setOrgList(org_list);
      // 禁用货主弹窗 默认赋值
      if (res?.data?.length === 1) {
        form?.setFieldsValue({
          in_org_id: res?.data?.[0].id,
          in_two_org_id: res?.data?.[0].parent_id,
        });
      }
      if (form.getFieldValue('in_org_id') && org_list?.length) {
        const op = org_list.find(
          (item: any) => item.id === form.getFieldValue('in_org_id'),
        )?.parent_id;
        form.setFieldsValue({ in_two_org_id: op });
      }
    }
  };

  const verificationRequired = async () => {
    const indexes =
      (
        rowData?.map((item: any, index: number) =>
          !item.quantity || !item.money ? index : -1,
        ) || []
      ).filter((index) => index !== -1) || [];
    if (indexes?.length) {
      console.log('请填写月度调拨量(预估) 和 调拨费用');
      await XlbTipsModal({
        tipsList: indexes?.map(
          (index) => `请填写第${index + 1}行 月度调拨量(预估) 和 调拨费用！`,
        ),
      });
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    try {
      await form.validateFields();
    } catch (err: any) {
      console.log(err);
      return false;
    }
    if (!rowData?.length) {
      XlbTipsModal({ tips: '请添加商品！' });
      return;
    }
    setIsLoading(true);
    await Promise.resolve();
    setTimeout(async () => {
      const updatedRowData = rowData.map((item: any) => ({
        ...item,
        _custemEdit: false,
      }));
      setRowData(updatedRowData);
      if (!await verificationRequired()) {
        setIsLoading(false);
        return;
      }
      const values = form.getFieldsValue(true);
      const data = {
        ...values,
        details: updatedRowData.map((item: any) => ({
          ...item,
          supplier_stores: null,
        })),
      };
      console.log(data, 'allData');
      const res = fid === 1 ? await save(data) : await update(data);
      setIsLoading(false);
      if (res?.code === 0) {
        setPageRefresh(true);
        setFid(res?.data?.fid);
        XlbMessage.success('保存成功');
        readinfo(res?.data?.fid);
      }
    }, 0);
  };
  // 确认作废
  const handleSureCancel = async (fid: any) => {
    const flag = await XlbTipsModal({
      tips: (
        <span style={{ wordBreak: 'break-all' }}>是否确认作废单据{fid}？</span>
      ),
    });
    invalidReasonForm.resetFields();
    if (!flag) return;
    await XlbTipsModal({
      title: '作废原因',
      isCancel: true,
      width: 380,
      tips: (
        <div>
          <XlbBasicForm form={invalidReasonForm}>
            <XlbBasicForm.Item name={'invalid_reason'} label={''}>
              <XlbInput.TextArea
                maxLength={50}
                placeholder={'请输入作废原因'}
                style={{
                  width: 348,
                  height: 115,
                  resize: 'none',
                  marginTop: 10,
                }}
              ></XlbInput.TextArea>
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </div>
      ),
      onOk: async () => {
        setIsLoading(true);
        const res = await invalid({
          fids: [fid],
          invalid_reason: invalidReasonForm.getFieldValue('invalid_reason'),
        });
        setIsLoading(false);
        if (res?.code === 0 && !res?.data?.tips?.length) {
          setPageRefresh(true);
          XlbMessage.success(`已作废！`);
        } else if (res?.code === 0 && res?.data?.tips?.length) {
          setPageRefresh(true);
          XlbTipsModal({
            isCancel: false,
            isConfirm: true,
            tips: `作废失败！`,
            tipsList: res?.data?.tips,
          });
        }
        readinfo(fid);
      },
    });
  };

  const handleAudit = async () => {
    try {
      await form.validateFields();
    } catch (err: any) {
      return false;
    }
    setIsLoading(true);
    if (!await verificationRequired()) {
      setIsLoading(false);
      return;
    }
    const values = form.getFieldsValue(true);
    const cargo_owner_id = values?.cargo_owner_id;
    let data = {
      ...values,
      store_id: values.store_id?.[0],
      cargo_owner_id: Array.isArray(cargo_owner_id)
        ? cargo_owner_id[0]
        : cargo_owner_id,
      details: [...rowData],
    };
    const res = await audit(data);
    setIsLoading(false);
    if (res?.code === 0) {
      setPageRefresh(true);
      XlbMessage.success('操作成功');
      readinfo(fid);
      return;
    }
  };
  const openStartapproval = async (fid: any) => {
    await XlbApprovalProcessModal({
      params: {
        fid,
      },
      // requestUrl:
      //   process.env.ERP_URL + '/erp/hxl.erp.storeitemattr.oa.findbyfid',
      requestUrl: process.env.ERP_URL + '/erp/hxl.erp.oa.findbyfid',
    });
  };

  const handleExport = async (e: any) => {
    setIsLoading(true);
    const res = await detailexport({ fid: fid });
    setIsLoading(false);
    if (res?.code === 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success('导出成功');
    }
  };
  const goBack = async () => {
    if (edit) {
      await XlbTipsModal({
        tips: '单据未保存，是否确认返回？',
        isCancel: true,
        onOkBeforeFunction: () => {
          onBack(true, isOA);
          return true;
        },
      });
      return false;
    }
    onBack(pageRefresh, isOA);
  };

  const importStores = async () => {
    await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.warehousetransferplan.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.warehousetransferplan.template.download`,
      templateName: '下载模板',
      params: {
        in_store_id: form.getFieldValue('in_store_id'),
        in_org_id: form.getFieldValue('in_org_id'),
        company_id: LStorage.get('userInfo')?.company_id,
      },
      callback: (res: any) => {
        if (res.code !== 0) return;
        confirmAdd(res?.data?.details || [], 'import');
      },
    });
  };

  const getReason = async () => {
    const arrayToTree = (data: any) => {
      const idMap = new Map();
      const tree: any[] = [];
      data.forEach((item: any) => {
        idMap.set(item.id, {
          ...item,
          value: item.id,
          disabled: item?.deleted || !item?.enabled,
          label: item.name,
          children: [],
        });
      });
      data.forEach((item: any) => {
        const node = idMap.get(item.id);
        if (item.parent_id) {
          const parent = idMap.get(item.parent_id);
          if (parent) {
            parent.children.push(node);
          }
        } else {
          tree.push(node); // 顶层节点
        }
      });
      return tree;
    };
    const res = await ErpRequest.post(
      '/erp/hxl.erp.warehousetransferreason.find',
      {},
    );
    if (res.code === 0) {
      const tl = arrayToTree(res.data);
      setReasonOptions(tl);
    }
  };

  const getOAPermissions = () => {
    ErpRequest.post('/oa/hxl.oa.feign.approvaltaskhistory.check', {
      tel: '',
      business_key: record.fid,
    }).then((res: any) => {
      console.log(res, 11111);
      if (res.code === 0) {
        setOA(res.data.approve);
      }
    });
  };

  useEffect(() => {
    if (record.fid === 1) {
      setFid(1);
    } else {
      readinfo(record.fid);
      // getOAPermissions()
    }
    // setOA(true);
    // setOA(record.source === 'oa')
    getReason();
  }, []);
  useEffect(() => {
    if (referenceRef.current) {
      const observer = new ResizeObserver(() => {
        const width = referenceRef.current?.offsetWidth - 120 || 180;
        setDialogWidth(width);
      });
      observer.observe(referenceRef.current);
      return () => observer.disconnect(); // 清理
    }
  }, []);
  useEffect(() => {
    footerSet();
  }, [rowData]);

  const in_store_id = XlbBasicForm.useWatch('in_store_id', form);
  const in_org_id = XlbBasicForm.useWatch('in_org_id', form);
  const plan_type = XlbBasicForm.useWatch('plan_type', form);
  const plan_month = XlbBasicForm.useWatch('plan_month', form);
  useEffect(() => {
    const hasOrgAndStore = in_org_id && in_store_id;
    let shouldEnableTable = false;
    if (hasOrgAndStore) {
      if (
        (plan_type === 'PLAN_TRANSFER' && plan_month) ||
        plan_type === 'TEMP_TRANSFER'
      ) {
        shouldEnableTable = true;
      }
    }
    setTableDisable(!shouldEnableTable);
  }, [in_store_id, in_org_id, plan_type, plan_month]);

  return (
    <ConfigProvider
      theme={{
        token: {
          screenXLMin: 1280,
          screenXL: 1280,
        },
      }}
    >
      <div
        style={{
          padding: 12,
          height: 'calc(100vh - 104px)',
          display: 'flex',
          flexDirection: 'column',
        }}
        className={styles.form_container_wrapper_Campaign}
      >
        <XlbButton.Group>
          {hasAuth(['仓间调拨计划单', '编辑']) && (
            <XlbButton
              label="保存"
              type="primary"
              disabled={info.state !== 'INIT' || isLoading}
              onClick={() => handleSave()}
              icon={<XlbIcon size={16} name="baocun" />}
            />
          )}
          {hasAuth(['仓间调拨计划单', '审核']) && (
            <XlbButton
              label="审核"
              type="primary"
              disabled={info.state !== 'INIT' || fid === 1 || isLoading}
              onClick={() => handleAudit()}
              icon={<XlbIcon size={16} name="shenhe" />}
            />
          )}
          {hasAuth(['仓间调拨计划单', '作废']) && (
            <XlbButton
              label="作废"
              type="primary"
              disabled={fid === 1 || info.state === 'INVALID' || isLoading}
              onClick={() => {
                handleSureCancel(fid);
              }}
              icon={<ExceptionOutlined />}
            />
          )}
          {/*{hasAuth(['领用申请单', '反审核']) && (*/}
          {/*  <XlbButton*/}
          {/*    label="撤回"*/}
          {/*    type="primary"*/}
          {/*    disabled={info.state !== 'AUDIT' || fid === 1 || isLoading}*/}
          {/*    loading={isLoading}*/}
          {/*    onClick={() => handleWithdraw(fid)}*/}
          {/*    icon={<XlbIcon size={16} name="fanshenhe" />}*/}
          {/*  />*/}
          {/*)}*/}
          {hasAuth(['仓间调拨计划单', '审核']) && (
            <XlbButton
              label="审批流程"
              type="primary"
              disabled={info.state === 'INIT' || fid === 1 || isLoading}
              onClick={() => openStartapproval(fid)}
              icon={<XlbIcon size={16} name="fanshenhe" />}
            />
          )}
          <XlbDropdownButton
            label="业务操作"
            disabled={isLoading}
            dropList={[
              {
                label: '生成仓间调拨单',
                disabled:
                  info.state !== 'APPROVE_STATUS_AGREE' ||
                  fid === 1 ||
                  !hasAuth(['仓间调拨计划单', '编辑']),
              },
              {
                label: '导出',
                disabled: fid === 1 || !hasAuth(['仓间调拨计划单', '导出']),
              },
            ]}
            dropdownItemClick={(index: number, item: any, e: any) => {
              switch (item?.label) {
                case '导出':
                  handleExport(e);
                  break;
                case '生成仓间调拨单':
                  setIsLoading(true);
                  ErpRequest.post(
                    '/erp/hxl.erp.warehousetransferplan.warehousetransferorder.check',
                    { fid, company_id: LStorage.get('userInfo')?.company_id },
                  )
                    .then((res: any) => {
                      if (res?.code === 0) {
                        NiceModal.show(GenerateInterOrders, { fid: fid });
                      }
                    })
                    .finally(() => {
                      setIsLoading(false);
                    });
                  break;
              }
            }}
          />
          {isOA && (
            <XlbButton
              label="同意"
              type="primary"
              disabled={
                isLoading ||
                info?.state === 'APPROVE_STATUS_AGREE' ||
                info?.state === 'APPROVE_STATUS_REFUSE'
              }
              onClick={async (e) => {
                setIsLoading(true);
                const ar = await NiceModal.show(ApprovalModal, {
                  type: 1,
                  fid,
                });
                setIsLoading(false);
                if (ar) {
                  setPageRefresh(true);
                  readinfo(fid);
                }
              }}
              icon={<span className={'iconfont icon-tongguo'} />}
            />
          )}
          {isOA && (
            <XlbButton
              label="驳回"
              type="primary"
              disabled={
                isLoading ||
                info?.state === 'APPROVE_STATUS_AGREE' ||
                info?.state === 'APPROVE_STATUS_REFUSE'
              }
              onClick={async (e) => {
                setIsLoading(true);
                const ar = await NiceModal.show(ApprovalModal, {
                  type: 2,
                  fid,
                });
                setIsLoading(false);
                if (ar) {
                  setPageRefresh(true);
                  readinfo(fid);
                }
              }}
              icon={<span className={'iconfont icon-bohui'} />}
            />
          )}
          <XlbButton
            label="返回"
            type="primary"
            onClick={() => goBack()}
            icon={<XlbIcon size={16} name="fanhui" />}
          />
        </XlbButton.Group>
        <XlbBasicForm
          style={{ paddingLeft: '12px' }}
          colon
          form={form}
          disabled={isLoading}
          layout="inline"
        >
          <XlbTabs
            defaultActiveKey="baseInfo"
            activeKey={baseInfoActiveKey}
            onChange={(e) => setBaseInfoActiveKey(e)}
            items={[
              {
                label: '基本信息',
                key: 'baseInfo',
                children: (
                  <div className={styles.form_container_transferDocument}>
                    <div className="row-flex">
                      <div
                        className="row-flex"
                        style={{ flex: 1, flexWrap: 'wrap' }}
                      >
                        <Row gutter={12} wrap>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <div style={{ width: '100%' }} ref={referenceRef}>
                              <XlbBasicForm.Item
                                label="调拨类型"
                                initialValue={'正常调拨'}
                                name={'allocation_type'}
                              >
                                <XlbInput
                                  style={{ width: '100%' }}
                                  disabled={true}
                                />
                              </XlbBasicForm.Item>
                            </div>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label="调入门店"
                              name="in_store_id"
                              normalize={(value) => value?.[0]}
                              rules={[
                                { required: true, message: '请选择调入门店' },
                              ]}
                            >
                              <XlbInputDialog
                                width={dialogWidth}
                                disabled={
                                  !!rowData?.length ||
                                  !hasAuth(['仓间调拨计划单', '编辑'])
                                }
                                dialogParams={{
                                  type: 'store',
                                  dataType: 'lists',
                                  data: {
                                    center_flag: true,
                                  },
                                }}
                                fieldNames={{
                                  idKey: 'id',
                                  nameKey: 'store_name',
                                }}
                                handleValueChange={(
                                  value: any,
                                  options: any,
                                ) => {
                                  form.setFieldsValue({ in_org_id: null });
                                  form.setFieldsValue({ in_two_org_id: null });
                                  setOrgList([]);
                                  setEdit(true);
                                }}
                                onChange={(value: any, options: any) => {
                                  if (options?.length) {
                                    getOrgList(options);
                                  }
                                }}
                              />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label="调入组织"
                              name="in_org_id"
                              rules={[
                                { required: true, message: '请选择调入组织' },
                              ]}
                            >
                              <XlbSelect
                                size="small"
                                showSearch
                                style={{ width: dialogWidth }}
                                disabled={
                                  !enable_cargo_owner ||
                                  orgList?.length === 1 ||
                                  info.state !== 'INIT' ||
                                  rowData?.length !== 0 ||
                                  !hasAuth(['仓间调拨计划单', '编辑'])
                                }
                                filterOption={(input, option) =>
                                  (option!.label as unknown as string)
                                    .toLowerCase()
                                    .includes(input.toLowerCase())
                                }
                                options={orgList}
                                onChange={(v, o) => {
                                  form.setFieldsValue({
                                    in_two_org_id: o?.parent_id,
                                  });
                                  setEdit(true);
                                }}
                              />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item label="单据号" name="fid">
                              <XlbInput disabled style={{ width: '100%' }} />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label="计划类型"
                              name="plan_type"
                              rules={[
                                { required: true, message: '请选择计划类型' },
                              ]}
                            >
                              <XlbSelect
                                style={{ width: '100%' }}
                                disabled={
                                  !!rowData?.length ||
                                  !hasAuth(['仓间调拨计划单', '编辑'])
                                }
                                options={[
                                  { label: '计划调拨', value: 'PLAN_TRANSFER' },
                                  { label: '临时调拨', value: 'TEMP_TRANSFER' },
                                ]}
                                onChange={() => {
                                  form.setFieldsValue({
                                    plan_month: '',
                                  });
                                  setEdit(true);
                                }}
                              />
                            </XlbBasicForm.Item>
                          </Col>
                          <XlbBasicForm.Item
                            noStyle
                            dependencies={['plan_type']}
                          >
                            {({ getFieldValue }) => {
                              return getFieldValue('plan_type') !==
                                'PLAN_TRANSFER' ? (
                                ''
                              ) : (
                                <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                                  <XlbBasicForm.Item
                                    label="计划月份"
                                    name="plan_month"
                                    rules={[
                                      {
                                        required: true,
                                        message: '请选择计划月份',
                                      },
                                    ]}
                                  >
                                    <XlbSelect
                                      style={{ width: '100%' }}
                                      disabled={
                                        !!rowData?.length ||
                                        !hasAuth(['仓间调拨计划单', '编辑'])
                                      }
                                      onChange={() => {
                                        setEdit(true);
                                      }}
                                    >
                                      {monthList.map((t) => (
                                        <XlbSelect.Option
                                          value={t.value}
                                          key={t.value}
                                        >
                                          {t.label}
                                        </XlbSelect.Option>
                                      ))}
                                    </XlbSelect>
                                  </XlbBasicForm.Item>
                                </Col>
                              );
                            }}
                          </XlbBasicForm.Item>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item label="单据状态" name="state">
                              <XlbSelect
                                disabled
                                style={{ width: '100%' }}
                                allowClear={false}
                                options={tableStateOptions}
                              ></XlbSelect>
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                            <XlbBasicForm.Item
                              label="商品部门"
                              name="item_dept_names"
                            >
                              <XlbInput
                                disabled
                                size="small"
                                style={{ width: '100%' }}
                              />
                            </XlbBasicForm.Item>
                          </Col>
                          <Col xs={16} sm={16} md={16} lg={16} xl={12} xxl={12}>
                            <XlbBasicForm.Item label="留言备注" name="memo">
                              <XlbInput
                                style={{ width: '100%' }}
                                maxLength={50}
                                placeholder="请输入"
                                disabled={
                                  info.state !== 'INIT' ||
                                  !hasAuth(['仓间调拨计划单', '编辑'])
                                }
                                onChange={(e) => {
                                  setEdit(true);
                                }}
                              />
                            </XlbBasicForm.Item>
                          </Col>
                        </Row>
                      </div>
                      {info?.state && (
                        <div
                          style={{
                            width: '150px',
                            flexBasis: '150px',
                            display: 'flex',
                            justifyContent: 'center',
                          }}
                        >
                          <img
                            src={orderStatusIcons[info?.state]}
                            width={86}
                            height={78}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ),
              },
              {
                label: '其他信息',
                key: 'otherInfo',
                children: (
                  <>
                    <div className={styles.form_container_transferDocument}>
                      <Row gutter={12} wrap>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item label="制单人" name="create_by">
                            <XlbInput style={{ width: '100%' }} disabled />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item
                            label="制单时间"
                            name="create_time"
                          >
                            <XlbInput style={{ width: '100%' }} disabled />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item label="审核人" name="audit_by">
                            <XlbInput style={{ width: '100%' }} disabled />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item label="审核时间" name="audit_time">
                            <XlbInput style={{ width: '100%' }} disabled />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item
                            label={'修改人'}
                            name={'update_by'}
                          >
                            <XlbInput style={{ width: '100%' }} disabled />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item
                            label={'修改时间'}
                            name={'update_time'}
                          >
                            <XlbInput style={{ width: '100%' }} disabled />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item
                            label={'审批人'}
                            name={'approve_by'}
                          >
                            <XlbInput style={{ width: '100%' }} disabled />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item
                            label={'审批时间'}
                            name={'approve_time'}
                          >
                            <XlbInput style={{ width: '100%' }} disabled />
                          </XlbBasicForm.Item>
                        </Col>
                      </Row>
                    </div>
                  </>
                ),
              },
            ]}
          />
        </XlbBasicForm>

        <XlbButton.Group>
          {hasAuth(['仓间调拨计划单', '编辑']) && (
            <XlbButton
              label="添加商品"
              type="primary"
              disabled={info.state !== 'INIT' || tableDisable || isLoading}
              onClick={async () => {
                invalidReasonForm.resetFields();
                const flag = await XlbTipsModal({
                  title: '添加商品',
                  isCancel: true,
                  width: 500,
                  bordered: true,
                  tips: (
                    <div style={{ margin: '20px 0 6px 12px' }}>
                      <XlbBasicForm form={invalidReasonForm}>
                        <XlbBasicForm.Item
                          name="store_id"
                          label="调出门店"
                          rules={[{ required: true, message: '请选择门店' }]}
                        >
                          <XlbInputDialog
                            dialogParams={{
                              type: 'store',
                              isMultiple: true,
                              data: {
                                center_flag: true,
                                // org_ids: form.getFieldValue('in_two_org_id')
                                //   ? [form.getFieldValue('in_two_org_id')]
                                //   : null,
                              },
                            }}
                            fieldNames={{
                              idKey: 'id',
                              nameKey: 'store_name',
                            }}
                            onChange={(e: any, options: any) => {
                              invalidReasonForm.setFieldsValue({
                                store_list: options,
                                store_id: e,
                                item_id: null,
                              });
                            }}
                            width={264}
                          />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item noStyle dependencies={['store_id']}>
                          {({ getFieldValue }) => (
                            <XlbBasicForm.Item
                              name="item_id"
                              label="商品名称"
                              rules={[
                                { required: true, message: '请选择商品' },
                              ]}
                            >
                              <XlbInputDialog
                                disabled={!getFieldValue('store_id')}
                                dialogParams={{
                                  type: 'planListGoods',
                                  dataType: 'lists',
                                  data: {
                                    in_org_id: form.getFieldValue('in_org_id'),
                                    in_store_id:
                                      form.getFieldValue('in_store_id'),
                                    out_store_ids: getFieldValue('store_id'),
                                  },
                                  isMultiple: true,
                                }}
                                onChange={(e: any, options: any) => {
                                  invalidReasonForm.setFieldsValue({
                                    // item_list: options,
                                    item_id: e,
                                  });
                                  itemListRef.current = options;
                                }}
                                width={264}
                              />
                            </XlbBasicForm.Item>
                          )}
                        </XlbBasicForm.Item>
                      </XlbBasicForm>
                    </div>
                  ),
                  onOkBeforeFunction: async () => {
                    try {
                      await invalidReasonForm.validateFields();
                    } catch (err: any) {
                      return false;
                    }
                    const res = await ErpRequest.post('/erp/hxl.erp.warehousetransferplan.store.check', {
                      in_store_id: form.getFieldValue('in_store_id'),
                      in_org_id: form.getFieldValue('in_org_id'),
                      out_store_ids: invalidReasonForm.getFieldValue('store_id'),
                    })
                    return res.code === 0;
                  },
                });
                if (flag) {
                  confirmAdd(
                    itemListRef.current || [],
                    // invalidReasonForm.getFieldValue('item_list'),
                    // invalidReasonForm.getFieldValue('store_list'),
                  );
                }
              }}
              icon={<XlbIcon size={16} name="jia" />}
            />
          )}
          {hasAuth(['仓间调拨计划单', '删除']) && (
            <XlbButton
              label="批量删除"
              type="primary"
              disabled={
                info.state !== 'INIT' || !chooseList?.length || tableDisable || isLoading
              }
              icon={<XlbIcon size={16} name="shanchu" />}
              onClick={() => {
                const dt = rowData.filter(
                  (item: any) => !chooseList?.includes(item._custem_id),
                );
                setChooseList([]);
                setRowData([...dt]);
              }}
            />
          )}
          {hasAuth(['仓间调拨计划单', '编辑']) && (
            <XlbButton
              label="批量修改"
              type="primary"
              disabled={
                info.state !== 'INIT' || tableDisable || !rowData?.length || isLoading
              }
              onClick={async () => {
                invalidReasonForm.resetFields();
                const flag = await XlbTipsModal({
                  title: '批量修改',
                  isCancel: true,
                  width: 480,
                  bordered: true,
                  tips: (
                    <div style={{ margin: '20px 0 6px 64px' }}>
                      <XlbBasicForm
                        form={invalidReasonForm}
                        layout="horizontal"
                      >
                        <XlbBasicForm.Item
                          name="checkValue"
                          rules={[{ required: true, message: '请勾选修改项' }]}
                        >
                          <XlbCheckbox.Group>
                            {/*<div*/}
                            {/*  style={{ display: 'flex', alignItems: 'center' }}*/}
                            {/*>*/}
                            {/*  <XlbCheckbox*/}
                            {/*    value={'order_order_quantity'}*/}
                            {/*    style={{ marginBottom: 12, width: 106 }}*/}
                            {/*  >*/}
                            {/*    单品起订量*/}
                            {/*  </XlbCheckbox>*/}
                            {/*  <XlbBasicForm.Item*/}
                            {/*    name="order_order_quantity"*/}
                            {/*    rules={[*/}
                            {/*      {*/}
                            {/*        validator: (_, value) => {*/}
                            {/*          const checkValue =*/}
                            {/*            invalidReasonForm.getFieldValue(*/}
                            {/*              'checkValue',*/}
                            {/*            );*/}
                            {/*          if (*/}
                            {/*            checkValue?.includes(*/}
                            {/*              'order_order_quantity',*/}
                            {/*            ) &&*/}
                            {/*            !value &&*/}
                            {/*            value !== 0*/}
                            {/*          ) {*/}
                            {/*            return Promise.reject(*/}
                            {/*              new Error('请填写单品起订量'),*/}
                            {/*            );*/}
                            {/*          }*/}
                            {/*          return Promise.resolve();*/}
                            {/*        },*/}
                            {/*      },*/}
                            {/*    ]}*/}
                            {/*  >*/}
                            {/*    <XlbInputNumber*/}
                            {/*      style={{ width: 180 }}*/}
                            {/*      step={0.001}*/}
                            {/*      min={0}*/}
                            {/*      precision={3}*/}
                            {/*    />*/}
                            {/*  </XlbBasicForm.Item>*/}
                            {/*</div>*/}
                            <div
                              style={{ display: 'flex', alignItems: 'center' }}
                            >
                              <XlbCheckbox
                                value={'monthly_allotment'}
                                style={{ marginBottom: 12, width: 106 }}
                              >
                                月度调拨量
                              </XlbCheckbox>
                              <XlbBasicForm.Item
                                name="monthly_allotment"
                                rules={[
                                  {
                                    validator: (_, value) => {
                                      const checkValue =
                                        invalidReasonForm.getFieldValue(
                                          'checkValue',
                                        );
                                      if (
                                        checkValue?.includes(
                                          'monthly_allotment',
                                        ) &&
                                        !value &&
                                        value !== 0
                                      ) {
                                        return Promise.reject(
                                          new Error('请填写月度调拨量'),
                                        );
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <XlbInputNumber
                                  style={{ width: 180 }}
                                  step={0.001}
                                  min={0}
                                  max={9999999}
                                  precision={3}
                                />
                              </XlbBasicForm.Item>
                            </div>
                            <div
                              style={{ display: 'flex', alignItems: 'center' }}
                            >
                              <XlbCheckbox
                                value={'allocation_fees'}
                                style={{ marginBottom: 12, width: 106 }}
                              >
                                调拨费用
                              </XlbCheckbox>
                              <XlbBasicForm.Item
                                name="allocation_fees"
                                rules={[
                                  {
                                    validator: (_, value) => {
                                      const checkValue =
                                        invalidReasonForm.getFieldValue(
                                          'checkValue',
                                        );
                                      if (
                                        checkValue?.includes(
                                          'allocation_fees',
                                        ) &&
                                        !value &&
                                        value !== 0
                                      ) {
                                        return Promise.reject(
                                          new Error('请填写调拨费用'),
                                        );
                                      }
                                      return Promise.resolve();
                                    },
                                  },
                                ]}
                              >
                                <XlbInputNumber
                                  style={{ width: 180 }}
                                  step={0.01}
                                  min={0}
                                  max={9999999}
                                  precision={2}
                                />
                              </XlbBasicForm.Item>
                            </div>
                          </XlbCheckbox.Group>
                        </XlbBasicForm.Item>
                      </XlbBasicForm>
                    </div>
                  ),
                  onOkBeforeFunction: async () => {
                    try {
                      await invalidReasonForm.validateFields();
                    } catch (err: any) {
                      return false;
                    }
                    return true;
                  },
                });
                if (flag) {
                  setEdit(true);
                  const fd = invalidReasonForm.getFieldsValue(true);
                  setRowData((pre) => {
                    return pre.map((item) => {
                      return {
                        ...item,
                        quantity: fd?.checkValue.includes('monthly_allotment')
                          ? fd?.monthly_allotment
                          : item.quantity,
                        money: fd?.checkValue.includes('allocation_fees')
                          ? fd?.allocation_fees
                          : item.money,
                      };
                    });
                  });
                }
              }}
              icon={<XlbIcon size={16} name="xiugai1" />}
            />
          )}
          {hasAuth(['仓间调拨计划单', '导入']) && (
            <XlbButton
              label="导入"
              type="primary"
              disabled={info.state !== 'INIT' || tableDisable || isLoading}
              onClick={() => importStores()}
              icon={<XlbIcon size={16} name="daoru" />}
            />
          )}
        </XlbButton.Group>
        <XlbTable
          isLoading={isLoading}
          selectMode="multiple"
          primaryKey="_custem_id"
          clickArea="checkbox"
          columns={itemArrdetail?.map((v) => tableRender(v))}
          style={{ flex: 1, marginTop: 5 }}
          dataSource={rowData}
          total={rowData.length}
          footerDataSource={footerData}
          showSearch
          selectedRowKeys={chooseList}
          onClickRow={(row: any) => {
            // 如果不使用这个方法自定义_custemEdit，会导致勾选和table编辑同时触发，更改clickArea后，不会触发_click。
            const d = rowData?.find((item) => item._custemEdit);
            if (d) {
              d._custemEdit = false;
            }
            const idx = rowData?.findIndex(
              (item) => item._custem_id === row?._custem_id,
            );
            if (rowData[idx]) {
              rowData[idx]._custemEdit = true;
              setRowData([...rowData]);
            }
          }}
          onSelectRow={(selectedRowKeys: any) => setChooseList(selectedRowKeys)}
        />
      </div>
    </ConfigProvider>
  );
};
export default Item;