import { SearchFormType, XlbTableColumnProps } from '@xlb/components';
import { LStorage } from '@xlb/utils';
const getOrgNames = () => {
  const queryOrgList = LStorage.get('userInfo')?.query_orgs || [];
  const orgIdList = LStorage.get('userInfo')?.org_ids || [];

  const orgNames = orgIdList
    .map((item) => queryOrgList?.find((org) => org.id === item))
    .filter(Boolean)
    .map((org) => org.name);

  return orgNames; // 返回组织名称数组
};

export const basicFormList: SearchFormType[] = [
  {
    width: 372,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
  },
  {
    type: 'select',
    name: 'state',
    label: '单据状态',
    options: [
      {
        label: '制单',
        value: 'INIT',
      },
      {
        label: '审核',
        value: 'AUDIT',
      },
    ],
  },
  {
    type: 'select',
    name: 'flag',
    label: '进出方向',
    options: [
      {
        label: '发货',
        value: 1,
      },
      {
        label: '退货',
        value: 0,
      },
    ],
  },
  {
    type: 'inputDialog',
    name: 'requisition_org_ids',
    label: '领用部门',
    treeModalConfig: {
      zIndex: 2002,
      title: '选择部门', // 标题
      url: '/erp/hxl.erp.requisitionorder.deptinfos', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'oid',
      params: {
        names: getOrgNames(),
      },
      fieldName: {
        id: 'oid',
        parent_id: 'parent_id',
        name: 'name',
      },
      width: 360,
      renderInputValue: (data: any) => {
        return data.map((item: any) => item.poid_org_admin_name);
      },
    },
    fieldNames: {
      idKey: 'oid',
      nameKey: 'poid_org_admin_name',
    } as any,
  },
  {
    type: 'select',
    name: 'time_type',
    label: '时间类型',
    options: [
      {
        label: '制单时间',
        value: 'create_date',
      },
      {
        label: '审核时间',
        value: 'audit_date',
      },
      {
        label: '领用时间',
        value: 'operate_date',
      },
    ],
  },
  {
    type: 'inputDialog',
    name: 'item_ids',
    label: '商品档案',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    type: 'input',
    name: 'fid',
    label: '单据号',
    tooltip: '单据号不受其他查询条件限制',
  },
  {
    type: 'inputDialog',
    label: '货主',
    name: 'cargo_owner_ids',
    fieldNames: {
      idKey: 'id',
      nameKey: 'source_name',
    } as any,
    dialogParams: {
      type: 'cargoOwner',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      data: {
        filterSupplier: true,
        owner_type: 'ORGANIZATION',
      },
    },
  },
  {
    type: 'inputDialog',
    label: '发货组织',
    name: 'org_ids',
    treeModalConfig: {
      title: '选择组织',
      url: '/erp/hxl.erp.org.tree',
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
    },
  },
  {
    type: 'inputDialog',
    label: '发货门店',
    name: 'store_ids',
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {},
    },
  },
  {
    type: 'select',
    name: 'storehouse_id',
    label: '仓库',
    options: [],
    dependencies: ['store_ids'],
    handleDefaultValue: (data: any, formData: any) => {
      if (data?.length === 0) {
        return null;
      }
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return defaultStoreHouse?.value;
    },
    disabled: (form) => {
      const store_ids = form.getFieldValue('store_ids');
      return !store_ids || store_ids?.length > 1;
    },
    // @ts-ignore
    selectRequestParams: (params: any, form: any) => {
      form?.setFieldsValue({
        storehouse_id: null,
      });
      if (params?.store_ids?.length == 1) {
        return {
          url: '/erp/hxl.erp.storehouse.store.find',
          postParams: {
            store_id: params?.store_ids?.[0],
          },
          responseTrans(data) {
            const options = data
              .filter((v: any) => v.distribution)
              .map((item: any) => ({
                label: item.name,
                value: item.id,
                default_flag: item.default_flag,
              }));
            return options;
          },
        };
      }
    },
  },
  {
    type: 'select',
    name: 'reason_ids',
    label: '领用原因',
    options: [],
    selectRequestParams: {
      url: '/erp/hxl.erp.requisitionreason.find',
      postParams: {},
      responseTrans(data) {
        const options = data.map((item: any) => ({
          label: item.name,
          value: item.id,
        }));
        return options;
      },
    },
  },
  {
    type: 'input',
    name: 'create_by',
    label: '制单人',
  },
  {
    type: 'input',
    name: 'audit_by',
    label: '审核人',
  },
];
export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 80,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '进出方向',
    code: 'flag',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '发货组织',
    code: 'org_id',
    width: 140,
    hiddenInXlbColumns: true,
    features: { sortable: true },
    align: 'left',
    render: (value: any, record: any) => {
      return record?.org_name;
    },
  },
  {
    name: '货主',
    code: 'cargo_owner_id',
    width: 140,
    hiddenInXlbColumns: true,
    features: { sortable: true },
    align: 'left',
    render: (value: any, record: any) => {
      return record?.cargo_owner_name;
    },
  },
  {
    name: '发货门店',
    code: 'store_id',
    width: 140,
    features: { sortable: true },
    align: 'left',
    render: (value, row) => {
      return row.store_name;
    },
  },
  {
    name: '仓库',
    code: 'storehouse_id',
    width: 140,
    features: { sortable: true },
    align: 'left',
    render: (value, row) => {
      return row.store_name;
    },
  },
  {
    name: '领用部门',
    code: 'requisition_org_id',
    width: 140,
    features: { sortable: true },
    align: 'left',
    render: (value, row) => {
      return row.requisition_org_name;
    },
  },
  {
    name: '领用原因',
    code: 'reason_id',
    width: 140,
    features: { sortable: true },
    align: 'left',
    render: (value, row) => {
      return row.reason_name;
    },
  },
  {
    name: '商品数',
    code: 'item_count',
    width: 140,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 140,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 140,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单据状态',
    code: 'state',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '制单人',
    code: 'create_by',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 180,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 180,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '领用时间',
    code: 'operate_date',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: 'WMS单号',
    code: 'wms_fid',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '留言备注',
    code: 'memo',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '关联领用申请单',
    code: 'requisition_order_fid',
    width: 160,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '打印次数',
    code: 'print_count',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
];
//商品明细
export const itemTableListDetail: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 100,
    align: 'center',
  },
  {
    name: '操作',
    code: '_operator',
    align: 'center',
    width: 80,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true, showShort: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'ratio',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '销项税率(%)',
    code: 'output_tax_rate',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '税费',
    code: 'tax_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '时点成本价',
    code: 'original_cost_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '时点成本价(去税)',
    code: 'original_no_tax_cost_money',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '保质期',
    code: 'period',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '库存数量',
    code: 'basic_stock_quantity',
    width: 160,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: 160,
    features: { sortable: true },
    align: 'left',
  },
];
export const itemTableListSummaryDetail: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 100,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true, showShort: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'ratio',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '销项税率(%)',
    code: 'output_tax_rate',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '税费',
    code: 'tax_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '时点成本价',
    code: 'original_cost_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '时点成本价(去税)',
    code: 'original_no_tax_cost_money',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '保质期',
    code: 'period',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '库存数量',
    code: 'basic_stock_quantity',
    width: 160,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: 160,
    features: { sortable: true },
    align: 'left',
  },
];
export const StateType = [
  {
    label: '制单',
    value: 'INIT',
  },
  {
    label: '审核',
    value: 'AUDIT',
  },
  {
    label: '处理拒绝',
    value: 'HANDLE_REFUSE',
  },
  {
    label: '处理通过',
    value: 'HANDLE',
  },
  {
    label: '批复拒绝',
    value: 'APPROVE_REFUSE',
  },
  {
    label: '批复通过',
    value: 'APPROVE',
  },
  {
    label: '处理中',
    value: 'HANDLE_ING',
  },
];
