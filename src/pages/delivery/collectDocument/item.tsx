import { orderStatusIcons } from '@/components/common/data';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import {
  XlbBaseUpload,
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbDatePicker,
  XlbDropdownButton,
  XlbIcon,
  XlbInput,
  XlbInputDialog,
  XlbInputNumber,
  XlbMessage,
  XlbPageContainer,
  XlbPrintModal,
  XlbSelect,
  XlbShortTable,
  XlbTable,
  XlbTableColumnProps,
  XlbTabs,
  XlbTipsModal,
} from '@xlb/components';
import openTreeModal from '@xlb/components/dist/components/XlbTree/index.modal';
import { LStorage, XlbFetch } from '@xlb/utils';
import { Tabs } from 'antd';
import TabPane from 'antd/es/tabs/TabPane';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { itemTableListDetail, itemTableListSummaryDetail } from './data';
import style from './index.less';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const Item = (props) => {
  const [info, setInfo] = useState({ state: 'INIT' });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [fid, setFid] = useState<any>(1);
  const [rowData, setRowData] = useState<any>([]);
  const [fileList, setFileList] = useState<any[]>([]);
  const [form] = XlbBasicForm.useForm();
  const { record, onBack } = props;
  const [storeHouse, setStoreHouse] = useState<any[]>([]);
  const { enable_organization, enable_cargo_owner } = useBaseParams(
    (state) => state,
  );
  const [cargo_org_id, setCargo_org_id] = useState<any>(null);
  const [itemArrdetail, setItemArrdetail] = useState<
    XlbTableColumnProps<any>[]
  >(cloneDeep(itemTableListDetail));
  const [itemArrdetailSummary, setItemArrdetailSummary] = useState<
    XlbTableColumnProps<any>[]
  >(cloneDeep(itemTableListSummaryDetail));

  const [activeKey, setActiveKey] = useState('goodsDetail');
  const [baseInfoActiveKey, setBaseInfoActiveKey] = useState('baseInfo');
  const [edit, setEdit] = useState(false);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 });
  const [reasonList, setReasonList] = useState<any[]>([]);
  const [deptinfoId, setDeptinfoId] = useState<any>('');
  const selectedNodesRef = useRef<any>(null);
  const [isHasItemCode, setIsHasItemCode] = useState(false);
  const [disabledCargoOwner, setDisabledCargoOwner] = useState(false);
  // 获取仓库
  const getStoreHouse = async (id, type = false) => {
    if (!id) {
      return;
    }
    const data = {
      store_id: id,
    };
    const res = await XlbFetch.post('/erp/hxl.erp.storehouse.store.find', data);
    if (res.code == 0) {
      const options = res?.data
        .filter((v: any) => v.distribution)
        .map((item: any) => ({
          label: item.name,
          value: item.id,
          default_flag: item.default_flag,
        }));
      setStoreHouse(options);
      if (type) {
        form.setFieldsValue({
          storehouse_id:
            options.find((v: any) => v.default_flag)?.value ||
            options[0]?.value,
        });
      }
    }
  };
  // 获取货主
  const getEnableCargoOwner = async (id) => {
    const data = {
      store_ids: [id],
      owner_type: 'ORGANIZATION',
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.cargo.owner.pageforinner',
      data,
    );
    console.log(res);
    // 只有一个货主
    if (res?.data?.content?.length == 1) {
      form.setFieldsValue({
        cargo_owner_id: res.data.content[0].id,
        cargo_owner_name: res.data.content[0].name,
      });
      setDisabledCargoOwner(true);
    }
    if (res?.data?.content?.length !== 1) {
      form.setFieldsValue({
        cargo_owner_id: null,
        cargo_owner_name: null,
      });
      setDisabledCargoOwner(false);
    }
  };
  const inputChange = (e: any, code: string, record: any, index: any) => {
    const targetValue = e;
    if (code == 'quantity') {
      record['basic_quantity'] = targetValue * record['ratio'];
      record['quantity'] = Number(targetValue);
      record['money'] = targetValue * record['price'];
    }
    setRowData([...rowData]);
  };
  const changeRatio = (e: any, record: any) => {
    let unitName = record.unit;
    record.units.forEach((item: any) => {
      if (item.value === e) {
        unitName = item.label;
      }
    });

    record.unit = unitName;
    record.ratio = e;
    record.price = e * record.basic_price;
    record.money = e * record.basic_price * record.quantity;
    record.basic_quantity = record.quantity * e;
    record['old_ratio'] = e;
    setRowData([...rowData]);
  };
  const exportItem = async (e) => {
    const data = {
      fid: record?.fid == 1 ? form.getFieldValue('fid') : record?.fid,
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.requisitioninoutorder.detail.export',
      data,
    );
    if (res?.code == 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success('导出受理成功,请到下载中心查看');
    }
  };
  const handleClick = async () => {
    const bool = await XlbFetch.post(
      '/erp/hxl.erp.requisitioninoutorder.print',
      {
        fid: record?.fid == 1 ? form.getFieldValue('fid') : record?.fid,
      },
    );
    XlbPrintModal({
      data: bool?.data,
      title: '领用进出单打印模板',
    });
  };
  const save = async () => {
    try {
      await form.validateFields();
    } catch (err: any) {
      return false;
    }
    setIsLoading(true);
    const values = form.getFieldsValue(true);
    const validRows = rowData.filter((item: any) => !item._empty);
    if (
      validRows.some(
        (item: any) =>
          item.quantity === undefined ||
          item.quantity === null ||
          item.quantity === '',
      )
    ) {
      XlbTipsModal({
        tips: '请填写数量！',
      });
      return false;
    }
    const cargo_owner_id = values.cargo_owner_id;
    const data = {
      ...values,
      store_id: values.store_id?.[0],
      cargo_owner_id: Array.isArray(cargo_owner_id)
        ? cargo_owner_id[0]
        : cargo_owner_id,
      details: rowData.filter((item: any) => !item._empty),
      files: fileList,
    };
    const url =
      record?.fid == 1 && !form.getFieldValue('fid')
        ? '/erp/hxl.erp.requisitioninoutorder.save'
        : '/erp/hxl.erp.requisitioninoutorder.update';
    const res = await XlbFetch.post(url, data);
    if (res?.code == 0) {
      XlbMessage.success('保存成功');
      readinfo(res?.data?.fid);
      setEdit(false);
    }
    setIsLoading(false);
  };
  const footerSet = () => {
    footerData[0] = {};
    footerData[0]._index = '合计';
    footerData[0].money = hasAuth(['领用进出单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) =>
              sum + Number(v.money && v.money != '****' ? v.money : 0),
            0,
          )
          .toFixed(4)
      : '****';
    footerData[0].quantity = rowData
      .reduce(
        (sum, v) =>
          sum + Number(v.quantity && v.quantity != '****' ? v.quantity : 0),
        0,
      )
      .toFixed(3);
    footerData[0].tax_money = hasAuth(['领用进出单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) =>
              sum +
              Number(v.tax_money && v.tax_money != '****' ? v.tax_money : 0),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].basic_quantity = rowData
      .reduce(
        (sum, v) =>
          sum +
          Number(
            v.basic_quantity && v.basic_quantity != '****'
              ? v.basic_quantity
              : 0,
          ),
        0,
      )
      .toFixed(3);
    footerData[0].original_no_tax_money = hasAuth(['领用进出单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) =>
              sum +
              Number(
                v.original_no_tax_money && v.original_no_tax_money != '****'
                  ? v.original_no_tax_money
                  : 0,
              ),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].original_money = hasAuth(['领用进出单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) =>
              sum +
              Number(
                v.original_money && v.original_money != '****'
                  ? v.original_money
                  : 0,
              ),
            0,
          )
          .toFixed(4)
      : '****';
    footerData[0].original_no_tax_money = hasAuth(['领用进出单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) =>
              sum +
              Number(
                v.original_no_tax_money && v.original_no_tax_money != '****'
                  ? v.original_no_tax_money
                  : 0,
              ),
            0,
          )
          .toFixed(4)
      : '****';
    setFooterData(footerData);
  };
  const getReasonList = async () => {
    const res = await XlbFetch.post('/erp/hxl.erp.requisitionreason.find', {});
    if (res?.code == 0) {
      const options = res?.data.map((item: any) => ({
        label: item.name,
        value: item.id,
        disabled: !item.flag,
      }));
      setReasonList(options);
    }
  };
  // 批量添加
  const confirmAdd = (list: any, add_type = 'item') => {
    let filterArr = [...list];
    const ids = rowData.map((v) => v.item_id);
    const repeatArr = list?.filter((v) =>
      ids.includes(add_type === 'import' ? v?.item_id : v.id),
    );
    const rName = [
      repeatArr.map(
        (v) => `【${add_type === 'import' ? v?.item_name : v.name}】`,
      ),
    ];
    if (repeatArr.length) {
      XlbTipsModal({
        tips: '以下商品已存在，不允许重复添加，系统已自动过滤！',
        tipsList: rName,
      });
    }
    filterArr = list.filter(
      (item: any) =>
        !ids.includes(add_type === 'import' ? item?.item_id : item.id),
    );

    const newList = filterArr?.map((v) => {
      const units = Array.from(
        new Set([
          JSON.stringify({
            label: v.delivery_unit,
            value: v.delivery_ratio,
          }),
          JSON.stringify({ label: v.unit, value: 1 }),
        ]),
      ).map((item) => {
        return JSON.parse(item);
      });
      return {
        ...v,
        units: units,
        ratio: v.delivery_ratio,
        item_id: v.id || v.item_id, // item_id 用于标识
        item_code: v.code || v.item_code, // 商品代码
        item_bar_code: v.bar_code || v.item_bar_code, // 商品条码
        item_name: v.name || v.item_name, // 商品名称
        item_spec: v.purchase_spec || v.item_spec, // 采购规格
        unit: v.delivery_unit || v.unit, // 单位
        quantity: Number(v.quantity) || 0, // 数量
        price: v.basic_price * v.delivery_ratio || 0, // 单价
        money: v.money || 0, // 金额
        basic_unit: v.unit, // 默认配送单位
        basic_quantity: v.quantity * v.delivery_ratio || 0, // 基本数量
        basic_price: v.basic_price,
        output_tax_rate: v.output_tax_rate || 0, // 销项税率
        tax_money: v.tax_money || 0, // 税费
        original_money: v.original_money || 0, // 时点成本价
        original_no_tax_money: v.original_no_tax_money || 0, // 时点成本价(去税)
        producing_date: v.producing_date || '', // 生产日期
        expire_date: v.expire_date || '', // 到期日期
        batch_number: v.batch_number || '', // 批次号
        period: v.period || '', // 保质期
        basic_stock_quantity: v.basic_stock_quantity || 0, // 库存数量
        memo: v.memo || '',
        _click: false,
        _edit: false,
      };
    });
    newList.length > 0 ? setEdit(true) : setEdit(false);
    // 最后过滤掉没有item_id的
    const mergeArr = [...rowData, ...newList]?.filter((v) => v?.item_id);
    setRowData(
      mergeArr?.map((v, index) => ({ ...v, key: index + '_' + v?.item_id })),
    );
  };
  const itemAudit = async () => {
    setIsLoading(true);
    const values = form.getFieldsValue(true);
    const validRows = rowData.filter((item: any) => !item._empty);
    if (
      validRows.some(
        (item: any) =>
          item.quantity === undefined ||
          item.quantity === null ||
          item.quantity === '',
      )
    ) {
      XlbTipsModal({
        tips: '请填写数量！',
      });
      return false;
    }
    const cargo_owner_id = values.cargo_owner_id;
    const data = {
      ...values,
      details: rowData,
      cargo_owner_id: Array.isArray(cargo_owner_id)
        ? cargo_owner_id[0]
        : cargo_owner_id,
      store_id: values.store_id?.[0],
      fid: record?.fid == 1 ? form?.getFieldValue('fid') : record?.fid,
      files: fileList,
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.requisitioninoutorder.audit',
      data,
    );
    if (res?.code == 0) {
      XlbMessage.success('审核成功');
      readinfo(fid);
    }
    setIsLoading(false);
  };
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'ratio':
        item.render = (value: any, record: any, index: number) => {
          return record._click && info.state === 'INIT' && record?.item_name ? (
            <div onClick={(e) => e.stopPropagation()}>
              <XlbSelect
                width={80}
                options={record.units || []}
                defaultValue={value}
                allowClear={false}
                onChange={(e) => {
                  changeRatio(e, record);
                }}
              />
            </div>
          ) : (
            record.units?.find((v) => v.value === record.ratio)?.label
          );
        };
        break;
      case 'quantity':
        item.render = (value: any, record: any, index: any) => {
          return record?._click &&
            info.state === 'INIT' &&
            record?.item_name ? (
            <XlbInputNumber
              style={{ width: 80 }}
              step={0.001}
              min={0}
              precision={3}
              value={value}
              onChange={(e) =>
                inputChange(e, item.code, record, index['index'])
              }
            ></XlbInputNumber>
          ) : (
            Number(value || 0)?.toFixed(3)
          );
        };
        break;
      case 'basic_quantity':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{Number(value || 0)?.toFixed(3)}</div>;
        };
        break;

      case 'tax_money':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{Number(value || 0)?.toFixed(2)}</div>;
        };
        break;
      case 'initial_quantity':
      case 'activity_quantity':
        item.render = (value: any, record: any, index: number) => {
          if (record?._click && record?.item_name) {
            return (
              <XlbInput
                id={item.code + '-' + index.toString()}
                defaultValue={value?.toFixed(3)}
                onFocus={(e) => e.target.select()}
                onChange={(e) => {}}
                onBlur={(e) => {}}
              />
            );
          } else {
            return <div className="info">{value?.toFixed(3)}</div>;
          }
        };
        break;
      case 'original_money':
      case 'original_no_tax_money':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{Number(value || 0)?.toFixed(4)}</div>;
        };
        break;
      case 'price':
      case 'basic_price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">
              {hasAuth(['领用进出单/成本价', '查询'])
                ? Number(value || 0)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'money':
        item.render = (value: any, record: any) => {
          return hasAuth(['领用进出单/成本价', '查询']) && value !== '****'
            ? Number(value || 0)?.toFixed(2)
            : '****';
        };
        break;
      case 'memo':
        item.render = (value: any, record: any, index: number) => {
          if (record?._click && record?.item_name) {
            return (
              <XlbInput
                id={item.code + '-' + index.toString()}
                onFocus={(e) => e.target.select()}
                onChange={(e) => {
                  record.memo = e?.target?.value;
                }}
                onBlur={(e) => {}}
              />
            );
          } else {
            return <div className="info">{value}</div>;
          }
        };
        break;
    }
    return item;
  };
  const summaryRender = (item: any) => {
    switch (item.code) {
      case 'original_money':
      case 'original_no_tax_money':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{Number(value || 0)?.toFixed(4)}</div>;
        };
        break;
      case 'price':
      case 'basic_price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">
              {hasAuth(['领用进出单/成本价', '查询'])
                ? Number(value || 0)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'money':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{Number(value || 0)?.toFixed(4)}</div>;
        };
        break;
    }
    return item;
  };
  const readinfo = async (fid?: number | string) => {
    const data = {
      fid: fid ? fid : form.getFieldValue('fid') || record?.fid,
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.requisitioninoutorder.read',
      data,
    );
    if (res.code === 0) {
      getStoreHouse(res?.data?.store_id, false);
      form.setFieldsValue({
        ...res.data,
        store_id: [res?.data?.store_id],
        flag: res?.data?.flag ? 1 : 0,
      });
      selectedNodesRef.current = [
        {
          oid: res?.data?.requisition_org_id,
          name: res?.data?.requisition_org_name,
        },
      ];
      setFileList(res.data.files);
      setFid(res.data.fid);
      setInfo({ state: res.data.state });
      setDeptinfoId(res.data.requisition_org_id);
      setRowData(
        res.data.details?.map((v, index) => {
          return {
            ...v,
            key: index + '_' + v?.item_id,
            units: Array.from(
              new Set([
                JSON.stringify({
                  label: v.delivery_unit,
                  value: v.delivery_ratio,
                }),
                JSON.stringify({ label: v.basic_unit, value: 1 }),
                JSON.stringify({ label: v.unit, value: v.ratio }),
              ]),
            ).map((item) => {
              return JSON.parse(item);
            }),
          };
        }),
      );
      setPagin({ ...pagin, total: res.data.details.length });
      if (enable_cargo_owner) {
        // 根据领用部门刷新cargo_org_id
        getSecondOrgAndCargoOwner(res?.data?.requisition_org_name, false);
      }
    }
  };
  const getOrgNames = () => {
    const queryOrgList = LStorage.get('userInfo')?.query_orgs || [];
    const orgIdList = LStorage.get('userInfo')?.org_ids || [];

    const orgNames = orgIdList
      .map((item) => queryOrgList?.find((org) => org.id === item))
      .filter(Boolean)
      .map((org) => org.name);

    return orgNames; // 返回组织名称数组
  };
  // 获取默认部门
  const getDept = async () => {
    const data = {
      names: getOrgNames(),
      phone: LStorage.get('userInfo').tel,
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.requisitionorder.deptinfo',
      data,
    );
    form.setFieldsValue({
      requisition_org_name: res?.data?.poid_org_admin_name,
      requisition_org_id: res?.data?.oid,
    });
  };
  const onChangeArea = async (e) => {
    form.setFieldsValue({
      requisition_org_name: '',
      requisition_org_id: null,
      store_id: null,
      storehouse_id: null,
      org_name: '',
      org_id: null,
      cargo_owner_id: null,
      source_name: '',
    });
    setCargo_org_id(null);
  };
  const getSecondOrgAndCargoOwner = async (
    name: any,
    isEdit: boolean = true,
  ) => {
    //分割name
    const nameArr = name.split('/')?.[1];

    const res = await XlbFetch.post('/erp/hxl.erp.requisitionorder.deptorgid', {
      name: nameArr,
    });
    if (res?.code == 0 && res?.data) {
      if (isEdit) {
        form.setFieldsValue({
          cargo_owner_name: res?.data?.cargo_owner_name,
          cargo_owner_id: res?.data?.cargo_owner_id,
        });
      }
      setCargo_org_id(res?.data?.org_id);
    }
  };
  const openStoreAreaModal = async () => {
    try {
      const res = await openTreeModal({
        zIndex: 2002,
        title: '选择部门', // 标题
        url: '/erp/hxl.erp.requisitionorder.deptinfos', // 请求地址
        dataType: 'lists',
        checkable: false, // 是否多选
        requestParams: {
          names: getOrgNames(),
        },
        defaultExpandKeys: [deptinfoId],
        selectedNodes: selectedNodesRef?.current,
        fieldName: {
          id: 'oid',
          parent_id: 'parent_id',
          name: 'name',
        },
        width: 360,
        renderInputValue: (data: any) => {
          return data.map((item: any) => item.poid_org_admin_name);
        },
      });
      if (res) {
        form.setFieldsValue({
          requisition_org_name: res?.[0].poid_org_admin_name,
          requisition_org_id: res?.[0].oid,
          store_id: null,
          storehouse_id: null,
          org_name: null,
          org_id: null,
          cargo_owner_id: null,
          cargo_owner_name: null,
        });
        setCargo_org_id(null);
        if (enable_cargo_owner) {
          getSecondOrgAndCargoOwner(res?.[0].poid_org_admin_name);
        }
        setDeptinfoId(res?.[0].oid);
        selectedNodesRef.current = res?.map((v) => {
          return {
            ...v,
            oid: v.oid,
            name: v.poid_org_admin_name,
          };
        });
        // 掉接口
      }
    } catch (error) {
      console.error('Error in handleOrg:', error);
    }
  };
  useEffect(() => {
    // readData()
    if (record.fid == 1) {
      getDept();
    } else {
      readinfo();
    }
    getReasonList();
  }, []);
  useEffect(() => {
    footerSet();
    if (
      rowData?.length > 0 &&
      rowData?.some((item: any) => item?.code || item?.item_code)
    ) {
      setIsHasItemCode(true);
    } else {
      setIsHasItemCode(false);
    }
  }, [rowData]);
  const goBack = async () => {
    if (edit) {
      await XlbTipsModal({
        tips: '单据未保存，是否确认返回？',
        onOkBeforeFunction: () => {
          onBack(true);
          return true;
        },
      });
      return false;
    }
    onBack(true);
  };
  return (
    <div
      style={{
        padding: 12,
        height: 'calc(100vh - 120px)',
        display: 'flex',
        flexDirection: 'column',
      }}
      className={style.itemConatainer}
    >
      <XlbButton.Group>
        {hasAuth(['领用进出单', '编辑']) && (
          <XlbButton
            label="保存"
            type="primary"
            disabled={info.state !== 'INIT'}
            loading={isLoading}
            onClick={() => save()}
            icon={<XlbIcon size={16} name="baocun" />}
          />
        )}
        {hasAuth(['领用进出单', '审核']) && (
          <XlbButton
            label="审核"
            type="primary"
            disabled={info.state !== 'INIT' || fid === 1}
            loading={isLoading}
            onClick={() => itemAudit()}
            icon={<XlbIcon size={16} name="shenhe" />}
          />
        )}
        {hasAuth(['领用进出单', '编辑']) && (
          <XlbBaseUpload
            uploadText="附件"
            multiple={true}
            disabledDelete={false}
            disabled={false}
            mode="primaryButton"
            action="/erp/hxl.erp.requisitioninoutorder.file.upload"
            listType={'table'}
            accept={'image'}
            data={{
              fid: record?.fid == 1 ? form.getFieldValue('fid') : record?.fid,
            }}
            onChange={(e) => {
              setFileList(e);
            }}
            maxCount={99}
            fileList={fileList || []}
          />
        )}

        {(hasAuth(['领用进出单', '导出']) ||
          hasAuth(['领用进出单', '打印'])) && (
          <XlbDropdownButton
            label="业务操作"
            dropList={[
              {
                label: '导出',
                disabled: !fid,
                isNoAuth: !hasAuth(['领用进出单', '导出']),
              },
              {
                label: '打印',
                disabled: !fid,
                isNoAuth: !hasAuth(['领用进出单', '打印']),
              },
            ]}
            dropdownItemClick={(index: number, item: any, e) => {
              switch (item?.label) {
                case '导出':
                  exportItem(e);
                  break;
                case '打印':
                  handleClick();
                  break;
              }
            }}
          />
        )}
        <XlbButton
          label="返回"
          type="primary"
          onClick={() => goBack()}
          icon={<XlbIcon size={16} name="fanhui" />}
        />
      </XlbButton.Group>
      <XlbBasicForm
        style={{ paddingLeft: '12px' }}
        colon
        form={form}
        autoComplete="off"
        layout="inline"
        initialValues={{
          flag: 1,
        }}
      >
        <XlbTabs
          defaultActiveKey="baseInfo"
          activeKey={baseInfoActiveKey}
          onChange={(e) => setBaseInfoActiveKey(e)}
          items={[
            {
              label: '基本信息',
              key: 'baseInfo',
              children: (
                <div style={{ display: 'flex' }}>
                  <div
                    className="row-flex"
                    style={{ flexWrap: 'wrap', flex: 1 }}
                  >
                    <XlbBasicForm.Item noStyle shouldUpdate>
                      {({ getFieldValue }) => {
                        return (
                          <>
                            <XlbBasicForm.Item
                              label="领用部门"
                              name={'requisition_org_name'}
                              rules={[
                                { required: true, message: '请选择领用部门' },
                              ]}
                              shouldUpdate
                            >
                              <XlbInput
                                style={{ width: 180 }}
                                size="small"
                                allowClear
                                disabled={
                                  info.state !== 'INIT' || isHasItemCode
                                }
                                suffix={
                                  <span
                                    style={{
                                      display: 'inline-block',
                                    }}
                                    onClick={openStoreAreaModal}
                                  >
                                    <XlbIcon
                                      name="sousuo"
                                      size={14}
                                      color="#c9cdd4"
                                    />
                                  </span>
                                }
                                onFocus={(e) => e.target.blur()}
                                onClick={openStoreAreaModal}
                                onChange={onChangeArea}
                              />
                            </XlbBasicForm.Item>
                            <XlbBasicForm.Item
                              rules={[
                                { required: true, message: '请选择发货门店' },
                              ]}
                              label="发货门店"
                              name="store_id"
                              dependencies={['requisition_org_name']}
                            >
                              <XlbInputDialog
                                width={180}
                                dialogParams={{
                                  type: 'store',
                                  dataType: 'lists',
                                  data: {
                                    center_flag: true,
                                    exclude_org_manage_store: true,
                                    org_ids: cargo_org_id
                                      ? [cargo_org_id]
                                      : null,
                                  },
                                }}
                                fieldNames={{
                                  idKey: 'id',
                                  nameKey: 'store_name',
                                }}
                                disabled={
                                  info.state !== 'INIT' ||
                                  isHasItemCode ||
                                  !getFieldValue('requisition_org_name')
                                }
                                handleValueChange={(value, options) => {
                                  if (options?.length) {
                                    form.setFieldsValue({
                                      org_name: options[0]?.org_store_name,
                                      org_id: options[0]?.org_store_id,
                                      store_id: [options?.[0]?.id],
                                    });

                                    getStoreHouse(options?.[0]?.id, true);
                                    if (!cargo_org_id && enable_cargo_owner) {
                                      form.setFieldsValue({
                                        cargo_owner_id: undefined,
                                        cargo_owner_name: undefined,
                                      });
                                      // 调接口获取货主
                                      if (enable_cargo_owner) {
                                        getEnableCargoOwner(options?.[0]?.id);
                                      }
                                    }
                                  }
                                  if (!value && !options) {
                                    form.setFieldsValue({
                                      storehouse_id: undefined,
                                    });
                                    setStoreHouse([]);
                                  }
                                }}
                              ></XlbInputDialog>
                            </XlbBasicForm.Item>

                            <XlbBasicForm.Item
                              rules={[
                                { required: true, message: '请选择发货仓库' },
                              ]}
                              label="仓库"
                              name="storehouse_id"
                            >
                              <XlbSelect
                                placeholder="请选择"
                                disabled={
                                  info.state !== 'INIT' ||
                                  isHasItemCode ||
                                  !getFieldValue('requisition_org_name')
                                }
                                style={{ width: '180px' }}
                                options={storeHouse}
                              />
                            </XlbBasicForm.Item>
                            {enable_organization && (
                              <XlbBasicForm.Item
                                hidden
                                label="发货组织"
                                name="org_id"
                              >
                                <XlbInput disabled style={{ width: '180px' }} />
                              </XlbBasicForm.Item>
                            )}

                            {enable_organization ? (
                              <XlbBasicForm.Item
                                label="发货组织"
                                name="org_name"
                              >
                                <XlbInput disabled style={{ width: '180px' }} />
                              </XlbBasicForm.Item>
                            ) : null}
                            {!cargo_org_id && enable_cargo_owner ? (
                              <XlbBasicForm.Item
                                label="货主"
                                name="cargo_owner_id"
                                rules={[
                                  { required: true, message: '请选择货主' },
                                ]}
                              >
                                <XlbInputDialog
                                  dialogParams={{
                                    type: 'cargoOwner',
                                    isMultiple: false,
                                    isLeftColumn: false,
                                    data: {
                                      store_ids: getFieldValue('store_id'),
                                      filterSupplier: true,
                                      owner_type: 'ORGANIZATION',
                                    },
                                  }}
                                  fieldNames={{
                                    idKey: 'id',
                                    nameKey: 'source_name',
                                  }}
                                  width={180}
                                  disabled={
                                    isLoading ||
                                    isHasItemCode ||
                                    info.state !== 'INIT' ||
                                    !form?.getFieldValue('store_id') ||
                                    disabledCargoOwner
                                  }
                                  placeholder="请选择"
                                  handleValueChange={(e: any, option: any) => {
                                    if (option?.length > 0) {
                                      form.setFieldsValue({
                                        cargo_owner_id: option?.[0]?.id,
                                        cargo_owner_name:
                                          option?.[0]?.source_name,
                                      });
                                    }
                                  }}
                                />
                              </XlbBasicForm.Item>
                            ) : enable_cargo_owner ? (
                              <XlbBasicForm.Item
                                label="货主"
                                name="cargo_owner_name"
                              >
                                <XlbInput disabled style={{ width: '180px' }} />
                              </XlbBasicForm.Item>
                            ) : null}
                          </>
                        );
                      }}
                    </XlbBasicForm.Item>

                    <XlbBasicForm.Item label="进出方向" name="flag">
                      <XlbSelect
                        disabled
                        style={{ width: '180px' }}
                        options={[
                          {
                            label: '发货',
                            value: 1,
                          },
                          {
                            label: '退货',
                            value: 2,
                          },
                        ]}
                      ></XlbSelect>
                    </XlbBasicForm.Item>

                    <XlbBasicForm.Item
                      rules={[{ required: true, message: '请选择领用原因' }]}
                      label="领用原因"
                      name={'reason_id'}
                    >
                      <XlbSelect
                        placeholder="请选择"
                        disabled={info.state !== 'INIT'}
                        style={{ width: '180px' }}
                        options={reasonList}
                      ></XlbSelect>
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="单据号" name="fid">
                      <XlbInput
                        size="small"
                        style={{ width: '180px' }}
                        disabled
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="商品部门" name="item_dept_names">
                      <XlbInput
                        size="small"
                        style={{ width: '180px' }}
                        disabled
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item
                      label="领用时间"
                      name="operate_date"
                      initialValue={dayjs().format('YYYY-MM-DD')}
                    >
                      <XlbDatePicker
                        style={{ width: 180 }}
                        allowClear={false}
                        format={'YYYY-MM-DD'}
                        disabled={
                          !hasAuth(['领用进出单', '编辑']) ||
                          info.state !== 'INIT'
                        }
                        disabledDate={(current) =>
                          current && current > dayjs().endOf('day')
                        }
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="留言备注" name="memo">
                      <XlbInput
                        placeholder="请输入"
                        style={{ width: 480 }}
                        maxLength={50}
                        disabled={
                          !hasAuth(['领用进出单', '编辑']) ||
                          info.state !== 'INIT'
                        }
                      />
                    </XlbBasicForm.Item>
                  </div>
                  {info?.state && (
                    <div
                      style={{
                        width: '150px',
                        flexBasis: '150px',
                        display: 'flex',
                        justifyContent: 'center',
                      }}
                    >
                      <img
                        src={orderStatusIcons[info?.state]}
                        width={86}
                        height={78}
                      />
                    </div>
                  )}
                </div>
              ),
            },
            {
              label: '其他信息',
              key: 'otherInfo',
              children: (
                <>
                  <div className="row-flex">
                    <XlbBasicForm.Item label="制单人" name="create_by">
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="制单时间" name="create_time">
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="审核人" name="audit_by">
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="审核时间" name="audit_time">
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                  </div>
                  <div className="row-flex">
                    <XlbBasicForm.Item label={'修改人'} name={'update_by'}>
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label={'修改时间'} name={'update_time'}>
                      <XlbInput style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                  </div>
                </>
              ),
            },
          ]}
        />
      </XlbBasicForm>

      <XlbButton.Group>
        {hasAuth(['领用进出单', '编辑']) && (
          <XlbButton
            label="批量添加"
            type="primary"
            disabled={info.state !== 'INIT' || !form.getFieldValue('store_id')}
            onClick={async () => {
              // handleDialogClick('item')
              const list = await XlbBasicData({
                type: 'goods',
                url: '/erp/hxl.erp.requisitioninoutorder.item.page',
                isMultiple: true,
                dataType: 'lists',
                primaryKey: 'id',
                resetForm: true,
                nullable: false,
                data: {
                  store_id: form.getFieldValue('store_id')?.[0],
                  storehouse_id: form.getFieldValue('storehouse_id'),
                  storeStatus: true,
                  cargo_owner_id: enable_cargo_owner
                    ? Array.isArray(form.getFieldValue('cargo_owner_id'))
                      ? form.getFieldValue('cargo_owner_id')[0]
                      : form.getFieldValue('cargo_owner_id')
                    : null,
                },
              });
              if (!list) return;
              if (Array.isArray(list)) {
                confirmAdd(list, 'item');
              }
            }}
            icon={<XlbIcon size={16} name="jia" />}
          />
        )}
      </XlbButton.Group>
      <Tabs activeKey={activeKey} onChange={(e) => setActiveKey(e)}>
        <TabPane tab={'商品明细'} key={'goodsDetail'}></TabPane>
        <TabPane tab={'商品汇总'} key={'goodsSummary'}></TabPane>
      </Tabs>
      {activeKey === 'goodsDetail' && (
        <XlbShortTable
          isLoading={isLoading}
          showSearch
          key={`${baseInfoActiveKey}_${activeKey}`}
          style={{ flex: 1 }}
          url={'/erp/hxl.erp.requisitioninoutorder.item.page'}
          data={{
            store_id: form.getFieldValue('store_id')?.[0],
            storehouse_id: form.getFieldValue('storehouse_id'),
            cargo_owner_id: enable_cargo_owner
              ? Array.isArray(form.getFieldValue('cargo_owner_id'))
                ? form.getFieldValue('cargo_owner_id')[0]
                : form.getFieldValue('cargo_owner_id')
              : null,
          }}
          onChangeData={(data: any[], type: string) => {
            const originIds = rowData.map((v) => v.item_id);
            const initialValues = Object.freeze({
              basic_stock_quantity: 0,
              memo: null,
            });
            const getInitials = (v: any) =>
              originIds.includes(v.item_id) ? {} : initialValues;

            setRowData(
              data.map((v, index) => {
                const itemId = v.id || v.item_id;
                const oldItem = rowData.find(
                  (item: any) => item.item_id === itemId,
                );
                if (oldItem) {
                  // 已存在，直接用老的
                  return oldItem;
                }
                return {
                  // 映射字段
                  ...v,
                  units: Array.from(
                    new Set([
                      JSON.stringify({
                        label: v.delivery_unit,
                        value: v.delivery_ratio,
                      }),
                      JSON.stringify({ label: v.unit, value: 1 }),
                    ]),
                  ).map((item) => {
                    return JSON.parse(item);
                  }),
                  key: `${index}_${v.id || v.item_id}`, // 唯一的行 key
                  ratio: v.old_ratio || v.delivery_ratio,
                  item_id: v.id || v.item_id, // item_id 用于标识
                  item_code: v.code || v.item_code, // 商品代码
                  item_bar_code: v.bar_code || v.item_bar_code, // 商品条码
                  item_name: v.name || v.item_name, // 商品名称
                  item_spec: v.purchase_spec || v.item_spec, // 采购规格
                  unit: v.delivery_unit || v.unit, // 单位
                  quantity: Number(v.quantity) || 0, // 数量
                  price: v.old_ratio
                    ? v.basic_price * v.old_ratio
                    : v.basic_price * v.delivery_ratio, // 单价
                  money: v.money || 0, // 金额
                  // basic_unit: v.unit, // 默认配送单位
                  basic_unit: v.basic_unit || v.unit, // 默认配送单位
                  basic_quantity: v.old_ratio
                    ? v.quantity * v.old_ratio
                    : v.quantity * v.delivery_ratio || 0, // 基本数量
                  basic_price: v.basic_price,
                  output_tax_rate: v.output_tax_rate || 0, // 销项税率
                  tax_money: v.tax_money || 0, // 税费
                  original_money: v.original_money || 0, // 时点成本价
                  original_no_tax_money: v.original_no_tax_money || 0, // 时点成本价(去税)
                  producing_date: v.producing_date || '', // 生产日期
                  expire_date: v.expire_date || '', // 到期日期
                  batch_number: v.batch_number || '', // 批次号
                  period: v.period || '', // 保质期
                  basic_stock_quantity: v.basic_stock_quantity || 0, // 库存数量
                  memo: v.memo || '',
                  _click: false,
                  _edit: false,
                  ...getInitials(v), // 初始化值
                };
              }),
            );
            setEdit(type !== 'onChangeSorts');
          }}
          disabled={
            info.state !== 'INIT' ||
            !hasAuth(['领用进出单', '编辑']) ||
            !form.getFieldValue('store_id')
          }
          columns={itemArrdetail?.map((v) => tableRender(v))}
          dataSource={rowData}
          footerDataSource={footerData}
          total={rowData?.length}
          selectMode="single"
          primaryKey="item_id"
          // popoverPrimaryKey="id"
        />
      )}
      {activeKey == 'goodsSummary' && (
        <XlbTable
          columns={itemArrdetailSummary.map((v) => summaryRender(v))}
          isLoading={isLoading}
          pagin={pagin}
          style={{ flex: 1 }}
          dataSource={rowData}
          total={rowData?.length}
          footerDataSource={footerData}
        />
      )}
    </div>
  );
};
export default Item;
