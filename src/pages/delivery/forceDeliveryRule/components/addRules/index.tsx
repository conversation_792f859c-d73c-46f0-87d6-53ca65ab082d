import { columnWidthEnum } from '@/pages/procurement/purchaseLatestPrice/data';
import { LStorage } from '@/utils/storage';
import { useModal } from '@ebay/nice-modal-react';
import type { BaseModalProps } from '@xlb/components';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbBlueBar,
  XlbButton,
  XlbCheckbox,
  XlbIcon,
  XlbInput,
  XlbInputDialog,
  XlbInputNumber,
  XlbModal,
  XlbRadio,
  XlbTable,
  XlbTabs,
  XlbTipsModal,
  XlbTreeModal,
} from '@xlb/components';
import { message } from 'antd';
import { useEffect, useState, type FC } from 'react';
import api from '../../server';
import dayjs from 'dayjs';
interface Props extends BaseModalProps {
  fetchData?: any;
  id?: number;
  create_by?: string;
  create_time?: string;
}

const AddItem: FC<Props> = ({ fetchData, id = -1, create_by, create_time }) => {
  const [form] = XlbBasicForm.useForm();
  const modal = useModal();
  const [rowData, setRowData] = useState<any[]>([]);
  const [goodsExcludeRowData, setGoodsExcludeRowData] = useState<any[]>([]);
  const [enabled, setEnabled] = useState(false);
  const [first_stock_log, setFirstStockLog] = useState(false);
  const [open_news_cycle, setOpenNewsCycle] = useState(false);
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [pageSize, setPageSize] = useState(200);
  const [excludeGoodsPageSize, setExcludeGoodsPageSize] = useState(200);
  const [selectRow, setSelectRow] = useState<any>([]); // 已选择的
  const [excludeGoodsSelectRow, setExcludeGoodsSelectRow] = useState<any>([]); // 已选择的

  //补货范围增加
  const addItem = async () => {
    const list = await XlbTreeModal({
      width: 320,
      multiple: true,
      title: '商品分类',
      dataType: 'lists',
      checkable: true,
      url: '/erp/hxl.erp.category.find',
    });
    if (list) {
      const obj = list?.filter((i: any) => i?.id);
      if (rowData && rowData?.length > 0) {
        const Arr3 = rowData.concat(obj);
        const newObj: any = [];
        //并集
        const result = Arr3.reduce(
          (prev: any, cur: any, index: number, array: any) => {
            newObj[cur.id] ? '' : (newObj[cur.id] = true && prev.push(cur));
            return prev;
          },
          [],
        );
        setRowData(
          result?.map((item: any) => ({
            ...item,
            create_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            create_by: LStorage.get('userInfo')?.name,
          })),
        );
      } else {
        setRowData(obj);
      }
    }
  };
  //补货范围删除
  const deleteItem = () => {
    const deleteItem = rowData?.filter(
      (item: any) => !selectRow.includes(item.id),
    );
    setRowData(deleteItem);
    setSelectRow([]);
  };

  // 排除商品
  const excludedGoods = async () => {
    const data = await XlbBasicData({
      type: 'goods',
      isMultiple: true,
      isLeftColumn: true,
      dataType: 'lists',
      primaryKey: 'id',
      resetForm: true,
      selectedList: goodsExcludeRowData || [],
      data: {
        company_id: LStorage.get('userInfo')?.company_id,
        operator_store_id: LStorage.get('userInfo').store_id,
      },
    });
    if (data) {
      const exclude_goods: any = [];
      data?.forEach((item: any, index: number) => {
        exclude_goods.push({
          id: item.id,
          code: item.code,
          item_category_name: item.item_category_name,
          name: item.name,
          create_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
          create_by: LStorage.get('userInfo')?.name,
        });
      });
      setGoodsExcludeRowData(exclude_goods);
      setExcludeGoodsSelectRow([]);
    }
  };
  // 排除商品删除
  const deleteGoodsExcludeItem = () => {
    const deleteItem = goodsExcludeRowData.filter(
      (item: any) => !excludeGoodsSelectRow.includes(item.id),
    );
    setGoodsExcludeRowData(deleteItem);
    setExcludeGoodsSelectRow([]);
  };

  useEffect(() => {
    if (id !== -1) {
      setisLoading(true);
      api.read({ id: id }).then((res: any) => {
        form.setFieldsValue({
          ...res?.data,
          store_label_ids: res?.data?.store_labels?.map((item: any) => item.id),
        });
        setEnabled(res?.data?.enabled);
        setFirstStockLog(res?.data?.first_stock_log);
        setOpenNewsCycle(res?.data?.open_news_cycle);
        setRowData(res?.data?.item_categories?.map((item: any) => ({
            ...item,
            create_by: create_by,
            create_time: create_time,
          })) || []);
        setGoodsExcludeRowData(res?.data?.items?.map((item: any) => ({
          ...item,
          create_by: create_by,
          create_time: create_time,
        })) || []);
        setisLoading(false);
      });
    }
  }, []);

  const handleOk = async (values: any) => {
    setisLoading(true);
    const params = {
      ...form.getFieldsValue(true),
      enabled: enabled,
      first_stock_log: first_stock_log,
      open_news_cycle: open_news_cycle,
      rule_item_category_ids: rowData?.map((item: any) => item.id),
      rule_item_ids: goodsExcludeRowData?.map((item: any) => item.id),
    };
    setisLoading(false);
    const res = id == -1 ? await api.save(params) : await api.update(params);
    if (res?.code === 0) {
      modal.hide();
      modal.resolve(false);
      fetchData?.();
      message.success('操作成功');
    }
  };
  return (
    <XlbModal
      title={form.getFieldValue('id') ? '编辑规则' : '新增规则'}
      open={modal.visible}
      isCancel={true}
      onOk={async () => {
        form.submit();
      }}
      onCancel={() => {
        modal.resolve(false);
        modal.hide();
      }}
      width={1000}
      keyboard={false}
    >
      <XlbBasicForm
        form={form}
        labelCol={{ span: 4 }}
        disabled={isLoading}
        layout={'horizontal'}
        style={{ margin: '16px 0' }}
        initialValues={{
          enabled: false,
          first_stock_log: false,
          open_news_cycle: false,
          force_delivery_quantity: 1,
          stop_request_stop_sale_force_delivery: false,
          stop_request: false,
        }}
        onFinish={() => {
          const values = form.getFieldsValue(true);
          handleOk(values);
        }}
      >
        <XlbBlueBar.SubTitle title="统配规则" style={{ margin: '16px 0' }} />
        <XlbBasicForm.Item
          label="规则名称"
          rules={[{ required: true, message: '规则名称不能为空' }]}
          name="rule_name"
        >
          <XlbInput width={400} placeholder="请输入" />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item label="是否启用" name="enabled">
          <XlbCheckbox
            checked={enabled}
            onChange={(value) => {
              setEnabled(value.target.checked);
            }}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item
          label="统配规则"
          required
          name="rule"
          rules={[
            {
              validator: (_, value) => {
                if (first_stock_log || open_news_cycle) {
                  return Promise.resolve();
                }
                return Promise.reject('请至少选择一个规则');
              },
            },
          ]}
        >
          <div style={{ marginTop: 3 }}>
            商品符合以下情景时，会自动统配到所有所选门店中
            <XlbBasicForm.Item noStyle name="first_stock_log">
              <XlbCheckbox
                style={{ width: '100%', margin: '16px 0' }}
                checked={first_stock_log}
                onChange={(value) => {
                  setFirstStockLog(value.target.checked);
                }}
              >
                仓库首次有库存记录
              </XlbCheckbox>
            </XlbBasicForm.Item>
            <XlbBasicForm.Item name="open_news_cycle">
              <XlbCheckbox
                checked={open_news_cycle}
                onChange={(value) => {
                  setOpenNewsCycle(value.target.checked);
                }}
              >
                商品档案中"新品"选项已勾选
              </XlbCheckbox>
            </XlbBasicForm.Item>
          </div>
        </XlbBasicForm.Item>
        <XlbBlueBar.SubTitle
          title="规则参数"
          style={{ margin: '-12px 0 16px 0' }}
        />
        <XlbBasicForm.Item
          label="统配件数"
          rules={[{ required: true, message: '统配件数不能为空' }]}
          name="force_delivery_quantity"
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <XlbBasicForm.Item noStyle name="force_delivery_quantity">
              <XlbInputNumber
                min={1}
                width={100}
                placeholder="请输入"
                precision={0}
              />
            </XlbBasicForm.Item>
            <span style={{ color: '#86909C', marginLeft: 8 }}>配送单位</span>
          </div>
        </XlbBasicForm.Item>
        <XlbBasicForm.Item
          label="停止要货是否统配"
          rules={[{ required: true }]}
          name="stop_request"
        >
          <XlbRadio.Group
            onChange={async (value) => {
              // 如果勾选，则提示是否统配
              if (value.target.value) {
                const bool = await XlbTipsModal({
                  isConfirm: true,
                  isCancel: true,
                  tips: '勾选后可能会触发自自动规则,默认向符合条件的门店自动统配一件,请确认后再操作。',
                });
                form.setFieldsValue({
                  stop_request: bool,
                });
              } else {
                form.setFieldsValue({
                  stop_request: false,
                });
              }
            }}
            options={[
              { label: '不统配', value: false },
              {
                label: '统配',
                value: true,
              },
            ]}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item
          label="停售是否统配"
          rules={[{ required: true }]}
          name="stop_request_stop_sale_force_delivery"
        >
          <XlbRadio.Group
            onChange={async (value) => {
              // 如果勾选，则提示是否统配
              if (value.target.value) {
                const bool = await XlbTipsModal({
                  isConfirm: true,
                  isCancel: true,
                  tips: '勾选后可能会触发自自动规则,默认向符合条件的门店自动统配一件,请确认后再操作。',
                });
                form.setFieldsValue({
                  stop_request_stop_sale_force_delivery: bool,
                });
              } else {
                form.setFieldsValue({
                  stop_request_stop_sale_force_delivery: false,
                });
              }
            }}
            options={[
              { label: '不统配', value: false },
              {
                label: '统配',
                value: true,
              },
            ]}
          />
        </XlbBasicForm.Item>
        <XlbBlueBar.SubTitle
          title={
            <div>
              应用范围
              <span style={{ color: '#86909C', fontSize: 12, fontWeight: 400 }}>
                （应用门店和门店标签请至少选择一个）
              </span>
            </div>
          }
          style={{ margin: '16px 0' }}
        />
        <XlbBasicForm.Item
          label="应用门店"
          name="force_delivery_stores"
          rules={[
            {
              validator: (_, value, callback) => {
                const storeLabelIds = form.getFieldValue('store_label_ids');
                if (
                  (value && value?.length > 0) ||
                  (storeLabelIds && storeLabelIds?.length > 0)
                ) {
                  callback();
                } else {
                  callback('应用门店和门店标签请至少选择一个');
                }
              },
            },
          ]}
        >
          <XlbInputDialog
            dialogParams={{
              type: 'store',
              isMultiple: true,
              dataType: 'lists',
              placeholder: '请选择',
              data: {
                status: true,
                filter_org_levels: [1, 3],
              },
            }}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_name',
            }}
            width={400}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item
          label="通过门店标签选择"
          name="store_label_ids"
          rules={[
            {
              validator: (_, value, callback) => {
                const forceDeliveryStores = form.getFieldValue(
                  'force_delivery_stores',
                );
                if (
                  (value && value?.length > 0) ||
                  (forceDeliveryStores && forceDeliveryStores?.length > 0)
                ) {
                  callback();
                } else {
                  callback('应用门店和门店标签请至少选择一个');
                }
              },
            },
          ]}
        >
          <XlbInputDialog
            dialogParams={{
              type: 'storeLabel',
              dataType: 'lists',
              isMultiple: true,
              isLeftColumn: false,
            }}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_label_name',
            }}
            width={400}
          />
        </XlbBasicForm.Item>

        <XlbBasicForm.Item
          label="排除门店"
          name="force_delivery_excluded_stores"
        >
          <XlbInputDialog
            dialogParams={{
              type: 'store',
              isMultiple: true,
              dataType: 'lists',
              placeholder: '请选择',
              data: {
                status: true,
                enable_organization: false,
                center_flag: false,
              },
            }}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_name',
            }}
            width={400}
          />
        </XlbBasicForm.Item>

        <XlbBasicForm.Item label="指定统配商品统配">
          <XlbTabs
            style={{ paddingLeft: 20 }}
            items={[
              {
                label: '指定统配商品分类',
                key: '2',
                children: (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      width: '100%',
                    }}
                  >
                    <div style={{ marginBottom: 10 }}>
                      <XlbButton.Group>
                        <XlbButton
                          label="添加商品分类"
                          type="primary"
                          onClick={addItem}
                          icon={<XlbIcon size={16} name="jia" />}
                        />
                        <XlbButton
                          label="删除"
                          type="primary"
                          onClick={deleteItem}
                          disabled={selectRow?.length === 0}
                          icon={<XlbIcon size={16} name="shanchu" />}
                        />
                      </XlbButton.Group>
                    </div>
                    <XlbTable
                      columns={[
                        {
                          name: '序号',
                          code: '_index',
                          width: columnWidthEnum.INDEX,
                          align: 'center',
                        },
                        {
                          name: '类别代码',
                          code: 'id',
                          width: 140,
                          features: { sortable: true },
                        },
                        {
                          name: '类别名称',
                          code: 'name',
                          width: 160,
                          features: { sortable: true },
                        },
                        {
                          name: '速记码',
                          code: 'shorthand_code',
                          width: columnWidthEnum.SHORTHAND_CODE,
                          features: { sortable: true },
                        },
                        {
                          name: '添加日期',
                          code: 'create_time',
                          width: 160,
                          features: { sortable: true, format: 'TIME' },
                        },
                        {
                          name: '操作人',
                          code: 'create_by',
                          width: 160,
                          features: { sortable: true },
                        },
                      ]}
                      dataSource={rowData}
                      total={rowData?.length}
                      onPaginChange={(page, pageSize) => {
                        setPageSize(pageSize);
                      }}
                      pageSize={pageSize}
                      selectMode="multiple"
                      style={{ maxHeight: 400, height: 400 }}
                      isLoading={isLoading}
                      emptyCellHeight={400}
                      selectedRowKeys={selectRow}
                      onSelectRow={(record) => setSelectRow(record)}
                    />
                  </div>
                ),
              },
              {
                label: '指定统配商品',
                key: '4',
                children: (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      width: '100%',
                    }}
                  >
                    <div style={{ marginBottom: 10 }}>
                      <XlbButton.Group>
                        <XlbButton
                          label="添加商品"
                          type="primary"
                          onClick={excludedGoods}
                          icon={<XlbIcon size={16} name="jia" />}
                        />
                        <XlbButton
                          label="删除"
                          type="primary"
                          onClick={deleteGoodsExcludeItem}
                          disabled={excludeGoodsSelectRow?.length === 0}
                          icon={<XlbIcon size={16} name="shanchu" />}
                        />
                      </XlbButton.Group>
                    </div>
                    <XlbTable
                      columns={[
                        {
                          name: '序号',
                          code: '_index',
                          width: columnWidthEnum.INDEX,
                          align: 'center',
                        },
                        {
                          name: '商品代码',
                          code: 'code',
                          width: 140,
                          features: { sortable: true },
                        },
                        {
                          name: '商品分类',
                          code: 'item_category_name',
                          width: 100,
                          features: { sortable: true },
                        },
                        {
                          name: '商品名称',
                          code: 'name',
                          width: 160,
                          features: { sortable: true },
                        },
                        {
                          name: '添加日期',
                          code: 'create_time',
                          width: 160,
                          features: { sortable: true, format: 'TIME' },
                        },
                        {
                          name: '操作人',
                          code: 'create_by',
                          width: 160,
                          features: { sortable: true },
                        },
                      ]}
                      dataSource={goodsExcludeRowData}
                      total={goodsExcludeRowData?.length}
                      pageSize={excludeGoodsPageSize}
                      onPaginChange={(page, pageSize) => {
                        setExcludeGoodsPageSize(pageSize);
                      }}
                      selectMode="multiple"
                      selectedRowKeys={excludeGoodsSelectRow}
                      style={{ maxHeight: 400, height: 400 }}
                      emptyCellHeight={400}
                      isLoading={isLoading}
                      onSelectRow={(record) => setExcludeGoodsSelectRow(record)}
                    />
                  </div>
                ),
              },
            ]}
          />
        </XlbBasicForm.Item>

        <div style={{ color: '#86909C' }}>
          {/* 1px灰线 */}
          <div
            style={{
              height: 1,
              backgroundColor: '#E5E6EA',
              margin: '16px 4px',
            }}
          ></div>
          1、功能说明：当前统配的场景主要是有新品时，给部分门店统配，此处设置自动规则可以减少人工手动做单。
          <br />
          2、设置规则后，会根据规则将符合条件的商品查出来，在应用范围内的门店，在下补货单时，会自动插入这部分统配商品，且不可删除或变更数量。即门店下补货单会自动一起下统配商品。
          <b>这样确保生成的统配单一定有补货单，便于仓库作业</b>
          <br />
          3、下单后会生成两个单据，自动统配单状态同步补货单。门店在审核补货单时，关联的统配单状态一起变更为审核
          <br />
          4、商品仅会对生效的门店统配一次，后续重复添加的统配商品，会自动删除，不会重复统配
        </div>
      </XlbBasicForm>
    </XlbModal>
  );
};
export default AddItem;
