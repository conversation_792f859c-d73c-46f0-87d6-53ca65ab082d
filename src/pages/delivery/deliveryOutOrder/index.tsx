import PromiseModal from '@/components/promiseModal/PromiseModal';
import { BatchChangeTimeModal } from '@/components/XlbErpBatchTimeModal';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { formatWithCommas } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbPrintModal,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTableColumnProps,
  XlbTipsModal,
  XlbTooltip,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import AttachmentOfReceiptForm from './component/attachmentOfReceiptForm';
import BatchOrderIndex from './component/batchOrder/batchOrder';
import {
  deliveryInStatesList,
  formList,
  invoiceStateList,
  settlementStateList,
  stateList,
  tableList,
} from './data';
import DetailOrder from './item/index';
import {
  batchAuditInfo,
  batchPrinter,
  batchread,
  copyInfo,
  deleteItems,
  RedCopy,
  refreshTaxRateCheck,
  refreshTaxRateInfo,
  syncInfo,
  unbatchAuditInfo,
} from './server';

const Index = () => {
  const [form] = XlbBasicForm.useForm<any>();
  const userInfo = LStorage.get('userInfo');
  const { enable_organization } = useBaseParams((state) => state);
  const detailRef = useRef<XlbProPageModalRef>(null);
  const [itemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(tableList)),
  );
  const [batchOrderVisible, setBatchOrderVisible] = useState<boolean>(false);
  const pageRef = useRef<any>(null);
  const { ToolBtn, SearchForm, Table } = XlbPageContainer;
  const [record, setRecord] = useState<any>({});

  const uploadInfo = async (fid: any) => {
    pageRef.current?.setLoading(true);
    const res = await syncInfo({ fid: fid });
    pageRef.current?.setLoading(false);
    if (res.code == 0) {
      if (res.data) {
        XlbMessage.error(res.data);
      } else {
        XlbMessage.success('上传成功！');
        pageRef.current?.fetchData();
      }
    }
  };

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'fid':
        item.render = (value: any, record: any, index: { index: any }) => {
          return (
            <div className="overwidth cursors">
              <span
                className="link cursors"
                style={{ color: record.reverse_fid ? '#FF0000' : '#3D66FE' }}
                onClick={(e) => {
                  e.stopPropagation();
                  setRecord({
                    fid: record.fid,
                    index: index.index,
                    total: pageRef.current?.dataSource?.length,
                    allRow: pageRef.current?.dataSource,
                  });
                  detailRef.current?.setOpen(true);
                }}
              >
                {value}
              </span>
            </div>
          );
        };
        break;
      case 'purchase_money':
      case 'no_tax_purchase_money':
        item.render = (value: any) => {
          return hasAuth(['调出单/采购价', '查询']) && value !== '****' ? (
            <div className="info overwidth">
              {formatWithCommas(Number(value || 0)?.toFixed(4))}
            </div>
          ) : (
            <div className="info overwidth">{'****'}</div>
          );
        };
        break;
      // 税费、成本含税、成本不含税----成本价
      case 'cost_money':
      case 'no_tax_cost_money':
      case 'tax_money':
        item.render = (value: any) => {
          return hasAuth(['调出单/成本价', '查询']) && value !== '****' ? (
            <div className="info overwidth">
              {formatWithCommas(Number(value || 0)?.toFixed(2))}
            </div>
          ) : (
            <div className="info overwidth">{'****'}</div>
          );
        };
        break;
      // 单据金额、单据金额去税---配送价
      case 'money':
      case 'no_tax_money':
        item.render = (value: any) => {
          return hasAuth(['调出单/配送价', '查询']) && value !== '****' ? (
            <div className="info overwidth">
              {formatWithCommas(Number(value || 0)?.toFixed(2))}
            </div>
          ) : (
            <div className="info overwidth">{'****'}</div>
          );
        };
        break;
      case 'in_state':
        item.render = (value: any, _record: any, index: any) => {
          const _item = deliveryInStatesList.find((v) => v.value === value);
          return (
            <div className={`overwidth ${_item ? _item.type : ''}`}>
              {_item ? _item.label : ''}
            </div>
          );
        };
        break;
      case 'state':
        item.render = (value: any, _record: any, index: any) => {
          const _item = stateList.find((v) => v.value === value);
          return (
            <div className={`overwidth ${_item ? _item.type : ''}`}>
              {_item ? _item.label : ''}
            </div>
          );
        };
        break;
      case 'license_type':
      case 'in_license_type':
        item.render = (value: any) => {
          const typeOfLicense: any = {
            COMPANY: '企业',
            PERSONAL: '个体',
          };
          return (
            <div className="info overwidth">
              {value ? typeOfLicense[value] : ''}
            </div>
          );
        };
        break;
      case 'invoice_state':
        item.render = (value: any) => {
          const _item = invoiceStateList.find((v) => v.value === value);
          return (
            <div className={`overwidth ${_item ? _item.type : ''}`}>
              {_item ? _item.label : ''}
            </div>
          );
        };
        break;
      case 'settlement_state':
        item.render = (value: any, record: any) => {
          const _item = settlementStateList.find((v) => v.value === value);
          return record.out_center_flag ? (
            <div className={`overwidth ${_item ? _item.type : ''}`}>
              {_item ? _item.label : ''}
            </div>
          ) : (
            <div className="info overwidth">{'——'}</div>
          );
        };
        break;
      case 'request_order_fids_str':
        item.render = (value: any, _record: any) => {
          const ids: string[] = _record?.request_order_fids ?? [];
          return (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-start',
              }}
            >
              <span
                className={'link cursors'}
                onClick={(e) => {
                  e.stopPropagation();
                  NiceModal.show(PromiseModal, {
                    order_type: '门店补货单',
                    order_fid: ids[0],
                  });
                }}
              >
                {ids[0]}
              </span>
              {ids?.length > 1 && (
                <XlbTooltip
                  color="#FFF"
                  title={
                    <div
                      style={{
                        color: '#1D2129',
                        minWidth: 80,
                        textAlign: 'left',
                      }}
                    >
                      {ids?.length > 1 &&
                        ids?.map((v: any) => {
                          return (
                            <div
                              key={v}
                              className="link cursors"
                              onClick={(e) => {
                                e.stopPropagation();
                                NiceModal.show(PromiseModal, {
                                  order_type: '门店补货单',
                                  order_fid: v,
                                });
                              }}
                            >
                              {v}
                            </div>
                          );
                        })}
                    </div>
                  }
                  placement={'bottom'}
                >
                  <span
                    className={'default'}
                    style={{ display: 'inline-block', marginLeft: 3 }}
                  >
                    ({ids?.length > 1 ? ids?.length : 0})
                  </span>
                </XlbTooltip>
              )}
            </div>
          );
        };
        break;
      case 'print_count':
        item.render = (value: any) => (
          <div className="info overwidth"> {value || 0}</div>
        );
        break;
      case 'in_store_label_name':
        item.render = (value: any) => (
          <div className="info overwidth"> {value?.substring(0, 20)}</div>
        );
        break;
      case 'ama_order_fid':
        item.render = (value: any, record: any) =>
          value ? (
            <div className="info overwidth"> {value}</div>
          ) : (
            <div
              className="link cursors"
              onClick={(e) => {
                if (record.state == 'INIT') return;
                e.stopPropagation();
                uploadInfo(record.fid);
              }}
            >
              {record.state == 'INIT' ? null : '上传'}
            </div>
          );
        break;
      case 'order_type':
        item.render = (value: any, _record: any) => {
          return _record.index === '合计' ? null : value ==
            'WAREHOUSE_TO_STORE' ? (
            <div className="info overwidth">仓店配送</div>
          ) : value == 'STORE_TO_WAREHOUSE' ? (
            <div className="info overwidth">店仓反配</div>
          ) : value == 'STORE_TO_STORE' ? (
            <div className="info overwidth">店间调拨</div>
          ) : value == 'WAREHOUSE_TO_WAREHOUSE' ? (
            <div className="info overwidth">仓间调拨</div>
          ) : null;
        };
        break;
    }
    return item;
  };
  //#region  删除
  const deleteItem = async (data = [], fids = []) => {
    const errFid = data.filter((_: any) => _?.state !== 'INIT');
    if (errFid.length > 0) {
      XlbTipsModal({
        tips: '只能删除单据状态为制单的单据!',
      });
      return;
    }
    await XlbTipsModal({
      tips: `已选择${fids.length}张单据，是否确认删除!`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        pageRef.current?.setLoading(true);
        const res = await deleteItems({ fids });
        pageRef.current?.setLoading(false);
        if (res?.code === 0) {
          XlbMessage.success('操作成功');
          pageRef.current?.fetchData();
          return true;
        }
      },
    });
  };

  const exportItem = async (e: any) => {
    const data = await prevPost();
    if (!data) return;
    pageRef.current?.setLoading(true);
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliveryoutorder.export',
      data,
    );
    pageRef.current?.setLoading(false);
    if (res?.code == 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };
  const exportItemDetail = async (e: any) => {
    const data = await prevPost();
    if (!data) return;
    pageRef.current?.setLoading(true);
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliveryoutorder.detail.export.sum',
      data,
    );
    pageRef.current?.setLoading(false);
    if (res?.code == 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };

  // 审核
  const batchAudit = async (data = [], fids = []) => {
    const errFid = data.filter((_: any) => _?.state !== 'INIT');

    if (errFid.length > 0) {
      XlbTipsModal({
        tips: '只能审核单据状态为制单的单据!',
      });
      return;
    }

    await XlbTipsModal({
      tips: `已选择${fids.length}张单据，是否确认审核!
        ${
          enable_organization
            ? '调出单审核后，系统会自动产生审核的调入单，'
            : ''
        }是否确认审核？`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await batchAuditInfo({ fids });
        if (res?.code === 0) {
          XlbMessage.success('操作成功');
          pageRef.current?.fetchData();
          return true;
        }
      },
    });
  };

  // 批量反审核
  const unbatchAudit = async (data = [], fids = []) => {
    const errFid = data.filter((_: any) => _?.state !== 'AUDIT');

    if (errFid.length > 0) {
      XlbTipsModal({
        tips: '只能反审核单据状态为审核的单据!',
      });
      return;
    }

    await XlbTipsModal({
      tips: `已选择${fids.length}张单据，是否确认反审核!`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await unbatchAuditInfo({ fids });
        if (res?.code === 0) {
          XlbMessage.success('操作成功');
          pageRef.current?.fetchData();
          return true;
        }
      },
    });
  };
  const checkIfSame = (arr: any) => {
    if (arr.length === 0) {
      // 数组为空，无法判断
      return false;
    }

    const firstItem = arr[0];

    return arr.every(
      (item: any) =>
        item.storehouse_id === firstItem.storehouse_id &&
        item.in_store_id === firstItem.in_store_id,
    );
  };
  //#region 复制
  const copyItem = async (arr = [], fids = []) => {
    if (!checkIfSame(arr)) {
      XlbTipsModal({
        tips: '批量复制仅支持对相同调出仓库和调入门店的单据进行操作!',
      });
      return;
    } else {
      await XlbTipsModal({
        tips: `是否确认复制单据"${fids.join(',')}"?`,
        isCancel: true,
        onOkBeforeFunction: async () => {
          const res = await copyInfo({ fids });
          if (res?.code === 0) {
            XlbMessage.success('操作成功');
            pageRef.current?.fetchData();
            return true;
          }
        },
      });
    }
  };
  //#region 红冲
  const reverseItem = async (arr = [], fids = []) => {
    const errFid = arr.filter((_: any) => _?.state !== 'AUDIT');
    if (errFid.length > 0) {
      XlbTipsModal({
        tips: '仅审核单据支持冲红复制!',
      });
      return;
    } else {
      await XlbTipsModal({
        tips: (
          <div>
            <div style={{ color: 'red' }}>
              是否确认冲红复制单据，冲红后，系统会自动审核
              {enable_organization ? '此调出单，且会自动冲红关联调入单' : ''}
            </div>
            {fids.join(',')}?
          </div>
        ),
        isCancel: true,
        onOkBeforeFunction: async () => {
          const res = await RedCopy({ fids });
          if (res?.code === 0) {
            XlbMessage.success('操作成功');
            pageRef.current?.fetchData();
            return true;
          }
        },
      });
    }
  };
  // #region 日期修改
  const batchTime = async (idsList: any) => {
    await NiceModal.show(BatchChangeTimeModal, {
      idsList,
      fetchData: pageRef.current?.fetchData,
      url: '/erp/hxl.erp.order.date.change',
      idKey: 'fid_list',
      type: 'delivery_out',
    });
  };
  //#region 批量打印
  const batchPrint = async (rowData: any[] | undefined, fids = []) => {
    rowData?.forEach((v) => {
      v.batchPrint = true;
    });

    const printArr = rowData;
    let store1Arr: any = [];
    let store2Arr: any = [];
    let storehouseArr: any = [];
    printArr?.forEach((v) => {
      store1Arr.push(v.in_store_id);
      store2Arr.push(v.store_id);
      storehouseArr.push(v.storehouse_id);
    });
    store1Arr = Array.from(new Set(store1Arr));
    store2Arr = Array.from(new Set(store2Arr));
    storehouseArr = Array.from(new Set(storehouseArr));
    if (
      store1Arr.length !== 1 ||
      store2Arr.length !== 1 ||
      storehouseArr.length !== 1
    ) {
      const errName = [];
      if (store1Arr.length !== 1) errName.push('调入门店');
      if (store2Arr.length !== 1) errName.push('调出门店');
      if (storehouseArr.length !== 1) errName.push('调出仓库');
      XlbTipsModal({
        tips: `${errName.join('、')}不一致，无法合并打印!`,
      });
      return;
    }
    const data = {
      fids,
    };
    pageRef.current?.setLoading(true);
    const res = await batchPrinter(data);
    pageRef.current?.setLoading(false);
    if (res?.code === 0) {
      XlbPrintModal({ data: res.data });
    }
  };
  //  刷新税率
  const refreshTaxRate = async (rowData = []) => {
    const errFid: any = rowData.filter((v: any) => {
      return v.state !== 'AUDIT';
    });

    if (errFid.length > 0) {
      await XlbTipsModal({
        tips: '只能刷新单据状态为审核的单据!',
      });
      return;
    }

    pageRef.current?.setLoading(true);
    const res = await refreshTaxRateCheck({
      fids: rowData.map((_: any) => _.fid),
    });
    pageRef.current?.setLoading(false);

    if (res.code === 0 && res?.msg) {
      await XlbTipsModal({
        tips: res?.msg,
      });
    }

    if (res?.code === 0) {
      pageRef.current?.setLoading(true);
      const bool = await XlbTipsModal({
        tips: '刷新税率会自动刷新关联调入单税率，是否确认？',
      });
      pageRef.current?.setLoading(false);
      if (bool) {
        pageRef.current?.setLoading(true);
        await refreshTaxRateInfo({
          fids: rowData.map((_: any) => _.fid),
        });
        pageRef.current?.setLoading(false);
        pageRef.current?.fetchData();
      }
    }
  };
  const getBatchread = async (fids = []) => {
    pageRef.current?.setLoading(true);
    const res = await batchread({
      fids,
    });
    pageRef.current?.setLoading(false);
    if (res?.code === 0) {
      NiceModal.show(NiceModal.create(AttachmentOfReceiptForm), {
        rowData: res?.data || [],
      });
    }
  };

  const prevPost = async () => {
    try {
      await form.validateFields();
      const formData = form.getFieldsValue(true);
      const both_reversed = formData.both_reversed || [{}];
      const item = both_reversed[0];
      const both_reversed_obj = {
        both_reversed: item?.value ? (item?.itemKey ? true : false) : undefined,
      };

      // 调入状态 装数组类型
      if (
        formData.delivery_in_states &&
        typeof formData.delivery_in_states === 'string'
      ) {
        formData.delivery_in_states = [formData.delivery_in_states];
      }
      const { compact_date, time_type } = formData;
      const compactDatePicker = [
        compact_date?.[0] + ':00',
        compact_date?.[1] + ':59',
      ];
      const data = {
        ...formData,
        audit_date: time_type === 'audit_date' ? compactDatePicker : undefined,
        create_date:
          time_type === 'create_date' ? compactDatePicker : undefined,
        operate_date:
          time_type === 'operate_date' ? compactDatePicker : undefined,
        receive_time:
          time_type === 'receive_time' ? compactDatePicker : undefined,
      };

      return { ...data, ...both_reversed_obj };
    } catch (error) {
      return false;
    }
  };

  return (
    <>
      <BatchOrderIndex
        open={batchOrderVisible}
        setOpen={setBatchOrderVisible}
      />

      <XlbProPageModal
        ref={detailRef}
        Content={({ onClose }) => {
          return (
            <section>
              <DetailOrder
                // parentRef={detailRef}
                // data={paramsData}
                record={record}
                onBack={(back: boolean) => {
                  if (back) {
                    pageRef?.current?.fetchData?.();
                  }
                  detailRef?.current?.setOpen(false);
                }}
              ></DetailOrder>
            </section>
          );
        }}
      >
        <></>
      </XlbProPageModal>
      <XlbPageContainer
        url={'/erp/hxl.erp.deliveryoutorder.page'}
        tableColumn={itemArr?.map((item) => tableRender(item))}
        footerDataSource={(data) => {
          const footerData = [
            {
              _index: '合计',

              money:
                hasAuth(['调出单/配送价', '查询']) && data?.money !== '****'
                  ? (data?.money || 0).toFixed(2) || '0.00'
                  : '****',
              quantity: data?.quantity?.toFixed(3) || '0.000',
              item_count: data?.item_count || 0,
              tax_money:
                hasAuth(['调出单/成本价', '查询']) && data?.tax_money !== '****'
                  ? data?.tax_money?.toFixed(2) || '0.00'
                  : '****',
              no_tax_money:
                hasAuth(['调出单/配送价', '查询']) &&
                data?.no_tax_money !== '****'
                  ? data?.no_tax_money?.toFixed(2) || '0.00'
                  : '****',
              cost_money:
                hasAuth(['调出单/成本价', '查询']) &&
                data?.cost_money !== '****'
                  ? data?.cost_money?.toFixed(2) || '0.00'
                  : '****',
              no_tax_cost_money:
                hasAuth(['调出单/成本价', '查询']) &&
                data?.no_tax_cost_money !== '****'
                  ? data?.no_tax_cost_money?.toFixed(2) || '0.00'
                  : '****',
              purchase_money:
                hasAuth(['调出单/采购价', '查询']) &&
                data?.purchase_money !== '****'
                  ? data?.purchase_money?.toFixed(4) || '0.00'
                  : '****',
              no_tax_purchase_money:
                hasAuth(['调出单/采购价', '查询']) &&
                data?.no_tax_purchase_money !== '****'
                  ? data?.no_tax_purchase_money?.toFixed(4) || '0.00'
                  : '****',
            },
          ];
          return footerData;
        }}
        prevPost={prevPost}
        immediatePost={false}
      >
        <ToolBtn>
          {(context: ContextState) => {
            const {
              loading,
              setLoading,
              fetchData,
              dataSource,
              selectRow,
              selectRowKeys,
            } = context;
            pageRef.current = context;

            return (
              <XlbButton.Group>
                <XlbButton
                  label="查询"
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    fetchData?.();
                  }}
                  icon={<XlbIcon name="sousuo" />}
                />
                {hasAuth(['调出单', '编辑']) ? (
                  <XlbButton
                    label="新增"
                    type="primary"
                    disabled={loading}
                    onClick={() => {
                      setRecord({
                        fid: 1,
                      });
                      detailRef?.current?.setOpen(true);
                    }}
                    icon={<XlbIcon name="jia" />}
                  />
                ) : null}

                {hasAuth(['调出单', '删除']) ? (
                  <XlbButton
                    label="删除"
                    type="primary"
                    disabled={!selectRowKeys?.length || loading}
                    onClick={() =>
                      deleteItem(selectRow as [], selectRowKeys as [])
                    }
                    icon={<XlbIcon name="shanchu" />}
                  />
                ) : null}

                {hasAuth(['调出单', '编辑']) ||
                hasAuth(['调出单', '审核']) ||
                hasAuth(['调出单', '反审核']) ||
                hasAuth(['调出单', '冲红复制']) ? (
                  <XlbDropdownButton
                    label="批量操作"
                    disabled={loading}
                    dropList={[
                      {
                        label: '批量制单',
                        isNoAuth: !hasAuth(['调出单', '编辑']),
                      },
                      {
                        label: '批量审核',
                        disabled: !selectRowKeys?.length,
                        isNoAuth: !hasAuth(['调出单', '审核']),
                      },
                      {
                        label: '批量反审核',
                        disabled: !selectRowKeys?.length,
                        isNoAuth:
                          userInfo?.company_id !== 67800 ||
                          !hasAuth(['调出单', '反审核']),
                      },
                      {
                        label: '批量复制',
                        disabled: !selectRowKeys?.length,
                        isNoAuth: !hasAuth(['调出单', '编辑']),
                      },
                      {
                        label: '批量冲红',
                        disabled: !selectRowKeys?.length,
                        isNoAuth: !hasAuth(['调出单', '冲红复制']),
                      },
                    ]}
                    dropdownItemClick={(value: number, item, e) => {
                      if (item?.label === '批量制单') {
                        setBatchOrderVisible(true);
                      } else if (item?.label === '批量审核') {
                        batchAudit(selectRow as [], selectRowKeys as []);
                      } else if (item?.label === '批量反审核') {
                        unbatchAudit(selectRow as [], selectRowKeys as []);
                      } else if (item?.label === '批量复制') {
                        copyItem(selectRow as [], selectRowKeys as []);
                      } else if (item?.label === '批量冲红') {
                        reverseItem(selectRow as [], selectRowKeys as []);
                      }
                    }}
                  />
                ) : null}

                {hasAuth(['调出单', '导出']) ? (
                  <XlbDropdownButton
                    label="导出"
                    disabled={loading}
                    dropList={[
                      {
                        label: '导出',
                        disabled: !dataSource?.length,
                      },
                      {
                        label: '明细导出',
                        disabled: !dataSource?.length,
                      },
                    ]}
                    dropdownItemClick={(value: any, item: any, e) => {
                      if (item?.label === '导出') {
                        exportItem(e);
                      } else if (item?.label === '明细导出') {
                        exportItemDetail(e);
                      }
                    }}
                  />
                ) : null}

                <XlbDropdownButton
                  label="批量打印"
                  disabled={loading}
                  dropList={[
                    {
                      label: '合并打印',
                      disabled: !selectRow?.length,
                      isNoAuth: !hasAuth(['调出单', '打印']),
                    },
                    {
                      label: '单据打印',
                      disabled: !selectRow?.length,
                    },
                  ]}
                  dropdownItemClick={(value: number, item, e) => {
                    if (item?.label == '合并打印') {
                      batchPrint(selectRow, selectRowKeys as []);
                    } else if (item?.label == '单据打印') {
                      getBatchread(selectRowKeys as []);
                    }
                  }}
                ></XlbDropdownButton>

                {hasAuth(['调出单/日期修改', '编辑']) ? (
                  <XlbButton
                    label="日期修改"
                    type="primary"
                    disabled={!selectRow?.length || loading}
                    onClick={() => batchTime(selectRowKeys)}
                    icon={<XlbIcon name="xiugai1" />}
                  />
                ) : null}
                {hasAuth(['调出单', '刷新税率']) && enable_organization ? (
                  <XlbButton
                    label="刷新税率"
                    type="primary"
                    disabled={!selectRow?.length || loading}
                    onClick={() => refreshTaxRate(selectRow as [])}
                    icon={<XlbIcon name="shuaxin1" />}
                  />
                ) : null}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>

        <SearchForm>
          <XlbForm
            formList={formList?.filter((v) => {
              if (
                !enable_organization &&
                ['out_org_ids', 'in_org_ids'].includes(v.name)
              ) {
                return false;
              } else {
                return true;
              }
            })}
            form={form}
            isHideDate={true}
            initialValues={{
              store_ids: LStorage.get('userInfo')?.store_id
                ? [LStorage.get('userInfo')?.store_id]
                : undefined,
              time_type: 'create_date',
              compact_date: [
                dayjs().format('YYYY-MM-DD 00:00'),
                dayjs().format('YYYY-MM-DD 23:59'),
              ],
            }}
            // getFormRecord={() => refresh()}
            // onValuesChange={onValuesChange}
          />
        </SearchForm>
        <Table selectMode="multiple" primaryKey="fid"></Table>
      </XlbPageContainer>
    </>
  );
};

export default Index;
