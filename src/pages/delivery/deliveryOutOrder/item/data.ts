import { columnWidthEnum } from '@/data/common/constant';
// import {XlbTableColumnProps} from '@xlb/components'
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';
//确认状态
export const Options6 = [
  {
    label: '未确认',
    value: 'UNCHECK',
    type: 'danger',
  },
  {
    label: '部分确认',
    value: 'PARTCHECK',
    type: 'info',
  },
  {
    label: '全部确认',
    value: 'ALLCHECK',
    type: 'success',
  },
];
//单据状态
export const stateList = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
];
// 结算状态
export const settlementStateList = [
  {
    label: '未结算',
    value: 'UNPAY',
    type: 'danger',
  },
  {
    label: '部分结算',
    value: 'PARTPAY',
    type: 'info',
  },
  {
    label: '已结算',
    value: 'ALLPAY',
    type: 'success',
  },
];

// 时间类型
export const timeTypeList = [
  {
    label: '制单时间',
    value: 'create_date',
  },
  {
    label: '审核时间',
    value: 'audit_date',
  },
];

//调入状态
export const deliveryInStatesList = [
  {
    label: '未调入',
    value: 'UNIN',
    type: 'danger',
  },
  {
    label: '部分调入',
    value: 'PARTIN',
    type: 'info',
  },
  {
    label: '全部调入',
    value: 'ALLIN',
    type: 'success',
  },
];
export const invoiceStateList = [
  {
    label: '开票成功',
    value: 'FINISH',
    type: 'success',
  },
  {
    label: '开票中',
    value: 'DOING',
    type: 'warning',
  },
  {
    label: '未开票',
    value: 'INIT',
    type: 'info',
  },
];
export const checkOptions = [
  {
    label: '停购商品',
    value: 'stop_purchase',
  },
  {
    label: '停售商品',
    value: 'stop_sale',
  },
  {
    label: '停止要货商品',
    value: 'stop_request',
  },
];

//经营类型
export const goodsType: any[] = [
  {
    label: '主规格商品',
    value: 'MAINSPEC',
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC',
  },
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
  {
    label: '分级商品',
    value: 'GRADING',
  },
];
//查询单位query_unit
export const queryUnit = [
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
  {
    label: '基本单位',
    value: 'BASIC',
  },
];
// PURCHASE——采购单位,
// STOCK——库存单位,
// DELIVERY——配送单位,
// WHOLESALE——批发单位,
// BASIC——基本单位

export const types: any[] = [
  {
    label: '置空',
    value: 'NULL',
  },
  {
    label: '按比例',
    value: 'RATIO',
  },
  {
    label: '按金额',
    value: 'MONEY',
  },
  {
    label: '固定金额',
    value: 'FIXED_MONEY',
  },
];
export const types1: any[] = [
  {
    label: ' ',
    value: 'NULL',
  },
  {
    label: '按比例',
    value: 'RATIO',
  },
  {
    label: '按金额',
    value: 'MONEY',
  },
  {
    label: '固定金额',
    value: 'FIXED_MONEY',
  },
];

// 签收类型
export const RECEIVER_TYPE_OPTIONS = [
  {
    label: '自动签收',
    value: 'AUTO',
  },
  {
    label: '手动签收',
    value: 'MANUAL',
  },
  {
    label: '电子签',
    value: 'ELECTRONIC',
  },
];

export const formList: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '日期范围',
    name: 'create_date',
    showTime: true,
    resultFormat: 'YYYY-MM-DD HH:mm:ss',
    format: 'YYYY-MM-DD HH:mm:ss',
    onChange: (value) => {
      console.log(value);
    },
  },

  {
    label: '单据状态',
    name: 'state',
    type: 'select',
    options: stateList,
  },
  {
    label: '结算状态',
    name: 'settlement_state',
    type: 'select',
    options: settlementStateList,
  },
  {
    label: '单据号',
    name: 'fid',
    id: 'commonInput',
  },
  {
    label: '时间类型',
    name: 'time_type',
    type: 'select',
    allowClear: false,
    options: timeTypeList,
  },

  {
    type: 'inputDialog',
    label: '调入组织',
    name: 'org_names',
    treeModalConfig: {
      title: '选择组织', // 标题
      url: '/erp/hxl.erp.org.tree', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      fieldName: {
        id: 'id',
        parent_id: 'parent_id',
      },
      width: 360, // 模态框宽度
    },
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'name',
    },
  },

  {
    label: '调入门店',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isMultiple: true,
      data: {
        enabled: true,
      },
    },
  },

  {
    label: '调入仓库',
    name: 'storehouse_id',
    type: 'inputDialog',
    dialogParams: {
      type: 'storeHouse',
      dataType: 'lists',
      isMultiple: true,
      data: {
        enabled: true,
      },
    },
  },
  {
    type: 'inputDialog',
    label: '调出组织',
    name: 'out_org_ids',
    treeModalConfig: {
      title: '选择组织', // 标题
      url: '/erp/hxl.erp.org.tree', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      fieldName: {
        id: 'id',
        parent_id: 'parent_id',
      },
      width: 360, // 模态框宽度
    },
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'name',
    },
  },
  {
    label: '调出门店',
    name: 'out_store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isMultiple: true,
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '签收类型',
    value: 'receive_types',
    type: 'select',
    clear: true,
    check: true,
    multiple: true,
    options: RECEIVER_TYPE_OPTIONS,
  },
  {
    label: '上传乐檬',
    name: 'upload_ama',
    type: 'select',
    options: [
      {
        label: '是',
        value: true,
      },
      {
        label: '否',
        value: false,
      },
    ],
  },
  {
    label: '确认状态',
    value: 'check_state',
    type: 'select',
    clear: true,
    check: true,
    options: Options6,
  },
  {
    label: '制单人',
    name: 'create_by',
    id: 'commonInput',
  },
  {
    label: '审核人',
    id: 'commonInput',
    name: 'audit_by',
  },
  {
    label: '开票状态',
    name: 'invoice_state',
    type: 'select',
    options: invoiceStateList,
  },
  // {
  //   label: '调入门店标签',
  //   name: 'in_store_label_names',
  //    type: 'inputDialog',
  //   dialogParams: {
  //     type: 'storeLabels',
  //     dataType: 'lists',
  //     isMultiple: true,
  //     data: {
  //       enabled: true,
  //     },
  //   },
  // },
  {
    label: '其他条件',
    name: 'both_reversed',
    type: 'inputPanel',
    onChange: (v, s) => {
      console.log(v, s, 33);
    },
    items: [
      {
        label: '仅显示',
        key: true,
      },
      {
        label: '不显示',
        key: false,
        value: false,
      },
    ],
    options: [{ label: '冲红和被冲红单据', value: true }],
  },

  // {
  //   label: '  ',
  //   value: 'in_store_label_names22',
  //   type: 'inputPanel',
  //   clear: true,
  //   check: true
  // }
];

export const historyArr: any[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
  },
  {
    name: '修改时间',
    code: 'create_time',
    width: columnWidthEnum.TIME,
    align: 'left',
  },
  {
    name: '修改人',
    code: 'create_by',
    width: columnWidthEnum.BY,
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 140,
    align: 'left',
  },
  {
    name: '操作明细',
    code: 'memo',
    width: 324,
    align: 'left',
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: columnWidthEnum.fid,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '调入日期',
    code: 'operate_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '付款日期',
    code: 'payment_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '调入门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '调入营业执照名称',
    code: 'license_name',
    width: 200,
    features: { sortable: false },
    align: 'left',
    hidden: true,
  },
  {
    name: '调入执照类型',
    code: 'license_type',
    width: 110,
    features: { sortable: false },
    align: 'left',
    hidden: true,
  },
  {
    name: '调入仓库',
    code: 'storehouse_name',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '调出营业执照名称',
    code: 'out_license_name',
    width: 200,
    features: { sortable: false },
    align: 'left',
    hidden: true,
  },
  {
    name: '调出执照类型',
    code: 'out_license_type',
    width: 110,
    features: { sortable: false },
    align: 'left',
    hidden: true,
  },
  {
    name: '确认状态',
    code: 'check_state',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单据金额',
    code: 'money',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单据金额(去税)',
    code: 'no_tax_money',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '商品数',
    code: 'item_count',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '税费',
    code: 'tax_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },

  {
    name: '单据状态',
    code: 'state',
    width: columnWidthEnum.ORDER_STATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '结算状态',
    code: 'settlement_state',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '制单人',
    code: 'create_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '签收时间',
    code: 'receive_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '签收类型',
    code: 'receive_type',
    width: columnWidthEnum.TIME,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '留言备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '打印次数',
    code: 'print_count',
    width: 90,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '开票状态',
    code: 'invoice_state',
    width: 100,
    align: 'left',
  },
  {
    name: '关联结算单号',
    code: 'related_settlement_fid',
    width: 120,
    align: 'left',
  },
  {
    name: '关联开票单据号',
    code: 'related_invoice_fid',
    width: 160,
    align: 'left',
  },
  {
    name: '关联调出单',
    code: 'delivery_out_order_fids_str',
    width: 180,
    features: { sortable: false },
    align: 'left',
  },
  {
    name: '乐檬关联单号',
    code: 'ama_order_fid',
    width: 180,
    features: { sortable: true },
    align: 'center',
  },
  {
    name: 'wms单号',
    code: 'wms_fid',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
];
export const itemTableList: any[] = [
  {
    title: '商品名称/商品代码',
    code: 'name',
    width: 360,
    align: 'center',
    children: [
      {
        title: '序号',
        name: '序号',
        code: 'index',
        width: 80,
        align: 'center',
      },
      { title: '操作', code: 'action', width: 50, align: 'center' },
      { title: '门店分组', code: 'store_type', width: 60, align: 'left' },
      { title: '调入门店名称', code: 'store_name', width: 150, align: 'left' },
    ],
  },
  {
    title: '',
    code: '',
    width: 100,
    align: 'center',
  },
];

export const itemTableList_all: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
    disabledChecked: true,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 156,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 360,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '销项税率(%)',
    code: 'tax_rate',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '税费',
    code: 'tax_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '商品采购金额',
    code: 'purchase_money',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '商品采购金额(去税)',
    code: 'no_tax_purchase_money',
    width: 180,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '零售单价',
    code: 'sale_price',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '赠品单位',
    code: 'present_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
];

export const itemTableListDetail = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
  },
  {
    name: '操作',
    code: '_operator',
    align: 'center',
    width: 60,
    lock: true,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 210,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 360,
    features: { sortable: true, showShort: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '销项税率(%)',
    code: 'tax_rate',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '税费',
    code: 'tax_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '成本',
    code: 'cost_money',
    width: 110,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '成本(去税)',
    code: 'no_tax_cost_money',
    width: 110,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '单件皮重',
    code: 'tare',
    width: 110,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '换算率',
    code: 'ratio',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 134,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '商品采购金额',
    code: 'purchase_money',
    width: 130,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '商品采购金额(去税)',
    code: 'no_tax_purchase_money',
    width: 180,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '时点成本价(去税)',
    code: 'original_no_tax_cost_money',
    width: 180,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '零售单价',
    code: 'sale_price',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '赠品单位',
    code: 'present_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 110,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '库存数量',
    code: 'basic_stock_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '可用库存',
    code: 'basic_available_stock_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '原价',
    code: 'origin_price',
    width: 110,
    features: { sortable: true},
    align: 'right',
  },
  {
    name: '特价单据号',
    code: 'special_fid',
    width: 210,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
];