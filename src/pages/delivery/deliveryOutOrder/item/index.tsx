import { orderStatusIcons } from '@/components/common/data';
import { useBaseParams } from '@/hooks/useBaseParams';
import { formatWithCommas, hasAuth } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import { LStorage } from '@/utils/storage';
import toFixed from '@/utils/toFixed';
import { useIRouter, wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbColumns,
  XlbDatePicker,
  XlbDropdownButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbMessage,
  XlbModal,
  XlbPrintModal,
  XlbSelect,
  XlbShortTable,
  XlbTable,
  XlbTabs,
  XlbTipsModal,
  XlbTooltip,
  XlbUploadFile,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { Badge, Col, Row } from 'antd';
import classnames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import SelectOrder from '../component/SelectOrder';
import XlbBaseGoods from '../component/xlbBaseGoods';
import { itemTableList_all, itemTableListDetail } from './data';
import styles from './item.less';
import {
  addInfo,
  auditInfo,
  deliveryout,
  deliveryparamRead,
  deliveryPrice,
  getReceiveOrderItem,
  getStock,
  getStoreAmout,
  getstoreName,
  print,
  readInfo,
  updateInfo,
} from './server';
const DeliveryInOrderItem = (props: any) => {
  const { navigate } = useIRouter();
  const { record, onBack } = props;
  const [fileList, setFileList] = useState<any[]>([]);
  const [rowData, setRowData] = useState<any[]>([]);
  const [summaryData, setSummaryData] = useState<any[]>([]); //汇总表数据
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [fileInfo, setFileInfo] = useState<any[]>([]);
  const [AmountInfo, setAmount] = useState<any>({}); //门店余额
  // const [unCenterStoreApplication, setUnCenterStoreApplication] =
  //   useState(false);
  // const [managementType, setManagementType] = useState(null);
  // const [enableDeliveryCenter, setEnableDeliveryCenter] = useState(null); //是否是配送中心门店
  const [itemArr, setItemArr] = useState<any[]>(
    JSON.parse(JSON.stringify(itemTableList_all)),
  );
  const [itemArrdetail, setdetailItemArr] = useState<any[]>(
    JSON.parse(JSON.stringify(itemTableListDetail)),
  );
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const [isFold, setIsFold] = useState<boolean>(false);
  const [uploadFileModalVisible, setUploadFileModalVisible] =
    useState<boolean>(false);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [stockList, setStockList] = useState<any[]>([]);
  const [form] = XlbBasicForm.useForm();
  const in_store_id = XlbBasicForm.useWatch('in_store_id', form);
  const [fid, setFid] = useState<any>();
  const [info, setInfo] = useState({ state: 'INIT', fid: null });
  const [batchLoding, setBatchLoding] = useState<boolean>(false);
  const [tabsKey, setTabsKey] = useState<string>('detailTab'); //明细汇总切换
  const [tableKey, setTableKey] = useState<any>('baseInfo');
  const [edit, setEdit] = useState<boolean>(false); //触发表格编辑
  const [isrush, setIsrush] = useState<boolean>(false);
  const [centerStore, setCenterStore] = useState({
    isCenter: '',
    deliveryCenterId: '',
  }); //记录调入门店是否为配送中心,上游配送中心id
  const [toOtherCenter, setToOtherCenter] = useState<boolean>(false); //是否支持跨配送中心调拨
  const [audit, setAudit] = useState<boolean>(true); //新增保存后审核可用
  const [ban, setBan] = useState<boolean>(true); //是否可编辑
  const [errNames, setErrNames] = useState<any>([]);
  const [WmsInfo, setWmsInfo] = useState<any>({});
  const [orgNameList, setOrgNameList] = useState<any[]>([]);
  const [outOrgNameList, setOutOrgNameList] = useState<any[]>([]);
  const [disabledCargoOwnerOut, setDisabledCargoOwnerOut] = useState(false);
  const [disabledCargoOwner, setDisabledCargoOwner] = useState(false);
  const { enable_organization, enable_cargo_owner } = useBaseParams(
    (state) => state,
  );

  const handleUid = () => {
    let uid: any = {
      key: '',
      name: '',
    };
    switch (tabsKey) {
      case 'detailTab':
        uid.key = '/erp/hxl.erp.deliveryoutorder.read-detail-columns';
        uid.name = '/erp/hxl.erp.deliveryoutorder.read-detail-columns-name';
        break;
      case 'totalTab':
        uid.key = '/erp/hxl.erp.deliveryoutorder.read-total-columns';
        uid.name = '/erp/hxl.erp.deliveryoutorder.read-total-columns-name';
        break;

      default:
        uid.key = '/erp/hxl.erp.deliveryoutorder.read-detail-columns';
        uid.name = '/erp/hxl.erp.deliveryoutorder.read-detail-columns-name';
    }
    return uid;
  };

  const getArrayChild = (v: any) => {
    const res = Object.prototype.toString.call(v).slice(8, -1).toLowerCase();
    return res === 'array' ? v[0] : v;
  };

  // 获取调入门店组织
  const getInStoreOrg = async (list: any, org_id?: any, org_name?: any) => {
    if (enable_cargo_owner) {
      // 获取货主
      const data = {
        store_id: Array.isArray(list) ? list[0].id : list,
      };
      // 组织接口
      const res = await ErpRequest.post(
        '/erp-mdm/hxl.erp.delivery.cargo.owner.org.find',
        data,
      );
      const cargoOwnerList = res?.data?.map((v: any) => {
        return {
          ...v,
          label: v?.name,
          value: v?.id,
        };
      });
      setOrgNameList(cargoOwnerList);
      // 禁用货主弹窗 默认赋值
      if (res?.data?.length == 1) {
        form?.setFieldsValue({
          org_id: res?.data?.[0]?.id,
          org_name: res?.data?.[0]?.name,
          disabled_cargo_owner: true,
        });
        setDisabledCargoOwner(true);
      }
      // 不操作..
      if (res?.data?.length !== 1) {
        form?.setFieldsValue({
          org_id: org_id ? org_id : null,
          org_name: org_name ? org_name : null,
          disabled_cargo_owner: false,
        });
        setDisabledCargoOwner(false);
      }
    }
  };

  const getOutStoreOrg = async (list: any, org_id?: any, org_name?: any) => {
    if (enable_cargo_owner) {
      // 获取货主
      const data = {
        store_id: Array.isArray(list) ? list[0].id : list,
      };
      const res = await ErpRequest.post(
        '/erp-mdm/hxl.erp.delivery.cargo.owner.org.find',
        data,
      );
      const cargoOwnerList = res?.data?.map((v: any) => {
        return {
          ...v,
          label: v?.name,
          value: v?.id,
        };
      });
      setOutOrgNameList(cargoOwnerList);
      // 禁用货主弹窗 默认赋值
      if (res?.data?.length == 1) {
        form?.setFieldsValue({
          out_org_id: res?.data?.[0]?.id,
          out_org_name: res?.data?.[0]?.name,
          org_parent_id: res?.data?.[0]?.parent_id,
          disabled_cargo_owner_out: true,
        });
        setDisabledCargoOwnerOut(true);
      }
      // 不操作..
      if (res?.data?.length !== 1) {
        form?.setFieldsValue({
          out_org_id: org_id ? org_id : null,
          out_org_name: org_name ? org_name : null,
          org_parent_id: null,
          disabled_cargo_owner_out: false,
        });
        setDisabledCargoOwnerOut(false);
      }
      // 回显
      if (org_id && org_name) {
        const filterRes = res?.data?.filter((v: any) => v?.id == org_id);
        form?.setFieldValue('org_parent_id', filterRes?.[0]?.parent_id);
      }
    }
  };

  const InvoiceRender = (item: any) => {
    switch (item.code) {
      case 'item_bar_code':
        item.render = (value: any, record: any) => (
          <div
            style={{
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <XlbTooltip title={value}>
              <div
                style={{
                  maxWidth: '80%',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {value}
              </div>
            </XlbTooltip>
            <span
              style={{
                fontSize: 12,
                color: '#ff8400',
                border: '1px solid #ff8400',
                borderRadius: 3,
                marginLeft: 5,
                padding: '1px 2px',
                display: !record?.special_fid ? 'none' : 'block',
              }}
            >
              {'特价'}
            </span>
            <XlbTooltip title={'该商品参与满赠活动,数量满足后自动添加赠品'}>
              <span
                style={{
                  color: '#fff',
                  background: 'rgb(133, 3, 255)',
                  borderRadius: '2px',
                  fontSize: '12px',
                  marginLeft: '5px',
                  lineHeight: '16px',
                  padding: '1px 4px',
                  whiteSpace: 'nowrap',
                  display:
                    record?.campaigns?.[0]?.campaign_type_name != '满赠'
                      ? 'none'
                      : 'block',
                }}
              >
                满赠
              </span>
            </XlbTooltip>
            <span
              style={{
                color: '#fff',
                background: 'rgb(217, 3, 30)',
                borderRadius: '2px',
                fontSize: '12px',
                marginLeft: '5px',
                lineHeight: '16px',
                padding: '1px 4px',
                whiteSpace: 'nowrap',
                display: !record?.present ? 'none' : 'block',
              }}
            >
              赠品
            </span>
          </div>
        );
        break;
      case 'basic_stock_quantity':
      case 'basic_available_stock_quantity':
        item.render = (value: any, record: any) => {
          return record?._empty ? null : (
            <div className="overwidth">
              {record?.present
                ? '——'
                : toFixed(
                    safeMath.divide(value, record.ratio) || 0,
                    'QUANTITY',
                    true,
                  )}
            </div>
          );
        };
        break;
      // 零售单价---零售价
      case 'purchase_money':
      case 'no_tax_purchase_money':
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              {hasAuth(['调入单/采购价', '查询']) && value !== '****'
                ? value == null
                  ? '——'
                  : Number(value || 0).toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'sale_price':
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              {hasAuth(['调入单/零售价', '查询']) && value !== '****'
                ? Number(value || 0).toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'origin_price':
        item.render = (value: any, record: any) => {
          return record?.basic_original_price ? (
            <div className="info overwidth">
              {Number(
                safeMath.multiply(
                  Number(record?.basic_original_price),
                  Number(record?.ratio),
                ) || 0,
              ).toFixed(4)}
            </div>
          ) : null;
        };
        break;
      case 'special_fid':
        item.render = (value: any, record: any) => {
          return record?.special_fid ? (
            <div className="overwidth cursors">
              <span
                className="link cursors"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate('/xlb_erp/deliverySpecialPrice/item', {
                    fid: value,
                  });
                }}
              >
                {value}
              </span>
            </div>
          ) : null;
        };
        break;
      case 'present_unit':
      case 'unit':
        item.render = (value: any, record: any, scope: { index: any }) => {
          const index = scope.index;
          return record?._click &&
            record?.item_name &&
            tabsKey == 'detailTab' ? (
            <XlbSelect
              style={{ width: '100%', marginBottom: -4 }}
              defaultValue={value}
              onChange={(e) => ratioChangeByUnit(e, item.code, index)}
            >
              {record?.units?.map((v: any, i: any) => {
                return JSON.parse(v).name ? (
                  <XlbSelect.Option key={i} value={JSON.parse(v).name}>
                    {JSON.parse(v).name}
                  </XlbSelect.Option>
                ) : null;
              })}
            </XlbSelect>
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      //#region 换算率
      case 'ratio':
      case 'basic_quantity': //基本数量
        item.render = (value: any, record: any, index: any) => {
          return record?._click &&
            record?.item_name &&
            tabsKey == 'detailTab' &&
            record.account_method === '中心手工批次' &&
            centerStore.isCenter &&
            record?.unit == record?.batch_unit ? (
            <XlbInput
              key={record?.[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index?.index?.toString()}
              defaultValue={Number(value || 0).toFixed(3)}
              onFocus={(e) => e.target.select()}
              onChange={(e) =>
                inputChange(e, index?.index, item.code, record?.ratio)
              }
              onBlur={(e) =>
                inputBlur(e, index?.index, item.code, record?.ratio)
              }
              style={{ textAlign: 'right' }}
            />
          ) : (
            <div className="info overwidth">
              {Number(value || 0).toFixed(3)}
            </div>
          );
        };
        break;
      //#region数量 赠品数量
      case 'quantity': //数量
      case 'present_quantity': //赠品数量
        item.render = (value: any, record: any, index: any) => {
          return record?._click && record?.item_name ? (
            <XlbInput
              key={record?.[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index?.index?.toString()}
              defaultValue={Number(value || 0).toFixed(3)}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onFocus={(e) => e.target.select()}
              onChange={(e) =>
                inputChange(e, index?.index, item.code, record?.ratio)
              }
              onBlur={(e) =>
                inputBlur(e, index?.index, item.code, record?.ratio)
              }
              style={{ textAlign: 'right' }}
            />
          ) : (
            <div className="info overwidth">
              {Number(value || 0).toFixed(3)}
            </div>
          );
        };
        break;
      // 税费、成本、成本去税---成本价
      case 'cost_money':
      case 'no_tax_cost_money':
      case 'tax_money':
      case 'original_no_tax_cost_money':
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              {hasAuth(['调入单/成本价', '查询']) && value !== '****'
                ? Number(value || 0)?.toFixed(2)
                : '****'}
            </div>
          );
        };
        break;

      //单价、金额、基本单价---配送价
      case 'money': //金额(含税)
        item.render = (value: any, record: any, index: any) => {
          return hasAuth(['调入单/配送价', '编辑']) &&
            record?._click &&
            record?.item_name &&
            tabsKey == 'detailTab' ? (
            <XlbInput
              key={record?.[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index?.index?.toString()}
              defaultValue={Number(value || 0).toFixed(2)}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onFocus={(e) => e.target.select()}
              onChange={(e) =>
                inputChange(e, index?.index, item.code, record?.ratio)
              }
              onBlur={(e) =>
                inputBlur(e, index?.index, item.code, record?.ratio)
              }
              style={{ textAlign: 'right' }}
            />
          ) : (
            <div className="info overwidth">
              {hasAuth(['调入单/配送价', '查询']) && value !== '****'
                ? formatWithCommas(Number(value || 0).toFixed(2))
                : '****'}
            </div>
          );
        };
        break;
      case 'price': //单价(含税)
      case 'basic_price': //基本单价(含税)
        item.render = (value: any, record: any, index: any) => {
          return hasAuth(['调入单/配送价', '编辑']) &&
            record?._click &&
            tabsKey == 'detailTab' &&
            record?.item_name ? (
            <XlbInput
              key={record?.[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index?.index?.toString()}
              defaultValue={Number(value ? value : 0).toFixed(4)}
              onFocus={(e) => e.target.select()}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onChange={(e) =>
                inputChange(e, index?.index, item.code, record?.ratio)
              }
              onBlur={(e) =>
                inputBlur(e, index?.index, item.code, record?.ratio)
              }
              style={{ textAlign: 'right' }}
            />
          ) : (
            <div className="info overwidth">
              {record?.index === '合计'
                ? null
                : hasAuth(['调入单/配送价', '查询']) && value !== '****'
                  ? Number(value ? value : 0).toFixed(4)
                  : '****'}
            </div>
          );
        };
        break;
      // #region 三个批次号有问题
      case 'producing_date':
        item.render = (value: any, record: any, index: any) => {
          return record?._click && record?.producing_date_flag ? (
            <XlbInput
              key={record?.[item.code]}
              style={{ width: '120px' }}
              autoComplete={'off'}
              defaultValue={value}
              size="small"
              suffix={<XlbIcon name="sousuo" />}
              onClick={async () => {
                NiceModal.show(NiceModal.create(XlbBaseGoods), {
                  title: `【${record?.item_name}】库存明细`,
                  BatchList: rowData.map((v) =>
                    String(
                      v.item_id +
                        v.batch_number +
                        v.producing_date +
                        v.expire_date,
                    ),
                  ),
                  handConfirm: (goodsList: any, chooseList: any) => {
                    getPrice(goodsList, chooseList, index?.index);
                  },
                  params: {
                    item_ids: [record?.item_id],
                    storehouse_ids: [form.getFieldValue('storehouse_id')],
                    store_ids: [form.getFieldValue('store_id')],
                    key: item.code,
                  },
                });
              }}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'expire_date':
        item.render = (value: any, record: any, index: any) => {
          return record?._click && record?.expire_date_flag ? (
            <XlbInput
              key={record?.[item.code]}
              style={{ width: '120px' }}
              autoComplete={'off'}
              defaultValue={value}
              size="small"
              suffix={<XlbIcon name="sousuo" />}
              onClick={async () => {
                NiceModal.show(NiceModal.create(XlbBaseGoods), {
                  title: `【${record?.item_name}】库存明细`,
                  BatchList: rowData.map((v) =>
                    String(
                      v.item_id +
                        v.batch_number +
                        v.producing_date +
                        v.expire_date,
                    ),
                  ),
                  handConfirm: (goodsList: any, chooseList: any) => {
                    getPrice(goodsList, chooseList, index?.index);
                  },
                  params: {
                    item_ids: [record?.item_id],
                    storehouse_ids: [form.getFieldValue('storehouse_id')],
                    store_ids: [form.getFieldValue('store_id')],
                    key: item.code,
                  },
                });
              }}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'batch_number':
        item.render = (value: any, record: any, index: any) => {
          return record?._click && record?.batch_number_flag ? (
            <XlbInput
              key={record?.[item.code]}
              style={{ width: '120px' }}
              autoComplete={'off'}
              defaultValue={value}
              size="small"
              suffix={<XlbIcon name="sousuo" />}
              onClick={async () => {
                NiceModal.show(NiceModal.create(XlbBaseGoods), {
                  title: `【${record?.item_name}】库存明细`,
                  BatchList: rowData.map((v) =>
                    String(
                      v.item_id +
                        v.batch_number +
                        v.producing_date +
                        v.expire_date,
                    ),
                  ),
                  handConfirm: (goodsList: any, chooseList: any) => {
                    getPrice(goodsList, chooseList, index?.index);
                  },
                  params: {
                    item_ids: [record?.item_id],
                    storehouse_ids: [form.getFieldValue('storehouse_id')],
                    store_ids: [form.getFieldValue('store_id')],
                    key: item.code,
                  },
                });
              }}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'memo':
        item.render = (value: any, record: any, index: any) => {
          return record?._click && record?.item_name ? (
            <XlbInput
              key={record?.[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index?.index?.toString()}
              defaultValue={value}
              onFocus={(e) => e.target.select()}
              onBlur={(e) =>
                inputBlur(e, index?.index, item.code, record?.ratio)
              }
              style={{ textAlign: 'left' }}
              onChange={(e) => (rowData[index?.index].memo = e.target.value)}
              onClick={(e) => {
                e.stopPropagation();
              }}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
    }
    return item;
  };
  // 单据选择添加
  const addOrder = async (list: any, type: string = 'no_count') => {
    setIsLoading(true);
    const res = await getReceiveOrderItem({
      fids: list,
      add_count: type === 'count',
      in_store_id: getArrayChild(form.getFieldValue('in_store_id')),
      storehouse_id: getArrayChild(form.getFieldValue('storehouse_id')),
      out_store_id: getArrayChild(form.getFieldValue('store_id')),
    });
    if (res?.code === 0) {
      // const newArr = itemConfirmCompare(res.data)
      const ids = rowData.map((v) => v?.item_id);
      let repeatArr: Array<any> = [];
      let newArr: Array<any> = [];
      if (centerStore?.isCenter) {
        repeatArr = res?.data?.filter(
          (v: any) =>
            ids.includes(v?.item_id) &&
            !(
              v?.account_method == '中心手工批次' ||
              (v?.account_method == '移动加权平均' &&
                (v?.expire_date_flag || v?.producing_date_flag))
            ),
        );
        newArr = res?.data?.filter(
          (item: any) =>
            !ids.includes(item?.item_id) ||
            item?.account_method == '中心手工批次' ||
            (item?.account_method == '移动加权平均' &&
              (item?.expire_date_flag || item?.producing_date_flag)),
        );
      } else {
        repeatArr = res?.data?.filter((v: any) => ids.includes(v?.item_id));
        newArr = res?.data?.filter((item: any) => !ids.includes(item?.item_id));
      }
      let rName = [repeatArr.map((v: any) => `【${v?.item_name}】`).join('、')];
      if (repeatArr?.length) {
        XlbTipsModal({
          tips: (
            <>
              <div style={{ color: 'red' }}>
                以下商品已存在，不允许重复添加，系统已自动过滤!
              </div>
              {rName.map((_) => {
                return <div key={_}>{_}</div>;
              })}
            </>
          ),
        });
        setIsLoading(false);
        return;
      }
      const newList = newArr?.map((v: any) => {
        return {
          // sale_price: v?.sale_price,
          account_method: v?.account_method,
          item_id: v?.item_id,

          item_code: v?.item_code,
          item_bar_code: v?.item_bar_code,
          item_name: v?.item_name,
          item_spec: v?.item_spec,
          unit: v?.unit,
          quantity: v?.quantity,
          price: v?.price,
          money: v?.money,
          tax_rate: v?.tax_rate,
          tax_money: v?.tax_money || 0,
          tare: v?.tare || 0,
          basic_unit: v?.basic_unit || v?.unit,
          ratio: v?.ratio,
          basic_quantity: v?.basic_quantity || 0,
          basic_price: v?.basic_price,
          present_unit: v?.present_unit,
          present_ratio: v?.present_ratio,
          present_quantity: v?.present_quantity,
          producing_date: v?.producing_date || null,
          expire_date: v?.expire_date || null,
          batch_number: v?.batch_number || null,
          // expire_type: v.expire_type,//保质期规则
          period: v?.period
            ? v?.period
            : v?.expire_type === 1
              ? v?.expire_type_num + '天'
              : v?.expire_type_num + '月', //保质期
          basic_stock_quantity: v?.basic_stock_quantity,
          basic_available_stock_quantity: v?.basic_available_stock_quantity,
          memo: v?.memo || '',
          cost_price: v?.cost_price,
          producing_date_flag: v?.producing_date_flag, //是否可选生产日期
          batch_number_flag: v?.batch_number_flag, //批次号
          expire_date_flag: v?.expire_date_flag, //到期日期
          date_in_type: v?.date_in_type, //日期录入规则 -----
          delivery_unit: v?.delivery_unit,
          delivery_ratio: v?.delivery_ratio, //配送单位换算率
          purchase_unit: v?.purchase_unit,
          purchase_ratio: v?.purchase_ratio, //采购单位
          stock_unit: v?.stock_unit,
          stock_ratio: v?.stock_ratio, //库存单位
          wholesale_unit: v?.wholesale_unit,
          wholesale_ratio: v?.wholesale_ratio, //批发单位
          receive_order_fids: v?.receive_order_fids, //关联采购单单据号
          // batch_unit: v.batch_unit || '', // ----
          // batch_ratio: v.batch_ratio || '', //批次单位 ----
          units: Array.from(
            new Set([
              JSON.stringify({ name: v?.basic_unit, ratio: 1 }),
              JSON.stringify({
                name: v?.unit,
                ratio: v?.ratio,
              }),
              JSON.stringify({
                name: v?.delivery_unit,
                ratio: v?.delivery_ratio,
              }),
              JSON.stringify({
                name: v?.purchase_unit,
                ratio: v?.present_ratio,
              }),
              JSON.stringify({
                name: v?.stock_unit,
                ratio: v?.stock_ratio,
              }),
              JSON.stringify({
                name: v?.wholesale_unit,
                ratio: v?.wholesale_ratio,
              }),
            ]),
          ),
        };
      });
      setEdit(newList?.length > 0); //   newList有数据可编辑
      let mergeArr = [...rowData, ...newList];
      mergeArr?.map((item, index) => {
        if (item?._empty) {
          mergeArr.splice(index, 1);
        }
      });
      setRowData([...mergeArr]);
    }
    setIsLoading(false);
  };

  //单位改变换算率
  const ratioChangeByUnit = (value: any, key: any, index: any) => {
    //触发编辑
    setEdit(true);
    rowData[index][key] = value;
    //切换【单位】计算单价=基本单价*切换后单位的换算率；计算金额=单价*数量 ；计算基本数量=数量*切换后单位的换算率
    if (key == 'unit') {
      rowData[index]?.units?.map((v: any, i: any) => {
        JSON.parse(v)?.name == value
          ? (rowData[index].ratio = JSON.parse(v)?.ratio)
          : null;
      });
      rowData[index]?.unit == rowData[index]?.batch_unit &&
        (rowData[index].ratio = rowData[index]?.batch_ratio);
      rowData[index].price = safeMath.multiply(
        rowData[index]?.basic_price,
        rowData[index]?.ratio,
      );
      rowData[index].money = safeMath.multiply(
        rowData[index]?.price,
        rowData[index]?.quantity,
      );
      rowData[index].basic_quantity = safeMath.multiply(
        rowData[index]?.quantity,
        rowData[index]?.ratio,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index]?.money,
        safeMath.divide(
          rowData[index]?.money,
          safeMath.add(1, safeMath.multiply(rowData[index]?.tax_rate, 0.01)),
        ),
      );
    }
    if (key === 'present_unit') {
      rowData[index]?.units?.map((v: any, i: any) => {
        JSON.parse(v)?.name == value
          ? (rowData[index].present_ratio = JSON.parse(v)?.ratio)
          : null;
      });
      rowData[index]?.present_unit == rowData[index]?.batch_unit &&
        (rowData[index].present_ratio = rowData[index]?.batch_ratio);
    }
    setRowData([...rowData]);
  }; //输入框改变
  const inputChange = (e: any, index: any, key: any, ratio: any) => {
    //记录编辑
    setEdit(true);
    let regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (
      key == 'quantity' &&
      regPos.test(e.target.value) &&
      e.target.value >= 0
    ) {
      //编辑【数量】,计算基本数量,金额（含税）;
      rowData[index][key] = Number(e.target.value);
      rowData[index].basic_quantity = safeMath.multiply(e.target.value, ratio);
      rowData[index].money = safeMath.multiply(
        rowData[index].price,
        rowData[index][key],
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
    }
    if (key == 'price' && regPos.test(e.target.value) && e.target.value >= 0) {
      //编辑【单价（含税）】 计算金额（含税）=单价（含税）*数量；计算基本单价（含税）=单价（含税）/ 换算率；
      rowData[index][key] = Number(e.target.value);
      rowData[index].money = safeMath.multiply(
        rowData[index][key],
        rowData[index].quantity,
      );
      rowData[index].basic_price = safeMath.divide(
        rowData[index][key],
        rowData[index].ratio,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
    }
    if (key == 'money' && regPos.test(e.target.value) && e.target.value >= 0) {
      //编辑【金额（含税）】 计算税费=金额(含税)*销项税率*0.01  计算单价含税
      rowData[index][key] = Number(e.target.value);
      if (rowData[index].quantity != 0) {
        rowData[index].price = safeMath.divide(
          rowData[index].money,
          rowData[index].quantity,
        );
        rowData[index].basic_price = safeMath.divide(
          rowData[index].money,
          rowData[index].basic_quantity,
        );
      }
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
    }
    if (key == 'ratio' && regPos.test(e.target.value) && e.target.value >= 0) {
      //编辑【换算率】 计算基本单价（含税）, 基本数量；
      rowData[index][key] = Number(e.target.value);
      rowData[index].basic_quantity = safeMath.multiply(
        rowData[index].ratio,
        rowData[index].quantity,
      );
      rowData[index].money = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].basic_quantity,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
      rowData[index].present_unit == rowData[index].unit &&
        (rowData[index].present_ratio = rowData[index].ratio);
      if (rowData[index].quantity != 0) {
        rowData[index].price = safeMath.divide(
          rowData[index].money,
          rowData[index].quantity,
        );
      }
    }
    if (key == 'basic_quantity' && regPos.test(e.target.value)) {
      //编辑【基本数量】计算换算率
      rowData[index][key] = Number(e.target.value);
      if (Number(e.target.value) === 0) {
        rowData[index].quantity = 0;
      }
      rowData[index].money = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].basic_quantity,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
      if (rowData[index].quantity != 0) {
        rowData[index].ratio = safeMath.divide(
          rowData[index].basic_quantity,
          rowData[index].quantity,
        );
        rowData[index].price = safeMath.divide(
          rowData[index].money,
          rowData[index].quantity,
        );
        rowData[index].present_unit == rowData[index].unit &&
          (rowData[index].present_ratio = rowData[index].ratio);
      }
    }
    if (
      key == 'basic_price' &&
      regPos.test(e.target.value) &&
      e.target.value >= 0
    ) {
      //编辑【基本单价】金额=基本单价*基本数量；计算单价=基本单价*换算率
      rowData[index][key] = Number(e.target.value);
      rowData[index].price = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].ratio,
      );
      rowData[index].money = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].basic_quantity,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
    }
    if (key == 'present_quantity' && regPos.test(e.target.value)) {
      rowData[index][key] = Number(e.target.value);
    }
    // debounce(setData)
  };
  //失去焦点
  const inputBlur = (e: any, index: any, key: any, ratio: any) => {
    let regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (
      key == 'quantity' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      XlbTipsModal({ tips: '数量请输入>=0的数字' });
      rowData[index][key] = 0;
      return;
    }
    if (
      key == 'price' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      XlbTipsModal({ tips: '单价（含税）请输入>=0的数字' });
      rowData[index][key] = 0;
      return;
    }
    if (
      key == 'money' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      XlbTipsModal({ tips: '金额（含税）请输入>=0的数字' });
      rowData[index][key] = 0;
      return;
    }
    if (
      key == 'ratio' &&
      (!regPos.test(e.target.value) || e.target.value <= 0)
    ) {
      XlbTipsModal({ tips: '换算率请输入>0的数字' });
      rowData[index][key] = 0;
      return;
    }
    if (key == 'basic_quantity' && !regPos.test(e.target.value)) {
      XlbTipsModal({ tips: '基本数量请输入数字' });
      rowData[index][key] = 0;
      return;
    }
    if (key == 'basic_quantity' && !Number(rowData[index].quantity)) {
      XlbTipsModal({ tips: '数量为0,请先输入数量' });
      rowData[index][key] = 0;
      return;
    }
    if (
      key == 'basic_price' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      XlbTipsModal({ tips: '基本单价（含税）请输入>=0的数字' });
      rowData[index][key] = 0;
      return;
    }
    if (key == 'present_quantity' && !regPos.test(e.target.value)) {
      XlbTipsModal({ tips: '赠品数量请输入数字' });
      rowData[index][key] = 0;
      return;
    }
    if (
      safeMath.multiply(
        rowData[index].present_quantity,
        rowData[index].quantity,
      ) < 0
    ) {
      XlbTipsModal({
        tips: `商品【${rowData[index].item_name}】数量与赠品数量必须同为正数或负数！`,
      });
      rowData[index].present_quantity = 0;
      rowData[index].quantity = 0;
      return;
    }
    if (key == 'memo' && e.target.value.length > 200) {
      XlbTipsModal({ tips: '备注长度应<=200' });
      rowData[index][key] = e.target.value.substring(0, 200);
      return;
    }
    setRowData([...rowData]);
  };
  //#region 查询门店下仓库
  const getStockData = async (id: any) => {
    const res = await getStock({ store_id: id });
    if (res?.code == 0) {
      const labArr = res.data.map((item: any) => ({
        label: item.name,
        value: item.id,
        default_flag: item.default_flag,
      }));
      const _lab = labArr.filter((item: any) => item.default_flag);
      if (_lab.length !== 0) {
        form.setFieldsValue({ storehouse_id: _lab[0].value });
      } else {
        form.setFieldsValue({ storehouse_id: labArr[0]?.value });
      }
      setStockList(labArr);
    }
  };
  //重新获取单价
  const getPrice = async (list: any, chooseLists: any[], chooseindex: any) => {
    const details = list.map((item: any) => ({
      batch_number: item.batch_number,
      expire_date: item.expire_date,
      producing_date: item.producing_date,
      item_id: item.item_id,
    }));
    const data = {
      details: details,
      in_store_id: getArrayChild(form.getFieldValue('in_store_id')),
      storehouse_id: form.getFieldValue('storehouse_id'),
    };
    setIsLoading(true);
    const res = await deliveryPrice(data);
    setIsLoading(false);
    if (res.code === 0) {
      const _list = list.map((v: any, i: any) => {
        if (v.batch_number === res.data[i].batch_number) {
          return { ...v, basic_price: res.data[i].basic_price };
        }
      });
      const goodsList = [...rowData];
      if (_list.length > 0) {
        let itemInfo = _list[0];
        let onlyIds: string[] = [];
        if (chooseindex == -1)
          chooseindex = goodsList.findIndex(
            (vo: any) => vo.item_id === itemInfo.item_id,
          );
        let goods = goodsList[chooseindex];
        let goodsIndex = goodsList.findIndex(
          (goods: any) =>
            (goods.batch_number_flag &&
              goods.item_id + '-3-' + goods.batch_number ==
                itemInfo.item_id + '-3-' + itemInfo.batch_number) ||
            (goods.expire_date_flag &&
              goods.item_id + '-2-' + goods.expire_date ==
                itemInfo.item_id + '-2-' + itemInfo.expire_date) ||
            (goods.producing_date_flag &&
              goods.item_id + '-1-' + goods.producing_date ==
                itemInfo.item_id + '-1-' + itemInfo.producing_date),
        );
        if (goodsIndex == -1 || goodsIndex == chooseindex) {
          !goodsList[chooseindex]['batch_id'] &&
            (goodsList[chooseindex]['quantity'] = 0);
          !goodsList[chooseindex]['batch_id'] &&
            (goodsList[chooseindex]['basic_quantity'] = 0);
          goodsList[chooseindex]['batch_number'] = itemInfo['batch_number'];
          goodsList[chooseindex]['producing_date'] = itemInfo['producing_date'];
          goodsList[chooseindex]['expire_date'] = itemInfo['expire_date'];
          goodsList[chooseindex]['tare'] = itemInfo['tare']
            ? itemInfo['tare']
            : 0;
          goodsList[chooseindex]['basic_stock_quantity'] =
            itemInfo['basic_quantity'];
          goodsList[chooseindex]['basic_price'] = itemInfo['basic_price'];
          goodsList[chooseindex]['money'] = safeMath.multiply(
            goodsList[chooseindex]['basic_price'],
            goodsList[chooseindex]['basic_quantity'],
          );
          goodsList[chooseindex]['price'] = safeMath.multiply(
            goodsList[chooseindex]['basic_price'],
            goodsList[chooseindex]['ratio'],
          );
          goodsList[chooseindex]['tax_money'] = safeMath.minus(
            goodsList[chooseindex]['money'],
            safeMath.divide(
              goodsList[chooseindex]['money'],
              safeMath.add(
                1,
                safeMath.multiply(goodsList[chooseindex]['tax_rate'], 0.01),
              ),
            ),
          );
          goodsList[chooseindex]['cost_price'] = itemInfo['basic_cost_price'];
          goodsList[chooseindex]['batch_unit'] = itemInfo['batch_unit'];
          goodsList[chooseindex]['batch_ratio'] = itemInfo['batch_ratio'];
          if (goodsList[chooseindex]['unit'] === itemInfo['batch_unit']) {
            goodsList[chooseindex]['ratio'] = itemInfo['batch_ratio'];
            goodsList[chooseindex]['basic_quantity'] = safeMath.multiply(
              itemInfo['batch_ratio'],
              goodsList[chooseindex]['quantity'],
            );
            goodsList[chooseindex]['money'] = safeMath.multiply(
              goodsList[chooseindex]['basic_price'],
              goodsList[chooseindex]['basic_quantity'],
            );
            goodsList[chooseindex]['price'] = safeMath.multiply(
              goodsList[chooseindex]['basic_price'],
              goodsList[chooseindex]['ratio'],
            );
            goodsList[chooseindex]['tax_money'] = safeMath.minus(
              goodsList[chooseindex]['money'],
              safeMath.divide(
                goodsList[chooseindex]['money'],
                safeMath.add(
                  1,
                  safeMath.minus(goodsList[chooseindex]['tax_rate'], 0.01),
                ),
              ),
            );
          }
          if (
            goodsList[chooseindex]['present_unit'] === itemInfo['batch_unit']
          ) {
            goodsList[chooseindex]['present_ratio'] = itemInfo['batch_ratio'];
          }
          goodsList[chooseindex]['batch_id'] = itemInfo['batch_id'];
        }
        let onlyCode = '';
        if (goods.batch_number_flag)
          onlyCode = itemInfo.item_id + '-3-' + itemInfo.batch_number;
        if (goods.producing_date_flag)
          onlyCode = itemInfo.item_id + '-1-' + itemInfo.producing_date;
        if (goods.expire_date_flag)
          onlyCode = itemInfo.item_id + '-2-' + itemInfo.expire_date;
        onlyIds.push(onlyCode);
        _list.map((item: any, index: number) => {
          if (index > 0) {
            onlyCode = '';
            if (goods.batch_number_flag)
              onlyCode = item.item_id + '-3-' + item.batch_number;
            if (goods.producing_date_flag)
              onlyCode = item.item_id + '-1-' + item.producing_date;
            if (goods.expire_date_flag)
              onlyCode = item.item_id + '-2-' + item.expire_date;
            onlyIds.push(onlyCode);
            goodsIndex = goodsList.findIndex(
              (goods: any) =>
                (goods.batch_number_flag &&
                  goods.item_id + '-3-' + goods.batch_number ==
                    item.item_id + '-3-' + item.batch_number) ||
                (goods.expire_date_flag &&
                  goods.item_id + '-2-' + goods.expire_date ==
                    item.item_id + '-2-' + item.expire_date) ||
                (goods.producing_date_flag &&
                  goods.item_id + '-1-' + goods.producing_date ==
                    item.item_id + '-1-' + item.producing_date),
            );
            if (goodsIndex == -1) {
              let goodsItem = JSON.parse(JSON.stringify(goods));
              goodsItem['quantity'] = 0;
              goodsItem['basic_quantity'] = 0;
              goodsItem['present_quantity'] = 0;
              goodsItem['batch_number'] = item['batch_number'];
              goodsItem['producing_date'] = item['producing_date'];
              goodsItem['expire_date'] = item['expire_date'];
              goodsItem['tare'] = item['tare'] ? item['tare'] : 0;
              goodsItem['basic_stock_quantity'] = item['basic_quantity'];
              goodsItem['basic_price'] = item['basic_price'];
              goodsItem['money'] = safeMath.multiply(
                goodsItem['basic_price'],
                goodsItem['basic_quantity'],
              );
              goodsItem['price'] = safeMath.multiply(
                goodsItem['basic_price'],
                goodsItem['ratio'],
              );
              goodsItem['tax_money'] = safeMath.minus(
                goodsItem['money'],
                safeMath.divide(
                  goodsItem['money'],
                  safeMath.add(
                    1,
                    safeMath.multiply(goodsItem['tax_rate'], 0.01),
                  ),
                ),
              );
              goodsItem['cost_price'] = item['basic_cost_price'];
              goodsItem['batch_unit'] = item['batch_unit'];
              goodsItem['batch_ratio'] = item['batch_ratio'];
              if (goodsItem['unit'] === item['batch_unit']) {
                goodsItem['ratio'] = item['batch_ratio'];
                goodsItem['basic_quantity'] = safeMath.multiply(
                  item['batch_ratio'],
                  goodsList[chooseindex]['quantity'],
                );
                goodsItem['money'] = safeMath.multiply(
                  goodsItem['basic_price'],
                  goodsItem['basic_quantity'],
                );
                goodsItem['price'] = safeMath.multiply(
                  goodsItem['basic_price'],
                  goodsItem['ratio'],
                );
                goodsItem['tax_money'] = safeMath.minus(
                  goodsItem['money'],
                  safeMath.divide(
                    goodsItem['money'],
                    safeMath.add(
                      1,
                      safeMath.multiply(goodsItem['tax_rate'], 0.01),
                    ),
                  ),
                );
              }
              if (goodsItem['present_unit'] === item['batch_unit']) {
                goodsItem['present_ratio'] = item['batch_ratio'];
              }
              goodsList.push({ ...goodsItem, _click: false });
              setRowData(goodsList);
            } else {
              goodsList[goodsIndex]['batch_number'] = item['batch_number'];
              goodsList[goodsIndex]['producing_date'] = item['producing_date'];
              goodsList[goodsIndex]['expire_date'] = item['expire_date'];
              goodsList[goodsIndex]['tare'] = item['tare'] ? item['tare'] : 0;
              goodsList[goodsIndex]['basic_stock_quantity'] =
                item['basic_quantity'];
              goodsList[goodsIndex]['basic_price'] = item['basic_price'];
              goodsList[goodsIndex]['money'] = safeMath.multiply(
                goodsList[goodsIndex]['basic_price'],
                goodsList[goodsIndex]['basic_quantity'],
              );
              goodsList[goodsIndex]['price'] = safeMath.multiply(
                goodsList[goodsIndex]['basic_price'],
                goodsList[goodsIndex]['ratio'],
              );
              goodsList[goodsIndex]['tax_money'] = safeMath.minus(
                goodsList[goodsIndex]['money'],
                safeMath.divide(
                  goodsList[goodsIndex]['money'],
                  safeMath.add(
                    1,
                    safeMath.multiply(goodsList[goodsIndex]['tax_rate'], 0.01),
                  ),
                ),
              );
              goodsList[goodsIndex]['cost_price'] = item['basic_cost_price'];
              goodsList[goodsIndex]['batch_unit'] = item['batch_unit'];
              goodsList[goodsIndex]['batch_ratio'] = item['batch_ratio'];
              if (goodsList[goodsIndex]['unit'] === item['batch_unit']) {
                goodsList[goodsIndex]['ratio'] = item['batch_ratio'];
                goodsList[goodsIndex]['basic_quantity'] = safeMath.multiply(
                  item['batch_ratio'],
                  goodsList[goodsIndex]['quantity'],
                );
                goodsList[goodsIndex]['money'] = safeMath.multiply(
                  goodsList[goodsIndex]['basic_price'],
                  goodsList[goodsIndex]['basic_quantity'],
                );
                goodsList[goodsIndex]['price'] = safeMath.multiply(
                  goodsList[goodsIndex]['basic_price'],
                  goodsList[goodsIndex]['ratio'],
                );
                goodsList[goodsIndex]['tax_money'] = safeMath.minus(
                  goodsList[goodsIndex]['money'],
                  safeMath.divide(
                    goodsList[goodsIndex]['money'],
                    safeMath.add(
                      1,
                      safeMath.multiply(
                        goodsList[goodsIndex]['tax_rate'],
                        0.01,
                      ),
                    ),
                  ),
                );
              }
              if (goodsList[goodsIndex]['unit'] === item['batch_unit']) {
                goodsList[goodsIndex]['present_ratio'] = item['batch_ratio'];
              }
            }
          }
        });
        let tmpGoodsList = goodsList.filter((v: any) => {
          let onlyCode = '';
          if (v.batch_number_flag)
            onlyCode = v.item_id + '-3-' + v.batch_number;
          if (v.producing_date_flag)
            onlyCode = v.item_id + '-1-' + v.producing_date;
          if (v.expire_date_flag) onlyCode = v.item_id + '-2-' + v.expire_date;
          if (v.item_id === itemInfo.item_id && !onlyIds.includes(onlyCode)) {
            v['only_code'] = onlyCode;
            return v;
          }
        });
        tmpGoodsList.map((v: any) => {
          let gi = goodsList.findIndex((gv: any) => {
            let onlyCode = '';
            if (gv.batch_number_flag)
              onlyCode = gv.item_id + '-3-' + gv.batch_number;
            if (gv.producing_date_flag)
              onlyCode = gv.item_id + '-1-' + gv.producing_date;
            if (gv.expire_date_flag)
              onlyCode = gv.item_id + '-2-' + gv.expire_date;
            return v.only_code === onlyCode;
          });
          goodsList.splice(gi, 1);
        });
        goodsList.map((item, index) => {
          if (item.newRow) {
            goodsList.splice(index, 1);
          }
        });
        setRowData([...goodsList]);
      }
    }
  };
  //#region 读取信息
  const readinfo = async (
    _fid: any,
    fids: any = [],
    summary: boolean = tabsKey == 'detailTab' ? false : true,
  ) => {
    setEdit(fid === 'handleBill'), fid !== 'handleBill' && setAudit(false);
    setIsLoading(true);
    const res =
      _fid === 'handleBill'
        ? await deliveryout({ fids: fids })
        : await readInfo({
            fid: _fid,
            summary: summary,
          });
    if (res?.code === 0) {
      const storeres = await getstoreName({ id: res?.data?.store_id });
      await getStockData(res.data.store_id);
      setCenterStore({
        isCenter: res.data.out_center_flag,
        deliveryCenterId: '',
      });
      // 货主字段回显
      if (enable_cargo_owner) {
        getInStoreOrg(
          res?.data.in_store_id,
          res?.data?.org_id,
          res?.data?.org_name,
        );
        getOutStoreOrg(
          res?.data.store_id,
          res?.data?.out_org_id,
          res?.data?.out_org_name,
        );
      }
      const details = res.data.details.map((v: any) => {
        const unit = { name: v.unit, ratio: v.ratio };
        const basic_unit = {
          name: v.basic_unit,
          ratio: v.basic_unit === v.unit ? unit.ratio : 1,
        };
        const delivery_unit = {
          name: v.delivery_unit,
          ratio: v.delivery_unit === v.unit ? unit.ratio : v.delivery_ratio,
        };
        const purchase_unit = {
          name: v.purchase_unit,
          ratio: v.purchase_unit === v.unit ? unit.ratio : v.purchase_ratio,
        };
        const stock_unit = {
          name: v.stock_unit,
          ratio: v.stock_unit === v.unit ? unit.ratio : v.stock_ratio,
        };
        const wholesale_unit = {
          name: v.wholesale_unit,
          ratio: v.wholesale_unit === v.unit ? unit.ratio : v.wholesale_ratio,
        };
        return {
          ...v,
          short_row_id: uuidv4(),
          basic_stock_quantity: v.basic_stock_quantity || 0,
          basic_available_stock_quantity: v.basic_available_stock_quantity || 0,
          units: Array.from(
            new Set([
              JSON.stringify(basic_unit),
              JSON.stringify(delivery_unit),
              JSON.stringify(purchase_unit),
              JSON.stringify(stock_unit),
              JSON.stringify(wholesale_unit),
              JSON.stringify(unit),
            ]),
          ),
        };
      });
      setWmsInfo(res.data?.wms_info ? res.data?.wms_info : {});
      setInfo({ state: res.data.state, fid: res.data.fid });
      setFileList(res.data.files);
      // setRowData(
      //   res.data.details?.map((v: any) => ({ ...v, short_row_id: uuidv4() })),
      // );

      tabsKey == 'detailTab'
        ? setRowData(details)
        : setSummaryData(res.data?.summary_details);
      // setSummaryData(res.data?.summary_details);
      // setEnableDeliveryCenter(res.data?.out_center_flag);
      // setManagementType(res.data.management_type);
      form.setFieldsValue({
        ...res.data,
        org_parent_id:
          storeres?.code == 0 ? storeres?.data?.org_parent_id : null,
        operate_date: res.data.operate_date
          ? dayjs(res.data.operate_date)
          : dayjs(),
        payment_date: res?.data?.payment_date
          ? dayjs(res.data.payment_date)
          : null,
      });
      setAmount(
        res.data.in_enable_credit_line
          ? {
              balance: res.data.balance,
              credit_line: res.data.credit_line,
              available_balance: res.data.available_balance,
            }
          : {},
      );
      res.data.state == 'INIT' &&
        res.data.in_enable_credit_line &&
        getAmount(res.data.in_store_id, res.data.store_id);
      setIsLoading(false);
    } else {
      setIsLoading(false);
    }
  };
  //门店管理 启用授信额度
  const getAmount = async (store_id: number, out_store_id: number) => {
    if (!store_id || !out_store_id) return;
    const res = await getStoreAmout({ store_id, out_store_id, freeze: false });
    if (res?.code === 0) setAmount(res.data);
  };
  //查询是否开启跨配送中心调拨
  const getdeliveryparam = async () => {
    const res = await deliveryparamRead();
    if (res?.code == 0) {
      setToOtherCenter(res.data.un_center_transform);
      // setUnCenterStoreApplication(res.data.un_center_store_application);
    }
  };

  const referenceRef = useRef<HTMLDivElement | null>(null);
  const [dialogWidth, setDialogWidth] = useState(180); // 默认值

  useEffect(() => {
    if (referenceRef.current) {
      const observer = new ResizeObserver(() => {
        const width = (referenceRef.current?.offsetWidth || 298) - 118;
        setDialogWidth(width);
      });

      observer.observe(referenceRef.current);

      return () => observer.disconnect(); // 清理
    }
  }, []);
  useEffect(() => {
    setBan(!hasAuth(['调出单', '编辑']));
    setFid(record.fid);
    if (record.fid === 1) {
      setRowData([{ _click: false, _edit: false, _empty: true }]);
      setCenterStore({
        isCenter: LStorage.get('userInfo').store.enable_delivery_center,
        deliveryCenterId: LStorage.get('userInfo').store.upstream_center_id,
      });
      if (!enable_cargo_owner) {
        getStockData(LStorage.get('userInfo').store_id);
      }
      form.setFieldsValue({
        store_name: enable_cargo_owner
          ? null
          : LStorage.get('userInfo').store_name,
        store_id: enable_cargo_owner ? null : LStorage.get('userInfo').store_id,
        out_org_name: enable_cargo_owner
          ? null
          : enable_organization && LStorage.get('userInfo').org_name,
        out_org_id: enable_cargo_owner
          ? null
          : enable_organization && LStorage.get('userInfo').org_id,
        org_parent_id: enable_cargo_owner
          ? null
          : enable_organization && LStorage.get('userInfo').org_parent_id,
        operate_date: dayjs(),
      });
    }
    getdeliveryparam();
  }, []);
  useEffect(() => {
    const rec = record || {};
    if (tabsKey === 'totalTab') {
      (rec?.fid !== 1 || form.getFieldValue('fid')) &&
        readinfo(
          rec?.fid == 1 ? form.getFieldValue('fid') : rec?.fid,
          [],
          true,
        );
      // setRowData([...rowData]);
    } else {
      (rec.fid !== 1 || form.getFieldValue('fid')) &&
        readinfo(rec.fid == 1 ? form.getFieldValue('fid') : rec.fid);
    }
  }, [JSON.stringify(tabsKey)]);
  useEffect(() => {
    getArrayChild(form.getFieldValue('store_id')) &&
    getArrayChild(form.getFieldValue('storehouse_id')) &&
    info.state === 'INIT'
      ? setBatchLoding(false)
      : setBatchLoding(true);
  }, [form.getFieldsValue()]);
  useEffect(() => {
    // 设置合计行
    footerData[0] = {};
    footerData[0]._index = '合计';
    footerData[0].money = hasAuth(['调出单/配送价', '查询'])
      ? rowData
          .reduce((sum, v) => safeMath.add(sum, Number(v.money || 0) || 0), 0)
          .toFixed(2)
      : '****';
    footerData[0].quantity = rowData
      .reduce((sum, v) => safeMath.add(sum, Number(v.quantity || 0) || 0), 0)
      .toFixed(3);
    footerData[0].tax_money = hasAuth(['调出单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) => safeMath.add(sum, Number(v.tax_money || 0) || 0),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].basic_quantity = rowData
      .reduce(
        (sum, v) => safeMath.add(sum, Number(v.basic_quantity || 0) || 0),
        0,
      )
      .toFixed(3);
    footerData[0].present_quantity = rowData
      .reduce(
        (sum, v) => safeMath.add(sum, Number(v.present_quantity || 0) || 0),
        0,
      )
      .toFixed(3);
    footerData[0].cost_money = hasAuth(['调出单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) => safeMath.add(sum, Number(v.cost_money || 0) || 0),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].no_tax_cost_money = hasAuth(['调出单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) =>
              safeMath.add(sum, Number(v.no_tax_cost_money || 0) || 0),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].original_no_tax_cost_money = hasAuth([
      '调出单/成本价',
      '查询',
    ])
      ? rowData
          .reduce(
            (sum, v) =>
              safeMath.add(sum, Number(v.original_no_tax_cost_money || 0) || 0),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].purchase_money = hasAuth(['调出单/采购价', '查询'])
      ? rowData
          .reduce(
            (sum, v) => safeMath.add(sum, Number(v.purchase_money || 0) || 0),
            0,
          )
          .toFixed(4)
      : '****';
    footerData[0].no_tax_purchase_money = hasAuth(['调出单/采购价', '查询'])
      ? rowData
          .reduce(
            (sum, v) =>
              safeMath.add(sum, Number(v.no_tax_purchase_money || 0) || 0),
            0,
          )
          .toFixed(4)
      : '****';
    setFooterData([...footerData]);
    // if (
    //   rowData?.some((item: any) => item?.item_name) &&
    //   form.getFieldValue('request_order_fids')
    // ) {
    //   allClear();
    // }
  }, [JSON.stringify(rowData)]);
  //数据校验
  const errListFun = (data: any) => {
    const regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    const Arow = [...data]?.filter((item: any) => item?.item_name);
    Arow.forEach((v: any, i: any) => {
      if (!regPos.test(v.quantity) || v.quantity < 0)
        errNames.push(`第${i + 1}行,【${v.item_name}】数量请输入>=0的数字!`);
      if (!(regPos.test(v.ratio) || v.ratio < 0 || v.ratio > *********.999))
        errNames.push(
          `第${i + 1}行,【${v.item_name}】换算率请输入>=0并且<=*********.999的数字!`,
        );
      if (!regPos.test(v.price) || v.price < 0)
        errNames.push(
          `第${i + 1}行,【${v.item_name}】单价（含税）请输入>=0的数字!`,
        );
      if (!regPos.test(v.money))
        errNames.push(`第${i + 1}行,【${v.item_name}】金额（含税）请输入数字!`);
      if (!regPos.test(v.basic_quantity))
        errNames.push(`第${i + 1}行,【${v.item_name}】基本数量请输入数字`);
      if (!regPos.test(v.basic_price) || v.basic_price < 0)
        errNames.push(
          `第${i + 1}行,【${v.item_name}】基本单价（含税）请输入>=0的数字!`,
        );
      if (!regPos.test(v.present_quantity))
        errNames.push(`第${i + 1}行,【${v.item_name}】赠品数量请输入数字`);
      if (
        regPos.test(v.present_quantity) &&
        regPos.test(v.quantity) &&
        safeMath.multiply(v.present_quantity, v.quantity) < 0
      )
        errNames.push(
          `第${i + 1}行,【${v.item_name}】数量与赠品数量必须同为正数或负数！`,
        );
      if (v.memo?.length > 200)
        errNames.push(`第${i + 1}行,【${v.item_name}】备注长度应<=200!`);
    });
    rowData.forEach((v) => {
      v.basic_price = Number(v.basic_price).toFixed(8);
      v.quantity = Number(v.quantity).toFixed(8);
      v.basic_quantity = Number(v.basic_quantity).toFixed(8);
    });
    if (errNames.length) {
      XlbTipsModal({
        tips: `以下数据编辑存在问题！`,
        tipsList: errNames,
      });
      setErrNames([]);
      return;
    }
  };

  // 保存
  const saveOrder = async () => {
    const rowDataFilter = rowData?.filter((item: any) => item?.item_name);
    if (!getArrayChild(form.getFieldValue('in_store_id'))) {
      XlbTipsModal({
        tips: `请先选择调入门店！`,
      });
      return;
    }
    if (rowDataFilter.length == 0) {
      XlbTipsModal({
        tips: `请先添加商品！`,
      });
      return;
    }
    errListFun(rowData);
    if (errNames.length) return;
    const data = {
      ...form.getFieldsValue(),
      memo: form.getFieldValue('memo'),
      fid: form.getFieldValue('fid'),
      operate_date: dayjs(form.getFieldValue('operate_date')).format(
        'YYYY-MM-DD',
      ),
      payment_date: form.getFieldValue('payment_date')
        ? dayjs(form.getFieldValue('payment_date')).format('YYYY-MM-DD')
        : null,
      in_store_id: getArrayChild(form.getFieldValue('in_store_id')),
      store_id: getArrayChild(form.getFieldValue('store_id')),
      storehouse_id: form.getFieldValue('storehouse_id'),
      details: rowDataFilter?.map((v: any) => ({
        ...v,
        basic_present_quantity:
          safeMath.multiply(v.present_quantity, v.present_ratio) || 0,
      })),
      files: fileList,
    };
    if (fid == 1) {
      setIsLoading(true);
      const res = await addInfo(data);
      setIsLoading(false);
      if (res?.code == 0) {
        readinfo(res.data.fid);
        setEdit(false);
        setFid(res.data.fid);
        setIsrush(true);
        XlbMessage.success('保存成功');
        return;
      }
    } else {
      setIsLoading(true);
      const res = await updateInfo(data);
      setIsLoading(false);
      if (res?.code == 0) {
        readinfo(res.data.fid);
        setEdit(false);
        setIsrush(true);
        XlbMessage.success('保存成功');
        return;
      }
    }
  };
  //审核
  const auditItem = async () => {
    const rowDataFilter = rowData?.filter((item: any) => item?.item_name);
    //判断是否添加商品
    if (rowDataFilter?.length === 0) {
      XlbTipsModal({
        tips: `请先添加商品！`,
      });
      return;
    }
    // 判断商品数量
    if (
      !(Number(footerData[0].quantity) + Number(footerData[0].present_quantity))
    ) {
      XlbTipsModal({
        tips: `数量、赠品数量都为0，无法审核！`,
      });
      return;
    }
    //判断商品是否选择明细
    let isNull = false;
    const isNullName: any = [];
    rowDataFilter.map((item, index) => {
      item.basic_present_quantity = safeMath.multiply(
        item.present_quantity,
        item.present_ratio,
      );
      if (
        !item.producing_date &&
        !item.expire_date &&
        !item.batch_number &&
        (item.producing_date_flag ||
          item.expire_date_flag ||
          item.batch_number_flag)
      ) {
        isNull = true;
        isNullName.push(`【${rowDataFilter[index].item_name}】`);
      }
    });
    if (isNull) {
      XlbTipsModal({
        tips: `以下商品未选择明细，无法审核，请核实！`,
        tipsList: isNullName,
      });
      return;
    }
    errListFun(rowDataFilter);
    if (errNames.length) return;
    if (enable_organization) {
      const bool = await XlbTipsModal({
        tips: `调出单审核后，系统会自动产生审核的调入单，是否确认审核？`,
        isConfirm: true,
        isCancel: true,
      });
      if (!bool) return;
    }
    const data = {
      ...form.getFieldsValue(),
      memo: form.getFieldValue('memo'),
      fid: form.getFieldValue('fid'),
      operate_date: dayjs(form.getFieldValue('operate_date')).format(
        'YYYY-MM-DD',
      ),
      payment_date: form.getFieldValue('payment_date')
        ? dayjs(form.getFieldValue('payment_date')).format('YYYY-MM-DD')
        : null,
      in_store_id: getArrayChild(form.getFieldValue('in_store_id')),
      store_id: getArrayChild(form.getFieldValue('store_id')),
      storehouse_id: form.getFieldValue('storehouse_id'),
      details: rowDataFilter,
      files: fileList,
    };
    setIsLoading(true);
    const res = await auditInfo(data);
    setIsLoading(false);
    if (res?.code == 0) {
      readinfo(res.data.fid);
      setEdit(false);
      setIsrush(true);
      XlbMessage.success('操作成功');
      return;
    }
  };

  //导出
  const exportItem = async (e: any) => {
    if (edit) return XlbMessage.warning('请先保存！');
    setIsLoading(true);
    const data = {
      fid: form.getFieldValue('fid'),
      summary: tabsKey === 'totalTab',
    };
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliveryoutorder.detail.export',
      { ...data },
    );
    if (res?.code == 0) {
      XlbMessage.success('导出受理成功，请前往下载中心查看');
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
    }
    setIsLoading(false);
  };
  //返回前判断保存状态
  const goBack = async () => {
    if (edit) {
      await XlbTipsModal({
        tips: '单据未保存，是否确认返回？',
        onOkBeforeFunction: () => {
          onBack(isrush);
          return true;
        },
      });
      return false;
    }
    onBack(isrush);
  };

  //确认事件
  const handleSetRowData = (list: any, type: string) => {
    const ids = rowData.map((v) => v.item_id);
    let repeatArr: Array<any> = [];
    let newArr: Array<any> = [];
    if (centerStore.isCenter) {
      repeatArr = list.filter(
        (v: any) =>
          ids.includes(type === 'import' ? v.item_id : v.id) &&
          !(
            v.account_method == '中心手工批次' ||
            (v.account_method == '移动加权平均' &&
              (v.expire_date_flag || v.producing_date_flag))
          ),
      );
      newArr = list.filter(
        (item: any) =>
          !ids.includes(type === 'import' ? item.item_id : item.id) ||
          item.account_method == '中心手工批次' ||
          (item.account_method == '移动加权平均' &&
            (item.expire_date_flag || item.producing_date_flag)),
      );
    } else {
      repeatArr = list.filter((v: any) =>
        ids.includes(type === 'import' ? v.item_id : v.id),
      );
      newArr = list.filter(
        (item: any) =>
          !ids.includes(type === 'import' ? item.item_id : item.id),
      );
    }
    let rName = [
      repeatArr
        .map((v: any) => `【${type === 'import' ? v.item_name : v.name}】`)
        .join('、'),
    ];
    if (repeatArr.length) {
      XlbTipsModal({
        tips: `以下商品已存在，请勿重复添加：${rName}`,
      });
    }

    const newList = newArr.map((v: any) => {
      return {
        sale_price: v?.sale_price,
        account_method: v.account_method,
        item_id: v.id || v.item_id,
        item_code: v.code || v.item_code,
        item_bar_code: v.bar_code || v.item_bar_code,
        item_name: v.name || v.item_name,
        item_spec: v.purchase_spec || v.item_spec,
        unit: type === 'import' ? v.unit : v.delivery_unit,
        quantity: v.quantity || 0,
        price: v.price || safeMath.multiply(v.basic_price, v.delivery_ratio),
        money: v.money || 0,
        tax_rate: v.tax_rate,
        tax_money: v.tax_money || 0,
        tare: v.tare || 0,
        basic_unit: type === 'import' ? v.basic_unit : v.unit,
        ratio: type === 'import' ? v.ratio : v.delivery_ratio,
        basic_quantity: v.basic_quantity || 0,
        basic_price: v.basic_price,
        present_unit: v.present_unit ? v.present_unit : v.delivery_unit,
        present_ratio: v.present_ratio ? v.present_ratio : v.delivery_ratio,
        present_quantity: v.present_quantity || 0,
        producing_date: v?.producing_date || null,
        expire_date: v?.expire_date || null,
        batch_number: v?.batch_number || null,
        // expire_type: v.expire_type,//保质期规则
        period: v.period
          ? v.period
          : v.expire_type === 1
            ? v.expire_type_num + '天'
            : v.expire_type_num + '月', //保质期
        basic_stock_quantity: v.basic_stock_quantity,
        basic_available_stock_quantity: v.basic_available_stock_quantity,
        memo: v.memo || '',
        cost_price: v.cost_price,
        producing_date_flag: v.producing_date_flag, //是否可选生产日期
        batch_number_flag: v.batch_number_flag, //批次号
        expire_date_flag: v.expire_date_flag, //到期日期
        date_in_type: v.date_in_type, //日期录入规则
        delivery_unit: v.delivery_unit,
        delivery_ratio: v.delivery_ratio, //配送单位换算率
        purchase_unit: v.purchase_unit,
        purchase_ratio: v.purchase_ratio, //采购单位
        stock_unit: v.stock_unit,
        stock_ratio: v.stock_ratio, //库存单位
        wholesale_unit: v.wholesale_unit,
        wholesale_ratio: v.wholesale_ratio, //批发单位
        batch_unit: v.batch_unit || '',
        batch_ratio: v.batch_ratio || '', //批次单位
        units: Array.from(
          new Set([
            JSON.stringify(
              type === 'import'
                ? {
                    name: v.basic_unit,
                    ratio: v.unit === v.basic_unit ? v.ratio : 1,
                  }
                : {
                    name: v.delivery_unit,
                    ratio:
                      type === 'import'
                        ? v.unit === v.delivery_unit
                          ? v.ratio
                          : v.delivery_ratio
                        : v.delivery_ratio,
                  },
            ),
            JSON.stringify({
              name: v.unit,
              ratio: type === 'import' ? v.ratio : 1,
            }),
            JSON.stringify({
              name: v.delivery_unit,
              ratio:
                type === 'import'
                  ? v.unit === v.delivery_unit
                    ? v.ratio
                    : v.delivery_ratio
                  : v.delivery_ratio,
            }),
            JSON.stringify({
              name: v.purchase_unit,
              ratio:
                type === 'import'
                  ? v.unit === v.purchase_unit
                    ? v.ratio
                    : v.purchase_ratio
                  : v.purchase_ratio,
            }),
            JSON.stringify({
              name: v.stock_unit,
              ratio:
                type === 'import'
                  ? v.unit === v.stock_unit
                    ? v.ratio
                    : v.stock_ratio
                  : v.stock_ratio,
            }),
            JSON.stringify({
              name: v.wholesale_unit,
              ratio:
                type === 'import'
                  ? v.unit === v.wholesale_unit
                    ? v.ratio
                    : v.wholesale_ratio
                  : v.wholesale_ratio,
            }),
          ]),
        ),
      };
    });
    newList.length > 0 ? setEdit(true) : setEdit(false);
    let mergeArr = [...rowData, ...newList];
    mergeArr.map((item, index) => {
      if (item._empty) {
        mergeArr.splice(index, 1);
      }
    });
    setRowData([
      ...mergeArr?.map((item) => ({ ...item, short_row_id: uuidv4() })),
    ]);
  };

  //门店弹框点击事件
  const handleDialogClick = async () => {
    const { in_store_id, store_id, storehouse_id } = form.getFieldsValue(true);
    if (getArrayChild(in_store_id) === getArrayChild(store_id)) {
      XlbMessage.warning('调出门店与调入门店不可相同!');
      return;
    }
    const result = await XlbBasicData({
      type: 'goods',
      url: '/erp/hxl.erp.deliveryoutorder.item.page',
      data: {
        company_id: LStorage.get('userInfo')?.company_id,
        operator_store_id: getArrayChild(store_id),
        in_store_id: getArrayChild(in_store_id),
        store_id: getArrayChild(store_id),
        storehouse_id: form.getFieldValue('storehouse_id'),
        org_id: form.getFieldValue('org_id'),
        out_org_id: form.getFieldValue('out_org_id'),
        status: 1,
      },
      isMultiple: true,
      dataType: 'lists',
      primaryKey: 'id',
      resetForm: true,
      nullable: false,
    });
    if (result) {
      handleSetRowData(result, 'items');
    }
  };
  //打印
  const printItem = async () => {
    const data = {
      fid: form.getFieldValue('fid'),
    };
    setIsLoading(true);
    const res = await print(data);
    setIsLoading(false);
    if (res?.code == 0) {
      XlbPrintModal({
        data: res?.data,
        title: '调出单打印',
      });
    }
  };
  const allClear = () => {
    setRowData([]);
    form.setFieldsValue({
      out_store_name: '',
      out_store_id: '',
      store_name: LStorage.get('userInfo').store_name,
      store_id: LStorage.get('userInfo').store_id,
      storehouse_id: '',
      item_dept_names: '',
      memo: '',
      delivery_out_order_fids: [],
      operate_date: dayjs(),
    });
    getStockData(LStorage.get('userInfo').store_id);
  };
  const openUpload = async () => {
    setFileInfo([...fileList]);
    setUploadFileModalVisible(true);
  };
  // #region  HTML
  return (
    <div
      style={{
        padding: 12,
        height: 'calc(100vh - 120px)',
        display: 'flex',
        flexDirection: 'column',
      }}
      className={styles.form_container_wrapper}
    >
      <XlbModal
        title={
          <>
            <div>
              上传附件
              <span
                style={{
                  fontSize: 14,
                  color: ' rgb(134, 144, 156)',
                  marginLeft: 8,
                  wordBreak: 'break-all',
                }}
              >
                (支持上传PNG,JPG,JPEG,png,jpeg,webp,jpg,gif,bmp格式，最多9个)
              </span>
            </div>
          </>
        }
        open={uploadFileModalVisible}
        centered
        onOk={() => {
          setFileList([...fileInfo]);
          setUploadFileModalVisible(false);
          setFileInfo([]);
        }}
        onCancel={() => setUploadFileModalVisible(false)}
      >
        <div style={{ padding: '12px' }}>
          <XlbUploadFile
            accept={'image'}
            fileList={fileInfo}
            onChange={(e = []) => {
              if (e) {
                setFileInfo([...e]);
              } else {
                setFileInfo([]);
              }
            }}
            listType={'picture'}
            maxCount={9}
            data={{ fid: form.getFieldValue('fid') }}
            action={`${process.env.BASE_URL}/erp/hxl.erp.deliveryoutorder.file.upload`}
          ></XlbUploadFile>
        </div>
      </XlbModal>
      <div className={'button_box row-flex'}>
        <div
          style={{ width: '90%', height: '100%' }}
          className="row-flex v-flex"
        >
          <XlbButton.Group>
            {hasAuth(['调出单', '编辑']) ? (
              <XlbButton
                type="primary"
                label="保存"
                disabled={info.state !== 'INIT' || isLoading}
                onClick={saveOrder}
                icon={<XlbIcon name="baocun" />}
              />
            ) : null}
            {hasAuth(['调出单', '审核']) ? (
              <XlbButton
                type="primary"
                label="审核"
                disabled={info.state !== 'INIT' || audit || isLoading}
                onClick={auditItem}
                icon={<XlbIcon name="shenhe" />}
              />
            ) : null}
            {hasAuth(['调出单', '编辑']) ? (
              <Badge
                className={styles.badge}
                count={fileList.length}
                offset={[-16, 10]}
                size="small"
              >
                <XlbButton
                  type="primary"
                  label="附件"
                  disabled={info.state !== 'INIT' && fileList.length == 0}
                  onClick={openUpload}
                  icon={<XlbIcon name="fujian" />}
                />
              </Badge>
            ) : null}
            {!hasAuth(['调出单', '导出']) &&
            !hasAuth(['调出单', '打印']) ? null : (
              <XlbDropdownButton
                label="业务操作"
                dropList={[
                  {
                    label: '导出',
                    isNoAuth: !hasAuth(['调出单', '导出']),
                    disabled:
                      isLoading || !hasAuth(['调出单', '导出']) || !info.fid,
                  },
                  {
                    label: '打印',
                    isNoAuth: !hasAuth(['调出单', '打印']),
                    disabled:
                      !form.getFieldValue('fid') ||
                      isLoading ||
                      !hasAuth(['调出单', '打印']),
                  },
                ]}
                dropdownItemClick={(value: number, opt: any, e) => {
                  switch (opt?.label) {
                    case '导出':
                      exportItem(e);
                      break;
                    case '打印':
                      printItem();
                      break;
                  }
                }}
              />
            )}
            <XlbButton
              type="primary"
              label="返回"
              onClick={goBack}
              icon={<XlbIcon name="fanhui" />}
            />
          </XlbButton.Group>
        </div>
        <div
          style={{
            width: '10%',
            height: '28px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            columnGap: 8,
          }}
        >
          <XlbTooltip title={isFold ? '收起' : '展开'}>
            <XlbIcon
              data-type={'顶层展开收起'}
              onClick={() => setIsFold(!isFold)}
              name="shouqi"
              size={20}
              className={classnames('xlb-columns-main-btn', {
                'xlb-columns-fold': !isFold,
                'xlb-columns-expand-btn-dev': true,
              })}
            />
          </XlbTooltip>
          <XlbColumns
            isFold={isFold}
            isFoldChange={setIsFold}
            url={handleUid()?.key}
            originColumns={
              tabsKey == 'detailTab'
                ? (itemTableListDetail as any)
                : (itemTableList_all as any)
            }
            value={tabsKey == 'detailTab' ? itemArrdetail : itemArr}
            onChange={(e) => {
              if (tabsKey == 'detailTab') {
                setdetailItemArr(e);
              } else {
                setItemArr(e);
              }
            }}
            name={handleUid()?.name}
          />
        </div>
      </div>
      <XlbBasicForm
        colon
        form={form}
        autoComplete="off"
        layout="inline"
        className={styles.contractTab}
        style={isFold ? { display: 'none' } : {}}
      >
        <XlbTabs
          defaultActiveKey={'baseInfo'}
          onChange={(key: string) => setTableKey(key)}
          tabBarExtraContent={{
            right: (
              <div>
                {centerStore.isCenter && Object.keys(AmountInfo)?.length ? (
                  <div className={styles.amount_box}>
                    <span>门店余额：</span>
                    {AmountInfo.balance?.toFixed(2) || 0}
                    <span>授信额度：</span>
                    {AmountInfo.credit_line?.toFixed(2) || 0}
                    <span>配送额度：</span>
                    {AmountInfo.available_balance?.toFixed(2) || 0}
                  </div>
                ) : null}
              </div>
            ),
          }}
          items={[
            {
              label: '基本信息',
              key: 'baseInfo',
              children: (
                <div className={styles.form_container_transferDocument}>
                  <div className="row-flex">
                    <div
                      className="row-flex"
                      style={{ flex: 1, flexWrap: 'wrap', marginTop: 12 }}
                    >
                      <Row gutter={12} wrap>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item label="调出门店" name="store_id">
                            <XlbInputDialog
                              width={dialogWidth}
                              dialogParams={{
                                type: 'store',
                                dataType: 'lists',
                                isMultiple: false,
                                allClear: false,
                                data: {
                                  status: true,
                                },
                              }}
                              disabled={
                                (fid !== 1 && info.state !== 'INIT') ||
                                (fid !== 1 &&
                                  info.state == 'INIT' &&
                                  rowData?.some((v) => v.item_id) &&
                                  !rowData[0]?._empty) ||
                                (fid === 1 &&
                                  rowData?.some((v) => v.item_id) &&
                                  !rowData[0]?._empty)
                              }
                              fieldNames={{
                                idKey: 'id',
                                nameKey: 'store_name',
                              }}
                              handleValueChange={(_: string[], list: any[]) => {
                                if (list?.length > 0) {
                                  setCenterStore({
                                    isCenter: list[0].enable_delivery_center,
                                    deliveryCenterId:
                                      list[0].delivery_store?.id,
                                  });
                                  form.setFieldsValue({
                                    out_org_id:
                                      !enable_cargo_owner && list[0].org_id,
                                    out_org_name:
                                      !enable_cargo_owner && list[0].org_name,
                                    org_parent_id: list[0].org_parent_id,
                                    org_id: '',
                                    org_name: '',
                                    in_store_id: '',
                                    in_store_name: '',
                                  });
                                  // setEnableDeliveryCenter(
                                  //   list[0]?.enable_delivery_center,
                                  // );
                                  // setManagementType(list[0]?.management_type);
                                  setAmount({});
                                  getStockData(list[0].id);
                                  if (enable_cargo_owner) {
                                    // 获取货主
                                    getOutStoreOrg(list);
                                  }
                                }
                              }}
                              onChange={(e: any, list: any) => {
                                if (e?.length > 0) {
                                  form.setFieldsValue({
                                    store_name: list[0].store_name,
                                    store_id: list[0].id,
                                  });
                                } else {
                                  form.setFieldsValue({
                                    store_name: '',
                                    store_id: '',
                                  });
                                }
                              }}
                            />
                          </XlbBasicForm.Item>
                        </Col>
                        {enable_organization ? (
                          enable_cargo_owner ? (
                            <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                              <XlbBasicForm.Item
                                rules={[
                                  {
                                    required: true,
                                    message: '请选择调出组织',
                                  },
                                ]}
                                label="调出组织"
                                name="out_org_id"
                              >
                                <XlbSelect
                                  showSearch
                                  style={{ width: dialogWidth }}
                                  optionFilterProp="children"
                                  disabled={
                                    disabledCargoOwnerOut ||
                                    (fid !== 1 && info.state !== 'INIT') ||
                                    (fid !== 1 &&
                                      info.state == 'INIT' &&
                                      rowData?.some((v) => v.item_id) &&
                                      !rowData[0]?._empty) ||
                                    (fid === 1 &&
                                      rowData?.some((v) => v.item_id) &&
                                      !rowData[0]?._empty)
                                  }
                                  filterOption={(input: any, option: any) =>
                                    (option?.label ?? '')
                                      .toLowerCase()
                                      .includes(input.toLowerCase())
                                  }
                                  options={outOrgNameList}
                                  onChange={(value, options: any) => {
                                    if (value) {
                                      form?.setFieldsValue({
                                        org_parent_id: options?.parent_id,
                                        in_store_name: null,
                                        in_store_id: null,
                                        org_id: null,
                                        org_name: null,
                                      });
                                    }
                                  }}
                                ></XlbSelect>
                              </XlbBasicForm.Item>
                            </Col>
                          ) : (
                            <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                              <XlbBasicForm.Item
                                label="调出组织"
                                name="out_org_name"
                              >
                                <XlbInput
                                  disabled
                                  style={{ width: dialogWidth }}
                                />
                              </XlbBasicForm.Item>
                            </Col>
                          )
                        ) : null}
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item
                            label="调入门店"
                            name="in_store_id"
                          >
                            <XlbInputDialog
                              dialogParams={{
                                type: 'store',
                                dataType: 'lists',
                                isMultiple: false,
                                allClear: false,
                                url: '/erp-mdm/hxl.erp.store.all.shortfind',
                                data: {
                                  skip_filter: true,
                                  superiors:
                                    !centerStore.isCenter && !toOtherCenter
                                      ? centerStore.deliveryCenterId
                                      : null,
                                  center_flag: LStorage.get('userInfo').store
                                    .enable_delivery_center
                                    ? null
                                    : false,
                                  org_ids: enable_organization
                                    ? [
                                        form.getFieldValue('org_parent_id') ||
                                          getArrayChild(
                                            form.getFieldValue('out_org_id'),
                                          ),
                                      ]
                                    : null,
                                  status: true,
                                  // management_type:
                                  //   !unCenterStoreApplication &&
                                  //   managementType !== null &&
                                  //   !enableDeliveryCenter
                                  //     ? managementType == '0'
                                  //       ? '0'
                                  //       : '1'
                                  //     : null,
                                },
                              }}
                              disabled={
                                (fid !== 1 && info.state !== 'INIT') ||
                                (fid !== 1 &&
                                  info.state == 'INIT' &&
                                  rowData?.some((v) => v.item_id) &&
                                  !rowData[0]?._empty) ||
                                (fid === 1 &&
                                  rowData?.some((v) => v.item_id) &&
                                  !rowData[0]?._empty)
                              }
                              fieldNames={{
                                idKey: 'id',
                                nameKey: 'store_name',
                              }}
                              handleValueChange={(_: string[], list: any[]) => {
                                if (list?.length > 0) {
                                  form.setFieldsValue({
                                    org_id:
                                      !enable_cargo_owner && list[0].org_id,
                                    org_name: !enable_cargo_owner
                                      ? list[0].org_name
                                      : null,
                                  });
                                  list[0].enable_credit_line
                                    ? getAmount(
                                        list[0].id ||
                                          form.getFieldValue('in_store_id'),
                                        form.getFieldValue('store_id'),
                                      )
                                    : setAmount({});
                                  // 调入门店逻辑
                                  getInStoreOrg(list);
                                }
                              }}
                              onChange={(e: any) => {
                                if (e?.length > 0) {
                                  form.setFieldsValue({
                                    in_store_id: e,
                                    in_store_name: e,
                                  });
                                }
                              }}
                              width={dialogWidth}
                            />
                          </XlbBasicForm.Item>
                        </Col>
                        {enable_organization ? (
                          enable_cargo_owner ? (
                            <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                              <XlbBasicForm.Item
                                rules={[
                                  {
                                    required: true,
                                    message: '请选择调入组织',
                                  },
                                ]}
                                label="调入组织"
                                name="org_id"
                              >
                                <XlbSelect
                                  showSearch
                                  style={{ width: dialogWidth }}
                                  optionFilterProp="children"
                                  disabled={
                                    disabledCargoOwner ||
                                    (fid !== 1 && info.state !== 'INIT') ||
                                    (fid !== 1 &&
                                      info.state == 'INIT' &&
                                      rowData?.some((v) => v.item_id) &&
                                      !rowData[0]?._empty) ||
                                    (fid === 1 &&
                                      rowData?.some((v) => v.item_id) &&
                                      !rowData[0]?._empty)
                                  }
                                  filterOption={(input: any, option: any) =>
                                    (option?.label ?? '')
                                      .toLowerCase()
                                      .includes(input.toLowerCase())
                                  }
                                  options={orgNameList}
                                ></XlbSelect>
                              </XlbBasicForm.Item>
                            </Col>
                          ) : (
                            <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                              <XlbBasicForm.Item
                                label="调入组织"
                                name="org_name"
                              >
                                <XlbInput
                                  disabled
                                  style={{ width: dialogWidth }}
                                />
                              </XlbBasicForm.Item>
                            </Col>
                          )
                        ) : null}
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <div style={{ width: '100%' }} ref={referenceRef}>
                            <XlbBasicForm.Item
                              label="调出仓库"
                              name="storehouse_id"
                            >
                              <XlbSelect
                                style={{ width: '100%' }}
                                disabled={
                                  (fid !== 1 && info.state !== 'INIT') ||
                                  (fid !== 1 &&
                                    info.state == 'INIT' &&
                                    rowData?.some((v) => v.item_id) &&
                                    !rowData[0]?._empty) ||
                                  (fid === 1 &&
                                    rowData?.some((v) => v.item_id) &&
                                    !rowData[0]?._empty)
                                }
                              >
                                {stockList.map((v, i) => {
                                  return (
                                    <XlbSelect.Option key={i} value={v.value}>
                                      {v.label}
                                    </XlbSelect.Option>
                                  );
                                })}
                              </XlbSelect>
                            </XlbBasicForm.Item>
                          </div>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item label="单据号" name="fid">
                            <XlbInput
                              size="small"
                              style={{ width: dialogWidth }}
                              disabled
                            />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item
                            label="商品部门"
                            name="item_dept_names"
                          >
                            <XlbInput
                              disabled
                              size="small"
                              style={{ width: dialogWidth }}
                            />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item
                            label="调出日期"
                            name="operate_date"
                          >
                            <XlbDatePicker
                              style={{ width: dialogWidth }}
                              disabled={info.state !== 'INIT' || ban}
                            />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item
                            label="付款日期"
                            name="payment_date"
                          >
                            <XlbDatePicker
                              style={{ width: dialogWidth }}
                              disabled={info.state !== 'INIT' || ban}
                            />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                          <XlbBasicForm.Item
                            label="门店补货单"
                            name="request_order_fids"
                          >
                            <XlbInput style={{ width: dialogWidth }} disabled />
                          </XlbBasicForm.Item>
                        </Col>
                        <Col xs={16} sm={16} md={16} lg={16} xl={12} xxl={12}>
                          <XlbBasicForm.Item label="留言备注" name="memo">
                            <XlbInput
                              maxLength={200}
                              style={{ width: '100%', height: 26 }}
                              disabled={info.state !== 'INIT' || ban}
                            />
                          </XlbBasicForm.Item>
                        </Col>
                      </Row>
                    </div>
                    <div>
                      {info?.state && (
                        <div
                          style={{
                            width: '150px',
                            flexBasis: '150px',
                            display: 'flex',
                            justifyContent: 'center',
                          }}
                        >
                          <img
                            src={orderStatusIcons[info?.state]}
                            width={86}
                            height={78}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ),
            },
            {
              label: '其他信息',
              key: 'otherInfo',
              children: (
                <div className={styles.form_container_transferDocument}>
                  {/* <div style={{ marginTop: 12 }}> */}
                  <Row gutter={12} wrap>
                    <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                      <XlbBasicForm.Item label="制单人" name="create_by">
                        <XlbInput style={{ width: '100%' }} disabled />
                      </XlbBasicForm.Item>
                    </Col>
                    <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                      <XlbBasicForm.Item label="制单时间" name="create_time">
                        <XlbInput style={{ width: '100%' }} disabled />
                      </XlbBasicForm.Item>
                    </Col>
                    <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                      <XlbBasicForm.Item label="审核人" name="audit_by">
                        <XlbInput style={{ width: '100%' }} disabled />
                      </XlbBasicForm.Item>
                    </Col>
                    <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                      <XlbBasicForm.Item label="审核时间" name="audit_time">
                        <XlbInput style={{ width: '100%' }} disabled />
                      </XlbBasicForm.Item>
                    </Col>
                    <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                      <XlbBasicForm.Item label={'修改人'} name={'update_by'}>
                        <XlbInput style={{ width: '100%' }} disabled />
                      </XlbBasicForm.Item>
                    </Col>
                    <Col xs={8} sm={8} md={8} lg={8} xl={6} xxl={6}>
                      <XlbBasicForm.Item
                        label={'修改时间'}
                        name={'update_time'}
                      >
                        <XlbInput style={{ width: '100%' }} disabled />
                      </XlbBasicForm.Item>
                    </Col>
                    {Object.keys(WmsInfo)?.length ? (
                      <div className={styles.data_box}>
                        <span>总件数：</span>
                        {WmsInfo.total_quantity}
                        <span>件数：</span>
                        {WmsInfo.quantity}
                        <span>拆零件数：</span>
                        {WmsInfo.box_quantity}
                      </div>
                    ) : null}
                  </Row>
                  {/* </div> */}
                </div>
              ),
            },
          ]}
        ></XlbTabs>
        <div className={'button_box row-flex'} style={{ padding: 0 }}>
          <XlbButton.Group>
            {hasAuth(['调出单', '编辑']) && (
              <XlbButton
                type="primary"
                label="批量添加"
                disabled={
                  isLoading ||
                  batchLoding ||
                  !in_store_id ||
                  (form.getFieldValue('request_order_fids') &&
                    form.getFieldValue('request_order_fids')[0]) ||
                  (rowData?.some((v) => v.item_id) &&
                    rowData.some(
                      (v) =>
                        v?.receive_order_fids && v?.receive_order_fids?.length,
                    ))
                }
                onClick={() => handleDialogClick()}
                icon={<XlbIcon name="jia" />}
              />
            )}
            {hasAuth(['调出单', '编辑']) && (
              <XlbButton
                type="primary"
                label="导入"
                disabled={
                  isLoading ||
                  batchLoding ||
                  !in_store_id ||
                  (form.getFieldValue('request_order_fids') &&
                    form.getFieldValue('request_order_fids')[0]) ||
                  (rowData?.some((v) => v.item_id) &&
                    rowData.some(
                      (v) =>
                        v?.receive_order_fids && v?.receive_order_fids?.length,
                    ))
                }
                onClick={async () => {
                  if (
                    getArrayChild(form.getFieldValue('in_store_id')) ==
                    getArrayChild(form.getFieldValue('store_id'))
                  ) {
                    XlbTipsModal({
                      tips: `调出门店与调入门店不可相同！`,
                    });
                    return;
                  }
                  const res = await XlbImportModal({
                    templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.deliveryoutorder.template.download`,
                    importUrl: `${process.env.BASE_URL}/erp/hxl.erp.deliveryoutorder.import`,
                    params: {
                      store_id: getArrayChild(form.getFieldValue('store_id')),
                      in_store_id: getArrayChild(
                        form.getFieldValue('in_store_id'),
                      ),
                      storehouse_id: form.getFieldValue('storehouse_id'),
                      org_id: form.getFieldValue('org_id'),
                      out_org_id: form.getFieldValue('out_org_id'),
                    },
                    templateName: '下载导入模板',
                    callback: (res) => {
                      if (res?.code === 0) {
                        handleSetRowData(res?.data?.details || [], 'import');
                      }
                    },
                  });
                }}
                icon={<XlbIcon name="daoru" />}
              />
            )}

            {hasAuth(['调出单', '编辑']) ? (
              <XlbButton
                label="单据选择"
                type="primary"
                disabled={
                  isLoading ||
                  batchLoding ||
                  !in_store_id ||
                  (form.getFieldValue('request_order_fids') &&
                    form.getFieldValue('request_order_fids')[0]) ||
                  (rowData?.some((v) => v.item_id) &&
                    rowData.findIndex(
                      (v) =>
                        !v._empty &&
                        (!v?.receive_order_fids ||
                          !v?.receive_order_fids?.length),
                    ) !== -1)
                }
                onClick={() => {
                  if (
                    getArrayChild(form.getFieldValue('in_store_id')) ==
                    getArrayChild(form.getFieldValue('store_id'))
                  ) {
                    XlbTipsModal({
                      tips: `调出门店与调入门店不可相同！`,
                    });

                    return;
                  }
                  NiceModal.show(NiceModal.create(SelectOrder), {
                    add: addOrder,
                  });
                }}
              />
            ) : null}
          </XlbButton.Group>
        </div>
      </XlbBasicForm>
      <div style={{ position: 'relative' }}>
        <XlbTabs
          defaultActiveKey={'detailTab'}
          onChange={(key) => setTabsKey(key)}
          items={[
            {
              label: '商品明细',
              key: 'detailTab',
            },
            {
              label: '商品汇总',
              key: 'totalTab',
              disabled: edit || fid == 1,
            },
          ]}
        />
        <div
          style={{ visibility: !(edit || fid == 1) ? 'hidden' : 'visible' }}
          className={styles.disbt}
          onClick={() =>
            XlbMessage.warning('请保存商品明细表内容后查看商品汇总！')
          }
        />
      </div>
      {tabsKey == 'detailTab' && (
        <XlbShortTable
          showSearch
          url={'/erp/hxl.erp.deliveryoutorder.item.page'}
          data={{
            store_id: getArrayChild(
              form?.getFieldValue('store_id'),
            )?.toString(),
            in_store_id: getArrayChild(
              form.getFieldValue('in_store_id'),
            )?.toString(),
            company_id: LStorage.get('userInfo')?.company_id,
            operator_store_id: form?.getFieldValue('store_id')?.toString(),
            storehouse_id: form.getFieldValue('storehouse_id')?.toString(),
            org_id: form.getFieldValue('org_id')?.toString(),
            out_org_id: form.getFieldValue('out_org_id')?.toString(),
            status: 1,
          }}
          disabledAdd={
            rowData.some((v) => v._empty) ||
            (form.getFieldValue('request_order_fids') &&
              form.getFieldValue('request_order_fids')[0]) ||
            (rowData.length &&
              rowData.some(
                (v) => v?.receive_order_fids && v?.receive_order_fids?.length,
              ))
          }
          disabled={isLoading || batchLoding || !in_store_id}
          placeholder="请输入关键字后按Enter进行搜索"
          onChangeData={(newDataSourcee, optionType) => {
            console.log(newDataSourcee, optionType, 'newDataSourcee');
            if (optionType === 'onAdd' || optionType === 'onChangeSorts') {
              setRowData(newDataSourcee);
            }
            if (optionType === 'onDelete') {
              setEdit(true);
              if (newDataSourcee?.length != 0) {
                setRowData(newDataSourcee);
                return;
              }
              setRowData([{ _empty: true }]);
            }
          }}
          afterPopupSelect={(oldArr) => {
            handleSetRowData(oldArr, 'items');
            return oldArr;
          }}
          style={{ flex: 1 }}
          key={isFold?.toString() + tableKey}
          isLoading={isLoading}
          dataSource={rowData}
          columns={itemArrdetail?.map((v) => InvoiceRender(v))}
          footerDataSource={footerData}
          total={rowData?.length}
          selectMode="single"
          primaryKey="short_row_id"
        />
      )}
      {tabsKey == 'totalTab' && (
        <XlbTable
          key={isFold?.toString() + tableKey}
          isLoading={isLoading}
          style={{ flex: 1 }}
          showSearch
          pagin={pagin}
          list={rowData}
          selectMode="single"
          footerData={footerData}
          popoverPrimaryKey="item_name"
          repeatKey="item_name"
          dataSource={summaryData}
          columns={itemArr?.map((v) => InvoiceRender(v))}
          primaryKey="fid"
        />
      )}
    </div>
  );
};

export default DeliveryInOrderItem;
