.button_box {
  border-bottom: 1px solid @color_line2;
}

.contractTab {
  position: relative;

  :global .ant-tabs-top > .ant-tabs-nav {
    margin: 0 0 4px 0;
  }
}

.contractTabs {
  position: relative;

  :global .ant-tabs-top > .ant-tabs-nav {
    margin: 0;
  }
}

.amount_box {
  position: absolute;
  top: 11px;
  left: 240px;
  color: @color_danger;

  span {
    margin-left: 20px;
  }
}

.data_box {
  line-height: 32px;
  margin-left: 50px;

  span {
    margin-left: 20px;
  }
}

.table_box {
  :global .art-table-body {
    min-height: calc(100vh - 170px);
  }
}

.table_fold_box {
  :global .art-table-body {
    min-height: calc(100vh - 180px);
  }
}

.badge {
  z-index: 10;
}

.disbt {
  position: absolute;
  left: 80px;
  top: 0;
  width: 78px;
  height: 44px;
  cursor: pointer;
}

.form_container_wrapper {
  width: 100%;
  .form_container_transferDocument {
    margin: 5px 0 5px 0;
    width: 100%;
    max-width: 1700px;
    :global .ant-form-item-label {
      width: 120px !important;
      min-width: 120px !important;
      max-width: 120px !important;
    }
    :global {
      .xlb-ant-form-inline {
        display: unset !important;
      }
      .xlb-ant-form-item-label {
        flex-shrink: 0 !important;
      }
      .xlb-input-dialog {
        width: 100% !important;
      }
      .-item-explain-error {
        color: #f53f3f !important;
      }
    }
  }
}

@media (max-width: 991.98px) {
  .form_container_transferDocument {
    width: 845px; /* 小于 992px 时保持最小宽度，触发滚动条 */
    min-width: 845px;
    :global .ant-form-item-label {
      width: 120px !important;
      min-width: 120px !important;
      max-width: 120px !important;
    }
  }
}
