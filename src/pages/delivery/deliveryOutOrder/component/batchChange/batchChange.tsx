import { XlbProgress } from '@/components/common';
import SelectOrg from '@/components/common/xlbOrgids/selectOrg';
import { LStorage } from '@/utils/storage';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbInputNumber,
  XlbModal,
  XlbSelect,
  XlbTipsModal as XLBTipsModal,
} from '@xlb/components';
import { Form, message, Radio, Space } from 'antd';
import { useEffect, useState } from 'react';
import { types1 } from '../../data';
import style from './batchChange.less';

const BatchChange = (props: any) => {
  const { visible, handleCancel, getData, enableOrganization } = props;
  const [form] = Form.useForm();
  const [stateTips, setStateTips] = useState<{
    tips: string;
    isConfirm: boolean;
    isCancel: boolean;
    showDesc: string;
  }>({
    tips: '',
    isConfirm: true,
    isCancel: false,
    showDesc: '',
  });
  const [loading, setloading] = useState<boolean>(false);
  const [isFold, setIsFold] = useState<boolean>(false);
  const [isNull, setIsNull] = useState<boolean>();

  const dataProcessing = () => {
    if (!form.getFieldValue('revise_ids')) {
      XLBTipsModal({ tips: '请先选择门店' });
      return;
    }
    const radiovalue = form.getFieldValue('radioValue');
    if (!radiovalue) {
      XLBTipsModal({ tips: '请先选择商品' });
      return;
    } else {
      if (
        form.getFieldValue('radioValue') == 2 &&
        !form.getFieldValue('item_category_ids')
      ) {
        XLBTipsModal({ tips: '请先选择商品' });
        return;
      }
      if (
        form.getFieldValue('radioValue') == 3 &&
        !form.getFieldValue('item_ids')
      ) {
        XLBTipsModal({ tips: '请先选择商品' });
        return;
      }
    }

    const data = {
      ...form.getFieldsValue(),
      store_ids: form.getFieldValue('revise_ids'),
      org_id: form.getFieldValue('org_id'),
      item_ids:
        form.getFieldValue('radioValue') === 3 && form.getFieldValue('item_ids')
          ? form.getFieldValue('item_ids')
          : null,
      item_category_ids:
        form.getFieldValue('radioValue') === 2 &&
        form.getFieldValue('item_category_ids')
          ? form.getFieldValue('item_category_ids')
          : null,
    };

    return data;
  };

  const handleOk = async () => {
    const { revise_ids, reviseNameList } = form.getFieldsValue(true);
    const data = dataProcessing();
    if (!data) return;
    const items = revise_ids?.map((id: any) => {
      return {
        ...data,
        store_ids: [id],
      };
    });
    setloading(true);
    //
    NiceModal.show(XlbProgress, {
      requestApi: '/erp/hxl.erp.storedeliveryprice.batchupdate',
      items: items || [],
      titleList: reviseNameList,
      promptTitle: '正在操作：',
    }).then((res: any) => {
      if (res.code == 0) {
        message.success('更新成功');
        handleCancel();
        getData(1);
        form.getFieldValue('radioValue') == '';
        form.resetFields();
        setIsFold(false);
      }
    });
    setloading(false);
  };

  // 导入门店
  const importStores = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.ERP_URL}/erp/hxl.erp.storename.import`,
      templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.storecodetemplate.download`,
      templateName: '门店导入模板',
      params: {
        checkOrg: enableOrganization,
      },
      callback: (res: any) => {
        if (res.code !== 0) return;
        form.setFieldsValue({
          org_id: enableOrganization
            ? res?.data?.org_ids?.join(',')
            : undefined,
          org_name: enableOrganization
            ? res?.data?.org_names?.join(',')
            : undefined,
          revise_ids: res?.data?.store_ids ? res?.data?.store_ids : [],
          revise_names: res?.data?.store_names_str
            ? res?.data?.store_names_str
            : '',
          reviseNameList: res?.data?.store_names ? res?.data?.store_names : [],
        });
      },
    });
  };
  // 导入商品
  const importShort = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.ERP_URL}/erp/hxl.erp.items.batchimport`,
      templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.item.shorttemplate.download`,
      templateName: '商品导入模板',
      callback: (res: any) => {
        if (res.code !== 0) return;
        form.setFieldsValue({
          item_ids: res?.data?.items?.map((v: any) => v.id),
          item_names: res?.data?.items?.map((v: any) => v.name)?.join(','),
        });
      },
    });
  };
  // 只在点清空的时候触发
  const onChangeArea = async () => {
    form.setFieldsValue({
      org_name: '',
      org_id: null,
    });
  };
  const openOrgModal = async () => {
    const bool: any = await NiceModal.show(NiceModal.create(SelectOrg), {
      selectKeys: form.getFieldValue('org_name')
        ? form.getFieldValue('org_id')
        : '',
      org_names: form.getFieldValue('org_name'),
      isMultiple: false,
    });
    if (!bool) return;
    form.setFieldsValue({
      org_id: bool?.org_ids?.[0],
      org_name: bool?.org_names,
      revise_ids: [],
      revise_names: '',
    });
  };

  useEffect(() => {
    visible && setIsNull(false);
    form.setFieldsValue({
      revise_names:
        LStorage.get('userInfo')?.query_stores &&
        LStorage.get('userInfo')?.query_stores.length === 1
          ? LStorage.get('userInfo')?.query_stores[0]?.store_name
          : undefined,
      revise_ids:
        LStorage.get('userInfo')?.query_stores &&
        LStorage.get('userInfo')?.query_stores.length === 1
          ? [LStorage.get('userInfo')?.query_stores[0]?.id]
          : undefined,
    });
  }, [visible]);
  const Valuechange = (key: any, value: any) => {
    form.setFieldValue(key, value);
  };
  const Valuechanges = (key: any, value: any) => {
    if (key === 'type' && value === 'RATIO') setIsFold(true);
    else setIsFold(false);
    form.setFieldValue(key, value);
  };
  return (
    <XlbModal
      title={'批量修改'}
      centered
      open={visible}
      maskClosable={false}
      onOk={handleOk}
      className={style.dragModel}
      isCancel
      onCancel={() => {
        form.resetFields(), handleCancel();
        setIsFold(false);
      }}
      width={470}
      confirmLoading={loading}
    >
      <Form form={form}>
        <div className={style.box}>
          <p className={style.title}>修改门店</p>
          {enableOrganization ? (
            <Form.Item
              label="组织"
              name="org_name"
              style={{ display: 'inline-block', marginLeft: '60px' }}
              colon={false}
            >
              <XlbInput
                style={{ width: 188 }}
                size="small"
                allowClear
                suffix={
                  <span
                    style={{
                      display: 'inline-block',
                    }}
                    onClick={openOrgModal}
                  >
                    <XlbIcon name="sousuo" size={14} color="#c9cdd4" />
                  </span>
                }
                onFocus={(e) => e.target.blur()}
                onClick={openOrgModal}
                onChange={onChangeArea}
              />
            </Form.Item>
          ) : null}
          <Form.Item name="org_id" dependencies={['org_name']}>
            <XlbInput style={{ display: 'none' }}></XlbInput>
          </Form.Item>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Form.Item noStyle dependencies={['org_id']} shouldUpdate>
              {({ getFieldValue }) => {
                return (
                  <Form.Item
                    name="revise_ids"
                    style={{ display: 'inline-block', margin: '0 10px 0 32px' }}
                    label="修改门店"
                    colon={false}
                    dependencies={['org_id']}
                  >
                    <XlbInputDialog
                      style={{ width: 156 }}
                      dialogParams={{
                        type: 'store',
                        dataType: 'lists',
                        isMultiple: true,
                        data: {
                          status: true,
                          enable_organization: false,
                          org_ids: getFieldValue('org_id')
                            ? [getFieldValue('org_id')]
                            : [],
                        },
                        onOkBeforeFunction: (_val: any, data: any[]) => {
                          let flag = true;
                          data?.reduce((pre, cur) => {
                            if (
                              Object.keys(pre)?.length &&
                              pre?.org_id !== cur?.org_id
                            ) {
                              flag = false;
                            }
                            return cur;
                          }, {});
                          if (!flag) {
                            XLBTipsModal({
                              tips: '请选择同一组织下的门店',
                              zIndex: 2111,
                            });
                          } else {
                            form.setFieldsValue({
                              org_id: data.find((v: any) => v.org_id)?.org_id,
                              org_name: data.find((v: any) => v.org_id)
                                ?.org_name,
                              revise_ids: data.map((v: any) => v.id),
                              revise_names: data
                                .map((v: any) => v.store_name)
                                .join(','),
                              reviseNameList: data.map(
                                (v: any) => v?.store_name || v.name,
                              ),
                            });
                            const bol = data
                              .map((v: any) => v.store_number)
                              .includes(99);
                            setIsNull(bol);
                          }
                          return flag;
                        },
                      }}
                      fieldNames={{
                        idKey: 'id',
                        nameKey: 'store_name',
                      }}
                    ></XlbInputDialog>
                  </Form.Item>
                );
              }}
            </Form.Item>
            <XlbButton
              type="primary"
              size="small"
              onClick={() => importStores()}
            >
              导入
            </XlbButton>
          </div>
        </div>
        <div className={style.box} style={{ marginBottom: 0 }}>
          <p className={style.title}>选择商品</p>
          <Form.Item name="radioValue">
            <Radio.Group>
              <Space direction="vertical">
                <Radio value={1}>全部商品</Radio>
                <Radio value={2}>
                  商品类别
                  <Form.Item name="item_category_ids">
                    {/* <Input
                      readOnly
                      suffix={<SearchOutlined style={{ color: 'rgb(102, 102, 102)' }} />}
                      onClick={() => handleDialogClick(2)}
                      size="small"
                    /> */}
                    <XlbInputDialog
                      style={{ width: 188 }}
                      treeModalConfig={{
                        title: '选择商品分类', // 标题
                        url: '/erp/hxl.erp.category.find', // 请求地址
                        dataType: 'lists',
                        checkable: true, // 是否多选
                        primaryKey: 'id',
                        data: {
                          enabled: true,
                        },
                        width: 360,
                      }}
                    ></XlbInputDialog>
                  </Form.Item>
                </Radio>
                <Radio value={3}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    商品档案
                    <Form.Item name="item_ids" style={{ marginRight: '10px' }}>
                      {/* <Input
                        readOnly
                        suffix={<SearchOutlined style={{ color: 'rgb(102, 102, 102)' }} />}
                        onClick={() => handleDialogClick(3)}
                        size="small"
                      /> */}
                      <XlbInputDialog
                        style={{ width: 156 }}
                        dialogParams={{
                          type: 'goods',
                          dataType: 'lists',
                          isMultiple: true,
                          data: {
                            status: 1,
                          },
                          // isLeftColumn: false,
                        }}
                      ></XlbInputDialog>
                    </Form.Item>
                    <XlbButton
                      type="primary"
                      size="small"
                      onClick={() => importShort()}
                    >
                      导入
                    </XlbButton>
                  </div>
                </Radio>
              </Space>
            </Radio.Group>
          </Form.Item>
        </div>
        <div className={style.box}>
          <p className={style.title}>修改数据</p>
          <Form.Item
            name={'unit_type'}
            label="单位"
            style={{ marginLeft: 62 }}
            colon={false}
            initialValue={'DELIVERY'}
          >
            <XlbSelect
              style={{ width: 188, paddingTop: 2, paddingBottom: 2 }}
              size="small"
              onChange={(value) => Valuechange('unit_type', value)}
            >
              <XlbSelect.Option value={'DELIVERY'}>配送单位</XlbSelect.Option>
              <XlbSelect.Option value={'BASIC'}>基本单位</XlbSelect.Option>
            </XlbSelect>
          </Form.Item>
          <Form.Item
            name={'type'}
            label="配送价类型"
            colon={false}
            style={{ display: 'inline-block', margin: '0 0 0 20px' }}
            initialValue="NULL"
          >
            <XlbSelect
              style={{ width: 120, paddingTop: 2, paddingBottom: 2 }}
              size="small"
              onChange={(value) => Valuechanges('type', value)}
            >
              {/* <Option value={'NULL'}> </Option>
                <Option value={'RATIO'}>按比例</Option>
                <Option value={'MONEY'}>按金额</Option>
                <Option value={'FIXED_MONEY'}>固定金额</Option> */}
              {!isNull
                ? types1.map((v: any, ind: number) => {
                    return (
                      <XlbSelect.Option key={ind} value={v.value}>
                        {v.label}
                      </XlbSelect.Option>
                    );
                  })
                : types1.slice(1).map((v: any, ind: number) => {
                    return (
                      <XlbSelect.Option key={ind} value={v.value}>
                        {v.label}
                      </XlbSelect.Option>
                    );
                  })}
            </XlbSelect>
          </Form.Item>
          <Form.Item
            name={'value'}
            style={{ display: 'inline-block', marginRight: 0 }}
            initialValue={0}
          >
            <XlbInputNumber
              defaultValue={0}
              controls={false}
              min={0}
              size="small"
              step={0.0001}
              // stringMode
              style={{
                width: 60,
                paddingBottom: 2,
                paddingTop: 1,
                marginTop: 5,
              }}
              onChange={(value) => Valuechange('value', value)}
            />
            {isFold && <span style={{ marginLeft: 10 }}>%</span>}
          </Form.Item>
        </div>
      </Form>
    </XlbModal>
  );
};
export default BatchChange;
