import { useState } from 'react'
import { Radio, Space, Select, Form, message } from 'antd'
import style from './copy.less'
import { copyStoreDeliveryPrice } from '../../server'
import { XlbInputDialog, XlbModal, XlbTipsModal } from '@xlb/components'

const Copy = (props: any) => {
  const { visible, handleCancel, getData } = props
  const [form] = Form.useForm()
  const [loading, setloading] = useState<boolean>(false)
  const handleOk = async () => {
    if (!form.getFieldValue('revise_ids')) {
      XlbTipsModal({ tips: '请先选择修改门店' })
      return
    } else {
      if (!form.getFieldValue('reference_ids')) {
        XlbTipsModal({ tips: '请先选择参照门店' })
        return
      }
    }
    const radiovalue = form.getFieldValue('radioValue')
    if (!radiovalue) {
      XlbTipsModal({ tips: '请先选择商品' })
      return
    } else {
      if (
        form.getFieldValue('radioValue') == 2 &&
        (form.getFieldValue('item_category_ids') == undefined ||
          form.getFieldValue('item_category_ids') == '')
      ) {
        XlbTipsModal({ tips: '请选择商品类别' })
        return
      }
      if (
        form.getFieldValue('radioValue') == 3 &&
        (form.getFieldValue('item_ids') == undefined || form.getFieldValue('item_ids') == '')
      ) {
        XlbTipsModal({ tips: '请选择商品档案' })
        return
      }
    }
    const data = {
      company_id: 1000,
      operator: JSON.parse(localStorage.userInfo).value.account,
      target_store_ids: form.getFieldValue('revise_ids'),
      source_store_id: form.getFieldValue('reference_ids')?.[0],
      item_category_ids: form.getFieldValue('item_category_ids')
        ? form.getFieldValue('item_category_ids')
        : null,
      item_ids: form.getFieldValue('item_ids') ? form.getFieldValue('item_ids') : null
    }
    setloading(true)
    const res = await copyStoreDeliveryPrice(data)
    setloading(false)
    if (res.code === 0) {
      message.success('更新成功')
      handleCancel()
      getData(1)
      form.getFieldValue('radioValue') == ''
      form.resetFields()
    }
  }
  return (
    <XlbModal
      title={'复制'}
      style={{ top: -50 }}
      centered
      visible={visible}
      maskClosable={false}
      onOk={handleOk}
      onCancel={() => {
        form.resetFields(), handleCancel()
      }}
      width={430}
      confirmLoading={loading}
    >
      <Form form={form}>
        <div className={style.box}>
          <p className={style.title}>复制门店</p>
          <Form.Item
            name="revise_ids"
            style={{ display: 'inline-block', marginLeft: '32px' }}
            label="修改门店"
            colon={false}
          >
            <XlbInputDialog
              style={{ width: 156 }}
              dialogParams={{
                type: 'store',
                dataType: 'lists',
                isMultiple: true,
                data: {
                  status: true
                }
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: 'store_name'
              }}
            ></XlbInputDialog>
          </Form.Item>
          <Form.Item
            name="reference_ids"
            style={{ display: 'inline-block', marginLeft: '32px' }}
            label="参照门店"
            colon={false}
          >
            <XlbInputDialog
              style={{ width: 156 }}
              dialogParams={{
                type: 'store',
                dataType: 'lists',
                isMultiple: false,
                data: {
                  status: true
                }
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: 'store_name'
              }}
            ></XlbInputDialog>
          </Form.Item>
        </div>
        <div className={style.box} style={{ marginBottom: 0 }}>
          <p className={style.title}>选择商品</p>
          <Form.Item name="radioValue">
            <Radio.Group>
              <Space direction="vertical">
                <Radio value={1}>全部商品</Radio>
                <Radio value={2}>
                  商品类别
                  <Form.Item name="item_category_ids">
                    <XlbInputDialog
                      style={{ width: 188 }}
                      treeModalConfig={{
                        title: '选择商品分类', // 标题
                        url: '/erp/hxl.erp.category.find', // 请求地址
                        dataType: 'lists',
                        checkable: true, // 是否多选
                        primaryKey: 'id',
                        data: {
                          enabled: true
                        },
                        width: 360
                      }}
                    ></XlbInputDialog>
                  </Form.Item>
                </Radio>
                <div className="v-flex">
                  <Radio value={3}>
                    商品档案
                    <Form.Item name="item_ids" colon={false}>
                      <XlbInputDialog
                        style={{ width: 156 }}
                        dialogParams={{
                          type: 'goods',
                          dataType: 'lists',
                          isMultiple: true,
                          data: {
                            status: 1
                          }
                          // isLeftColumn: false,
                        }}
                      ></XlbInputDialog>
                    </Form.Item>
                  </Radio>
                </div>
              </Space>
            </Radio.Group>
          </Form.Item>
        </div>
      </Form>
    </XlbModal>
  )
}
export default Copy
