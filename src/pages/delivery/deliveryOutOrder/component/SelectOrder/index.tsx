import { useModal } from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbModal,
  XlbPageContainer,
  XlbTooltip,
} from '@xlb/components';
import PromiseModal from '@/components/promiseModal/PromiseModal';
import NiceModal from '@ebay/nice-modal-react';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { SelectOrderformList, receiveTable, stateList } from '../../data';
const { ToolBtn, SearchForm, Table } = XlbPageContainer;
const Index = (props: {
  add: (arg0: any[], arg1: string | undefined) => void;
}) => {
  const modal = useModal();
  const [formList] = useState(SelectOrderformList);
  const [form] = XlbBasicForm.useForm();
  const pageRef = useRef<any>(null);
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'fid':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div
              className="link overwidth"
              onClick={(e) => {
                e.stopPropagation();
                NiceModal.show(PromiseModal, {
                  order_type: '收货单',
                  order_fid: value,
                });
              }}
            >
              {value}
            </div>
          );
        };
        break;
      case 'state':
        item.render = (value: any) => {
          return (
            <div
              className={
                'overwidth ' + stateList.find((v) => v.value === value)?.type
              }
            >
              {stateList.find((v) => v.value === value)?.label}
            </div>
          );
        };
        break;
    }
    return item;
  };
  return (
    <>
      <XlbModal
        open={modal.visible}
        isConfirm={false}
        title="单据选择"
        // @ts-ignore
        width={'90%'}
        onCancel={() => {
          modal.resolve(false);
          modal.hide();
        }}
        footerExtroContent={[
          <XlbButton
            key={0}
            type="primary"
            onClick={() => {
              modal.resolve(false);
              modal.hide();
            }}
          >
            取消
          </XlbButton>,
          <XlbButton
            type="primary"
            onClick={() => {
              console.log(pageRef?.current);
              if (pageRef?.current?.selectRow?.length === 0) {
                XlbMessage.warning('请选择订单');
                return;
              }
              modal.resolve(false);
              modal.hide();
              props.add(pageRef?.current?.selectRowKeys, 'no_count');
            }}
            key={1}
          >
            添加
          </XlbButton>,
          <XlbButton
            type="primary"
            onClick={() => {
              if (pageRef?.current?.selectRow?.length === 0) {
                XlbMessage.warning('请选择订单');
                return;
              }
              modal.resolve(false);
              modal.hide();
              props.add(pageRef?.current?.selectRowKeys, 'count');
            }}
            key={1}
          >
            带数量添加
          </XlbButton>,
        ]}
      >
        <div style={{ height: 'calc(100vh - 500px)', marginBottom: 10 }}>
          <XlbPageContainer
            immediatePost={true}
            tableColumn={receiveTable?.map((v) => tableRender(v))}
            prevPost={(pageInfo, params) => {
              const formData = form.getFieldsValue();
              return {
                ...formData,
                create_date: [
                  dayjs(formData.create_date[0]).format('YYYY-MM-DD 00:00:00'),
                  dayjs(formData.create_date[1]).format('YYYY-MM-DD 23:59:59'),
                ],
              };
            }}
            url="/erp/hxl.erp.receiveorder.page?outorder=true"
          >
            <ToolBtn showColumnsSetting={false}>
              {(current) => {
                pageRef.current = current;
                return (
                  <XlbButton
                    onClick={() => {
                      current?.fetchData();
                    }}
                    type="primary"
                    icon={<XlbIcon name="sousuo" />}
                  >
                    查询
                  </XlbButton>
                );
              }}
            </ToolBtn>
            <SearchForm>
              <XlbForm
                initialValues={{
                  create_date: [
                    dayjs().format('YYYY-MM-DD'),
                    dayjs().format('YYYY-MM-DD'),
                  ],
                  order_type: 'receipt_order',
                }}
                isHideDate={true}
                form={form}
                formList={formList}
              ></XlbForm>
            </SearchForm>
            <Table selectMode="multiple" primaryKey="fid"></Table>
          </XlbPageContainer>
        </div>
      </XlbModal>
    </>
  );
};
export default Index;
