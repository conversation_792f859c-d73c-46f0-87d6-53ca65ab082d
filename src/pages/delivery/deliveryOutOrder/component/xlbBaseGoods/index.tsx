import { columnWidthEnum } from '@/data/common/constant';
import { getStockDetail } from '@/pages/delivery/deliveryOutOrder/item/server';
import { hasAuth } from '@/utils/kit';
import { useModal } from '@ebay/nice-modal-react';
import {
  XlbModal,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { useEffect, useState } from 'react';
const XlbBaseGoods = (props: any) => {
  const { title, BatchList, handConfirm, params, } = props;
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<any[]>([]);
  const modal = useModal();
  const [chooseList, setChooseList] = useState<any[]>([]);
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  //换页事件 basic_cost_price  money cost_price
  const pageChange = (p: number) => {
    setPagin({
      ...pagin,
      pageNum: p,
    });
    getData(p).then();
  };
  const dateGoodsColumns: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
    },
    {
      name: '批次号',
      code: 'batch_number',
      width: 110,
      features: { sortable: true },
    },
    {
      name: '生产日期',
      code: 'producing_date',
      width: columnWidthEnum.DATE,
      features: { sortable: true },
    },
    {
      name: '到期日期',
      code: 'expire_date',
      width: columnWidthEnum.DATE,
      features: { sortable: true },
    },
    {
      name: '单位',
      code: 'unit',
      width: 80,
      features: { sortable: true },
    },
    {
      name: '数量',
      code: 'quantity',
      width: 80,
      features: { sortable: true },
    },
    {
      name: '库存单价',
      code: 'cost_price',
      width: 100,
      features: { sortable: true },
    },
    {
      name: '库存金额',
      code: 'money',
      width: 100,
      features: { sortable: true },
    },
    {
      name: '基本数量',
      code: 'basic_quantity',
      width: 100,
      features: { sortable: true },
    },
    {
      name: '基本单价',
      code: 'basic_cost_price',
      width: 110,
      features: { sortable: true },
    },
    {
      name: '供应商',
      code: 'latest_supplier_name',
      width: 110,
      features: { sortable: true },
    },
  ];
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'cost_price':
      case 'money':
      case 'basic_cost_price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {hasAuth(['库存查询/价格', '查询']) &&
              hasAuth(['调出单/配送价', '查询'])
                ? value
                : '****'}
            </div>
          );
        };
        break;
    }
    return item;
  };
  const getData = async (page_number: number) => {
    setRowData([]);
    setisLoading(true);
    const res = await getStockDetail({
      ...params,
      orders:
        params.key !== 'batch_number'
          ? [{ direction: 'ASC', property: params.key }]
          : [],
      show_batch_unit: params.key == 'batch_number' && true,
      page_number: 0,
      page_size: 200000,
    });
    setisLoading(false);
    if (res.code === 0) {      
      let choose: any[] = [];
      res?.data?.content?.map((v: any, index: any) => {
        v.quantity = v?.quantity?.toFixed(3);
        v.cost_price = v?.cost_price?.toFixed(4);
        v.money = v?.money?.toFixed(2);
        v.basic_quantity = v?.basic_quantity?.toFixed(3);
        v.basic_cost_price = v?.basic_cost_price?.toFixed(4);
        if (
          BatchList.includes(
            String(
              v.item_id + v.batch_number + v.producing_date + v.expire_date,
            ),
          )
        ) {
          choose.push(index.toString());
        }
      });
      setChooseList([...choose]);
      setRowData(res.data.content || []);
      setPagin({
        ...pagin,
        pageNum: page_number,
        total: res.data.total_elements,
      });
    }
  };
  //弹窗确定回调
  const handOk = () => {
    const goodsList = rowData.filter((item: any, index) => {
      if (chooseList.includes(index.toString()) == true) {
        return item;
      }
    });
    modal.hide();
    modal.resolve(false);
    handConfirm(goodsList, chooseList);
  };

  useEffect(() => {
    getData(1);
  }, []);

  return (
    <XlbModal
      title={title}
      open={modal.visible}
      width={1150}
      onCancel={() => {
        modal.resolve(false);
        modal.hide();
      }}
      onOk={handOk}
      centered
      isCancel
      bodyStyle={{ padding: 10 }}
      confirmLoading={isLoading}
    >
      <XlbTable
        columns={dateGoodsColumns.map((v) => tableRender(v))}
        dataSource={rowData}
        pagination={false}
        style={{ height: 'calc(100vh - 500px)' }}
        rowKey="_index"
        total={pagin.total}
        selectMode="multiple"
        selectRowKeys={chooseList}
        onSelectRow={(selectedRowKeys, selectedRows) => {
          setChooseList(selectedRowKeys);
          console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
        }}
        pageNum={pagin.pageNum}
        pageSize={pagin.pageSize}
        isLoading={isLoading}
      />
    </XlbModal>
  );
};
export default XlbBaseGoods;
