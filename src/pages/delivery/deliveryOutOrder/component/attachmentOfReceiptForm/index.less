.printTable{
  margin-top: 10px;
}
.formInformation {
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
}
.page-break{
  break-before: page;
}
.titleInformation{
  width: 25%;
  font-size: 14px;
}
.tabularInformation{
  width:100%;
  // height:60px;
  margin-top:20px;
  table {
    table-layout: fixed;
    width: 100%;
    border: 1px solid #000;
    font-size: 16px;
  }
  table td, table th {
    // width: 10%;
    border: 1px solid #000;
    text-align: center;
  }
  table td {
    height: 40px;
  }
  .print-image{
    border: none;
    text-align: center;
  }
}
