import { columnWidthEnum } from '@/data/common/constant';
// import {XlbTableColumnProps} from '@xlb/components'
import { LStorage } from '@/utils/storage';
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

//单据状态
export const stateList = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
];
// 结算状态
export const settlementStateList = [
  {
    label: '未结算',
    value: 'UNPAY',
    type: 'danger',
  },
  {
    label: '部分结算',
    value: 'PARTPAY',
    type: 'info',
  },
  {
    label: '已结算',
    value: 'ALLPAY',
    type: 'success',
  },
];

// 时间类型
export const timeTypeList = [
  {
    label: '制单时间',
    value: 'create_date',
  },
  {
    label: '审核时间',
    value: 'audit_date',
  },
  {
    label: '调出时间',
    value: 'operate_date',
  },
  {
    label: '签收日期',
    value: 'receive_time',
  },
];

//调入状态
export const deliveryInStatesList = [
  {
    label: '未调入',
    value: 'UNIN',
    type: 'danger',
  },
  {
    label: '部分调入',
    value: 'PARTIN',
    type: 'info',
  },
  {
    label: '全部调入',
    value: 'ALLIN',
    type: 'success',
  },
];
export const invoiceStateList = [
  {
    label: '开票成功',
    value: 'FINISH',
    type: 'success',
  },
  {
    label: '开票中',
    value: 'DOING',
    type: 'warning',
  },
  {
    label: '未开票',
    value: 'INIT',
    type: 'info',
  },
];
export const checkOptions = [
  {
    label: '停购商品',
    value: 'stop_purchase',
  },
  {
    label: '停售商品',
    value: 'stop_sale',
  },
  {
    label: '停止要货商品',
    value: 'stop_request',
  },
];

//经营类型
export const goodsType: any[] = [
  {
    label: '主规格商品',
    value: 'MAINSPEC',
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC',
  },
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
  {
    label: '分级商品',
    value: 'GRADING',
  },
];
//查询单位query_unit
export const queryUnit = [
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
  {
    label: '基本单位',
    value: 'BASIC',
  },
];

export const types: any[] = [
  {
    label: '置空',
    value: 'NULL',
  },
  {
    label: '按比例',
    value: 'RATIO',
  },
  {
    label: '按金额',
    value: 'MONEY',
  },
  {
    label: '固定金额',
    value: 'FIXED_MONEY',
  },
];
export const types1: any[] = [
  {
    label: ' ',
    value: 'NULL',
  },
  {
    label: '按比例',
    value: 'RATIO',
  },
  {
    label: '按金额',
    value: 'MONEY',
  },
  {
    label: '固定金额',
    value: 'FIXED_MONEY',
  },
];
export const orderTypeList = [
  {
    label: '仓店配送',
    value: 'WAREHOUSE_TO_STORE',
  },
  {
    label: '店仓反配',
    value: 'STORE_TO_WAREHOUSE',
  },
  {
    label: '店间调拨',
    value: 'STORE_TO_STORE',
  },
  {
    label: '仓间调拨',
    value: 'WAREHOUSE_TO_WAREHOUSE',
  },
];
export const formList: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '日期范围',
    name: 'compact_date',
    allowClear: false,
    showTime: true,
    resultFormat: 'YYYY-MM-DD HH:mm',
    format: 'YYYY-MM-DD HH:mm',
  },
  {
    label: '时间类型',
    name: 'time_type',
    type: 'select',
    allowClear: false,
    options: timeTypeList,
  },
  {
    label: '单据类型',
    name: 'order_types',
    type: 'select',
    clear: true,
    multiple: true,
    check: true,
    options: orderTypeList,
  },
  {
    label: '单据状态',
    name: 'state',
    type: 'select',
    options: stateList,
  },
  {
    label: '结算状态',
    name: 'settlement_state',
    type: 'select',
    options: settlementStateList,
  },
  {
    label: '单据号',
    name: 'fid',
    type: 'input',
    tooltip: '单据号不受其他查询条件限制',
  },

  {
    type: 'inputDialog',
    label: '调出组织',
    name: 'out_org_ids',
    treeModalConfig: {
      // @ts-ignore
      title: '选择组织', // 标题
      url: '/erp-mdm/hxl.erp.org.tree', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      fieldName: {
        id: 'id',
        parent_id: 'parent_id',
      },
      width: 360, // 模态框宽度
    },
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'name',
    },
  },

  {
    label: '调出门店',
    name: 'store_ids',
    type: 'inputDialog',
    rules: [{ required: true, message: '门店不能为空' }],
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isMultiple: true,
      data: {
        status: true,
        storeStatus: true,
        centerFlag: true,
      },
    },
  },

  {
    label: '调出仓库',
    name: 'storehouse_id',
    type: 'select',
    dependencies: ['store_ids'],
    dropdownStyle: { width: 200 },
    // @ts-ignore
    disabled: (form: any) => {
      const store_ids = form.getFieldValue('store_ids');
      return !store_ids || store_ids?.length > 1;
    },
    // @ts-ignore
    selectRequestParams: (params: any, form: any) => {
      form?.setFieldValue('storehouse_id', null);
      if (params?.store_ids?.length == 1) {
        return {
          url: '/erp/hxl.erp.storehouse.store.find',
          postParams: {
            store_id: params?.store_ids?.[0],
          },
          responseTrans(data) {
            const options = data.map((item: any) => ({
              label: item.name,
              value: item.id,
              default_flag: item.default_flag,
            }));
            return options;
          },
        };
      }
    },
  },
  {
    type: 'inputDialog',
    label: '调入组织',
    name: 'org_ids',
    treeModalConfig: {
      // @ts-ignore
      title: '选择组织', // 标题
      url: '/erp-mdm/hxl.erp.org.tree', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      params: { operator_store_id: LStorage.get('userInfo').store_id || null },
      fieldName: {
        id: 'id',
        parent_id: 'parent_id',
      },
      width: 360, // 模态框宽度
    },
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'name',
    },
  },
  {
    label: '调入门店',
    name: 'in_store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isMultiple: true,
      url: '/erp-mdm/hxl.erp.store.all.shortfind',
      data: {
        status: true,
        skip_filter: true,
        storeStatus: true,
        centerFlag: true,
      },
    },
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
      data: {
        status: true,
      },
    },
  },
  {
    label: '调入状态',
    type: 'select',
    name: 'delivery_in_states',
    options: deliveryInStatesList,
  },
  {
    label: '上传乐檬',
    name: 'upload_ama',
    type: 'select',
    // @ts-ignore
    options: [
      {
        label: '是',
        // @ts-ignore
        value: true,
      },
      {
        label: '否',
        // @ts-ignore
        value: false,
      },
    ],
  },
  {
    label: '制单人',
    name: 'create_by',
    type: 'input',
  },
  {
    label: '审核人',
    name: 'audit_by',
    type: 'input',
  },
  {
    label: '开票状态',
    name: 'invoice_state',
    type: 'select',
    options: invoiceStateList,
  },
  {
    label: '调入门店标签',
    name: 'in_store_label_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'storeLabels',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      data: {
        apply_delivery_out: true,
      },
    },
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_label_name',
    },
  },
  {
    label: '其他条件',
    name: 'both_reversed',
    type: 'inputPanel',
    allowClear: true,
    placeholder: '冲红和被冲红单据',
    items: [
      {
        label: '仅显示',
        // @ts-ignore
        key: true,
      },
      {
        label: '不显示',
        // @ts-ignore
        key: false,
        value: false,
      },
    ],
    // @ts-ignore
    options: [{ label: '冲红和被冲红单据', value: true }],
  },
];

export const historyArr: any[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
  },
  {
    name: '修改时间',
    code: 'create_time',
    width: columnWidthEnum.TIME,
    align: 'left',
  },
  {
    name: '修改人',
    code: 'create_by',
    width: columnWidthEnum.BY,
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 100,
    align: 'left',
  },
  {
    name: '操作明细',
    code: 'memo',
    width: 324,
    align: 'left',
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
  },
  {
    name: '单据号',
    code: 'fid',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '调出日期',
    code: 'operate_date',
    width: 110,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '付款日期',
    code: 'payment_date',
    width: 110,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '签收日期',
    code: 'receive_time',
    width: columnWidthEnum.DATE,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '调出门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '调出营业执照名称',
    code: 'license_name',
    width: 200,
    features: { sortable: false },
    align: 'left',
    hidden: true,
  },
  {
    name: '调出执照类型',
    code: 'license_type',
    width: 110,
    features: { sortable: false },
    align: 'left',
    hidden: true,
  },
  {
    name: '调出仓库',
    code: 'storehouse_name',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '调入门店',
    code: 'in_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '门店标签',
    code: 'in_store_label_name',
    width: 120,
    features: { sortable: false },
    hidden: true,
    align: 'left',
  },
  {
    name: '调入营业执照名称',
    code: 'in_license_name',
    width: 200,
    features: { sortable: false },
    align: 'left',
    hidden: true,
  },
  {
    name: '调入执照类型',
    code: 'in_license_type',
    width: 110,
    features: { sortable: false },
    align: 'left',
    hidden: true,
  },
  {
    name: '单据类型',
    code: 'order_type',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单据金额',
    code: 'money',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单据金额(去税)',
    code: 'no_tax_money',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '成本',
    code: 'cost_money',
    width: 134,
    align: 'right',
    hidden: true,
  },
  {
    name: '成本(去税)',
    code: 'no_tax_cost_money',
    width: 134,
    align: 'right',
    hidden: true,
  },
  {
    name: '商品采购金额',
    code: 'purchase_money',
    width: 134,
    align: 'right',
    hidden: true,
  },
  {
    name: '商品采购金额(去税)',
    code: 'no_tax_purchase_money',
    width: 164,
    align: 'right',
    hidden: true,
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '商品数',
    code: 'item_count',
    width: 90,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '税费',
    code: 'tax_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单据状态',
    code: 'state',
    width: columnWidthEnum.ORDER_STATE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '调入状态',
    code: 'in_state',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '最后调入时间',
    code: 'last_in_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '结算状态',
    code: 'settlement_state',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '制单有效期',
    code: 'valid_date',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '制单人',
    code: 'create_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '留言备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '打印次数',
    code: 'print_count',
    width: 90,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '开票状态',
    code: 'invoice_state',
    width: 100,
    align: 'left',
  },
  {
    name: '关联结算单号',
    code: 'related_settlement_fid',
    width: 120,
    align: 'left',
  },
  {
    name: '关联开票单据号',
    code: 'related_invoice_fid',
    width: 160,
    align: 'left',
  },
  {
    name: '关联单号',
    code: 'request_order_fids_str',
    width: 180,
    features: { sortable: false },
    align: 'left',
  },
  {
    name: '乐檬关联单号',
    code: 'ama_order_fid',
    width: 180,
    features: { sortable: true },
    align: 'center',
  },
  {
    name: 'WMS单号',
    code: 'wms_fid',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: 'TMS单号',
    code: 'tms_fid',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
];
export const itemTableList: any[] = [
  {
    title: '商品名称/商品代码',
    code: 'name',
    width: 420,
    align: 'center',
    children: [
      {
        title: '序号',
        name: '序号',
        code: 'index',
        width: 80,
        align: 'center',
      },
      { title: '操作', code: 'action', width: 50, align: 'center' },
      { title: '门店分组', code: 'store_type', width: 80, align: 'left' },
      { title: '调入门店名称', code: 'store_name', width: 200, align: 'left' },
    ],
  },
  {
    title: '',
    code: '',
    width: 100,
    align: 'center',
  },
];

export const SelectOrderformList: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '日期范围',
    name: 'create_date',
    showTime: true,
    resultFormat: 'YYYY-MM-DD',
    format: 'YYYY-MM-DD',
  },
  {
    label: '单据类型',
    name: 'order_type',
    type: 'select',
    allowClear: false,
    options: [
      {
        label: '采购收货单',
        value: 'receipt_order',
      },
    ],
  },
  {
    label: '单据状态',
    name: 'state',
    type: 'select',
    options: stateList,
  },

  {
    label: '单号',
    name: 'fid',
    type: 'input',
  },

  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isMultiple: true,
      data: {
        status: true,
      },
    },
  },

  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isMultiple: true,
      data: {
        status: true,
      },
    },
  },
  {
    label: '备注',
    name: 'memo',
    id: 'commonInput',
  },
];

//采购收货单
export const receiveTable: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
  },
  {
    name: '单据号',
    code: 'fid',
    width: columnWidthEnum.fid,
    features: { sortable: true },
    align: 'left',
  },

  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '供应商名称',
    code: 'supplier_name',
    width: 250,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '状态',
    code: 'state',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },

  {
    name: '制单人',
    code: 'create_by',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '备注',
    code: 'memo',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
];
