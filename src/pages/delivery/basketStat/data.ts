import { XlbTableColumnProps } from '@xlb/components';
import { LStorage } from '@xlb/utils';

export const formList: any = [
  {
    width: 392,
    type: 'compactDatePicker',
    label: '日期选择',
    hidden: false,
    name: 'compactDatePicker',
    allowClear: false,
  },
  {
    label: '载具名称',
    name: 'basket_ids',
    type: 'inputDialog',
    allowClear: true,
    dialogParams: {
      type: 'basket',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      primaryKey: 'id',
    },
  },
  {
    label: '调出门店',
    name: 'out_store_ids',
    type: 'inputDialog',
    allowClear: LStorage.get('userInfo').store.enable_delivery_center
      ? false
      : true,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        status: true,
        center_flag: true,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '调入门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: true,
    hidden: false,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      url: '/erp/hxl.erp.store.short.page',
      isMultiple: true,
      primaryKey: 'id',
      data: {
        status: true,
        center_flag: false,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '调出组织',
    name: 'out_org_ids',
    type: 'select',
    allowClear: true,
    multiple: true,
    selectRequestParams: (params: any, form: any) => {
        return {
          url: '/erp/hxl.erp.org.find',
          responseTrans(data: any) {
            return data?.map((item: any) => ({
                label: item.name,
                value: item.id,
              }));
          },
        };
    },
  },
  {
    label: '过滤:',
    name: 'checkValue',
    colon: false,
    type: 'checkbox',
    hidden: false,
    options: [
      {
        label: '仅查询欠筐数量>0的数据',
        value: 'query_num_greater_than_zero',
      },
    ],
  },
];

export const modal2FormList: any[] = [
  {
    label: '载具名称',
    value: 'basket_names',
    type: 'dialog',
    clear: true,
    check: true,
  },
  {
    label: '操作门店',
    value: 'store_names',
    type: 'dialog',
    clear: true,
    check: true,
  },
];

export const modal2Arr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '操作门店',
    code: 'store_name',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '操作人',
    code: 'create_by',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '操作时间',
    code: 'create_time',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '载具名称',
    code: 'basket_name',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '出入库数量',
    code: 'quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '备注',
    code: 'memo',
    width: 260,
    features: { sortable: true },
  },
];

export const SummaryArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    width: 140,
    hiddenInXlbColumns: true,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'store_name',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '载具名称',
    code: 'basket_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '发货数量',
    code: 'quantity',
    align: 'right',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货数量',
    code: 'out_quantity',
    align: 'right',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '欠筐数量',
    code: 'owe_quantity',
    align: 'right',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '欠筐金额',
    code: 'money',
    align: 'right',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '',
  },
];

export const DetailArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: 196,
    features: { sortable: true },
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    width: 140,
    hiddenInXlbColumns: true,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'store_name',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '载具名称',
    code: 'basket_name',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '进出方向',
    code: 'direction',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '数量',
    code: 'quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '单价',
    code: 'price',
    width: 90,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额',
    code: 'money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
];

export const BasketArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    width: 140,
    hiddenInXlbColumns: true,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '载具名称',
    code: 'basket_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '发货数量',
    code: 'out_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '发货金额',
    code: 'money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '库存数量',
    code: 'stock_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '库存金额',
    code: 'stock_money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
];