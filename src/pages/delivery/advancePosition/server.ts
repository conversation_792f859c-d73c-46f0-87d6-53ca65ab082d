import {XlbFetch as ErpRequest } from '@xlb/utils'

export default class Api {
  // 门店列表
  static getStores = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.store.center.find', data)
  }
  // 商品分页查询
  static getGoodsStores = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.frontwarhouserelation.item.page', data)
  }
  // 保存
  static save = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.frontwarhouserelation.save', data)
  }
  // 读取
  static read = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.frontwarhouserelation.read', data)
  }
  // 更新
  static update = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.frontwarhouserelation.update', data)
  }
}
