import { LStorage } from '@/utils/storage';
import {
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbCheckbox,
  XlbForm,
  XlbPageContainer,
  XlbTableColumnProps,
} from '@xlb/components';
import Api from './../server';

import { XlbFetch as ErpRequest } from '@xlb/utils';
import { useEffect, useState } from 'react';
import DetailDialog from './../components/DetailDialog';
import { goodsType } from './../data';

const Index = () => {
  // const userInfo = LStorage.get('userInfo');
  const [form] = XlbBasicForm.useForm();
  const { Table, SearchForm, ToolBtn } = XlbPageContainer;
  const [visible, setVisible] = useState(false);
  const [queryParams, setQueryParams] = useState({});
  const handleDetail = (record: { detail_category_ids: any }, type = '') => {
    const data = {
      ...form.getFieldsValue(true),
      [type]: true,
    };
    data.store_id = data.store_ids[0];
    data.item_category_ids = record.detail_category_ids;

    setQueryParams({ ...data });
    setVisible(true);
  };
  const formList: SearchFormType[] = [
    {
      label: '组织',
      name: 'org_ids',
      type: 'select',
      clear: true,
      check: true,
      linkId: 'org_options',
      onChange: () => {
        form.setFieldValue('store_ids', undefined);
      },
      options: [],
    },
    {
      label: '门店',
      name: 'store_ids',
      type: 'inputDialog',
      initialValue: [LStorage.get('userInfo').store_id],
      immediatePost: true,
      onSelect: (e, d) => {
        const { store_id, company_id } = LStorage.get('userInfo');
        if (d && d.id) {
          Api.fetchStoreHouse({
            operator_store_id: store_id,
            company_id: company_id,
            store_id: d.id,
          }).then((res) => {
            if (res?.code === 0) {
              const data = res.data?.map((item: { id: any; name: any }) => {
                return {
                  value: item.id,
                  label: item.name,
                };
              });
              form.setFieldValue('storehouse_ids_options', data);
              form.setFieldValue('storehouse_ids', [data?.[0]?.value]);
            }
          });
        }
      },
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: false,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
      allowClear: false,
      fieldNames: {
        // @ts-ignore
        idKey: 'id',
        nameKey: 'store_name',
      },
    },

    {
      dependencies: ['store_ids'],
      label: '仓库',
      name: 'storehouse_ids',
      multiple: true,
      type: 'select',
      linkId: 'storehouse_ids_options',
      clear: true,
      dialogParams: {
        type: 'storehouse',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: false,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
      fieldNames: {
        // @ts-ignore
        idKey: 'id',
        nameKey: 'name',
      },
    },
    {
      label: '商品分类',
      name: 'item_category_id',
      type: 'inputDialog',
      treeModalConfig: {
        title: '选择商品分类', // 标题
        url: '/erp/hxl.erp.category.find', // 请求地址
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
        data: {
          enabled: true,
        },
        width: 360, // 模态框宽度
      } as any,
      width: 200,
    },
    {
      label: '商品类型',
      name: 'item_type',
      type: 'select',
      clear: true,
      check: true,
      options: goodsType,
    },
    {
      label: '商品品牌',
      name: 'item_brand_id',
      type: 'inputDialog',
      clear: true,
      check: true,
      showDialogByDisabled: false,
      dialogParams: {
        type: 'productBrand',
        dataType: 'lists',
        isLeftColumn: false,
        isMultiple: false,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
      onChange: (e) => {
        form.setFieldValue('item_brand_id', e?.[0] || undefined);
      },
    },
    {
      label: '商品部门',
      name: 'item_dept_ids',
      type: 'inputDialog',
      check: false,
      dialogParams: {
        type: 'productDept',
        dataType: 'lists',
        isMultiple: false,
        isLeftColumn: false,
        immediatePost: true,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
      onChange: (e) => {
        form.setFieldValue('item_dept_id', e?.[0] || undefined);
      },
    },
    {
      label: '类别等级',
      name: 'category_level',
      type: 'select',
      clear: true,
      defaultValue: 1,
      check: true,
      options: [
        { label: '一级类别', value: 1 },
        { label: '二级类别', value: 2 },
        { label: '三级类别', value: 3 },
      ],
      onChange(e) {
        form.setFieldValue('category_level', e);
      },
    },
    {
      label: '是否读取档案',
      name: 'read_item_attr',
      type: 'custom',
      render: (itemData, form) => {
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <XlbCheckbox
              onChange={(e) => {
                form?.setFieldValue('read_item_attr', e.target.checked);
              }}
            >
              是否读取档案
            </XlbCheckbox>
          </div>
        );
      },
    },
  ];
  const [formArr, setFormArr] = useState(formList);

  const tableList: XlbTableColumnProps<any>[] = [
    // {
    //   name: '序号',
    //   code: 'index',
    //   width: 60,
    //   align: 'center'
    // },
    {
      name: '商品分类',
      code: 'category_name',
      width: 200,
      align: 'left',
    },
    {
      name: 'sku数量',
      code: 'skuQuantity',
      width: 100,
      align: 'center',
      children: [
        {
          name: '中心理论初始值',
          code: 'theory_business_sku_num',
          width: 144,
          align: 'left',
        },
        {
          name: '门店理论初始值',
          code: 'store_theory_initial_sku_num',
          width: 144,
          align: 'left',
        },
        {
          name: '正常',
          code: 'normal_quantity',
          width: 100,
          align: 'center',
          render: (text, record, index) => {
            return record.category_name !== '合计' ? (
              <span
                onClick={() => {
                  handleDetail(record, 'normal_quantity');
                }}
                style={{ color: '#1989fa', cursor: 'pointer' }}
              >
                {text}
              </span>
            ) : (
              text
            );
          },
        },
        {
          name: '停购',
          code: 'stop_purchase_quantity',
          align: 'center',
          width: 100,
          render: (text, record, index) => {
            return record.category_name !== '合计' ? (
              <span
                onClick={() => {
                  handleDetail(record, 'stop_purchase_quantity');
                }}
                style={{ color: '#1989fa', cursor: 'pointer' }}
              >
                {text}
              </span>
            ) : (
              text
            );
          },
        },
        {
          name: '停售',
          code: 'stop_sale_quantity',
          align: 'center',
          width: 100,
          render: (text, record, index) => {
            return record.category_name !== '合计' ? (
              <span
                onClick={() => {
                  handleDetail(record, 'stop_sale_quantity');
                }}
                style={{ color: '#1989fa', cursor: 'pointer' }}
              >
                {text}
              </span>
            ) : (
              text
            );
          },
        },
        {
          name: '停止要货',
          code: 'stop_request_quantity',
          align: 'center',
          width: 100,
          render: (text, record, index) => {
            return record.category_name !== '合计' ? (
              <span
                onClick={() => {
                  handleDetail(record, 'stop_request_quantity');
                }}
                style={{ color: '#1989fa', cursor: 'pointer' }}
              >
                {text}
              </span>
            ) : (
              text
            );
          },
        },
        {
          name: '称重',
          code: 'weigh_quantity',
          align: 'center',
          width: 100,
          render: (text, record, index) => {
            return record.category_name !== '合计' ? (
              <span
                onClick={() => {
                  handleDetail(record, 'weigh_quantity');
                }}
                style={{ color: '#1989fa', cursor: 'pointer' }}
              >
                {text}
              </span>
            ) : (
              text
            );
          },
        },
      ],
    },
    {
      name: '',
      code: '',
      align: 'right',
    },
  ];
  const fetchOrgList = async () => {
    const data = { level: 2 };
    const res = await ErpRequest.post('/erp/hxl.erp.org.find', data);
    if (res?.code === 0) {
      const org_list = res.data.map((i: any) => ({
        value: i.id,
        label: i.name,
      }));
      form.setFieldValue('org_options', org_list);
      // setOrgList(org_list);
    }
  };
  useEffect(() => {
    form.setFieldValue('category_level', 1); //  默认一级列别
    fetchOrgList();
  }, []);
  return (
    <>
      <DetailDialog
        queryParams={queryParams}
        visible={visible}
        close={() => {
          setVisible(false);
        }}
      ></DetailDialog>
      <XlbPageContainer
        url={'/erp/hxl.erp.item.summary'}
        footerDataSource={(data) => {
          const footerData = [
            {
              category_name: '合计',
              ...data,
            },
          ];
          return footerData;
        }}
        prevPost={(p) => {
          const formData = form.getFieldsValue(true);
          if (formData.store_ids) {
            formData.store_id = formData.store_ids[0];
          }
          return { ...p, ...formData };
        }}
        immediatePost={false}
        tableColumn={tableList}
      >
        <ToolBtn showColumnsSetting>
          {(content) => {
            const { loading, fetchData } = content;
            return (
              <XlbButton
                key="query"
                label="查询"
                type="primary"
                disabled={loading}
                onClick={() => {
                  fetchData();
                }}
                icon={<span className="iconfont icon-sousuo" />}
              >
                查询
              </XlbButton>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm form={form} isHideDate formList={formArr}></XlbForm>
        </SearchForm>
        <Table></Table>
      </XlbPageContainer>
    </>
  );
};
export default Index;