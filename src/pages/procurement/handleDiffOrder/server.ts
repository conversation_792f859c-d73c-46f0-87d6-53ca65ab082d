import { XlbFetch } from '@xlb/utils';

// 导出差异处理单
export const exportDiffOrder = async (data: any) => {
  return await XlbFetch.post(
    '/erp-purchase/hxl.erp.differencehandleorder.export',
    data,
  );
};

// 导出差异处理单明细
export const exportDiffOrderDetail = async (data: any) => {
  return await XlbFetch.post(
    '/erp-purchase/hxl.erp.differencehandleorder.detail.export',
    data,
  );
};

// 编辑差异处理单
export const editDiffOrder = async (data: any) => {
  return await XlbFetch.post(
    '/erp-purchase/hxl.erp.differencehandleorder.update',
    data,
  );
};

// 审批差异处理单
interface ApproveRequest {
  fid: string;
  approve_result: string;
  approve_comment: string;
}
export const approveDiffOrder = async (data: ApproveRequest) => {
  return await XlbFetch.post(
    '/erp-purchase/hxl.erp.differencehandleorder.approval.approve',
    data,
  );
};

export const readReceiveOrder = async (data: { fid: string }) => {
  return await XlbFetch.post('/erp-purchase/hxl.erp.receiveorder.read', data);
};

export const readWholesaleOrder = async (data: any) => {
  return await XlbFetch.post(
    '/erp-purchase/hxl.erp.wholesaleorder.item.page',
    data,
  );
};
