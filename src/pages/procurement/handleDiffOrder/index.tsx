import { hasAuth } from '@/utils/kit';
import { wujieBus } from '@/wujie/utils';
import { history } from '@@/core/history';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbProPageModal,
  XlbProPageModalRef,
} from '@xlb/components';
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import { FC, ReactNode, useEffect, useRef, useState } from 'react';
import { DIFF_REASON_LIST, FORM_LIST, TABLE_COLUMNS } from './constants';
import HandleDiffOrderDetail from './item';
import { exportDiffOrder, exportDiffOrderDetail } from './server';

const Index: FC = () => {
  // layout
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageContainerRef = useRef<XlbPageContainerRef>(null);
  const [record, setRecord] = useState<any>();
  useEffect(() => {
    const pageRecord = history.location.state as any;
    if (pageRecord?.source === 'oa') {
      setRecord({
        fid: pageRecord?.business_key,
        isOA: pageRecord?.source === 'oa',
      });
      pageModalRef.current?.setOpen(true);
    }
  }, []);
  // form
  const [form] = XlbBasicForm.useForm();
  const [formList, setFormList] = useState(cloneDeep(FORM_LIST));
  const onValuesChange = (changedValues: any) => {
    if (Object.hasOwn(changedValues, 'different_type')) {
      // 差异类型变化时，隐藏/显示表单项
      const updateList = formList.map((item) => {
        if (item.name === 'client_ids') {
          return {
            ...item,
            hidden:
              changedValues.different_type !==
              DIFF_REASON_LIST.enums.comp.value,
          };
        }
        if (item.name === 'supplier_ids') {
          return {
            ...item,
            hidden:
              changedValues.different_type !==
              DIFF_REASON_LIST.enums.return.value,
          };
        }
        return item;
      });
      setFormList(updateList);
      form.setFieldsValue({
        client_ids: [],
        supplier_ids: [],
      });
    }
  };

  // table format
  const getClientIds = () => {
    if (!form.getFieldValue('different_type')) {
      return [];
    }
    return form.getFieldValue('different_type') ===
      DIFF_REASON_LIST.enums.comp.value
      ? form.getFieldValue('client_ids')
      : form.getFieldValue('supplier_ids');
  };
  const prevPost = () => ({
    ...form.getFieldsValue(true),
    client_ids: getClientIds(),
  });

  // init
  useEffect(() => {
    form.setFieldsValue({
      create_date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    });
  }, []);

  // operate
  const exportItem = async (e: any, requestForm: any, label: ReactNode) => {
    const res =
      label === '导出'
        ? await exportDiffOrder(requestForm)
        : await exportDiffOrderDetail(requestForm);
    if (res?.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success(res?.data || '导出受理成功，请前往下载中心查看');
    }
  };

  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={() => {
          return (
            <HandleDiffOrderDetail
              onBack={(back: boolean, isOA: boolean) => {
                if (back) {
                  pageContainerRef?.current?.fetchData?.();
                }
                pageModalRef.current?.setOpen(false);
              }}
              record={record}
            />
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbPageContainer
        ref={pageContainerRef}
        url={'/erp-purchase/hxl.erp.differencehandleorder.page'}
        tableColumn={TABLE_COLUMNS.map((item) => {
          if (item.code === 'fid') {
            return {
              ...item,
              render: (value: string, records: any) => {
                return (
                  <span
                    className="link cursors"
                    onClick={(e) => {
                      e.stopPropagation();
                      setRecord({ ...records, isOA: false });
                      pageModalRef.current?.setOpen(true);
                    }}
                  >
                    {value}
                  </span>
                );
              },
            };
          }
          return item;
        })}
        immediatePost
        prevPost={prevPost}
      >
        <XlbPageContainer.ToolBtn showColumnsSetting>
          {({
            fetchData,
            loading,
            dataSource,
            requestForm,
          }: ContextState<any>) => {
            return (
              <XlbButton.Group>
                {hasAuth(['买赔审批单', '查询']) && (
                  <XlbButton
                    label="查询"
                    type="primary"
                    loading={loading}
                    onClick={() => {
                      fetchData();
                    }}
                    icon={<XlbIcon name="sousuo" />}
                  />
                )}
                {hasAuth(['买赔审批单', '导出']) && (
                  <XlbDropdownButton
                    label={'导出'}
                    icon={<XlbIcon name="daochu" />}
                    dropList={[
                      {
                        label: '导出',
                        disabled: !dataSource?.length,
                      },
                      {
                        label: '导出明细',
                        disabled: !dataSource?.length,
                      },
                    ]}
                    dropdownItemClick={(_, item, e) => {
                      exportItem(e, requestForm, item?.label);
                    }}
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </XlbPageContainer.ToolBtn>
        <XlbPageContainer.SearchForm>
          <XlbForm
            form={form}
            formList={formList}
            isHideDate
            onValuesChange={onValuesChange}
          />
        </XlbPageContainer.SearchForm>

        <XlbPageContainer.Table key="fid" primaryKey="fid" />
      </XlbPageContainer>
    </>
  );
};
export default Index;
