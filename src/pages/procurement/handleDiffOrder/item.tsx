import { hasAuth } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbInput,
  XlbInputDialog,
  XlbInputNumber,
  XlbMessage,
  XlbPageContainer,
  XlbSelect,
  XlbTabs,
  XlbTipsModal,
} from '@xlb/components';
import { useRef, useState } from 'react';
// TODO: 待替换至components中同名组件
import XlbApprovalProcessModal from './components/xlbApprovalProcessModal';
import {
  DIFF_REASON_LIST,
  ITEM_TABLE_COLUMNS,
  STATE_LIST,
  TabKeys,
} from './constants';
import {
  approveDiffOrder,
  editDiffOrder,
  readReceiveOrder,
  readWholesaleOrder,
} from './server';

interface IProps {
  record: any;
  onBack: (back: boolean, isOA: boolean) => void;
}
const Index = (props: IProps) => {
  const { record, onBack } = props;
  // form
  const [form] = XlbBasicForm.useForm();
  const [receiveOrder, setReceiveOrder] = useState<any>();
  // tab
  const [tabActiveKey, setTabActiveKey] = useState<TabKeys>(TabKeys.baseInfo);
  // search
  const refresh = useRef<any>();
  const rowData = useRef<any>();
  const setRowData = useRef<any>();
  const prevPost = () => {
    return {
      fid: record?.fid,
    };
  };
  const afterPost = async (data: any) => {
    if (data?.receive_order_fid) {
      const res = await readReceiveOrder({ fid: data?.receive_order_fid });
      setReceiveOrder(res.data);
    }
    form.setFieldsValue({
      ...data,
      client_type: DIFF_REASON_LIST.get(data?.different_type)?.clientType,
    });
    return data?.details || [];
  };

  // operate
  const [operateForm] = XlbBasicForm.useForm();
  const operate = (type: string, dataSource: any[], fetchData: any) => {
    if (!form.getFieldValue('client_id')?.length) {
      XlbMessage.error('买赔客户不能为空');
      return;
    }
    XlbTipsModal({
      title: '审批',
      tips: (
        <XlbBasicForm form={operateForm} layout="vertical">
          <XlbBasicForm.Item
            label="审核意见"
            name="approve_comment"
            rules={[{ required: true, message: '审核意见不能为空' }]}
          >
            <XlbInput.TextArea
              maxLength={500}
              style={{ height: 100, resize: 'none' }}
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      ),
      isCancel: true,
      onCancel: () => operateForm.resetFields(),
      onOkBeforeFunction: async () => {
        return operateForm
          .validateFields()
          .then(async () => {
            if (type === STATE_LIST.enums.approve.value) {
              const updateRes = await editDiffOrder({
                ...form.getFieldsValue(true),
                client_id: form.getFieldValue('client_id')?.[0],
                details: dataSource,
              });
              if (updateRes.code !== 0) {
                return false;
              }
            }
            const res = await approveDiffOrder({
              fid: record?.fid,
              approve_comment: operateForm.getFieldValue('approve_comment'),
              approve_result: type,
            });
            if (res.code === 0) {
              XlbMessage.success('操作成功');
              operateForm.resetFields();
              fetchData();
            }
            return res.code === 0;
          })
          .catch(() => false);
      },
    });
  };
  // 价格计算
  const getPrice = (price: any, records: any) => {
    const formatNum = (num: any) => Number(num) ?? 0;
    const money = safeMath.multiply(
      formatNum(price),
      formatNum(records.quantity),
    );
    const getNoTax = (itemPrice: number) =>
      safeMath.divide(
        formatNum(itemPrice),
        safeMath.add(
          1,
          safeMath.divide(formatNum(records.input_tax_rate), 100),
        ),
      );
    return {
      price: formatNum(price),
      no_tax_price: getNoTax(price),
      money,
      no_tax_money: getNoTax(money),
    };
  };
  // 差异类型切换
  const diffTypeChange = (e: string) => {
    if (e === DIFF_REASON_LIST.enums.return.value) {
      form.setFieldsValue({
        client_id: receiveOrder?.supplier_id,
        client_name: receiveOrder?.supplier_name,
        client_type: DIFF_REASON_LIST.get(e)?.clientType,
      });
      const updateList = rowData.current?.map((item: any) => {
        const itemData = receiveOrder?.details?.find(
          (child: any) => child.item_id === item.item_id,
        );
        const priceData = getPrice(itemData.price, item);
        return {
          ...item,
          ...priceData,
          _click: false,
        };
      });
      setRowData.current?.(updateList || []);
      return;
    }
    form.setFieldsValue({
      client_id: [],
      client_type: DIFF_REASON_LIST.get(e)?.clientType,
    });
  };
  const clientBeforeChange = async (e: number) => {
    if (e) {
      const res = await readWholesaleOrder({
        client_id: e,
        store_id: receiveOrder?.store_id,
        storehouse_id: receiveOrder?.storehouse_id,
        cargo_owner_id: receiveOrder?.cargo_owner_id,
        ids: rowData.current?.map((item: any) => item.item_id),
      });
      if (res?.code === 0) {
        const wholesaleDetails = res?.data?.content || [];
        const updateList = rowData.current?.map((item: any) => {
          const itemData = wholesaleDetails?.find(
            (child: any) => child.id === item.item_id,
          );
          const itemPrice =
            itemData?.price ||
            safeMath.multiply(itemData?.basic_price, itemData?.wholesale_ratio);
          const priceData = getPrice(itemPrice, item);
          return {
            ...item,
            ...priceData,
            _click: false,
          };
        });
        setRowData.current?.(updateList || []);
      }
      return res?.code === 0;
    }
    return true;
  };

  return (
    <XlbPageContainer
      url={'/erp-purchase/hxl.erp.differencehandleorder.read'}
      tableColumn={ITEM_TABLE_COLUMNS.map((item) => {
        if (item.code === 'price') {
          return {
            ...item,
            render: (value: string, records: any) => {
              if (
                hasAuth(['买赔审批单/单价', '查询']) &&
                record?.isOA &&
                records?._click &&
                form.getFieldValue('state') === STATE_LIST.enums.doing.value
              ) {
                return (
                  <XlbInputNumber
                    defaultValue={value}
                    min={0}
                    precision={4}
                    onChange={(e) => {
                      const priceData = getPrice(e, records);
                      Object.assign(records, priceData);
                    }}
                  />
                );
              }
              return hasAuth(['买赔审批单/单价', '查询'])
                ? Number(value).toFixed(4)
                : '****';
            },
          };
        }
        return item;
      })}
      afterPost={afterPost}
      prevPost={prevPost}
      immediatePost
    >
      <XlbPageContainer.ToolBtn showColumnsSetting>
        {({ dataSource, fetchData, setDataSource }: ContextState<any>) => {
          refresh.current = fetchData;
          rowData.current = dataSource;
          setRowData.current = setDataSource;
          return (
            <XlbButton.Group>
              {hasAuth(['买赔审批单', '查询']) && (
                <XlbButton
                  label="查看审批流"
                  type="primary"
                  icon={<XlbIcon name="shenhe" />}
                  onClick={async () => {
                    console.log(form.getFieldsValue(true));
                    await XlbApprovalProcessModal({
                      params: {
                        fid: record?.fid,
                      },
                      requestUrl:
                        process.env.ERP_URL +
                        '/erp-purchase/hxl.erp.differencehandleorder.oa.findbyfid',
                    });
                  }}
                />
              )}
              {hasAuth(['买赔审批单', '编辑']) && record?.isOA && (
                <XlbButton
                  label="同意"
                  type="primary"
                  disabled={
                    form.getFieldValue('state') !== STATE_LIST.enums.doing.value
                  }
                  icon={<XlbIcon name="shenhe" />}
                  onClick={() =>
                    operate(
                      STATE_LIST.enums.approve.value,
                      dataSource || [],
                      fetchData,
                    )
                  }
                />
              )}
              {hasAuth(['买赔审批单', '编辑']) && record?.isOA && (
                <XlbButton
                  label="驳回"
                  type="primary"
                  disabled={
                    form.getFieldValue('state') !== STATE_LIST.enums.doing.value
                  }
                  icon={<XlbIcon name="fanshenhe" />}
                  onClick={() =>
                    operate(
                      STATE_LIST.enums.reject.value,
                      dataSource || [],
                      fetchData,
                    )
                  }
                />
              )}
              <XlbButton
                label="返回"
                type="primary"
                icon={<XlbIcon name="fanhui" />}
                onClick={() => onBack(false, record?.isOA)}
              />
            </XlbButton.Group>
          );
        }}
      </XlbPageContainer.ToolBtn>
      <XlbPageContainer.SearchForm>
        <XlbBasicForm form={form}>
          <XlbTabs
            defaultActiveKey={TabKeys.baseInfo}
            activeKey={tabActiveKey}
            onChange={(e) => setTabActiveKey(e as TabKeys)}
            items={[
              {
                label: '基本信息',
                key: TabKeys.baseInfo,
                children: (
                  <div className="v-flex flex-wrap">
                    <XlbBasicForm.Item noStyle dependencies={['state']}>
                      {(form) => (
                        <XlbBasicForm.Item
                          label="差异原因"
                          name="different_type"
                        >
                          <XlbSelect
                            disabled={
                              form.getFieldValue('state') !==
                                STATE_LIST.enums.doing.value || !record?.isOA
                            }
                            options={DIFF_REASON_LIST.values}
                            allowClear={false}
                            onChange={diffTypeChange}
                          />
                        </XlbBasicForm.Item>
                      )}
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item
                      noStyle
                      dependencies={['different_type']}
                    >
                      {(data) => {
                        return data.getFieldValue('different_type') ? (
                          <XlbBasicForm.Item label="买赔客户" name="client_id">
                            <XlbBasicForm.Item
                              noStyle
                              dependencies={['different_type']}
                            >
                              {(form) => {
                                return form.getFieldValue('different_type') ===
                                  DIFF_REASON_LIST.enums.comp.value ? (
                                  <XlbBasicForm.Item name="client_id" noStyle>
                                    <XlbInputDialog
                                      disabled={
                                        form.getFieldValue('state') !==
                                          STATE_LIST.enums.doing.value ||
                                        !record?.isOA
                                      }
                                      width={240}
                                      dialogParams={{
                                        type: 'wholesaler',
                                        dataType: 'lists',
                                        isLeftColumn: true,
                                        isMultiple: false,
                                        primaryKey: 'id',
                                        data: {
                                          enabled: true,
                                        },
                                        onOkBeforeFunction: clientBeforeChange,
                                      }}
                                      onChange={(_: any, list: any[]) => {
                                        form.setFieldsValue({
                                          client_name: list?.[0]?.name,
                                        });
                                      }}
                                    />
                                  </XlbBasicForm.Item>
                                ) : (
                                  <XlbBasicForm.Item name="client_id" noStyle>
                                    <XlbInputDialog
                                      width={240}
                                      disabled={
                                        form.getFieldValue('state') !==
                                          STATE_LIST.enums.doing.value ||
                                        !record?.isOA ||
                                        form.getFieldValue('different_type') ===
                                          DIFF_REASON_LIST.enums.return.value
                                      }
                                      dialogParams={{
                                        type: 'supplier',
                                        dataType: 'lists',
                                        isLeftColumn: true,
                                        isMultiple: false,
                                        primaryKey: 'id',
                                        data: {
                                          enabled: true,
                                        },
                                      }}
                                      onChange={(_: any, list: any[]) => {
                                        form.setFieldsValue({
                                          client_name: list?.[0]?.name,
                                        });
                                      }}
                                    />
                                  </XlbBasicForm.Item>
                                );
                              }}
                            </XlbBasicForm.Item>
                          </XlbBasicForm.Item>
                        ) : (
                          <></>
                        );
                      }}
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item noStyle dependencies={['state']}>
                      {(form) => (
                        <XlbBasicForm.Item label="处理备注" name="memo">
                          <XlbInput
                            disabled={
                              form.getFieldValue('state') !==
                                STATE_LIST.enums.doing.value || !record?.isOA
                            }
                            width={520}
                          />
                        </XlbBasicForm.Item>
                      )}
                    </XlbBasicForm.Item>
                  </div>
                ),
              },
              {
                label: '其他信息',
                key: TabKeys.otherInfo,
                children: (
                  <div className="flex-wrap">
                    <XlbBasicForm.Item label="制单人" name="create_by">
                      <XlbInput width={180} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="制单时间" name="create_time">
                      <XlbInput width={180} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="处理人" name="handle_by">
                      <XlbInput width={180} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="处理时间" name="handle_time">
                      <XlbInput width={180} disabled />
                    </XlbBasicForm.Item>
                  </div>
                ),
              },
            ]}
          />
        </XlbBasicForm>
      </XlbPageContainer.SearchForm>

      <XlbPageContainer.Table
        key="item_id"
        primaryKey="item_id"
        selectMode="single"
      />
    </XlbPageContainer>
  );
};
export default Index;
