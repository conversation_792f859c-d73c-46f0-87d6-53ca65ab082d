import NiceModal from '@ebay/nice-modal-react';
import { XlbApprovalProcess, XlbModal } from '@xlb/components';

type GoodsModalProps = {
  params?: any; // 接口参数
  requestUrl?: string; // 业务接口 url
};

// TODO: 待删除，使用components中相同组件
const openModal = NiceModal.create((props: any) => {
  const { params, requestUrl } = props;

  const modal = NiceModal.useModal();

  const onOK = async () => {
    modal.resolve(true);
    modal.hide();
  };

  return (
    <XlbModal
      title={'审批流'}
      open={modal.visible}
      width={720}
      onCancel={onOK}
      onOk={onOK}
      zIndex={2002}
    >
      <div style={{ padding: '10px 0px', minHeight: 200 }}>
        <XlbApprovalProcess params={params} requestUrl={requestUrl} />
      </div>
    </XlbModal>
  );
});

const XlbApprovalProcessModal = (props: GoodsModalProps): Promise<string> => {
  return NiceModal.show(openModal, props);
};

export default XlbApprovalProcessModal;
