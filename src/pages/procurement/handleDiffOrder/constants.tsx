import { Constant } from '@/constants/index';
import { hasAuth } from '@/utils/kit';
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';
// TODO: 待替换至components中同名组件
import XlbApprovalProcessModal from './components/xlbApprovalProcessModal';

export const STATE_LIST = new Constant({
  doing: {
    label: '审批中',
    value: 'DOING',
  },
  approve: {
    label: '处理通过',
    value: 'APPROVE',
  },
  reject: {
    label: '处理拒绝',
    value: 'REJECT',
  },
});

export const DIFF_REASON_LIST = new Constant({
  comp: {
    label: '买赔',
    value: 'COMP',
    clientType: '批发客户',
  },
  return: {
    label: '退单',
    value: 'RETURN',
    clientType: '供应商',
  },
});

export const FORM_LIST: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '创建日期',
    name: 'create_date',
    allowClear: false,
  },
  {
    label: '所属组织',
    name: 'org_ids',
    multiple: true,
    type: 'select',
    check: true,
    // @ts-ignore
    onChange: (_e: any, form: any) => {
      form?.setFieldsValue({ store_ids: [] });
    },
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.org.find',
      postParams: { level: 2 },
      responseTrans(response: any) {
        return (
          response?.map((item: any) => ({
            ...item,
            label: item.name,
            value: item.id,
          })) || []
        );
      },
    },
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    dependencies: ['org_ids'],
    dialogParams: (data) => ({
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: { org_ids: data.org_ids, status: true },
    }),
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '商品',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
    },
  },
  {
    label: '单据状态',
    name: 'states',
    type: 'select',
    multiple: true,
    clear: true,
    check: true,
    options: STATE_LIST.values,
  },
  {
    label: '审批单号',
    name: 'fid',
    type: 'input',
    clear: true,
    check: true,
  },
  {
    label: '差异原因',
    name: 'different_type',
    type: 'select',
    clear: true,
    check: true,
    options: DIFF_REASON_LIST.values,
  },
  {
    label: '买赔客户',
    name: 'client_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'wholesaler',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    hidden: true,
  },
  {
    label: '买赔客户',
    name: 'supplier_ids',
    hidden: true,
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '制单人',
    name: 'create_by',
    type: 'input',
    clear: true,
    check: true,
  },
];

export const TABLE_COLUMNS: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '审批单号',
    code: 'fid',
    width: 220,
    features: { sortable: true },
  },
  {
    name: '组织',
    code: 'org_id',
    width: 180,
    features: { sortable: true },
    render: (_, record: any) => record?.org_name,
  },
  {
    name: '门店',
    code: 'store_id',
    width: 180,
    features: { sortable: true },
    render: (_, record: any) => record?.store_name,
  },
  {
    name: '差异原因',
    code: 'different_type',
    width: 120,
    features: { sortable: true },
    render: (value: string) => DIFF_REASON_LIST.get(value)?.label,
  },
  {
    name: '买赔客户',
    code: 'client_name',
    width: 220,
    features: { sortable: true },
  },
  {
    name: '入库单',
    code: 'in_order_fid',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '状态',
    code: 'state',
    width: 120,
    features: { sortable: true },
    render: (value: string) => STATE_LIST.get(value)?.label,
  },
  {
    name: '制单人',
    code: 'create_by',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '处理人',
    code: 'handle_by',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '处理时间',
    code: 'handle_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '操作',
    code: 'operate',
    width: 120,
    features: { sortable: true },
    align: 'center',
    render: (_, record: any) => (
      <span
        className="default cursors"
        onClick={async () => {
          await XlbApprovalProcessModal({
            params: { fid: record?.fid },
            requestUrl:
              process.env.ERP_URL +
              '/erp-purchase/hxl.erp.differencehandleorder.oa.findbyfid',
          });
        }}
      >
        审批流
      </span>
    ),
  },
];

export enum TabKeys {
  baseInfo = 'baseInfo',
  otherInfo = 'otherInfo',
}

export const ITEM_TABLE_COLUMNS: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 160,
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 240,
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: 120,
  },
  {
    name: '单位',
    code: 'unit',
    width: 120,
  },
  {
    name: '数量',
    code: 'quantity',
    width: 120,
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 120,
    align: 'right',
  },
  {
    name: '进项税率(%)',
    code: 'input_tax_rate',
    width: 120,
    align: 'right',
  },
  {
    name: '单价(不含税)',
    code: 'no_tax_price',
    width: 120,
    align: 'right',
    render: (value: number | string) =>
      hasAuth(['买赔审批单/单价', '查询']) ? Number(value).toFixed(4) : '****',
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    align: 'right',
    render: (value: number | string) =>
      hasAuth(['买赔审批单/单价', '查询']) ? Number(value).toFixed(2) : '****',
  },
  {
    name: '金额(不含税)',
    code: 'no_tax_money',
    width: 120,
    align: 'right',
    render: (value: number | string) =>
      hasAuth(['买赔审批单/单价', '查询']) ? Number(value).toFixed(2) : '****',
  },
  {
    name: '赠品单位',
    code: 'present_unit',
    width: 120,
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 120,
    align: 'right',
  },
];
