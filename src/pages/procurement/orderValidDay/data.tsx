import { columnWidthEnum } from '@/data/common/constant';
import type { XlbTableColumnProps } from '@xlb/components';

export const formList = (enable_organization: boolean) => [
  {
    label: '组织',
    name: 'org_ids',
    type: 'select',
    multiple: true,
    clear: true,
    hidden: !enable_organization,
    selectRequestParams: {
      url: '/erp/hxl.erp.org.find',
      postParams: { level: 2 },
      responseTrans: {
        label: 'name',
        value: 'id',
      },
    },
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '组织',
    code: 'org_name',
    width: columnWidthEnum.STORE_NAME,
    align: 'center',
  },
  {
    name: '采购订单有效天数',
    code: 'valid_day',
    width: 160,
    align: 'center',
  },
  {
    name: '最近更新人',
    code: 'update_by',
    width: 120,
    align: 'center',
  },
  {
    name: '最近更新时间',
    code: 'update_time',
    width: 160,
    align: 'center',
  },
];
