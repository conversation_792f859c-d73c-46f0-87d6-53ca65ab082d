.forecast-analysis-container {
  background: #f3f4f7;
  min-width: 1120px;
  height: 100%;
  overflow: auto;
  .analysis-detail-container {
    padding: 16px;
    background: #ffffff;
    border-radius: 4px;
    position: relative;
    &.filter-group {
      padding: 12px 16px 0 12px;
    }
    &__title {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 500;
      height: 24px;
      &::before {
        width: 4px;
        height: 16px;
        content: '';
        background: #1a6aff;
        margin-right: 8px;
      }
    }
    &.is-charts .analysis-detail-container__title {
      position: absolute;
    }
    &__operate {
      position: absolute;
      right: 16px;
      top: 14px;
      display: flex;
      align-items: center;
      .operate-btn {
        padding: 0 12px !important;
        margin: 0 !important;
        position: relative;
        border: none !important;
        & + .operate-btn ::before {
          content: '';
          position: absolute;
          inset: 0 auto 0 -1px;
          margin: auto;
          width: 1px;
          height: 16px;
          background: rgba(26, 106, 255, 0.2);
        }
      }
    }

    .view-activity {
      position: absolute;
      top: 18px;
      right: 16px;
      display: flex;
      align-items: center;
      color: #1a6aff;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      cursor: pointer;
      user-select: none;
      &::after {
        content: '';
        width: 9px;
        height: 9px;
        border: 2px solid #1a6aff;
        border-radius: 1px;
        border-width: 2px 2px 0 0;
        transform: rotate(45deg);
        margin-left: 4px;
      }
    }
    .analysis-detail-list {
      margin-top: 10px;
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      grid-gap: 12px;
      position: relative;
      &__item {
        background: #f9fafc;
        padding: 16px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        color: #1f2126;
        font-size: 14px;
        line-height: 20px;
      }
      &__value {
        font-size: 18px;
        font-weight: bold;
        line-height: 22px;
        margin-top: 12px;
      }
    }
  }
  .mask-loading {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
  }
}
