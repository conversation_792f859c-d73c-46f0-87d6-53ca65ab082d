.analysis-data-container {
  position: relative;
  overflow: hidden;
  .shadow-mask-container {
    width: 140px;
    position: absolute;
    left: 0;
    height: calc(100% - 14px);
    overflow: hidden;
    z-index: 10;
    .shadow-mask {
      width: 121px;
      height: 100%;
      border-right: 1px solid #e5e6ea;
      box-shadow: rgba(152, 152, 152, 0.5) 0 0 6px 2px;
      &.show-border {
        box-shadow: none;
      }
    }
  }
  .analysis-data-table {
    font-size: 14px;
    color: #1d2129;
    overflow: auto;
    .row-fixed {
      position: sticky;
      left: 0;
      background: #ffffff;
    }
    &--table {
      width: 100%;
      border: 1px solid #e5e6ea;
      table-layout: fixed;
      border-collapse: collapse;
      .line-active {
        background: #e8f0ff;
      }
    }
    &--header {
      background: #f2f3f5;
      .row-fixed {
        background: #f2f3f5;
      }
      .table-title {
        padding: 10px;
        width: 120px;
        border: 1px solid #e5e6ea;
        .font-normal {
          font-weight: normal;
        }
        &__sub {
          font-weight: normal;
          font-size: 12px;
          color: #86909c;
          margin-top: 4px;
        }
      }
    }
    &--body {
      .table-cell {
        line-height: 20px;
        padding: 6px;
        border: 1px solid #e5e6ea;
        text-align: center;
        font-weight: 500;
        font-family: HarmonyOS Sans SC;
        &__title {
          font-weight: normal;
        }
        .table-cell__value {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .no-data {
      width: 100%;
      height: 166px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #4e5969;
    }
  }
}
