import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import { FC, useEffect, useRef, useState } from 'react';
import type { TableResponse } from '../server';
import './analysisDataTable.less';

export interface IProps {
  data: TableResponse[];
}
const AnalysisDataTable: FC<IProps> = ({ data }) => {
  // 判断展示阴影还是边框
  const [scrollLeft, setScrollLeft] = useState<number>(0);
  const tableContainerRef = useRef<any>(null);
  tableContainerRef.current?.addEventListener('scroll', (e: any) => {
    setScrollLeft(e.target?.scrollLeft ?? 0);
  });

  // 判断是否展示固定列样式
  const [showShadow, setShowShadow] = useState<boolean>(false);
  const tableRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  window.addEventListener('resize', () => {
    setShowShadow(
      (tableRef.current?.getBoundingClientRect()?.width || 0) >
        (containerRef.current?.getBoundingClientRect()?.width || 0),
    );
  });
  useEffect(() => {
    // 滚动至当前周
    const index =
      data?.findIndex((item) => item.week === dayjs().format('YYYY-WW')) || 0;
    const scrollDom = document.querySelector('.analysis-data-table');
    const scrollDomWidth = scrollDom?.getBoundingClientRect()?.width || 0;
    const domWidth = 120;
    scrollDom?.scrollTo({
      behavior: 'smooth',
      left: domWidth * (index + 1) - scrollDomWidth / 2,
    });
    // 是否滚动
    setShowShadow(
      (tableRef.current?.getBoundingClientRect()?.width || 0) >
        (containerRef.current?.getBoundingClientRect()?.width || 0),
    );
  }, [data]);
  const formatNumber = (value: any) => {
    if (typeof value === 'number') {
      return parseFloat(value.toFixed(2));
    }
    return '-';
  };
  const judgeDigits = (value: any, digits: number) => {
    if (typeof value === 'number') {
      return String(formatNumber(value))?.length > digits;
    }
    return false;
  };

  return (
    <div ref={containerRef} className="analysis-data-container">
      {!!data?.length && showShadow && (
        <div className="shadow-mask-container">
          <div
            className={`shadow-mask ${scrollLeft === 0 ? 'show-border' : ''}`}
          />
        </div>
      )}
      <div ref={tableContainerRef} className="analysis-data-table">
        {data?.length ? (
          <table
            ref={tableRef}
            border={1}
            className="analysis-data-table--table"
          >
            <thead className="analysis-data-table--header">
              <tr>
                <th className="table-title row-fixed">
                  <div className="flex-column flex-center">Week</div>
                </th>
                {data?.map((item) => (
                  <th
                    className={`table-title ${item.week === dayjs().format('YYYY-WW') ? 'line-active' : ''}`}
                    key={item.week}
                  >
                    <div className="flex-column flex-center">
                      <span className="font-normal">
                        {item.week === dayjs().format('YYYY-WW')
                          ? '本周'
                          : `${item.week.split('-').shift()}年${item.week.split('-').pop()}周`}
                      </span>
                      <span className="table-title__sub">
                        {item.date
                          .split('-')
                          ?.map((v: string) => dayjs(`20${v}`).format('MM/DD'))
                          .join('-')}
                      </span>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>

            <tbody className="analysis-data-table--body">
              <tr>
                <td className="table-cell row-fixed" width="104px">
                  <span className="table-cell__title">库存数</span>
                </td>
                {data?.map((item) => (
                  <td
                    className={`table-cell ${item.week === dayjs().format('YYYY-WW') ? 'line-active' : ''}`}
                    key={item.week}
                  >
                    {judgeDigits(item.basic_stock_quantity, 12) ? (
                      <Tooltip
                        placement="topLeft"
                        autoAdjustOverflow
                        title={formatNumber(item.basic_stock_quantity)}
                      >
                        <span className="table-cell__value">
                          {formatNumber(item.basic_stock_quantity)}
                        </span>
                      </Tooltip>
                    ) : (
                      <span className="table-cell__value">
                        {formatNumber(item.basic_stock_quantity)}
                      </span>
                    )}
                  </td>
                ))}
              </tr>

              {/* TODO:先注释，后续迭代加 */}
              {/* <tr>
                <td className="table-cell row-fixed">
                  <div className="table-cell__title">销量</div>
                </td>
                {data?.map((item) => (
                  <td
                    className={`table-cell ${item.week === dayjs().format('YYYY-WW') ? 'line-active' : ''}`}
                    key={item.week}
                  >
                    <span>{item.sale_quantity ?? '-'}</span>
                  </td>
                ))}
              </tr> */}
              <tr>
                <td className="table-cell row-fixed">
                  <div className="table-cell__title">预测数值</div>
                </td>
                {data?.map((item) => (
                  <td
                    className={`table-cell ${item.week === dayjs().format('YYYY-WW') ? 'line-active' : ''}`}
                    key={item.week}
                  >
                    {judgeDigits(item.forecast_out_quantity, 12) ? (
                      <Tooltip
                        placement="topLeft"
                        autoAdjustOverflow
                        title={formatNumber(item.forecast_out_quantity)}
                      >
                        <span className="table-cell__value">
                          {formatNumber(item.forecast_out_quantity)}
                        </span>
                      </Tooltip>
                    ) : (
                      <span className="table-cell__value">
                        {formatNumber(item.forecast_out_quantity)}
                      </span>
                    )}
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        ) : (
          <div className="no-data">暂无数据</div>
        )}
      </div>
    </div>
  );
};
export default AnalysisDataTable;
