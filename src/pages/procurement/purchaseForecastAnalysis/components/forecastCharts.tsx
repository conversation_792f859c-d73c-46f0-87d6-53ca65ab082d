import { getEndDataZoom, isShowDataZoom } from '@/utils/charts';
import { ECharts, EChartsOption, XlbChart, XlbChartProps } from '@xlb/datav';
import { useDebounceFn } from 'ahooks';
import { Skeleton } from 'antd';
import dayjs from 'dayjs';
import { FC, useCallback, useEffect, useRef } from 'react';
import { ChartsTypes } from '../data';
import type { OutQuantityResponse } from '../server';
import './forecastCharts.less';

export interface IProps extends XlbChartProps {
  data: OutQuantityResponse[];
  loading: boolean;
  customData?: EChartsOption;
  chartType: ChartsTypes;
}
const ForecastCharts: FC<IProps> = ({
  height,
  data,
  loading,
  customData,
  chartType,
}) => {
  const instanceRef = useRef<ECharts>();
  const getInstance = useCallback((instance: ECharts) => {
    instanceRef.current = instance;
  }, []);

  interface ITooltipItem {
    label: string;
    value: keyof OutQuantityResponse;
    isPercent?: boolean;
    showInFuture?: boolean;
  }
  const tooltipList: ITooltipItem[] = [
    {
      label: `实际${chartType}量`,
      value: 'out_quantity',
    },
    {
      label: `系统${chartType}量预测`,
      value: 'forecast_out_quantity',
      showInFuture: true,
    },
    {
      label: `实际${chartType}量预测`,
      value: 'adjust_out_quantity',
      showInFuture: true,
    },
    {
      label: '门店数',
      value: 'out_store',
    },
    {
      label: '促销活动',
      value: 'promotion_count',
      showInFuture: true,
    },
    {
      label: '0库存率',
      value: 'zero_stock_rate',
      isPercent: true,
    },
    {
      label: 'DOH',
      value: 'doh',
    },
    {
      label: '履约满足率',
      value: 'fulfillment_rate',
      isPercent: true,
    },
    {
      label: 'OTIF',
      value: 'otif',
    },
  ];
  const COLORS = ['#1A6AFF', '#339DFF', '#FF9340', '#41D985'];
  // 渲染图表
  function renderChart() {
    const windowWidth = window.innerWidth;
    const chartsWidth = Math.max(
      1064,
      windowWidth -
        120 /* 左侧侧边栏 */ -
        24 /* 外侧padding */ -
        32 /* 内侧padding */,
    );
    const maxChartCount = Math.floor(chartsWidth / 70);

    const option: EChartsOption = {
      color: COLORS,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          shadowStyle: {
            color: 'rgba(76,114,255,0.1)',
          },
        },
        formatter: (values: any) => {
          const axisValue = values?.[0]?.axisValue;
          const dataItem = data.find((item) => item.date === axisValue);
          const isFuture =
            dayjs().valueOf() <
            (dataItem?.date
              ?.split('-')
              .map((item: string) =>
                dayjs(`20${item}`)?.startOf('day').valueOf(),
              )
              .shift() || 0);
          const formatValue = (item: ITooltipItem) => {
            const originValue = Number(dataItem?.[item.value]) ?? 0;
            if (item.isPercent) {
              return `${parseFloat((originValue * 100).toFixed(2))}%`;
            }
            return parseFloat(originValue.toFixed(2));
          };
          const domItem = (
            item: ITooltipItem,
          ) => `<div class="forecast-tooltip-item">
                  <span class="forecast-tooltip-item-title">${item.label}</span>
                  <span class="forecast-tooltip-item-value">${formatValue(item)}</span>
                </div>`;
          return `<div class="forecast-tooltip-container">
                    <div class="forecast-tooltip-title">${axisValue}</div>
                    <div class="forecast-tooltip-content">
                      ${tooltipList
                        .filter((item) => !isFuture || item.showInFuture)
                        .map((item) => domItem(item))
                        .join('')}
                    </div>
                  </div>`;
        },
        ...customData?.tooltip,
      },
      legend: {
        itemWidth: 14,
        itemHeight: 8,
        itemGap: 12,
        left: 136,
        top: 0,
        textStyle: {
          color: '#4E5969',
          lineHeight: 17,
          align: 'center',
        },
        data: [
          {
            name: '门店数',
            icon: 'roundRect',
          },
          {
            name: `实际${chartType}量`,
          },
          {
            name: `系统${chartType}量预测`,
          },
          {
            name: `实际${chartType}量预测`,
          },
        ],
        ...customData?.legend,
      },
      grid: {
        left: 70,
        right: 62,
        top: 64,
        ...customData?.grid,
      },
      xAxis: {
        type: 'category',
        axisTick: { show: false },
        axisLine: { lineStyle: { color: '#C9CDD4' } },
        axisLabel: {
          color: '#4E5969',
          lineHeight: 14,
          align: 'center',
          fontSize: 10,
          formatter: (value: any) => {
            if (value && value.split('-')?.length) {
              return value
                .split('-')
                .map((item: string) => dayjs(`20${item}`)?.format('MM/DD'))
                .join('-');
            }
            return value;
          },
        },
        data: data?.map((item: any) => item.date) || [],
      },
      yAxis: [
        {
          type: 'value',
          name: `${chartType}量：件`,
          position: 'left',
          alignTicks: true,
          nameTextStyle: {
            color: '#86909C',
            fontSize: 12,
            align: 'right',
            padding: [0, 10, 0, 0],
          },
          axisLine: { show: false },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#C9CDD4',
              width: 1,
              type: 'dashed',
            },
          },
          axisLabel: {
            color: '#4E5969',
            fontSize: 12,
          },
        },
        {
          type: 'value',
          name: '门店数：家',
          position: 'right',
          alignTicks: true,
          nameTextStyle: {
            color: '#86909C',
            fontSize: 12,
            align: 'left',
          },
          axisLine: { show: false },
          axisLabel: {
            color: '#4E5969',
            fontSize: 12,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#C9CDD4',
              width: 1,
              type: 'dashed',
            },
          },
        },
      ],
      dataZoom: [
        {
          type: 'slider',
          show: isShowDataZoom(data || [], maxChartCount),
          brushSelect: false,
          height: 10,
          xAxisIndex: [0],
          left: 40,
          right: 30,
          bottom: 20,
          start: 0,
          end: getEndDataZoom(data || [], maxChartCount),
          handleSize: '0px',
          handleStyle: {
            color: '#BEBEBE',
            borderColor: '#BEBEBE',
          },
          showDetail: true,
          textStyle: false,
        },
      ],
      series: [
        {
          name: '门店数',
          type: 'bar',
          yAxisIndex: 1,
          data: data?.map((item: any) => item.out_store) || [],
          barWidth: 20,
        },
        {
          name: `实际${chartType}量`,
          type: 'line',
          data:
            data
              ?.filter(
                (item) =>
                  dayjs(`20${item.date.split('-').shift()}`).startOf('day') <=
                  dayjs().startOf('day'),
              )
              ?.map((item: any) => item.out_quantity) || [],
          lineStyle: { width: 2 },
          symbolSize: 6,
        },
        {
          name: `系统${chartType}量预测`,
          type: 'line',
          data: data?.map((item: any) => item.forecast_out_quantity) || [],
          lineStyle: { width: 2 },
          symbolSize: 6,
        },
        {
          name: `实际${chartType}量预测`,
          type: 'line',
          data: data?.map((item: any) => item.adjust_out_quantity) || [],
          lineStyle: { width: 2 },
          symbolSize: 6,
        },
      ],
    };
    instanceRef.current?.setOption(option);
  }

  // 监听窗口大小变化
  const handleResize = useDebounceFn(() => renderChart(), {
    wait: 500,
  });
  addEventListener('resize', () => handleResize.run());

  useEffect(() => {
    renderChart();
  }, [data]);

  return (
    <div className="forecast-chart-container">
      <Skeleton
        style={{ height, paddingTop: 38 }}
        active
        paragraph={{ rows: 8 }}
        loading={loading}
      >
        <div className="forecast-chart-container__detail">
          {data?.length ? (
            <XlbChart height={height} getInstance={getInstance} />
          ) : (
            <div className="no-data">暂无数据</div>
          )}
        </div>
      </Skeleton>
    </div>
  );
};
export default ForecastCharts;
