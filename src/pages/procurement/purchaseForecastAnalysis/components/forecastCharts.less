.forecast-chart-container {
  position: relative;
  height: 340px;
  &__detail {
    height: 100%;
    .no-data {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #4e5969;
    }
  }
}

.forecast-tooltip-container {
  padding: 2px;
  border-radius: 4px;
  .forecast-tooltip-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #1d2129;
  }
  .forecast-tooltip-content {
    margin-top: 6px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 4px 8px;
    .forecast-tooltip-item {
      display: flex;
      align-items: center;
      line-height: 20px;
      &::before {
        content: '';
        width: 4px;
        height: 4px;
        border-radius: 4px;
        background: #1a6aff;
        margin-right: 8px;
      }
      .forecast-tooltip-item-title {
        color: #4e5969;
        font-size: 14px;
        font-weight: normal;
        width: 100px;
      }
      .forecast-tooltip-item-value {
        margin-left: 8px;
        font-size: 15px;
        font-weight: bold;
      }
    }
  }
}
