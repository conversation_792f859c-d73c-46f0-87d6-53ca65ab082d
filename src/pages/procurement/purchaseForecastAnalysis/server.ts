import { XlbFetch } from '@xlb/utils';
import type { Response } from './types';

// 出库量预测
interface CommonRequest {
  category_ids: number[];
  item_ids: number[];
  org_ids: number[];
  store_ids: number[];
}
export interface OutQuantityResponse {
  date: string;
  doh: number;
  forecast_out_quantity: number;
  adjust_out_quantity: number;
  fulfillment_rate: number;
  otif: number;
  out_quantity: number;
  out_store: number;
  promotion_count: number;
  week: string;
  zero_stock_rate: number;
}
export const forecastOutQuantity = async (data: CommonRequest) => {
  return (await XlbFetch.post(
    '/erp/hxl.erp.purchaseforecast.outquantity',
    data,
  )) as Response<OutQuantityResponse[]>;
};

// 采购计划指标汇总
interface SummaryResponse {
  avl_stock_qty_difc: number;
  smape: number;
  stock_qty: number;
  store_count: number;
  total_avl_stock_qty: number;
  wmape: number;
}
export const forecastSummary = async (data: CommonRequest) => {
  return (await XlbFetch.post(
    '/erp/hxl.erp.purchaseforecast.summary',
    data,
  )) as Response<SummaryResponse>;
};

// 预测表格
export interface TableResponse {
  date: string;
  basic_stock_quantity: number;
  forecast_out_quantity: number;
  out_quantity: number;
  sale_quantity: number;
  week: string;
}
export const forecastTable = async (data: CommonRequest) => {
  return (await XlbFetch.post(
    '/erp/hxl.erp.purchaseforecast.table',
    data,
  )) as Response<TableResponse[]>;
};

// 预测表格-导出
export const forecastExport = async (data: CommonRequest) => {
  return (await XlbFetch.post(
    '/erp/hxl.erp.purchaseforecast.table.export',
    data,
  )) as Response<string>;
};

// 预测表格-导出
export const getActivityList = async (data: CommonRequest) => {
  return (await XlbFetch.post(
    '/erp/hxl.erp.purchaseforecast.promotions',
    data,
  )) as Response<string[]>;
};
