import { activityModal } from '@/pages/procurement/components/activityModal';
import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbInputDialog,
  XlbMessage,
} from '@xlb/components';
import { Spin } from 'antd';
import { useState } from 'react';
import AnalysisDataTable from './components/analysisDataTable';
import ForecastCharts from './components/forecastCharts';
import { ChartsTypes, DATA_LIST } from './data';
import './index.less';
import {
  forecastExport,
  forecastOutQuantity,
  forecastSummary,
  forecastTable,
  getActivityList,
} from './server';

const Index = () => {
  const [form] = XlbBasicForm.useForm();

  // search
  const [requestForm, setRequestForm] = useState<any>();
  // 指标
  const [summaryLoading, setSummaryLoading] = useState<boolean>(false);
  const [summaryData, setSummaryData] = useState<any>();
  const summarySearch = async (formData: any) => {
    setSummaryLoading(true);
    const res = await forecastSummary(formData);
    if (res?.code === 0) {
      setSummaryData(res.data);
    }
    setSummaryLoading(false);
  };
  // 出库量预测
  const [outLoading, setOutLoading] = useState<boolean>(false);
  const [outData, setOutData] = useState<any>();
  const outSearch = async (formData: any) => {
    setOutLoading(true);
    const res = await forecastOutQuantity(formData);
    if (res?.code === 0) {
      setOutData(res.data);
    }
    setOutLoading(false);
  };
  // 数据
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [tableData, setTableData] = useState<any>();
  const tableSearch = async (formData: any) => {
    setTableLoading(true);
    const res = await forecastTable(formData);
    if (res?.code === 0) {
      setTableData(res.data);
    }
    setTableLoading(false);
  };
  // 促销信息
  const [activityList, setActivityList] = useState<string[]>([]);
  const activitySearch = async (formData: any) => {
    const res = await getActivityList(formData);
    if (res?.code === 0) {
      setActivityList(res.data);
    }
  };
  const [loading, setLoading] = useState<boolean>(false);
  const search = (formData?: any) => {
    if (!formData) {
      setRequestForm(form.getFieldsValue(true));
    }
    const useForm = formData || form.getFieldsValue(true);
    if (Object.values(useForm).every((item: any) => !item?.length)) {
      XlbMessage.error('请至少选择一个查询条件');
      return;
    }
    setLoading(true);
    Promise.all([
      summarySearch(useForm),
      outSearch(useForm),
      tableSearch(useForm),
      activitySearch(useForm),
    ]).finally(() => setLoading(false));
  };

  // operate
  const exportItem = async (e: any) => {
    const res = await forecastExport(form.getFieldsValue(true));
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };

  return (
    <div className="forecast-analysis-container">
      <div className="analysis-detail-container filter-group">
        <XlbButton
          type="primary"
          label="查询"
          loading={loading}
          onClick={() => search()}
          icon={<span className="iconfont icon-sousuo" />}
        />
        <XlbBasicForm form={form} layout="inline" className="analysis-form mt8">
          <XlbBasicForm.Item label="组织" name="org_ids">
            <XlbInputDialog
              placeholder="请选择组织"
              treeModalConfig={{
                title: '选择组织',
                url: '/erp-mdm/hxl.erp.org.find',
                dataType: 'lists',
                checkable: true, // 是否多选
                primaryKey: 'id',
              }}
              handleOnChange={() => form.setFieldsValue({ store_ids: [] })}
              width={180}
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item label="门店" name="store_ids">
            <XlbInputDialog
              dependencies={['org_ids']}
              dialogParams={(data: any) => ({
                type: 'store',
                isMultiple: true,
                data: {
                  org_ids: data?.org_ids || [],
                  center_flag: true,
                },
              })}
              placeholder="请选择门店"
              fieldNames={{
                idKey: 'id',
                nameKey: 'store_name',
              }}
              width={180}
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item label="品类" name="category_ids">
            <XlbInputDialog
              treeModalConfig={{
                title: '选择商品分类', // 标题
                url: '/erp-mdm/hxl.erp.category.find', // 请求地址
                dataType: 'lists',
                checkable: true, // 是否多选
                primaryKey: 'id',
                data: {
                  enabled: true,
                },
                width: 360,
              }}
              placeholder="请选择品类"
              width={180}
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item label="商品" name="item_ids">
            <XlbInputDialog
              dialogParams={{
                type: 'goods',
                dataType: 'lists',
                isMultiple: true,
                data: {
                  status: 1,
                  supplierSwitch: false,
                },
              }}
              placeholder="请选择商品"
              width={180}
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      </div>

      <div className="analysis-detail-container mt12">
        <div className="analysis-detail-container__title">指标</div>
        {!!activityList?.length && (
          <div
            className="view-activity"
            onClick={() => {
              NiceModal.show(activityModal, {
                activeList: activityList,
              });
            }}
          >
            查看促销信息
          </div>
        )}
        <div className="analysis-detail-list">
          {summaryLoading && (
            <div className="mask-loading">
              <Spin />
            </div>
          )}
          {DATA_LIST.map((item) => (
            <div className="analysis-detail-list__item" key={item.key}>
              <span>{item.label}</span>
              <span className="analysis-detail-list__value">
                {summaryData?.[item.key] ?? 0}
              </span>
            </div>
          ))}
        </div>
      </div>

      <div className="analysis-detail-container mt12 is-charts">
        <div className="analysis-detail-container__title">
          {ChartsTypes.Outbound}量预测
        </div>
        <div className="analysis-detail-container__chart">
          <ForecastCharts
            height={340}
            data={outData}
            loading={outLoading}
            chartType={ChartsTypes.Outbound}
          />
        </div>
      </div>

      {/* TODO:先注释，后续迭代加 */}
      {/* <div className="analysis-detail-container mt12 is-charts">
        <div className="analysis-detail-container__title">
          {ChartsTypes.Sale}预测
        </div>
        <div className="analysis-detail-container__chart">
          <ForecastCharts
            height={340}
            data={testData2}
            loading={saleLoading}
            chartType={ChartsTypes.Sale}
            customData={{
              legend: {
                left: 112,
              },
            }}
          />
        </div>
      </div> */}

      <div className="analysis-detail-container mt12">
        <div className="analysis-detail-container__title">数据</div>
        <div className="analysis-detail-container__operate">
          {hasAuth(['需求预测', '导入']) && (
            <XlbButton
              icon={<XlbIcon size={16} name="daoru" />}
              type="text"
              disabled={!tableData?.length}
              className="operate-btn"
              onClick={async () => {
                const res = await XlbImportModal({
                  importUrl: '/erp/hxl.erp.purchaseforecast.table.import',
                  templateUrl:
                    '/erp/hxl.erp.purchaseforecast.template.download',
                  title: '导入数据',
                });
                if (res.code !== 0) return;
                search(requestForm);
              }}
            >
              导入
            </XlbButton>
          )}
          {hasAuth(['需求预测', '导出']) && (
            <XlbButton
              icon={<XlbIcon size={16} name="daochu" />}
              type="text"
              disabled={!tableData?.length}
              className="operate-btn"
              onClick={exportItem}
            >
              导出
            </XlbButton>
          )}
        </div>

        <div className="mt16 pr">
          {tableLoading && (
            <div className="mask-loading">
              <Spin />
            </div>
          )}
          <AnalysisDataTable data={tableData} />
        </div>
      </div>
    </div>
  );
};

export default Index;
