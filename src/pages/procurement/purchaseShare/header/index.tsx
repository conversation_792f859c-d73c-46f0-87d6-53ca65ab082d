import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import type { ContextState } from '@xlb/components';
import {
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbProPageContainerWithMemo,
  XlbTipsModal,
} from '@xlb/components';
import { DataType } from '@xlb/components/dist/components/XlbTree/type';
import { message } from 'antd';
import { type FC, useRef } from 'react';
import operateItemModal from './components/operateItemModal';
import { OPERATE_OPTIONS, operateTypes, typeOptions } from './data';
import {
  addPurchaseShare,
  deletePurchaseShare,
  detailsExport,
  updatePurchaseShare,
} from './server';
import type { OperateType } from './types';

const PurchaseShareIndex: FC<{ title: string }> = () => {
  const tableColumn = (tab: 'summary' | 'details') => {
    return [
      {
        name: '序号',
        code: '_index',
        width: 50,
        lock: true,
        align: 'center',
      },
      {
        name: '供应商代码',
        code: 'supplier_code',
        width: 140,
        render: (text: any, record: any) => (
          <div
            className="cursor link"
            onClick={(e) => {
              e.stopPropagation();
              operateItem(
                tab === 'summary' ? operateTypes.view : operateTypes.edit,
                record,
                tab,
              );
            }}
          >
            {text}
          </div>
        ),
      },
      {
        name: '供应商名称',
        code: 'supplier_name',
        width: 280,
        features: { sortable: true },
        align: 'left',
      },
      {
        name: '共享类型',
        code: 'share_type',
        width: 120,
        features: { sortable: true },
        align: 'left',
        render: (text) => {
          const item = typeOptions?.find((v) => v?.value === text);
          return <span>{item?.label}</span>;
        },
      },
      {
        name: '采购共享中心',
        code: 'store_name',
        width: 280,
        features: { sortable: true, tips: '采购收退货单内的收、退货门店' },
        align: 'left',
      },
      {
        name: '实际门店',
        code: 'share_store_names',
        width: 280,
        features: { sortable: true, tips: '采购收退货单内的实际收、退货门店' },
        align: 'left',
        render: (value: string[], record: any) => {
          if (value?.length > 1) {
            return (
              <div className="v-flex">
                {value[0]}
                <div
                  className="cursors blue ml-auto"
                  onClick={(e: any) => {
                    e.stopPropagation();
                    operateItem(operateTypes.view, record);
                  }}
                >
                  更多
                </div>
              </div>
            );
          }
          return <div>{value?.[0]}</div>;
        },
      },
    ];
  };

  const detailContextRef = useRef<ContextState>();
  const summaryContextRef = useRef<ContextState>();
  const getPromise = async (type: OperateType, modalValues: any) => {
    if (type === operateTypes.add) {
      return addPurchaseShare(modalValues);
    }
    if (type === operateTypes.edit) {
      return updatePurchaseShare(modalValues);
    }
    return Promise.resolve(() => ({ code: 0 }));
  };
  const operateItem = (
    type: OperateType,
    dataItem?: any,
    tab?: 'details' | 'summary',
  ) => {
    NiceModal.show(operateItemModal, {
      type,
      title: OPERATE_OPTIONS.find((item) => item.value === type)?.label,
      dataItem,
      tab,
      onOk: async (modalValues) => {
        const res = await getPromise(type, modalValues);
        if (res.code === 0) {
          message.success('操作成功');
          detailContextRef.current?.fetchData &&
            detailContextRef.current.fetchData();
          summaryContextRef.current?.fetchData &&
            summaryContextRef.current.fetchData();
          return true;
        }
        return false;
      },
    });
  };
  const deleteItem = async (keys: string[] | undefined, fetchData: any) => {
    if (!keys?.length) return;
    XlbTipsModal({
      tips: `已选择${keys?.length}条数据，是否确认？`,
      isCancel: true,
      onOk: async () => {
        const res = await deletePurchaseShare({ ids: keys.map(Number) });
        if (res.code === 0) {
          message.success('删除成功');
          fetchData && fetchData();
          return true;
        }
        return false;
      },
    });
  };

  const importItem = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp-purchase/hxl.erp.suppliersharedconfig.import`,
      templateUrl: `${process.env.BASE_URL}/erp-purchase/hxl.erp.suppliersharedconfig.template.download`,
      params: {},
    });
  };

  const exportItem = async (form: any, e: any) => {
    const res = await detailsExport({ ...form });
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      message.success('导出受理成功，请前往下载中心查看');
    }
  };

  return (
    <XlbProPageContainerWithMemo
      treeFieldProps={{
        leftUrl: '/erp/hxl.erp.org.tree',
        dataType: DataType.LISTS,
        isWithChild: true,
        leftKey: 'org_ids',
      }}
      tabFieldProps={{
        defaultActiveKey: 'details',
        name: 'tabKey',
        items: [
          {
            label: '明细',
            key: 'details',
            children: {
              searchFieldProps: {
                formList: [
                  {
                    label: '供应商',
                    id: ErpFieldKeyMap.erpSupplierIds,
                  },
                  {
                    id: ErpFieldKeyMap.erpPurchaseShare,
                    label: '采购共享中心',
                  },
                  {
                    id: ErpFieldKeyMap.erpActualStore,
                    label: '实际门店',
                  },
                  {
                    id: 'commonSelect',
                    label: '共享类型',
                    name: 'share_type',
                    options: typeOptions,
                  },
                ],
              },
              extra: (content: ContextState) => {
                const { requestForm } = content;
                detailContextRef.current = content;
                return (
                  <>
                    {hasAuth(['采购共享配置', '导出']) && (
                      <XlbButton
                        type="primary"
                        disabled={!content?.dataSource?.length}
                        icon={<XlbIcon name="daochu" />}
                        onClick={(e: any) => exportItem(requestForm, e)}
                      >
                        导出
                      </XlbButton>
                    )}
                    {hasAuth(['采购共享配置', '编辑']) && (
                      <XlbButton
                        type="primary"
                        icon={<XlbIcon name="jia" />}
                        onClick={() => operateItem(operateTypes.add)}
                      >
                        新增
                      </XlbButton>
                    )}
                    {hasAuth(['采购共享配置', '删除']) && (
                      <XlbButton
                        type="primary"
                        key={content?.selectRow?.join(',')}
                        disabled={!content?.selectRowKeys?.length}
                        icon={<XlbIcon name="shanchu" />}
                        onClick={() =>
                          deleteItem(content?.selectRowKeys, content?.fetchData)
                        }
                      >
                        删除
                      </XlbButton>
                    )}
                    {hasAuth(['采购共享配置', '导入']) && (
                      <XlbButton
                        type="primary"
                        disabled={!content?.dataSource?.length}
                        icon={<XlbIcon name="daoru" />}
                        onClick={importItem}
                      >
                        导入
                      </XlbButton>
                    )}
                  </>
                );
              },
              tableFieldProps: {
                url: '/erp-purchase/hxl.erp.suppliersharedconfig.find',
                tableColumn: tableColumn('details'),
                selectMode: 'multiple',
                showColumnsSetting: false,
                changeColumnAndResetDataSource: false,
                keepDataSource: false,
                immediatePost: true,
              },
            },
          },
          {
            label: '汇总',
            key: 'summary',
            children: {
              searchFieldProps: {
                formList: [
                  {
                    label: '供应商',
                    id: ErpFieldKeyMap.erpSupplierIds,
                  },
                  {
                    id: ErpFieldKeyMap.erpPurchaseShare,
                    label: '采购共享中心',
                  },
                  {
                    id: ErpFieldKeyMap.erpActualStore,
                    label: '实际门店',
                  },
                  {
                    id: 'commonSelect',
                    label: '共享类型',
                    name: 'share_type',
                    options: typeOptions,
                  },
                ],
              },
              extra: (content: ContextState) => {
                summaryContextRef.current = content;
                return (
                  <>
                    {hasAuth(['采购共享配置', '编辑']) && (
                      <XlbButton
                        type="primary"
                        icon={<XlbIcon name="jia" />}
                        onClick={() => operateItem(operateTypes.add)}
                      >
                        新增
                      </XlbButton>
                    )}
                    {hasAuth(['采购共享配置', '删除']) && (
                      <XlbButton
                        type="primary"
                        key={content?.selectRow?.join(',')}
                        disabled={!content?.selectRowKeys?.length}
                        icon={<XlbIcon name="shanchu" />}
                        onClick={() =>
                          deleteItem(content?.selectRowKeys, content?.fetchData)
                        }
                      >
                        删除
                      </XlbButton>
                    )}
                  </>
                );
              },
              tableFieldProps: {
                url: '/erp-purchase/hxl.erp.suppliersharedconfig.summary.find',
                tableColumn: tableColumn('summary'),
                selectMode: 'multiple',
                showColumnsSetting: false,
                changeColumnAndResetDataSource: false,
                keepDataSource: false,
                immediatePost: true,
              },
            },
          },
        ],
      }}
    />
  );
};
export default PurchaseShareIndex;
