import { columnWidthEnum } from '@/data/common/constant';
import { hasAuth } from '@/utils';
import { XlbSwitch } from '@xlb/components';
import { message } from 'antd';
import { updateMinDeliveryConfig } from './server';

export const summaryType = [
  {
    label: '组织',
    value: 'ORGANIZATION',
    weight: 10,
  },
  {
    label: '门店类型',
    value: 'MANAGEMENTTYPE',
    weight: 20,
  },
  {
    label: '业务区域',
    value: 'BUSINESSAREA',
    weight: 30,
  },
  {
    label: '省',
    value: 'PROVINCE',
    weight: 40,
  },
  {
    label: '市',
    value: 'CITY',
    weight: 50,
  },
  {
    label: '区',
    value: 'DISTRICT',
    weight: 60,
  },
  {
    label: '门店',
    value: 'STORE',
    weight: 70,
  },
  // {
  //   label: '设备',
  //   value: 'DEVICE'
  // }
];

export const typeOptions = [
  {
    label: '单仓单品',
    value: 'single.item',
  },
  {
    label: '供应商单仓品类',
    value: 'single.category',
  },
  {
    label: '供应商单仓',
    value: 'single.warehouse',
  },
];
export const categoryOptions = [
  {
    label: '体积',
    value: 'volume',
  },
  {
    label: '重量',
    value: 'weight',
  },
  {
    label: '单位',
    value: 'unit',
  },
  {
    label: '金额',
    value: 'money',
  },
];
// const [dataSource, setDataSource] = useState<any[]>([])

export const itemsColumns = [
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.fid,
    features: { sortable: true },
    render: (text, record) => {
      return record?.item_res_dtos?.[0]?.code;
    },
  },
  {
    name: '商品档案',
    code: 'item_name',
    width: columnWidthEnum.fid,
    features: { sortable: true },
    render: (text, record) => {
      return record?.item_res_dtos?.[0]?.name;
    },
  },
];

export const tableColumn = [
  {
    name: '序号',
    code: '_index',
    width: 50,
  },
  {
    name: '组织',
    code: 'center_org_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '配送中心',
    code: 'center_store_id',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    render(text, record) {
      return record?.center_store_name;
    },
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: columnWidthEnum.fid,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_id',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true, details: true },
    render(text, record) {
      return record?.supplier_name;
    },
  },
];

export const categoryColumn = [
  {
    name: '商品品类',
    code: 'item_category_name',
    width: columnWidthEnum.fid,
    features: { sortable: true },
    render: (text, record) => {
      return record?.category_res_dtos?.map((v) => v?.name)?.join(',');
    },
  },
];

export const basicColumns = [
  {
    name: '起订量类型',
    code: 'type',
    width: 120,
    features: { sortable: true },
    render: (text) => {
      const item = typeOptions?.find((v) => v?.value === text);
      return <span>{item?.label}</span>;
    },
  },
  {
    name: '起订量维度',
    code: 'dimension',
    width: 120,
    features: { sortable: true },
    render: (text) => {
      const item = categoryOptions?.find((v) => v?.value === text);
      return <span>{item?.label}</span>;
    },
  },
  {
    name: '起订量系数',
    code: 'threshold_value',
    features: { sortable: true },
  },
  {
    name: '起订量',
    code: 'quantity',
    features: { sortable: true },
  },
  {
    name: '是否强校验',
    code: 'strong_validation',
    width: 160,
    render(text, record, operate) {
      return (
        <div className="v-flex">
          <XlbSwitch
            value={Boolean(text)}
            loading={record?._loading}
            disabled={!hasAuth(['起送量配置', '编辑'])}
            onChange={async (e: boolean) => {
              record._loading = true;
              const res = await updateMinDeliveryConfig({
                ...record,
                strong_validation: e,
              });
              if (res.code === 0) {
                message.success('更新成功');
                record.strong_validation = e;
              }
              operate?.fetchData?.();
            }}
          />
          <span style={{ marginLeft: 8 }}>{text ? '是' : '否'}</span>
        </div>
      );
    },
  },
];
