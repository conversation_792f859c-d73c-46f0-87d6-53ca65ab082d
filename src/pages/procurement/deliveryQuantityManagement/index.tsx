import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { XlbProPageContainer } from '@xlb/components';
import dayjs from 'dayjs';
import {
  basicColumns,
  categoryColumn,
  categoryOptions,
  itemsColumns,
  tableColumn,
  typeOptions,
} from './data';

const DeliveryQuantityManagement = () => {
  const { enable_organization } = useBaseParams((state) => state);
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        initialValues: {
          date: [dayjs()?.format('YYYY-MM-DD'), dayjs()?.format('YYYY-MM-DD')],
        },
        formList: [
          // TODO: 待后端完成后添加
          // {
          //   id: ErpFieldKeyMap?.erpOrgIdsMultiple,
          //   name: 'org_ids',
          //   label: '组织',
          //   onChange: (_, form) => {
          //     form?.setFieldsValue({ center_store_ids: [] });
          //   },
          // },
          {
            label: '组织',
            name: 'org_ids',
            id: ErpFieldKeyMap?.erpOrgIdsLevel2,
            hidden: !enable_organization,
          },
          {
            id: ErpFieldKeyMap?.erpCenteMultipleStoreId,
            name: 'center_store_ids',
            label: '配送中心',
          },
          {
            id: ErpFieldKeyMap?.erpSupplierIds,
          },
          'erpitemIds',
          {
            id: ErpFieldKeyMap.erpStoreStatus,
            name: 'strong_validation',
            label: '是否强校验',
          },
          {
            id: 'commonSelect',
            label: '起订量类型',
            name: 'types',
            fieldProps: { mode: 'multiple' },
            options: typeOptions,
          },
          {
            id: 'commonSelect',
            label: '起订量维度',
            name: 'dimensions',
            fieldProps: { mode: 'multiple' },
            options: categoryOptions,
          },
        ],
      }}
      addFieldProps={{
        url: hasAuth(['起订量配置', '编辑'])
          ? '/erp-purchase/hxl.erp.mindeliveryconfig.save'
          : undefined,
        beforePost: (data) => {
          return {
            ...data,
          };
        },
      }}
      deleteFieldProps={{
        name: '删除',
        url: hasAuth(['起订量配置', '删除'])
          ? '/erp-purchase/hxl.erp.mindeliveryconfig.delete'
          : '',
        params: (data: any, v: any) => {
          return { id: v[0].id };
        },
      }}
      details={{
        width: 500,
        mode: 'modal',
        hiddenSaveBtn: true,
        isCancel: true,
        primaryKey: 'id',
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>;
        },
        queryFieldProps: {
          url: hasAuth(['起订量配置', '查询'])
            ? '/erp-purchase/hxl.erp.mindeliveryconfig.read'
            : '',
          params: (row: any) => {
            return {
              id: row.id,
            };
          },
          afterPost(res) {
            console.log('rrrr:', res);
            return {
              ...res,
              item_ids: res?.item_res_dtos?.length
                ? res.item_res_dtos?.map((item: any) => item.id)
                : [],
              item_category_ids: res?.category_res_dtos?.length
                ? res.category_res_dtos?.map((item: any) => item.id)
                : [],
            };
          },
        },
        updateFieldProps: {
          url: hasAuth(['起订量配置', '编辑'])
            ? '/erp-purchase/hxl.erp.mindeliveryconfig.update'
            : undefined,
          beforePost: (data) => {
            return {
              ...data,
              category_res_dtos: undefined,
              item_res_dtos: undefined,
            };
          },
        },
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: 'erpSupplierId',
                  itemSpan: 22,
                  name: 'supplier_id',
                  label: '供应商',
                  rules: [{ required: true, message: '请选择供应商' }],
                },
                {
                  id: 'erpCenterSingleStoreId',
                  itemSpan: 22,
                  label: '配送中心',
                  name: 'center_store_id',
                  fieldProps: {
                    placeholder: '请选择',
                  },
                  rules: [{ required: true, message: '请选择配送中心' }],
                },
                {
                  id: 'commonSelect',
                  label: '起订量类型',
                  name: 'type',
                  options: typeOptions,
                  itemSpan: 22,
                  rules: [{ required: true, message: '请选择起订量类型' }],
                  onChange: (e, form) => {
                    form?.setFieldsValue({
                      item_ids: [],
                      item_category_ids: [],
                    });
                  },
                },
                {
                  id: 'erpitemIds',
                  itemSpan: 22,
                  name: 'item_ids',
                  label: '商品档案',
                  dependencies: ['type'],
                  rules: [
                    ({ getFieldValue }) => {
                      return {
                        required:
                          getFieldValue('type') === 'single.item'
                            ? true
                            : false,
                        message: '请选择商品档案',
                      };
                    },
                  ],
                  fieldProps: {
                    dialogParams: {
                      type: 'goods',
                      dataType: 'lists',
                      isMultiple: false,
                    },
                  },
                  disabled: (formValues: any) => {
                    return (
                      formValues?.type === 'single.category' ||
                      formValues?.type === 'single.warehouse'
                    );
                  },
                },

                {
                  label: '商品品类',
                  name: 'item_category_ids',
                  id: ErpFieldKeyMap?.erpRateStatisticsItemCategory,
                  rules: [
                    ({ getFieldValue }) => {
                      return {
                        required:
                          getFieldValue('type') === 'single.category'
                            ? true
                            : false,
                        message: '请选择商品品类',
                      };
                    },
                  ],
                  fieldProps: {
                    treeModalConfig: {
                      title: '选择商品分类', // 标题
                      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
                      dataType: 'lists',
                      checkable: true, // 是否多选
                      primaryKey: 'id',
                      params: {
                        enabled: true,
                        level: 2,
                      },
                      width: 360, // 模态框宽度
                    },
                  },
                  itemSpan: 22,
                  dependencies: ['type'],
                  hidden: (formValues) => {
                    return formValues?.type !== 'single.category';
                  },
                },
                {
                  id: ErpFieldKeyMap?.purchasePlanBuyNumber,
                  label: '起订量',
                  name: 'quantity',
                  itemSpan: 22,
                  fieldProps: { placeholder: '请输入' },
                  rules: [
                    { required: true, message: '请输入起订量' },
                    { pattern: /^(0|[1-9]\d*)$/, message: '请输入整数' },
                  ],
                },
                {
                  id: 'commonSelect',
                  label: '起订量维度',
                  name: 'dimension',
                  options: categoryOptions,
                  itemSpan: 22,
                  rules: [{ required: true, message: '请选择起订量维度' }],
                },
                {
                  id: ErpFieldKeyMap?.purchasePlanBuyNumber,
                  label: '起订量系数',
                  name: 'threshold_value',
                  itemSpan: 22,
                  fieldProps: {
                    placeholder: '请输入',
                    min: 0,
                    precision: 2,
                  },
                  rules: [
                    { required: true, message: '请输入起订量系数' },
                    {
                      validator: (_, value) => {
                        if (value <= 0 || value >= 1) {
                          return Promise.reject(
                            new Error('请输入大于0小于1的小数'),
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      }}
      tableFieldProps={{
        url: hasAuth(['起订量配置', '查询'])
          ? '/erp-purchase/hxl.erp.mindeliveryconfig.page'
          : undefined,
        tableColumn: (formValues) => {
          const extendColumnsMap = {
            'single.item': itemsColumns,
            'single.category': categoryColumn,
          };
          const selectedTypes = formValues?.types || [];
          const extraColumns = (
            selectedTypes.length > 0
              ? selectedTypes
                  .map((type) => extendColumnsMap[type])
                  .filter(Boolean)
              : Object.values(extendColumnsMap)
          ).flat();
          return [...tableColumn, ...extraColumns, ...basicColumns];
        },
        selectMode: 'single',
      }}
      uploadFieldProps={{
        order: 30,
        url: hasAuth(['起订量配置', '导入'])
          ? '/erp-purchase/hxl.erp.mindeliveryconfig.import'
          : '',
        templateUrl: '/erp-purchase/hxl.erp.mindeliveryconfig.download',
      }}
      // 导出
      // exportFieldProps={{
      //   url: hasAuth(['设备分析', '导出'])
      //     ? '/erp/hxl.erp.alipaymetricsdevice.export'
      //     : '',
      //   fileName: '设备分析.xlsx',
      // }}
    />
  );
};

export default DeliveryQuantityManagement;
