import NiceModal from '@ebay/nice-modal-react';
import type { XlbTableColumnProps } from '@xlb/components';
import { XlbModal, XlbTable } from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { useEffect, useState } from 'react';

const Index = (props: any) => {
  const { params } = props;
  const [dataSource, setDataSource] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const modal = NiceModal.useModal();
  const getTableInfo = async () => {
    setLoading(true);
    const historyParams = {
      ...params,
    };
    const res = await XlbFetch.post(
      process.env.BASE_URL + '/erp-purchase/hxl.erp.storeitemsupplierlog.page',
      { ...historyParams },
    );
    if (res?.code === 0) {
      setDataSource(res?.data?.content);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (modal.visible) {
      getTableInfo();
    }
  }, [modal.visible]);

  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 50,
      align: 'center',
    },
    {
      name: '变更字段',
      code: 'field_name',
      width: 100,
    },
    {
      name: '原先内容',
      code: 'old_value',
      width: 150,
      features: { sortable: true, format: 'TIME' },
    },
    {
      name: '变更后内容',
      code: 'new_value',
      width: 150,
    },
    {
      name: '操作时间',
      code: 'create_time',
      width: 220,
    },
    {
      name: '操作人',
      code: 'create_by',
      width: 150,
    },
  ];

  return (
    <XlbModal
      wrapClassName={'xlbDialog'}
      title={'修改记录'}
      keyboard={false}
      open={modal.visible}
      maskClosable={false}
      onCancel={() => modal.hide()}
      onOk={() => modal.hide()}
      width={800}
      zIndex={2012}
      centered
    >
      <XlbTable
        columns={tableList}
        dataSource={dataSource}
        selectMode="single"
        style={{ height: 400 }}
        primaryKey="id"
        total={dataSource?.length}
        loading={loading}
      />
    </XlbModal>
  );
};

export default NiceModal.create(Index);
