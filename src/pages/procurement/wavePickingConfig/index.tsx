import { getAllStoreList } from '@/api/common';
import { columnWidthEnum, MAX_INT } from '@/constants';
import { hasAuth } from '@/utils/kit';
import NiceModal from '@ebay/nice-modal-react';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbPageContainer,
  XlbProPageContainerProps,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbSwitch,
  XlbTableColumnProps,
} from '@xlb/components';
import { message } from 'antd';
import { FC, useEffect, useRef, useState } from 'react';
import { AddItemModal } from './component/addItemModal';
import { ENABLE_LIST, getFormList } from './data';
import Item from './item';
import { updateWavePickingConfig } from './server';

const Index: FC = () => {
  // from
  const [form] = XlbBasicForm.useForm();
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbProPageContainerProps>(null);
  const [record, setRecord] = useState<any>({});
  const prevPost = () => ({ ...form.getFieldsValue(true) });

  // table 配置
  const tableColumn: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
    },
    {
      name: '收货门店',
      code: 'store_name',
      width: columnWidthEnum.STORE_NAME,
      features: { sortable: true },
      render: (text: any, record: any, operate: any) => {
        return (
          <div
            className="xlb-table-clickBtn"
            onClick={() => {
              NiceModal.show(AddItemModal, {
                title: '波次配置',
                type: 'store',
                updateData: record,
                fetchData: operate?.fetchData,
              });
            }}
          >
            {text}
          </div>
        );
      },
    },
    {
      name: '波次配置',
      code: 'time',
      width: 160,
      align: 'center',
      render: (_text: any, record: any) => {
        return (
          <div>
            {record?.config?.times
              .sort((a: number, b: number) => a - b)
              ?.join(',')}
          </div>
        );
      },
    },
    {
      name: '状态',
      code: 'enable',
      width: 160,
      align: 'center',
      render: (text: any, record: any, operate: any) => {
        return (
          <div className="v-flex">
            <XlbSwitch
              value={text === 1}
              loading={record?._loading}
              disabled={!hasAuth(['波次配置', '编辑'], 'ERP')}
              onChange={async (e) => {
                record._loading = true;
                const res = await updateWavePickingConfig({
                  ...record,
                  enable: Number(e),
                });
                if (res.code === 0) {
                  message.success('更新成功');
                  record.enable = Number(e);
                }
                operate?.fetchData?.();
              }}
            />
            <span style={{ marginLeft: 8 }}>
              {ENABLE_LIST.find((item) => item.value === text)?.label}
            </span>
          </div>
        );
      },
    },
    {
      name: '最新更新人',
      code: 'update_by',
      width: 160,
    },
    {
      name: '最近更新时间',
      code: 'update_time',
      width: columnWidthEnum.TIME,
      features: { sortable: true, format: 'TIME' },
    },
    {
      name: '操作',
      code: 'operator',
      width: 120,
      align: 'center',
      render: (value, record) => {
        return (
          <div
            className="link"
            onClick={() => {
              console.log('00--:', record);
              setRecord({ id: record?.id });
              pageModalRef.current?.setOpen(true);
            }}
          >
            详情
          </div>
        );
      },
    },
  ];

  // filterData
  interface OptionType {
    label: string;
    value: number;
  }
  const [storeList, setStoreList] = useState<OptionType[]>([]);
  const getStoreList = async () => {
    const res = await getAllStoreList({
      center_flag: true,
      page_size: MAX_INT,
      enable_organization: false,
    });
    if (res?.code === 0) {
      const formatList =
        res.data?.content?.map((item: any) => ({
          label: item.store_name,
          value: item.id,
        })) || [];
      setStoreList(formatList);
    }
  };
  useEffect(() => {
    getStoreList();
  }, []);

  // operate
  const addItem = async (fetchData: () => void) => {
    await NiceModal.show(AddItemModal, {
      title: '波次配置',
      type: 'store',
      fetchData,
    });
  };

  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <Item
              onBack={(isFresh) => {
                if (isFresh) {
                  pageConatainerRef?.current?.fetchData?.();
                }

                pageModalRef.current?.setOpen(false);
              }}
              record={record}
            ></Item>
          );
        }}
      ></XlbProPageModal>
      <XlbPageContainer
        ref={pageConatainerRef}
        url={'/erp-purchase/hxl.erp.wavepickingconfig.find'}
        tableColumn={tableColumn}
        immediatePost
        prevPost={prevPost}
      >
        <XlbPageContainer.ToolBtn showColumnsSetting>
          {({ fetchData, loading }: ContextState<any>) => {
            return (
              <XlbButton.Group>
                {hasAuth(['波次配置', '查询'], 'ERP') && (
                  <XlbButton
                    label="查询"
                    type="primary"
                    loading={loading}
                    onClick={() => fetchData()}
                    icon={<XlbIcon name="sousuo" />}
                  />
                )}
                {hasAuth(['波次配置', '编辑'], 'ERP') && (
                  <XlbButton
                    label="新增"
                    type="primary"
                    icon={<XlbIcon name="jia" />}
                    onClick={() => addItem(fetchData)}
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </XlbPageContainer.ToolBtn>

        <XlbPageContainer.SearchForm>
          <XlbForm
            form={form}
            formList={getFormList({ storeList })}
            isHideDate
          />
        </XlbPageContainer.SearchForm>

        <XlbPageContainer.Table selectMode="single" keepDataSource={false} />
      </XlbPageContainer>
    </>
  );
};
export default Index;
