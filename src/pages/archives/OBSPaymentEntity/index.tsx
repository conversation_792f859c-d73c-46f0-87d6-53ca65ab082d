import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { coverListToTree } from '@/utils/kit';
import {
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbInput,
  XlbInputDialog,
  XlbPageContainer,
  XlbSelect,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { message } from 'antd';
import React, { FC, useEffect, useRef } from 'react';
import Api, { currencyEmail, payAccountEmail } from './data';
import styles from './index.less';

const { ToolBtn, Table } = XlbPageContainer;

type DataSource = {
  id: number;
  name: string;
  age?: number;
  sex?: number;
  job?: string;
};

const Demo: FC = () => {
  const { enable_organization } = useBaseParams((state) => state);
  const tableColumn: XlbTableColumnProps<DataSource>[] = [
    {
      name: '序号',
      code: '_index',
      width: 70,
      align: 'center',
    },
    {
      name: '组织',
      code: 'org_name',
      width: 140,
      hidden: !enable_organization,
    },
    {
      name: '付款主体',
      code: 'name',
      width: 200,
    },
    {
      name: '付款账号',
      code: 'pay_account',
      width: 190,
    },
    {
      name: '开户行',
      code: 'pay_account_name',
      width: 216,
    },
    {
      name: '账户信息',
      code: 'pay_account_type',
      width: 106,
      render(text: any) {
        return payAccountEmail[text];
      },
    },
    {
      name: '币种',
      code: 'currency',
      width: 88,
      render(text: any) {
        return currencyEmail[text];
      },
    },
    {
      name: '操作',
      code: 'options',
      width: 74,
      align: 'left',
      render(_text: any, record: any) {
        return (
          <XlbButton
            key={record.id}
            type="link"
            onClick={() => deleteItem(record)}
          >
            删除
          </XlbButton>
        );
      },
    },
  ];
  const [form] = XlbBasicForm.useForm();
  const ref = useRef<XlbPageContainerRef>(null);

  const onDragSortEnd = async (dataSource: any) => {
    const res = await Api.updateStoreCBSPaymentEntity({
      ids: dataSource.map((item: any) => item.id),
    });
  };

  const deleteItem = async (record: any) => {
    if (!hasAuth(['CBS付款主体', '编辑'])) return;
    XlbTipsModal({
      title: `删除付款主体`,
      tips: `${record.name}将被删除，是否继续？`,
      isCancel: true,
      getContainer: false,
      rootClassName: 'xlbSaveTemplate',
      onOkBeforeFunction: async () => {
        const res = await Api.deleteCBSPaymentEntity({ id: record.id });
        if (res.code === 0) {
          message.success('删除成功');
          ref.current?.fetchData();
          return true;
        }
      },
    });
  };

  const saveItem = async () => {
    form.resetFields();
    await XlbTipsModal({
      title: '新增付款主体',
      tips: (
        <XlbBasicForm
          form={form}
          autoComplete="off"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 16 }}
        >
          {enable_organization && (
            <XlbBasicForm.Item
              label="组织"
              name="org_id"
              style={{ width: '100%' }}
              rules={[{ required: true, message: '请选择组织' }]}
            >
              <XlbInputDialog
                treeModalConfig={{
                  title: '选择组织',
                  topLevelTreeDisabled: true,
                  url: '/erp/hxl.erp.org.tree',
                  dataType: 'lists',
                  checkable: false,
                  primaryKey: 'id',
                }}
                style={{ width: 267 }}
              />
            </XlbBasicForm.Item>
          )}
          <XlbBasicForm.Item
            label="付款主体"
            name="name"
            rules={[{ required: true, message: '请输入付款主体' }]}
            style={{ width: '100%' }}
          >
            <XlbInput style={{ width: '100%' }} maxLength={25} />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="付款账号"
            name="pay_account"
            rules={[
              { required: true, message: '请输入付款账号' },
              { pattern: /^[0-9]{0,}$/, message: '请输入纯数字' },
            ]}
            style={{ width: '100%' }}
          >
            <input className={styles.inputNumber} maxLength={50} step={0} />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="开户行"
            name="pay_account_name"
            rules={[{ required: true, message: '请输入开户行' }]}
            style={{ width: '100%' }}
          >
            <XlbInput style={{ width: '100%' }} maxLength={25} />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="账户信息"
            name="pay_account_type"
            rules={[{ required: true, message: '请选择账户信息' }]}
            style={{ width: '100%' }}
          >
            <XlbSelect style={{ width: '100%' }}>
              {Object.keys(payAccountEmail).map((item: any) => (
                <XlbSelect.Option key={item} value={item}>
                  {payAccountEmail[item]}
                </XlbSelect.Option>
              ))}
            </XlbSelect>
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            label="币种"
            name="currency"
            initialValue={'CNY'}
            rules={[{ required: true, message: '请选择币种' }]}
            style={{ width: '100%' }}
          >
            <XlbSelect style={{ width: '100%' }}>
              {Object.keys(currencyEmail).map((item: any) => (
                <XlbSelect.Option key={item} value={item}>
                  {currencyEmail[item]}
                </XlbSelect.Option>
              ))}
            </XlbSelect>
          </XlbBasicForm.Item>
        </XlbBasicForm>
      ),
      onOkBeforeFunction: async () => {
        try {
          await form.validateFields();
        } catch (err: any) {
          return false;
        }
        const params = form.getFieldsValue();
        if (enable_organization) {
          params.org_id = params?.org_id[params?.org_id?.length - 1];
        }
        const res = await Api.saveCBSPaymentEntity(params);
        if (res.code === 0) {
          message.success('保存成功');
          ref.current?.fetchData();
          form.resetFields();
          return true;
        }
      },
    });
  };

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.cbscashbank.find'}
      tableColumn={tableColumn}
      immediatePost
      searchFormProps={{
        hideToggle: true,
      }}
      ref={ref}
    >
      <ToolBtn showColumnsSetting={false} withHLine={false}>
        {() => {
          return (
            <XlbButton.Group>
              {hasAuth(['CBS付款主体', '编辑']) && (
                <XlbButton
                  type="primary"
                  onClick={saveItem}
                  icon={<XlbIcon size={16} name="jia" />}
                >
                  新增
                </XlbButton>
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table dragSort={true} onDragSortEnd={onDragSortEnd} />
    </XlbPageContainer>
  );
};

export default Demo;