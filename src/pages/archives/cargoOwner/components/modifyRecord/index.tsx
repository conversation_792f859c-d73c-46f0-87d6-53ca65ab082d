import NiceModal from '@ebay/nice-modal-react';
import type { XlbTableColumnProps } from '@xlb/components';
import { XlbModal, XlbTable } from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { useEffect, useState } from 'react';

const Index = () => {
  //   const { relateStoreRecord, storeIds } = props;
  const [dataSource, setDataSource] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const modal = NiceModal.useModal();
  const getTableInfo = async () => {
    setLoading(true);
    const res = await XlbFetch.post(
      process.env.BASE_URL + '/erp/hxl.erp.cargo.owner.log.page',
      {},
    );
    if (res?.code === 0) {
      setDataSource(res?.data?.content);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (modal.visible) {
      getTableInfo();
    }
  }, [modal.visible]);

  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 50,
      align: 'center',
    },
    {
      name: '修改人',
      code: 'update_by',
      width: 100,
    },
    {
      name: '修改时间',
      code: 'update_time',
      width: 150,
      features: { sortable: true, format: 'TIME' },
    },
    {
      name: '修改内容',
      code: 'log_content',
      width: 300,
    },
  ];

  return (
    <XlbModal
      wrapClassName={'xlbDialog'}
      title={'修改记录'}
      keyboard={false}
      open={modal.visible}
      maskClosable={false}
      onCancel={() => modal.hide()}
      onOk={() => modal.hide()}
      width={800}
      zIndex={2012}
      centered
    >
      <XlbTable
        columns={tableList}
        dataSource={dataSource}
        selectMode="single"
        style={{ height: 400 }}
        primaryKey="id"
        total={dataSource?.length}
        loading={loading}
      />
    </XlbModal>
  );
};

export default NiceModal.create(Index);
