.box {
  position: relative;
  // width: 860px;
  margin: 25px auto;
  padding: 10px;
  border: 1px solid #d8d8d8;
  border-radius: 5px;

  .title {
    position: absolute;
    top: -13px;
    left: 20px;
    padding: 0 13px;
    background: white;
  }
  :global .ant-checkbox-group {
    gap: 8px;
  }
  :global .ant-form-item {
    display: inline-block;
    margin: 0 20px 0 10px;
  }

  :global .ant-space-item {
    display: inline-block;
  }

  :global label.ant-checkbox-wrapper.ant-checkbox-wrapper-in-form-item {
    width: 100px;
  }

  :global .ant-radio-wrapper {
    line-height: 26px;
  }

  :global .ant-input-affix-wrapper > .ant-input {
    height: 26px;
  }

  :global .ant-select {
    display: inline-block;
    width: 140px;
  }

  :global .ant-input-suffix {
    height: 26px;
  }

  // :global .ant-checkbox-wrapper {
  //   width: 120px;
  // }
}

.copyProgress {
  position: absolute;
  top: 40px;
  left: 0;
  z-index: 100;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 90%;
  padding: 40px;
  background-color: rgba(255, 255, 255, 1);

  &__title {
    height: 24px;
    color: #1d2129;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
  }

  &__text {
    height: 22px;
    color: rgba(131, 138, 150, 1);
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
  }

  &__progressText {
    position: absolute;
    top: 8;
    left: 0;
    bottom: 0;
    right: 0;
    text-align: center;
    color: white;
    z-index: 100;
  }
  &__confirm {
    color: white !important;
    background: rgba(245, 63, 63, 1) !important;
    border-color: rgba(245, 63, 63, 1) !important;
    border: 1px solid rgba(245, 63, 63, 1) !important;
  }
}
