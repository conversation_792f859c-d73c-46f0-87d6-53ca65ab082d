import { hasAuth } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import { LStorage } from '@/utils/storage';
import { XlbInputDialog, XlbModal, XlbTipsModal } from '@xlb/components';
import { Checkbox, Form, message, Space } from 'antd';
import { Fragment, useState } from 'react';
import { batch } from '../../data';
import { copyInfo } from '../../server';
import style from './copy.less';
import XlbProgress from './progress';

const Copy = (props: any) => {
  const userInfo = LStorage.get('userInfo');
  const { visible, handleCancel, getData } = props;
  const [apiCalls, setApiCalls] = useState(0);
  const [form] = Form.useForm();
  const [progress, setProgress] = useState<any>({
    open: false,
    tips: '',
    percentNum: 0,
  });

  const [loading, setLoading] = useState<boolean>(false);
  const handleOk = async (apiCalls = 0) => {
    const arr = form
      .getFieldValue('revise_ids')
      ?.filter((v: any) => v == form.getFieldValue('reference_ids'));
    if (!form.getFieldValue('revise_ids')) {
      XlbTipsModal({
        tips: '请先选择修改门店',
      });
      return;
    } else {
      if (!form.getFieldValue('reference_ids')) {
        XlbTipsModal({
          tips: '请先选择参照门店',
        });

        return;
      }
    }
    if (arr.length) {
      XlbTipsModal({
        tips: '修改门店需与参照门店不一致',
      });
      return;
    }
    if (
      form.getFieldValue('checkValue') === undefined ||
      form.getFieldValue('checkValue').length <= 0
    ) {
      XlbTipsModal({
        tips: '请先选择复制内容',
      });
      return;
    }
    const apiCallsCount = apiCalls + 1;
    const targets = form.getFieldValue('revise_ids')
      ? form.getFieldValue('revise_ids')
      : null;
    const targetsNames = form.getFieldValue('revise_ids_names');
    const source_store_id = form.getFieldValue('reference_ids')
      ? form.getFieldValue('reference_ids')?.join(',')
      : null;
    const target_store_ids = [targets[apiCalls]];
    const revise_names = targetsNames[apiCalls];

    const data = {
      ...form.getFieldsValue(),
      revise_names,
      target_store_ids,
      source_store_id,
    };

    form.getFieldValue('checkValue')?.forEach((item: any) => (data[item] = 1));
    setApiCalls(apiCallsCount);
    const len = targets?.length;
    const percentNum =
      safeMath.divide(Number(apiCallsCount) || 1, Number(len)) * 100;
    setProgress({
      ...progress,
      tips: targetsNames[apiCalls] || '',
      percentNum,
      open: true,
    });

    setLoading(true);
    const res = await copyInfo(data);
    setLoading(false);

    if (res.code === 0) {
      if (apiCallsCount <= len - 1) {
        handleOk(apiCallsCount);
      } else {
        setProgress({
          ...progress,
          open: false,
        });
        message.success('操作成功');
        setApiCalls(0);
        getData(1);
        form.resetFields();
        handleCancel();
      }
    } else {
      setProgress({
        ...progress,
        open: false,
      });
      getData(1);
      form.resetFields();
    }
  };

  return (
    // <div className={style.copy} style={{ background: 'red' }}>
    <XlbModal
      title={'复制'}
      isCancel={true}
      centered
      open={visible}
      maskClosable={false}
      onOk={() => handleOk(0)}
      onCancel={() => {
        form.resetFields(), handleCancel();
      }}
      width={513}
      confirmLoading={loading}
    >
      <Form form={form}>
        <div className={style.box}>
          <p className={style.title}>复制门店</p>
          <Form.Item>
            <Space
              direction="vertical"
              style={{
                width: 300,
                display: 'inline-block',
                marginLeft: 20,
                marginBottom: 7,
              }}
            >
              <Form.Item
                name="revise_ids"
                style={{ display: 'inline-block' }}
                label="修改门店"
              >
                <XlbInputDialog
                  dialogParams={{
                    type: 'store',
                    isMultiple: true,
                    data: {
                      enable_organization: false,
                    },
                  }}
                  onChange={(val: any, list: any) => {
                    console.log("🚀 ~ Copy ~ list:", list)
                    console.log("🚀 ~ Copy ~ val:", val)
                    form.setFieldsValue({
                      revise_ids: val,
                      revise_ids_names: list.map((i: any) => i.store_name),
                    });
                  }}
                  fieldNames={{
                    idKey: 'id',
                    nameKey: 'store_name',
                  }}
                  width={180}
                />
              </Form.Item>
            </Space>
            <Space
              direction="vertical"
              style={{ width: 300, display: 'inline-block', marginLeft: 20 }}
            >
              <Form.Item
                name="reference_ids"
                style={{ display: 'inline-block' }}
                label="参照门店"
              >
                <XlbInputDialog
                  dialogParams={{
                    type: 'store',
                    isMultiple: false,
                    data: {
                      enable_organization: false,
                    },
                  }}
                  fieldNames={{
                    idKey: 'id',
                    nameKey: 'store_name',
                  }}
                  width={180}
                />
              </Form.Item>
            </Space>
          </Form.Item>
        </div>
        <div className={style.box} style={{ marginBottom: 0 }}>
          <p className={style.title}>复制内容</p>
          <Form.Item name="checkValue">
            <Checkbox.Group style={{ width: '100%' }}>
              {batch.map((item) => {
                return (
                  <Fragment key={item.value}>
                    <Checkbox
                      value={item.value}
                      disabled={
                        !hasAuth([`门店商品属性/${item.label}`, '编辑']) &&
                        item.label !== '销售模式'
                      }
                    >
                      {item.label}
                    </Checkbox>
                    {/* <Form.Item name={item.value} key={item.value}></Form.Item> */}
                  </Fragment>
                );
              })}
            </Checkbox.Group>
          </Form.Item>
        </div>
      </Form>
      <XlbProgress progress={progress} onCancel={setProgress} />
    </XlbModal>
    // </div>
  );
};
export default Copy;
