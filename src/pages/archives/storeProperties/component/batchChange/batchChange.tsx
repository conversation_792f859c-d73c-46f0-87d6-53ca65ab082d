import { XlbProgress } from '@/components/common';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbInputDialog,
  XlbModal,
  XlbPopover,
  XlbSelect,
  XlbTipsModal,
  XlbTooltip,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { Checkbox, Form, message, Radio, Space } from 'antd';
import { Fragment, useEffect, useState } from 'react';
import { batch, itemStatus } from '../../data';
import style from './batchChange.less';

const BatchChange = (props: any) => {
  const userInfo = LStorage.get('userInfo');
  const { visible, handleCancel, getData, enableOrganization } = props;
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const handleOk = async (apiCalls = 0) => {
    if (form.getFieldValue('store_ids') === undefined) {
      XlbTipsModal({
        tips: '请先选择门店',
      });
      return;
    }
    if (form.getFieldValue('modify_scope') === undefined) {
      XlbTipsModal({
        tips: '请先选择商品范围',
      });
      return;
    }
    if (
      form.getFieldValue('modify_scope') === 1 &&
      !form.getFieldValue('category_ids')
    ) {
      XlbTipsModal({
        tips: '请先选择商品类别',
      });
      return;
    }
    if (
      form.getFieldValue('modify_scope') === 2 &&
      !form.getFieldValue('item_ids')
    ) {
      XlbTipsModal({
        tips: '请先选择商品档案',
      });
      return;
    }
    if (!form.getFieldValue('checkValue')?.length) {
      XlbTipsModal({
        tips: '请先选择修改内容',
      });
      return;
    }
    const data = {
      ...form.getFieldsValue(true),
    };
    batch.forEach((item: any) => {
      // 筛选勾选项并赋值
      if (form.getFieldValue('checkValue')?.includes(item.value)) {
        data[item.value] = form.getFieldValue(item.value);
      } else {
        data[item.value] = undefined;
      }
    });
    const modify_scope = form.getFieldValue('modify_scope');
    if (modify_scope === 1) {
      data.item_category_ids = form.getFieldValue('category_ids') || [];
    } else if (modify_scope === 2) {
      data.item_ids = form.getFieldValue('item_ids') || [];
    }

    // 二次弹窗确认
    const formData = form.getFieldsValue(true);
    let cut: any = {};
    if (formData?.modify_scope === 0) {
      setLoading(true);
      cut = await ErpRequest.post(
        '/erp/hxl.erp.storeitemattr.batchupdate.count',
        data,
      );
      setLoading(false);
      if (cut.code !== 0) {
        return;
      }
    }
    const sure = await XlbTipsModal({
      title: (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          请核实批量修改信息
          <span style={{ color: '#FF2121', marginLeft: 8, fontSize: 12 }}>
            *提交后无法修改，请仔细核实修改信息
          </span>
        </div>
      ),
      isCancel: true,
      width: 500,
      tips: (
        <div>
          <div
            className={style.verify_title_box}
            style={{ display: 'flex', margin: '6px 0 12px 0' }}
          >
            <span>修改门店</span>
            <span>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {formData?.store_ids_names?.[0] || ''}
                <XlbPopover
                  placement="rightTop"
                  overlayClassName={style.popoverWrap}
                  content={formData?.store_ids_names?.map((t: string) => (
                    <div key={t}>{t}</div>
                  ))}
                  trigger="hover"
                >
                  <XlbButton type="link">{`(${formData?.store_ids_names?.length || 0})`}</XlbButton>
                </XlbPopover>
              </div>
            </span>
          </div>
          <div className={style.verify_title_box}>
            <span>修改范围</span>
            <span>
              {formData?.modify_scope === 0
                ? `全部商品(${cut.data}个)`
                : formData?.modify_scope === 1
                  ? `商品类别(${formData.category_ids?.length}个)`
                  : formData?.modify_scope === 2
                    ? `商品档案(${formData.item_ids?.length}个)`
                    : ''}
            </span>
          </div>
          <div className={style.verify_content_box}>
            <h4>修改内容</h4>
            {formData?.checkValue?.map((value: any) =>
              renderTipsContent(value, formData),
            )}
          </div>
        </div>
      ),
    });
    if (sure) {
      progressBar(data);
    }
  };

  function chunkArray(arr: [], size: number) {
    const chunks = [];
    for (let i = 0; i < arr.length; i += size) {
      chunks.push(arr.slice(i, i + size));
    }
    return chunks;
  }

  //打开进度条
  const progressBar = (data: any) => {
    setLoading(true);
    const { store_ids } = data;
    let size: number = 20;
    if (
      (form.getFieldValue('category_ids') &&
        form.getFieldValue('category_ids')?.length < 100) ||
      (form.getFieldValue('item_ids') &&
        form.getFieldValue('item_ids')?.length < 100)
    ) {
      size = 100;
    }
    const arraysOfTen = chunkArray(store_ids, size);
    const items = arraysOfTen.map((item: any) => {
      return {
        ...data,
        store_ids: item,
      };
    });
    NiceModal.show(XlbProgress, {
      requestApi: '/erp/hxl.erp.storeitemattr.batchupdate',
      items: items || [],
      showCancelButton: true,
      // titleList,
      // batchNumber: 5, // 浏览器限制只能是5
      promptTitle: `正在操作${store_ids?.length}个门店`,
    }).then((res: any) => {
      if (res.code === 0) {
        message.success('操作成功');
        getData(1);
        form.resetFields();
        handleCancel();
      }
      setLoading(false);
      if (res.code !== 0) {
        return;
      }
    });
  };
  const renderTipsContent: any = (value: any, formData: any) => {
    const dt = batch.find((t: any) => t.value === value);
    return (
      <div className={style.verify_content_box_item}>
        <span>{dt?.label}</span>
        <span>
          {(() => {
            switch (dt?.label) {
              case '销售模式':
                return {
                  EMPTY: '',
                  DIRECT: '经销',
                  AGENCY: '代销',
                }[formData?.[value]];
              default:
                return ['　', '否', '是'][formData?.[value]];
            }
          })()}
        </span>
      </div>
    );
  };
  const renderCheckBox = (item: string, disabled?: boolean) => {
    switch (item) {
      case '销售模式':
        return (
          <XlbSelect
            style={{ width: '120px' }}
            onChange={(e) => {
              const checkValue = form.getFieldValue('checkValue');
              if (checkValue?.includes('sale_mode') && e === 'AGENCY') {
                form.setFieldsValue({
                  pos_bargain: 1,
                  pos_discount: 1,
                  checkValue: [
                    ...new Set([...checkValue, 'pos_bargain', 'pos_discount']),
                  ],
                });
              }
            }}
          >
            <XlbSelect.Option key={'EMPTY'} value={'EMPTY'}>
              {'　'}
            </XlbSelect.Option>
            <XlbSelect.Option key={'DIRECT'} value={'DIRECT'}>
              经销
            </XlbSelect.Option>
            <XlbSelect.Option key={'AGENCY'} value={'AGENCY'}>
              代销
            </XlbSelect.Option>
          </XlbSelect>
        );
      case '商品状态':
        return (
          <XlbSelect
            style={{ width: '120px' }}
            options={itemStatus}
            disabled={!hasAuth([`门店商品属性/${item}`, '编辑'])}
          />
        );
      default:
        return (
          <XlbSelect
            style={{ width: '120px' }}
            disabled={!hasAuth([`门店商品属性/${item}`, '编辑']) || disabled}
          >
            <XlbSelect.Option value={0}>　</XlbSelect.Option>
            <XlbSelect.Option value={2}>是</XlbSelect.Option>
            <XlbSelect.Option value={1}>否</XlbSelect.Option>
          </XlbSelect>
        );
    }
  };

  // 导入门店
  const importStores = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storename.import`,
      templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storecodetemplate.download`,
      templateName: '门店导入模板',
      callback: (res: any) => {
        if (res.code !== 0) return;
        form.setFieldsValue({
          store_ids: res?.data?.store_ids || [],
          store_ids_names: res?.data?.store_names || [],
        });
      },
    });
  };
  // 导入商品
  const importShort = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.items.batchimport`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.item.shorttemplate.download`,
      templateName: '商品导入模板',
      callback: (res: any) => {
        if (res.code !== 0) return;
        form.setFieldsValue({
          item_ids: res?.data?.items?.map((v: any) => v.id),
          item_ids_name: res?.data?.items?.map((v: any) => v.name),
        });
      },
    });
  };
  useEffect(() => {
    console.log('334:', LStorage.get('userInfo')?.query_stores);
    form.setFieldsValue({
      store_ids_names:
        LStorage.get('userInfo')?.query_stores &&
        LStorage.get('userInfo')?.query_stores.length === 1
          ? [LStorage.get('userInfo')?.query_stores[0]?.store_name]
          : undefined,
      store_ids:
        LStorage.get('userInfo')?.query_stores &&
        LStorage.get('userInfo')?.query_stores.length === 1
          ? [LStorage.get('userInfo')?.query_stores[0]?.id]
          : undefined,
    });
  }, [visible]);
  const onValuesChange = (e, val) => {
    const changedKey = Object.keys(e)[0]; // 当前变化字段
    const checkValues = val?.checkValue || [];

    const isForceDeliver = val?.force_transfer == 2;
    const isSkipWarehouse = val?.skip_warehouse == 2;

    const isForceChecked = checkValues.includes('force_transfer');
    const isSkipChecked = checkValues.includes('skip_warehouse');

    if (isForceDeliver && isSkipWarehouse && isForceChecked && isSkipChecked) {
      message.error('越库属性与统配属性互斥，如需设置，需要先设置另一个为否');

      if (changedKey === 'force_transfer' || changedKey === 'skip_warehouse') {
        form.setFieldsValue({
          [changedKey]: 1,
          checkValue: checkValues.filter((key) => key !== changedKey),
        });
      } else if (changedKey === 'checkValue') {
        const lastChecked = e.checkValue?.find((k) =>
          ['force_transfer', 'skip_warehouse'].includes(k),
        );

        if (lastChecked) {
          form.setFieldsValue({
            [lastChecked]: 1,
            checkValue: checkValues.filter((key) => key !== lastChecked),
          });
        }
      }
    }
  };

  return (
    <XlbModal
      title={'批量修改'}
      centered
      isCancel={true}
      open={visible}
      maskClosable={false}
      wrapClassName="xlbDialog"
      onOk={() => handleOk(0)}
      onCancel={() => {
        handleCancel();
        form.resetFields();
      }}
      width={920}
      confirmLoading={loading}
    >
      <Form form={form} onValuesChange={onValuesChange}>
        <div className={style.box}>
          <p className={style.title}>修改门店</p>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Space
              direction="vertical"
              style={{ marginLeft: 61, marginTop: 5 }}
            >
              门店
            </Space>
            <Form.Item name="store_ids" style={{ marginRight: '16px' }}>
              <XlbInputDialog
                dependencies={['org_id']}
                dialogParams={{
                  type: 'store',
                  isMultiple: true,
                  dataType: 'lists',
                  data: {
                    org_ids: form.getFieldValue('org_id')
                      ? [form.getFieldValue('org_id')]
                      : [],
                    enable_organization: false,
                    status: true,
                  },
                }}
                fieldNames={{
                  idKey: 'id',
                  nameKey: 'store_name',
                }}
                onChange={(value: any, option: any) => {
                  form.setFieldValue(
                    'store_ids_names',
                    option?.map((v: any) => v.store_name),
                  );
                }}
                width={180}
              />
            </Form.Item>
            <XlbButton type='text' onClick={() => importStores()}>导入</XlbButton>
          </div>
        </div>
        <div className={style.box}>
          <p className={style.title}>商品范围</p>
          <Form.Item name="modify_scope">
            <Radio.Group>
              <Space direction="vertical">
                <Radio value={0}>全部商品</Radio>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Radio value={1}>商品类别</Radio>
                  <Form.Item name="category_ids">
                    <XlbInputDialog
                      style={{ width: '180px' }}
                      treeModalConfig={{
                        title: '选择商品分类', // 标题
                        url: '/erp-mdm/hxl.erp.category.find', // 请求地址
                        dataType: 'lists',
                        checkable: true, // 是否多选
                        primaryKey: 'id',
                        width: 360, // 模态框宽度
                      }}
                    />
                  </Form.Item>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Radio value={2}>商品档案</Radio>
                  <Form.Item name="item_ids" style={{ marginRight: '16px' }}>
                    <XlbInputDialog
                      dialogParams={{
                        type: 'goods',
                        isMultiple: true,
                        nullable: true,
                        dataType: 'lists',
                        // data: { status: 1 }
                      }}
                      width={180}
                    />
                  </Form.Item>
                  <XlbButton type='text' onClick={() => importShort()}>导入</XlbButton>
                </div>
              </Space>
            </Radio.Group>
          </Form.Item>
        </div>
        <div className={style.box} style={{ marginBottom: 0 }}>
          <p className={style.title}>修改内容</p>
          <Form.Item noStyle dependencies={['sale_mode', 'checkValue']}>
            {({ getFieldValue }) => {
              return (
                <Form.Item name="checkValue">
                  <Checkbox.Group style={{ width: '100%' }}>
                    {batch.map((item) => {
                      return (
                        <Fragment key={item.value}>
                          <Checkbox
                            value={item.value}
                            disabled={
                              (!hasAuth([
                                `门店商品属性/${item.label}`,
                                '编辑',
                              ]) &&
                                item.label !== '销售模式') ||
                              ((item.value === 'pos_discount' ||
                                item.value === 'pos_bargain') &&
                                getFieldValue('sale_mode') === 'AGENCY' &&
                                getFieldValue('checkValue')?.includes(
                                  'sale_mode',
                                ))
                            }
                            onChange={({ target: { value, checked } }) => {
                              if (
                                value === 'sale_mode' &&
                                checked &&
                                getFieldValue('sale_mode') === 'AGENCY'
                              ) {
                                // 循环触发
                                setTimeout(() => {
                                  form.setFieldsValue({
                                    pos_bargain: 1,
                                    pos_discount: 1,
                                    checkValue: [
                                      ...new Set([
                                        ...getFieldValue('checkValue'),
                                        'pos_bargain',
                                        'pos_discount',
                                      ]),
                                    ],
                                  });
                                });
                              }
                            }}
                          >
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '5px',
                              }}
                            >
                              {item.label}
                              {item.value == 'force_transfer' ? (
                                <XlbTooltip
                                  title={
                                    '统配与越库冲突，越库商品不可以设置统配'
                                  }
                                >
                                  <XlbIcon name="bangzhu"></XlbIcon>
                                </XlbTooltip>
                              ) : null}
                            </div>
                          </Checkbox>
                          <Form.Item name={item.value} key={item.value}>
                            {renderCheckBox(
                              item.label,
                              (item.value === 'pos_discount' ||
                                item.value === 'pos_bargain') &&
                                getFieldValue('sale_mode') === 'AGENCY' &&
                                getFieldValue('checkValue')?.includes(
                                  'sale_mode',
                                ),
                            )}
                          </Form.Item>
                        </Fragment>
                      );
                    })}
                  </Checkbox.Group>
                </Form.Item>
              );
            }}
          </Form.Item>
        </div>
      </Form>
    </XlbModal>
  );
};
export default BatchChange;
