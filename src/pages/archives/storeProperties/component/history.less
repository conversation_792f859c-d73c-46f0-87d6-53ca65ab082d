.contractTab {
    :global .ant-tabs-nav {
      margin: 10px 0 5px 0;
    }
  
    :global .ant-tabs-tab {
      width: 120px;
      height: 32px;
      margin-left: 3px;
      color: #999999;
      font-size: 16px;
      background-color: white;
      border: 1px solid #dcdfe6;
      border-radius: 4pt 4pt 0pt 0pt;
    }
  
    :global .ant-tabs-tab-active {
      background: #eaecf1;
  
      .ant-tabs-tab-btn {
        color: #000 !important;
        font-weight: 'PingFangSC-Regular';
      }
    }
  }
  
  .erpButton {
    margin-left: 10px;
  }
  
  .table_box {
    :global .art-table-body {
      min-height: calc(100vh - 300px);
    }
  }
  
  .table_fold_box {
    :global .art-table-body {
      min-height: calc(100vh - 200px);
    }
  }
  
  .loading {
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    line-height: 50;
    text-align: center;
    background: rgba(0, 0, 0, 0.2) !important;
  }
  
  .spanss {
    display: inline-block;
    width: 15px;
    height: 15px;
    padding-left: 4px;
    color: white;
    font-size: 12px;
    line-height: 15px;
    text-align: center;
    background-color: #000;
    border-radius: 50%;
    transform: scale(0.7);
    cursor: default;
  }
  .redTab{
    color: red;
  }
  .uploadBtn{
      :global .xlb_btn_text:is(:disabled){
          background-color: inherit;
      }
  }