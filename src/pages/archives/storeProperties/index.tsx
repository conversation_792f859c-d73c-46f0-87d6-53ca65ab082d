import { columnWidthEnum } from '@/data/common/constant';
import { useBaseParams } from '@/hooks/useBaseParams';
import useDownload from '@/hooks/useDownload';
import dateManipulation from '@/utils/dateManipulation';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import type { XlbTableColumnProps } from '@xlb/components';
import {
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbProPageContainer,
  XlbSelect,
} from '@xlb/components';
import type { SearchListItem } from '@xlb/components/dist/lowcodes/XlbProForm/components/ProFormList';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import type { FormInstance } from 'antd';
import { message, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';
import BatchChange from './component/batchChange/batchChange';
import Copy from './component/copy/copy';
import History from './component/history';
import {
  batch,
  categoryLevelList,
  formList,
  goodsType,
  itemStatus,
  types,
  types1,
} from './data';
import styles from './index.less';
import { updateInfo } from './server';
import { wujieBus } from '@/wujie/utils';

const StoreProperties = () => {
  const userInfo = LStorage.get('userInfo');
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [batchVisible, setBatchVisible] = useState<boolean>(false); //批量设置弹框
  const [storeList, setStoreList] = useState<any[]>([]);
  const [chooseList, setChooseList] = useState<any[]>([]);
  const [sortType, setSortType] = useState<{ order: string; code: string }>({
    order: '',
    code: '',
  });
  //复制弹框
  const [copyVisible, setCopyVisible] = useState<boolean>(false);
  const [historyVisible, setHistoryVisible] = useState<boolean>(false); //修改记录
  const [formlist, setFormList] = useState<SearchListItem[]>(
    JSON.parse(JSON.stringify(formList)),
  );
  const formInstens = useRef<FormInstance | null>(null);
  const requestFormInstens = useRef<any>(null);
  const refreshRef = useRef<(page?: number) => void>(() => {});
  const { enable_organization } = useBaseParams((state) => state);
  const authSwitch = (code: string) =>
    batch.find((e) => e.value === code)?.label;

  // 表格编辑处理
  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
    },
    {
      name: '组织',
      code: 'org_name',
      width: 100,
      hidden: !enable_organization,
    },
    {
      name: '门店',
      code: 'store_name',
      width: columnWidthEnum.STORE_NAME,
      features: { sortable: true },
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: columnWidthEnum.ITEM_BAR_CODE,
      features: { sortable: true },
    },
    {
      name: '商品条码',
      code: 'item_bar_code',
      width: columnWidthEnum.ITEM_BAR_CODE,
      features: { sortable: true },
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 280,
      features: { sortable: true },
    },
    {
      name: '商品规格',
      code: 'item_spec',
      width: columnWidthEnum.ITEM_SPEC,
      features: { sortable: true },
    },
    {
      name: '基本单位',
      code: 'basic_unit',
      width: 100,
      features: { sortable: true },
    },
    {
      name: '采购类型',
      code: 'purchase_type',
      width: 100,
      features: { sortable: true },
      render: (value: any) => {
        return value === 'COLLECTIVE_PURCHASE'
          ? '集采品'
          : value === 'COLLECTIVE_SALE'
            ? '集售品'
            : value === 'GROUND_PURCHASE'
              ? '地采品'
              : value === 'SHOP_PURCHASE'
                ? '店采品'
                : '';
      },
    },
    {
      name: '商品类型',
      code: 'item_type',
      width: 100,
      features: { sortable: true },
      render: (value: any) => {
        return (
          <div className="cursors">
            {goodsType.find((e) => e.value === value)?.label}
          </div>
        );
      },
    },
    {
      name: '商品类别',
      code: 'item_category_name',
      width: 140,
      features: { sortable: true },
    },
  ];
  const tipElement = (record: any) => {
    return (
      <div>
        <div>修改时间:{record?.stop_purchase_time}</div>
        <div>
          <span>修改人:{record?.stop_purchase_by}</span>
          <span style={{ marginLeft: 10 }}>
            备注:{record?.stop_purchase_remark}
          </span>
        </div>
      </div>
    );
  };
  //表格更新
  const selectChange = async (event: any, record: any, key: any) => {
    const data = {
      item_id: record.item_id,
      store_id: record.store_id,
      [key]: types1.find((e) => e.label == JSON.stringify(event))?.value,
    };
    record[key] = event;
    const res = await updateInfo(data);
    if (res.code === 0) {
      message.success('操作成功');
    }
  };

  const selectChangeWithSaleMode = async (
    event: any,
    record: any,
    key: any,
    index: number | undefined,
  ) => {
    const data = {
      item_id: record.item_id,
      store_id: record.store_id,
      [key]: event,
    };
    record[key] = event;
    if (event === 'AGENCY') {
      data.pos_discount = 1;
      data.pos_bargain = 1;
      record.pos_discount = false;
      record.pos_bargain = false;
    }
    const res = await updateInfo(data);
    if (res.code === 0) {
      message.success('操作成功');
    }
  };
  const selectChangewithitemStatus = async (
    event: any,
    record: any,
    key: any,
    index: number | undefined,
  ) => {
    const data = {
      item_id: record.item_id,
      store_id: record.store_id,
      [key]: event,
    };
    record[key] = event;
    const res = await updateInfo(data);
    if (res.code === 0) {
      message.success('操作成功');
    }
  };
  interface CellJudgeProp {
    record: any;
    code: string;
    value?: any;
    index?: number;
  }
  const getCellJudge = ({ record, code }: CellJudgeProp) => {
    // if (record._click) {
    //   console.log(
    //     `门店商品属性/${authSwitch(code)}`,
    //     hasAuth(userInfo, [`门店商品属性/${authSwitch(code)}`, '编辑']),
    //     record._click &&
    //       (hasAuth(userInfo, [`门店商品属性/${authSwitch(code)}`, '编辑']) ||
    //         code === 'sale_mode') &&
    //       !formInstens.current?.getFieldValue('product_actual_attribute'),
    //     'getCellJudge'
    //   )
    // }
    return (
      record._click &&
      (hasAuth([`门店商品属性/${authSwitch(code)}`, '编辑']) ||
        code === 'sale_mode') &&
      !formInstens.current?.getFieldValue('product_actual_attribute')
    );
  };
  const getSelect = ({ value, record, code, index }: CellJudgeProp) => (
    <XlbSelect
      style={{ width: '100%' }}
      value={value}
      onChange={(e) =>
        code === 'sale_mode'
          ? selectChangeWithSaleMode(e, record, code, index)
          : code === 'item_status'
            ? selectChangewithitemStatus(e, record, code, index)
            : selectChange(e, record, code)
      }
      allowClear={false}
      disabled={
        (code === 'pos_discount' || code === 'pos_bargain') &&
        record.sale_mode === 'AGENCY'
      }
    >
      {code === 'sale_mode' ? (
        <>
          <XlbSelect.Option key={'DIRECT'} value={'DIRECT'}>
            <div className={styles.optionMin}>经销</div>
          </XlbSelect.Option>
          <XlbSelect.Option key={'AGENCY'} value={'AGENCY'}>
            <div className={styles.optionMin}>代销</div>
          </XlbSelect.Option>
          <XlbSelect.Option key={'EMPTY'} value={'EMPTY'}>
            <div className={styles.optionMin}></div>
          </XlbSelect.Option>
        </>
      ) : code === 'item_status' ? (
        <>
          {itemStatus.map((p) => {
            return (
              <XlbSelect.Option
                disabled={p.disabled}
                key={p?.value}
                value={p?.value}
              >
                <div className={styles.optionMin}>{p.label}</div>
              </XlbSelect.Option>
            );
          })}
        </>
      ) : (
        types.map((v, index) => (
          <XlbSelect.Option key={index} value={v.value}>
            <div className={styles.optionMin}>{v.label}</div>
          </XlbSelect.Option>
        ))
      )}
    </XlbSelect>
  );
  const selectRender = (
    value: any,
    record: any,
    code: string,
    index?: number,
  ) => {
    return getCellJudge({ record, code }) ? (
      getSelect({ value, record, code, index })
    ) : (
      <div className="info" style={{ display: 'flex', alignItems: 'center' }}>
        <div>
          {code === 'sale_mode'
            ? value === 'DIRECT'
              ? '经销'
              : value === 'AGENCY'
                ? '代销'
                : ''
            : code === 'item_status'
              ? itemStatus?.find((item) => item.value === value)?.label || ''
              : types.find((e) => e.value === value)?.label}
        </div>
        {code === 'stop_purchase' &&
          record?.stop_purchase &&
          !requestFormInstens.current?.product_actual_attribute && (
            <Tooltip
              overlayStyle={{ marginLeft: '5px' }}
              title={tipElement(record)}
            >
              <span>
                <XlbIcon
                  color="#979faa"
                  hoverColor="#3D66FE"
                  name="bangzhu"
                  size={16}
                />
              </span>
            </Tooltip>
          )}
      </div>
    );
  };
  const selectRender2 = (value: any, record: any, code: string) => {
    if (getCellJudge({ record, code })) {
      if (record.center_flag) {
        return getSelect({ value, record, code });
      }
      return <div>{'——'}</div>;
    }
    return (
      <div className="info">{types.find((e) => e.value === value)?.label}</div>
    );
  };
  const tableListEnd: XlbTableColumnProps<any>[] = [
    {
      name: '停购',
      code: 'stop_purchase',
      width: 80,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender(value, record, 'stop_purchase'),
    },
    {
      name: '停售',
      code: 'stop_sale',
      width: 80,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender(value, record, 'stop_sale'),
    },
    {
      name: '停止要货',
      code: 'stop_request',
      width: 100,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender(value, record, 'stop_request'),
    },
    {
      name: '停止批发',
      code: 'stop_wholesale',
      width: 100,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender(value, record, 'stop_wholesale'),
    },
    {
      name: '越库',
      code: 'skip_warehouse',
      width: 100,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender(value, record, 'skip_warehouse'),
    },
    {
      name: '预定',
      code: 'reserve',
      width: 70,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender2(value, record, 'reserve'),
    },
    {
      name: '统配',
      code: 'force_transfer',
      width: 70,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender2(value, record, 'force_transfer'),
    },
    {
      name: '直配',
      code: 'direct_delivery',
      width: 70,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender2(value, record, 'direct_delivery'),
    },
    {
      name: '前台折扣',
      code: 'pos_discount',
      width: 100,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender(value, record, 'pos_discount'),
    },
    {
      name: '前台议价',
      code: 'pos_bargain',
      width: 100,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender(value, record, 'pos_bargain'),
    },
    {
      name: '禁止退仓',
      code: 'stop_return',
      width: 100,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender(value, record, 'stop_return'),
    },
    {
      name: '必卖品',
      code: 'must_sell',
      width: 100,
      features: { sortable: true },
      render: (value: any, record: any) =>
        selectRender(value, record, 'must_sell'),
    },
    {
      name: '销售模式',
      code: 'sale_mode',
      width: 100,
      features: { sortable: true },
      render: (value: any, record: any, index) =>
        selectRender(value, record, 'sale_mode', index.index),
      // value === 'DIRECT' ? '经销' : value === 'AGENCY' ? '代销' : ''
    },
    {
      name: '商品状态',
      code: 'item_status',
      width: 120,
      features: { sortable: true },
      render: (value: any, record: any, index) =>
        selectRender(value, record, 'item_status', index.index),
    },
    {
      name: '最后修改人',
      code: 'update_by',
      width: 150,
      features: { sortable: true },
    },
    {
      name: '最后修改时间',
      code: 'update_time',
      width: 180,
      features: { sortable: true },
      render: (value: any) => (
        <div className="info">{dateManipulation(value)}</div>
      ),
    },
  ];

  const getOrgList = () => {
    formlist.find((i) => i.name === 'org_ids')!.hidden = !enable_organization;
    setFormList([...formlist]);
  };
  useEffect(getOrgList, [enable_organization]);

  useEffect(() => {
    formInstens.current?.setFieldsValue({
      // store_names: JSON.parse(localStorage.userInfo).value.store_name,
      // store_ids: [JSON.parse(localStorage.userInfo).value.store_id],
      product_actual_attribute: false,
      show_diff_stop_request: false,
    });
    setStoreList([JSON.parse(localStorage.userInfo).value.store]);
    const storage = LStorage.get('storeProperties');
    if (storage) formInstens.current?.setFieldsValue({ ...storage });
  }, []);
  useEffect(() => {
    sortType.code !== '' && refreshRef.current(1);
  }, [sortType]);
  //导入
  const imports = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.storeitemattr.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.storeitemattrtemplate.download`,
    });
    if (res.code === 0) {
      if (!res?.data?.error_meaages) {
        refreshRef.current();
      }
    }
  };

  //导出
  const exportItem = async (requestForm: any, e: any) => {
    const data: any = {
      ...requestForm,
      // responseType: 'blob'
    };
    setisLoading(true);
    const res = await ErpRequest.post(
      '/erp/hxl.erp.storeitemattr.export',
      data,
    );
    setisLoading(false);
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success('导出受理成功，请前往下载中心查看');
    }
  };

  return (
    <>
      <BatchChange
        visible={batchVisible}
        handleCancel={() => setBatchVisible(false)}
        getData={refreshRef.current}
        enableOrganization={enable_organization}
      />
      <Copy
        visible={copyVisible}
        handleCancel={() => setCopyVisible(false)}
        getData={refreshRef.current}
      />
      <History
        visible={historyVisible}
        id={chooseList}
        onCancel={() => setHistoryVisible(false)}
      />

      <XlbProPageContainer
        searchFieldProps={{
          formList: () =>
            formlist.map((item) => {
              if (
                item.name === 'item_status' &&
                hasAuth(['门店商品属性/商品状态', '查询'])
              ) {
                item.hidden = false;
              }
              return item;
            }),
          initialValues: {
            category_levels: [1],
          },
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.storeitemattr.page',
          tableColumn: (formValues: any) => {
            const categoryLevels = formValues.category_levels || [];
            const showList = categoryLevelList.filter((item) =>
              categoryLevels.includes(item.levelValue),
            );
            return [...tableList, ...showList, ...tableListEnd].filter(
              (i) =>
                !(
                  i.name === '商品状态' &&
                  !hasAuth(['门店商品属性/商品状态', '查询'])
                ),
            );
          },
          selectMode: 'single',
          immediatePost: false,
          // TODO: 列定制影响表格列显示顺序，暂时去掉
          showColumnsSetting: false,
          changeColumnAndResetDataSource: false,
        }}
        extra={(context) => {
          const { fetchData, dataSource, selectRow, form, requestForm } =
            context;
          refreshRef.current = fetchData;
          formInstens.current = form;
          requestFormInstens.current = requestForm;
          setChooseList(selectRow || []);
          return (
            <XlbButton.Group>
              {hasAuth(['门店商品属性', '导入']) ? (
                <XlbButton
                  type="primary"
                  label="导入"
                  onClick={imports}
                  icon={<XlbIcon size={16} name="daoru" />}
                />
              ) : null}
              {hasAuth(['门店商品属性', '导出']) ? (
                <XlbButton
                  disabled={!dataSource?.length}
                  type="primary"
                  label="导出"
                  loading={isLoading}
                  onClick={(_, e) => exportItem(requestForm, e)}
                  icon={<XlbIcon size={16} name="daochu" />}
                />
              ) : null}
              <XlbButton
                type="primary"
                label="批量设置"
                onClick={() => {
                  setBatchVisible(true);
                }}
                icon={<XlbIcon size={16} name="shezhi" />}
              />
              <XlbButton
                type="primary"
                label="复制"
                onClick={() => setCopyVisible(true)}
                icon={<XlbIcon size={16} name="fuzhi" />}
              />
              <XlbButton
                type="primary"
                label="修改记录"
                disabled={!selectRow?.length}
                onClick={() => setHistoryVisible(true)}
                icon={<XlbIcon size={16} name="shenqing" />}
              />
            </XlbButton.Group>
          );
        }}
      ></XlbProPageContainer>
    </>
  );
};

export default StoreProperties;
