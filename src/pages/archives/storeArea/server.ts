import { XlbFetch as ErpRequest } from '@xlb/utils'
import { StoreAreaBatchDTO, StoreAreaFindDTO, StoreAreaSaveDTO, StoreAreaUpdateDTO } from './type'

export default {
  //获取数据
  getStore: async (data: StoreAreaFindDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.storearea.find', data)
  },

  //新增区域
  add: async (data: StoreAreaSaveDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.storearea.save', data)
  },

  //删除区域
  delete: async (data: { id: number }) => {
    return await ErpRequest.post('/erp/hxl.erp.storearea.delete', data)
  },

  //导出区域
  export: async (data: { ids: number }) => {
    return await ErpRequest.post('/erp/hxl.erp.storearea.batch.export', data)
  },

  //更新修改区域
  update: async (data: StoreAreaUpdateDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.storearea.update', data)
  },

  //批量设置
  batchSave: async (data: StoreAreaBatchDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.storearea.batchupdate', data)
  },

  //读取
  read: async (data: { id: number }) => {
    return await ErpRequest.post('/erp/hxl.erp.storearea.read', data)
  },

  // 获取门店分组
  getstoreGrop: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storegroup.find', data)
  },

  // 获取门店分组
  categoryDelete: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storeareacategory.delete', data)
  },

  // 获取门店分组
  categoryFind: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storeareacategory.find', data)
  },

  // 获取门店分组
  categorySave: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storeareacategory.save', data)
  },

  // 获取门店分组
  categoryUpdate: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storeareacategory.update', data)
  }
}