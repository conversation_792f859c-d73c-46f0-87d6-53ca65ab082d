import { tableColumn } from './data'
import { hasAuth } from '@/utils/kit'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { XlbProPageContainer } from '@xlb/components'

const Index: React.FC = () => (
  <XlbProPageContainer
    searchFieldProps={{
      formList: [{ id: ErpFieldKeyMap?.erpItemKeyword }]
    }}
    tableFieldProps={{
      immediatePost: true,
      url: '/erp/hxl.erp.suppliermainbody.find',
      selectMode: 'single',
      tableColumn: tableColumn
    }}
    deleteFieldProps={{
      name: '删除',
      showField: 'name',
      url: hasAuth(['供货主体', '删除']) ? '/erp/hxl.erp.suppliermainbody.delete' : ''
    }}
    addFieldProps={{
      name: '新增',
      url: hasAuth(['供货主体', '编辑']) ? '/erp/hxl.erp.suppliermainbody.save' : ''
    }}
    details={{
      mode: 'modal',
      isCancel: true,
      width: 350,
      title: (item) => {
        return <div>{item?.id ? '编辑' : '新增'}</div>
      },
      hiddenSaveBtn: true,
      primaryKey: 'id',
      formList: [
        {
          componentType: 'form',
          fieldProps: {
            formList: [
              {
                id: ErpFieldKeyMap.erpName,
                itemSpan: 24,
                label: '供货主体名称',
                rules: [{ required: true, message: '供货主体名称不能为空' }],
                fieldProps: { maxLength: 20 }
              },
              {
                id: ErpFieldKeyMap.erpName,
                name: 'addr',
                itemSpan: 24,
                label: '供货主体地址'
              },
              {
                id: ErpFieldKeyMap.erpName,
                name: 'account_commissioner_wechat',
                itemSpan: 24,
                label: '对账专员电话'
              },
              {
                id: ErpFieldKeyMap.erpName,
                name: 'account_commissioner_phone',
                itemSpan: 24,
                label: '对账专员微信'
              }
            ]
          }
        }
      ],
      updateFieldProps: {
        url: hasAuth(['供货主体', '编辑']) ? '/erp/hxl.erp.suppliermainbody.update' : ''
      }
    }}
  />
)

export default Index