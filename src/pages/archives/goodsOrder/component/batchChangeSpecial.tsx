import { LStorage } from '@/utils/storage';
import { Checkbox, Form, Input, Radio, Select, Space, message } from 'antd';
import { Fragment, useEffect, useState } from 'react';
import { batch_special } from '../data';
import style from './batchChange.less';
const { Option } = Select;

import NiceModal from '@ebay/nice-modal-react';
import {
  XlbButton,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbModal,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import Api from '../server';

const BatchChangeSpecial = (props: any) => {
  const { search, enableOrganization } = props;
  const { visible, hide, resolve } = NiceModal.useModal();

  const userInfo = LStorage.get('userInfo');
  const [loading, setloading] = useState(false);
  const [form] = Form.useForm();

  const handleCancel = (flag: boolean) => {
    resolve(flag);
    hide();
  };

  const handleOk = async () => {
    if (!form.getFieldValue('radioValue')) {
      XlbTipsModal({
        tips: '请先选择商品',
      });
      return;
    }
    if (
      form.getFieldValue('radioValue') == 3 &&
      !form.getFieldValue('item_category_ids')
    ) {
      XlbTipsModal({
        tips: '请选择商品类别',
      });
      return;
    }
    if (
      form.getFieldValue('radioValue') == 4 &&
      !form.getFieldValue('item_ids')
    ) {
      XlbTipsModal({
        tips: '请选择指定商品',
      });
      return;
    }
    if (!form.getFieldValue('checkValue')) {
      XlbTipsModal({
        tips: '请先选择修改数据',
      });
      return;
    }
    const data = {
      ...form.getFieldsValue(),
      org_id: Array.isArray(form.getFieldValue('org_id'))
        ? form.getFieldValue('org_id')[0]
        : form.getFieldValue('org_id'),
      store_ids: form.getFieldValue('store_ids'),
      item_category_ids: form.getFieldValue('item_category_ids'),
      item_ids: form.getFieldValue('item_ids'),
      types: form.getFieldValue('types') ? [form.getFieldValue('types')] : null,
    };
    batch_special.forEach((item: any) => {
      // 筛选勾选项并赋值
      form.getFieldValue('checkValue')?.includes(item.value)
        ? (data[item.value] =
            form.getFieldValue(item.value) === undefined
              ? false
              : form.getFieldValue(item.value))
        : (data[item.value] = undefined);
    });
    if (form.getFieldValue('radioValue') == 2) {
      const obj: any = {};
      Object.keys(search).forEach((key) => {
        if (search[key]) {
          obj[key] = search[key];
        }
      });
      data.find_condition = obj;
    }
    if (form.getFieldValue('checkValue').includes('end_cap_quantity')) {
      const reg = /^(?:0|[1-9]\d{0,4})$/;
      if (!reg.test(form.getFieldValue('end_cap_quantity'))) {
        XlbTipsModal({
          tips: '端头推荐量需输入0-99999的整数',
        });
        return;
      }
    }
    setloading(true);
    const res = await Api.batchUpdateSpecial(data);
    setloading(false);
    if (res.code === 0) {
      message.success('更新成功');
      handleCancel(true);
      form.setFieldValue('radioValue', '');
      form.resetFields();
    }
  };
  const renderCheckBox_special = (item: string) => {
    switch (item) {
      case '推荐商品':
      case '端头商品':
        return (
          <XlbSelect style={{ width: 180, marginLeft: '-49px' }} size="small">
            <XlbSelect.Option value={true}>是</XlbSelect.Option>
            <XlbSelect.Option value={false}>否</XlbSelect.Option>
          </XlbSelect>
        );
      case '端头推荐量':
        return (
          <XlbInput style={{ width: 160, marginLeft: '-30px' }} size="small" />
        );
      default:
        return <div>无</div>;
    }
  };

  // 导入门店
  const importStores = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.storename.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.storecodetemplate.download`,
      templateName: '门店导入模板',
      params: {
        checkOrg: enableOrganization,
      },
      callback: (res: any) => {
        if (res.code !== 0) return;
        form.setFieldsValue({
          org_id: enableOrganization ? res?.data?.org_ids : undefined,
          org_id_name: enableOrganization ? res?.data?.org_names : undefined,
          store_ids: res?.data?.store_ids || [],
          store_ids_name: res?.data?.store_names || [],
        });
      },
    });
  };
  // 导入商品
  const importShort = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.items.batchimport`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.item.shorttemplate.download`,
      templateName: '商品导入模板',
      callback: (res: any) => {
        if (res.code !== 0) return;
        form.setFieldsValue({
          item_ids: res?.data?.items?.map((v: any) => v.id),
          item_ids_name: res?.data?.items?.map((v: any) => v.name),
        });
      },
    });
  };

  useEffect(() => {
    if (visible) {
      if (userInfo?.store_name && userInfo?.store.enable_delivery_center) {
        form.setFieldsValue({
          org_id: [userInfo.org_id],
          org_id_name: [userInfo.org_name],
          store_ids: userInfo.store_id ? [userInfo.store_id] : [],
        });
      }
    }
  }, [visible]);

  return (
    <XlbModal
      title={'订购特性批量修改'}
      centered
      visible={visible}
      maskClosable={false}
      onOk={handleOk}
      onCancel={() => {
        form.resetFields();
        handleCancel(false);
      }}
      width={490}
      confirmLoading={loading}
    >
      <Form form={form}>
        <div className={style.box}>
          <p className={style.title}>设置门店</p>
          {enableOrganization ? (
            <Form.Item
              label="组织"
              name="org_id"
              style={{ marginLeft: '38px' }}
            >
              <XlbInputDialog
                style={{ width: '180px' }}
                treeModalConfig={{
                  title: '选择组织',
                  url: '/erp/hxl.erp.org.find',
                  dataType: 'lists',
                  checkable: false, // 是否多选
                  primaryKey: 'id',
                }}
              />
            </Form.Item>
          ) : null}
          <Form.Item noStyle dependencies={['org_id']}>
            {({ getFieldValue }) => (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Form.Item name="store_ids" label="选择门店">
                  <XlbInputDialog
                    style={{ width: '148px' }}
                    dialogParams={{
                      type: 'store',
                      isMultiple: true,
                      nullable: true,
                      data: getFieldValue('org_id')
                        ? {
                            org_ids: getFieldValue('org_id'),
                            center_flag: true,
                            status: true,
                          }
                        : { center_flag: true, status: true },
                    }}
                    fieldNames={{
                      idKey: 'id',
                      nameKey: 'store_name',
                    }}
                    width={260}
                  />
                </Form.Item>
                <XlbButton size="small" onClick={() => importStores()}>
                  导入
                </XlbButton>
              </div>
            )}
          </Form.Item>
        </div>
        <div className={style.box}>
          <p className={style.title}>修改类型</p>
          <Form.Item label="订购类型：" name="types" initialValue={'REQUEST'}>
            <XlbSelect
              options={[
                {
                  label: '门店补货',
                  value: 'REQUEST',
                },
                {
                  label: '批发订货',
                  value: 'WHOLESALE',
                },
              ]}
              style={{ width: 180 }}
              maxTagCount={1}
            ></XlbSelect>
          </Form.Item>
        </div>
        <div className={style.box}>
          <p className={style.title}>选择商品</p>
          <Form.Item name="radioValue">
            <Radio.Group>
              <Space direction="vertical">
                <Radio value={1}>全部商品</Radio>
                <Radio value={2}>仅查询到的商品</Radio>
                <Radio value={3}>
                  商品类别
                  <Form.Item name="item_category_ids">
                    <XlbInputDialog
                      style={{ width: '180px' }}
                      treeModalConfig={{
                        title: '选择商品分类', // 标题
                        url: '/erp/hxl.erp.category.find', // 请求地址
                        dataType: 'lists',
                        checkable: true, // 是否多选
                        primaryKey: 'id',
                        width: 360, // 模态框宽度
                      }}
                    />
                  </Form.Item>
                </Radio>
                <Radio value={4}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    指定商品
                    <Form.Item name="item_ids">
                      <XlbInputDialog
                        style={{ width: '148px' }}
                        dialogParams={{
                          type: 'goods',
                          isMultiple: true,
                          nullable: true,
                          data: {
                            filter_item_types: ['COMBINATION', 'MAKEBILL'],
                          },
                        }}
                        width={260}
                      />
                    </Form.Item>
                    <XlbButton size="small" onClick={() => importShort()}>
                      导入
                    </XlbButton>
                  </div>
                </Radio>
              </Space>
            </Radio.Group>
          </Form.Item>
        </div>

        <div className={style.box} style={{ marginBottom: 10 }}>
          <p className={style.title}>设置内容</p>
          <Form.Item name="checkValue">
            <Checkbox.Group style={{ width: '100%' }}>
              {batch_special.map((item, index) => {
                return (
                  <Fragment key={index}>
                    <Checkbox value={item.value} style={{ marginTop: 10 }}>
                      {item.label}
                    </Checkbox>
                    <Form.Item
                      name={item.value}
                      key={item.value}
                      style={{ marginTop: 8 }}
                      initialValue={
                        item.label == '推荐商品' || item.label == '端头商品'
                          ? false
                          : 0
                      }
                    >
                      {renderCheckBox_special(item.label)}
                    </Form.Item>
                  </Fragment>
                );
              })}
            </Checkbox.Group>
          </Form.Item>
        </div>
      </Form>
    </XlbModal>
  );
};
export default NiceModal.create(BatchChangeSpecial);
