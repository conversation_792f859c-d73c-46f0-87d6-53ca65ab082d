import { SearchOutlined } from '@ant-design/icons';
import {
  XlbBasicForm,
  XlbButton,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbModal,
  XlbTipsModal,
} from '@xlb/components';
import openTreeModal from '@xlb/components/dist/components/XlbTree/index.modal';
import { XlbFetch } from '@xlb/utils';
import { Checkbox, InputNumber, message, Radio, Space } from 'antd';
import { Fragment, useEffect, useState } from 'react';
import { batch } from '../../data';
import style from './batchChange.less';
import TemplateModel from './TemplateModel';

const BatchChange = (props: any) => {
  const { visible, handleCancel, record, getData } = props;
  const [priceTemplateVisible, setPriceTemplateVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = XlbBasicForm.useForm();
  const handleOk = async () => {
    const checkValue: string[] = form.getFieldValue('checkValue');
    const modify_scope = form.getFieldValue('modify_scope');
    const sale_max_price = form.getFieldValue('sale_max_price');
    const sale_min_price = form.getFieldValue('sale_min_price');
    const sale_price = form.getFieldValue('sale_price');
    const sale_price_s = form.getFieldValue('sale_price_s');
    const sale_price_t = form.getFieldValue('sale_price_t');
    const sale_price_f = form.getFieldValue('sale_price_f');
    const arr = [sale_price, sale_price_s, sale_price_t, sale_price_f];
    const data = { ...form.getFieldsValue(true) };

    if (modify_scope === undefined) {
      return message.error('请选择修改范围');
    }
    if (modify_scope === 1 && !form.getFieldValue('category_names')) {
      return message.error('请选择商品分类');
    }
    if (modify_scope === 2 && !form.getFieldValue('item_ids')) {
      return message.error('请选择商品档案');
    }
    if (!checkValue?.length) {
      return message.error('请选择修改内容');
    }
    if (checkValue.some((v) => data[v] === null)) {
      return message.error('请填入勾选项的值');
    }
    const maxLimit = arr.some(
      (v, i) => ((i !== 0 && v != 0) || i === 0) && v > sale_max_price,
    );
    const minLimit = arr.some(
      (v, i) => ((i !== 0 && v != 0) || i === 0) && v < sale_min_price,
    );
    if (
      (sale_max_price != 0 && maxLimit) ||
      (sale_min_price != 0 && minLimit) ||
      (sale_min_price != 0 && sale_max_price != 0 && (minLimit || maxLimit))
    ) {
      //最高价和最低价都不为0
      XlbTipsModal({
        tips: '零售价设置与最高售价、最低售价存在冲突，请检查！',
      });

      return false;
    }
    // 筛选勾选项并赋值
    batch.forEach((v) => {
      data[v.value] = checkValue.includes(v.value)
        ? form.getFieldValue(v.value)
        : undefined;
    });
    if (modify_scope === 1) {
      data['item_ids'] = [];
      data['item_names'] = '';
      data['category_ids'] = form.getFieldValue('category_ids')
        ? form.getFieldValue('category_ids')
        : [];
    } else if (modify_scope === 2) {
      data['category_ids'] = [];
      data['category_names'] = '';
      data['item_ids'] = form.getFieldValue('item_ids')
        ? form.getFieldValue('item_ids')
        : [];
    } else if (modify_scope === 0) {
      data['category_ids'] = [];
      data['category_names'] = '';
      data['item_ids'] = [];
      data['item_names'] = '';
    }
    setLoading(true);
    const res = await XlbFetch.post(
      '/erp/hxl.erp.retailpricetemplate.retailprice.batchupdate',
      data,
    );
    setLoading(false);
    if (res.data?.results.length) {
      XlbTipsModal({
        tips: '零售价设置与最高售价、最低售价存在冲突，请检查！',
      });
      // setErrorState({
      //   ...ErrorState,
      //   msg: '零售价设置与最高售价、最低售价存在冲突，请检查！',
      //   fail_list: res.data?.results,
      // });
    } else if (res.code === 0) {
      message.success('更新成功');
      getData && getData();
      onCancel();
    }
  };
  // 导入商品
  const importItem = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.items.batchimport`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.item.shorttemplate.download`,
      templateName: '商品导入模板',
      callback: (res: any) => {
        if (res.code !== 0) return;
        form.setFieldsValue({
          item_ids: res?.data?.items?.map((v: any) => v.id),
          item_names: res?.data?.items?.map((v: any) => v.name)?.join(','),
        });
      },
    });
  };
  const TemplateHandelOk = (id: number[], list: any[]) => {
    form.setFieldsValue({
      ids: id,
      names: list.map((v) => v.name).join(','),
    });
  };
  const onCancel = () => {
    handleCancel();
    form.resetFields();
  };
  useEffect(() => {
    visible &&
      record &&
      form.setFieldsValue({
        names: record.name || '',
        ids: [record.id] || [],
      });
  }, [visible]);
  return (
    <XlbModal
      title={'批量修改'}
      centered
      visible={visible}
      maskClosable={false}
      onOk={handleOk}
      onCancel={onCancel}
      width={500}
      confirmLoading={loading}
    >
      <XlbBasicForm form={form}>
        <div className={style.box}>
          <p className={style.title}>修改类型</p>
          <XlbBasicForm.Item name="names" label={'零售价模板'}>
            <XlbInput
              readOnly
              disabled={record?.name}
              style={{ width: 168 }}
              size="small"
              suffix={<SearchOutlined style={{ color: '#666666' }} />}
              onClick={() => setPriceTemplateVisible(true)}
            />
          </XlbBasicForm.Item>
        </div>
        <div className={style.box}>
          <p className={style.title}>选择商品</p>
          <XlbBasicForm.Item name="modify_scope">
            <Radio.Group>
              <Space direction="vertical">
                <Radio value={0}>全部商品</Radio>
                <Radio value={1}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span>商品类别</span>
                    <XlbBasicForm.Item name="category_names">
                      <XlbInput
                        readOnly
                        style={{ width: 168, marginTop: 10 }}
                        size="small"
                        suffix={<SearchOutlined style={{ color: '#666666' }} />}
                        onClick={async () => {
                          const bool = await openTreeModal({
                            url: '/erp/hxl.erp.category.find',
                            dataType: 'lists',
                            zIndex: 2000,
                            checkable: true,
                            title: '选择商品分类',
                          });
                          console.log(bool, 'bool===>');
                          if (bool?.length) {
                            form.setFieldsValue({
                              category_ids: bool.map((v: any) => v.id),
                              category_names: bool
                                .map((v: any) => v.name)
                                .join(','),
                            });
                          }
                        }}
                      />
                    </XlbBasicForm.Item>
                  </div>
                </Radio>
                <Radio value={2}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span>商品档案</span>
                    <XlbBasicForm.Item name="item_ids" style={{ margin: 10 }}>
                      <XlbInputDialog
                        fieldNames={{
                          idKey: 'id',
                          nameKey: 'name',
                        }}
                        dialogParams={{
                          type: 'supplierContractItem',
                          dataType: 'lists',
                          isMultiple: true,
                          data: {
                            show_sale_unit: true,
                          },
                        }}
                        width={168}
                      ></XlbInputDialog>
                    </XlbBasicForm.Item>
                    <XlbButton size="small" onClick={() => importItem()}>
                      导入
                    </XlbButton>
                  </div>
                </Radio>
              </Space>
            </Radio.Group>
          </XlbBasicForm.Item>
        </div>
        <div className={style.box2} style={{ marginBottom: 0 }}>
          <p className={style.title}>修改数据</p>
          <XlbBasicForm.Item name="checkValue">
            <Checkbox.Group style={{ width: '100%' }}>
              {batch.map((item) => {
                return (
                  <Fragment key={item.value}>
                    <Checkbox value={item.value}>{item.label}</Checkbox>
                    <XlbBasicForm.Item name={item.value} key={item.value}>
                      <InputNumber
                        controls={false}
                        step={0.01}
                        style={{ width: 100 }}
                        size="small"
                        min={0}
                        max={99999999}
                      />
                    </XlbBasicForm.Item>
                  </Fragment>
                );
              })}
            </Checkbox.Group>
          </XlbBasicForm.Item>
        </div>
      </XlbBasicForm>
      <TemplateModel
        visible={priceTemplateVisible}
        handleCancel={() => setPriceTemplateVisible(false)}
        handleOk={TemplateHandelOk}
      />
    </XlbModal>
  );
};
export default BatchChange;