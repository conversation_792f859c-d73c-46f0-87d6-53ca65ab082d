import { XlbProPageContainer } from '@xlb/components'
import { isUndefined } from 'lodash'

export enum DataType {
  LISTS = 'lists',
  TREE = 'tree'
}

export default () => {
  return (
    <XlbProPageContainer
      searchFieldProps={{ formList: ['keyword', 'erpStoreIdsForArea'] }}
      treeFieldProps={{
        dataType: DataType.LISTS,
        leftUrl: '/erp/hxl.erp.store.area.find.all',
        leftKey: 'code',
        fieldName: { parent_id: 'parent_code', id: 'code' },
        onSelect: (data: any, form: any) => {
          return {
            ...form?.current?.getFieldsValue(true),
            ...(!isUndefined(data?.code) && { area_code: data.code })
          }
        }
      }}
      exportFieldProps={{
        url: '/erp/hxl.erp.store.area.detail.export',
        fileName: '行政区域'
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.store.area.detail.page',
        selectMode: 'single',
        immediatePost: true,
        tableColumn: [
          {
            name: '序号',
            code: '_index',
            width: 50,
            align: 'center'
          },
          {
            name: '省',
            code: 'province',
            width: 160,
            features: { sortable: true, copy: true }
          },
          {
            name: '市',
            code: 'city',
            width: 160,
            features: { sortable: true }
          },
          {
            name: '区',
            code: 'district',
            width: 160,
            features: { sortable: true }
          },
          {
            name: '门店代码',
            code: 'store_code',
            width: 160,
            features: { sortable: true }
          },
          {
            name: '门店名称',
            code: 'store_name',
            width: 312,
            features: { sortable: true }
          }
        ]
      }}
    />
  )
}