import { type FC } from 'react'
import { tableColumn } from './data'
import { hasAuth } from '@/utils/kit'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { XlbProPageContainer } from '@xlb/components'

const ProForm: FC<{ title: string }> = () => {
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        formList: [{ id: 'keyword', name: 'keyword', label: '关键字' }]
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.dept.find',
        tableColumn: tableColumn,
        selectMode: 'single',
        keepDataSource: false,
        showColumnsSetting: false,
        immediatePost: true
      }}
      deleteFieldProps={{
        name: '删除',
        showField: 'name',
        url: hasAuth(['商品部门', '删除']) ? '/erp/hxl.erp.dept.delete' : ''
      }}
      addFieldProps={{
        name: '新增',
        url: hasAuth(['商品部门', '编辑']) ? '/erp/hxl.erp.dept.save' : ''
      }}
      details={{
        mode: 'modal',
        isCancel: true,
        width: 350,
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>
        },
        hiddenSaveBtn: true,
        primaryKey: 'id',
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: ErpFieldKeyMap.otherIncomeExpensesName,
                  itemSpan: 24,
                  label: '商品部门名称',
                  rules: [{ required: true, message: '商品部门名称不能为空' }],
                  fieldProps: { maxLength: 20, width: '100%' }
                  // width:180
                }
              ]
            }
          }
        ],
        updateFieldProps: {
          url: '/erp/hxl.erp.dept.update'
        }
      }}
    />
  )
}

export default ProForm