import { ProForm as XlbProForm } from '@ant-design/pro-components';
import { type FC } from 'react';

import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { XlbProPageContainer } from '@xlb/components';
import { DataType } from '@xlb/components/dist/components/XlbTree/type';
import CustomerTable from './CustomerTable';
import { tableColumn } from './data';

const ProForm: FC<{ title: string }> = () => {
  const { enable_organization } = useBaseParams((state) => state);

  // console.log('enable_organization 2', localStorage.getItem('baseParams'));
  const isObjectEmpty = (obj: any): any[] => {
    // const fields = ['bank', 'bank_account_name', 'bank_account', 'bank_unionpay', 'default_flag'];
    if (Array.isArray(obj)) {
      return obj.filter((v) => {
        if (typeof v === 'string' && v.trim() === '') {
          return false;
        }
        if (v === null) {
          return false;
        }

        if (typeof v === 'object') {
          return !!Object.values(v).filter((v) => isObjectEmpty(v)).length;
        }

        if (typeof v === 'number' && isNaN(v)) {
          return false;
        }
      });
    }
    return obj;
  };
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        formList: [{ id: 'keyword', label: '关键字' }],
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.companyinvoice.page',
        tableColumn: () => {
          // const columns = cloneDeep(tableColumn);
          const columns = tableColumn;
          columns.find((i) => i.code == 'org_name')!.hidden =
            !enable_organization;
          return columns;
        },
        selectMode: 'single',
        keepDataSource: false,
        showColumnsSetting: true,
        immediatePost: true,
      }}
      treeFieldProps={
        enable_organization
          ? {
              leftUrl: '/erp/hxl.erp.org.tree',
              dataType: DataType.LISTS,
              leftKey: 'org_id',
            }
          : undefined
      }
      deleteFieldProps={{
        name: '删除',
        showField: 'title',
        url: hasAuth(['公司抬头管理', '删除'])
          ? '/erp/hxl.erp.companyinvoice.delete'
          : '',
      }}
      addFieldProps={{
        name: '新增',
        url: hasAuth(['公司抬头管理', '编辑'])
          ? '/erp/hxl.erp.companyinvoice.save'
          : '',
        beforePost: (formData) => {
          const obj = formData?.company_invoice_accounts?.map((v) => {
            return {
              bank: v.bank,
              bank_account_name: v.bank_account_name,
              bank_account: v.bank_account,
              bank_unionpay: v.bank_unionpay,
              default_flag: v.default_flag,
            };
          });
          const result = isObjectEmpty(obj);
          formData.company_invoice_accounts = result;
          return formData;
        },
      }}
      // 导出
      exportFieldProps={{
        url: hasAuth(['公司抬头管理', '导出'])
          ? '/erp/hxl.erp.companyinvoice.export'
          : '',
        fileName: '公司抬头管理.xlsx',
      }}
      details={{
        queryFieldProps: {
          url: '/erp/hxl.erp.companyinvoice.read',
        },
        mode: 'modal',
        isCancel: true,
        width: 1200,
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>;
        },
        disabled: () => !hasAuth(['公司抬头管理', '编辑']),
        hiddenSaveBtn: true,
        primaryKey: 'id',
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: ErpFieldKeyMap?.erpOrgIdByCompanyHeader,
                  itemSpan: 12,
                  label: '组织',
                  name: 'org_id',
                  rules: [
                    { required: enable_organization, message: '组织不能为空' },
                  ],
                  dependencies: ['enable_organization'],
                  hidden(formValues) {
                    return !enable_organization;
                  },
                },
                {
                  id: ErpFieldKeyMap.erpCompanyTitle,
                  itemSpan: 12,
                  rules: [{ required: true, message: '公司抬头不能为空' }],
                  fieldProps: { maxLength: 50 },
                },
                {
                  id: ErpFieldKeyMap.erpCompanyTaxNum,
                  itemSpan: 12,
                  fieldProps: { maxLength: 50 },
                  rules: [{ required: true, message: '公司税号不能为空' }],
                },
                {
                  id: ErpFieldKeyMap.erpLegalPerson,
                  itemSpan: 12,
                  fieldProps: { maxLength: 10 },
                },
                {
                  id: ErpFieldKeyMap.erpClerk,
                  itemSpan: 12,
                  fieldProps: { maxLength: 10 },
                },
                {
                  id: ErpFieldKeyMap.erpCompanyTitle,
                  itemSpan: 12,
                  name: 'tel',
                  label: '对账专员电话',
                  fieldProps: { maxLength: 50 },
                },
                {
                  id: ErpFieldKeyMap.erpCompanyTitle,
                  itemSpan: 12,
                  name: 'we_chat',
                  label: '对账专员微信',
                  fieldProps: { maxLength: 50 },
                },
                {
                  id: ErpFieldKeyMap.erpCompanyTitle,
                  itemSpan: 12,
                  name: 'addr',
                  label: '公司地址',
                  fieldProps: { maxLength: 50 },
                },
                {
                  id: ErpFieldKeyMap.erpMemo,
                  itemSpan: 24,
                  fieldProps: {
                    autoSize: { minRows: 3, maxRows: 4 },
                    maxLength: 50,
                    showCount: true,
                  },
                },
                {
                  id: ErpFieldKeyMap.erpCompanyIsDefault,
                  itemSpan: 12,
                  dependencies: ['is_default'],
                  disabled: (record: any) => {
                    return record?.is_default;
                  },
                },
              ],
            },
          },
          {
            componentType: 'customer',
            render: (record: any) => {
              return (
                <XlbProForm.Item shouldUpdate noStyle>
                  {() => {
                    return (
                      <XlbProForm.Item
                        name="company_invoice_accounts"
                        getValueFromEvent={(e) => {
                          return e;
                        }}
                        getValueProps={(value) => {
                          return {
                            value: value ?? [],
                          };
                        }}
                        valuePropName="dataSource"
                        trigger="onChangeData"
                      >
                        <CustomerTable />
                      </XlbProForm.Item>
                    );
                  }}
                </XlbProForm.Item>
              );
            },
          },
        ],
        updateFieldProps: {
          url: '/erp/hxl.erp.companyinvoice.save',
          beforePost: (formData) => {
            const obj = formData?.company_invoice_accounts?.map((v) => {
              return {
                bank: v.bank,
                bank_account_name: v.bank_account_name,
                bank_account: v.bank_account,
                bank_unionpay: v.bank_unionpay,
                default_flag: v.default_flag,
              };
            });
            const result = isObjectEmpty(obj);
            formData.company_invoice_accounts = result;
            return formData;
          },
        },
      }}
    />
  );
};

export default ProForm;