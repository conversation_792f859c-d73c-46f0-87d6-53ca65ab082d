import { default as XlbFetch } from '@/utils/XlbFetch';
import { XlbFetch as fetch } from '@xlb/utils/';

// 新品采购计划导出
export const exportPurchasePlan = async (data: any) => {
  return await fetch.post(
    `${process.env.BASE_URL}` + '/erp-purchase/hxl.erp.newitempurchaseplan.export',
    data,
    {
      responseType: 'blob',
    },
  );
};

// 新品采购计划批量审核通过
export const batchAuditPurchaseOrder = async (data: { fids: string[] }) => {
  return await XlbFetch('/erp-purchase/hxl.erp.newitempurchaseplan.batchaudit', data);
};

// 新品采购计划批量审核拒绝
export const batchRejectPurchaseOrder = async (data: {
  fids: string[];
  memo: string;
}) => {
  return await XlbFetch('/erp-purchase/hxl.erp.newitempurchaseplan.batchreject', data);
};

// 新品采购计划更新
export const updatePurchaseOrder = async (data: any) => {
  return await XlbFetch('/erp-purchase/hxl.erp.newitempurchaseplan.update', data);
};
