import { MustSellGoodState } from '@/pages/purchase/mustSellGoodsManagement/data';
import { hasAuth } from '@/utils/kit';
import { wujieBus } from '@/wujie/utils';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
} from '@xlb/components';
import dayjs from 'dayjs';
import { FC, useEffect, useState } from 'react';
import { FORM_LIST, tableColumn } from './data';
import { exportGoodsDetail } from './server';

const Index: FC = () => {
  // form
  const [form] = XlbBasicForm.useForm();

  // table format
  const prevPost = () => ({
    ...form.getFieldsValue(true),
    state: MustSellGoodState.Pass,
  });

  // init
  useEffect(() => {
    form.setFieldsValue({
      create_date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    });
  }, []);

  // operate
  const [exportLoading, setExportLoading] = useState(false);
  const exportItem = async (e: any, requestForm: any) => {
    setExportLoading(true);
    const res = await exportGoodsDetail(requestForm);
    setExportLoading(false);
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };

  return (
    <XlbPageContainer
      url={'/erp-purchase/hxl.erp.mustsellitemassortment.detail.page'}
      tableColumn={tableColumn}
      immediatePost
      prevPost={prevPost}
    >
      <XlbPageContainer.ToolBtn showColumnsSetting>
        {({
          fetchData,
          loading,
          dataSource,
          requestForm,
        }: ContextState<any>) => {
          return (
            <XlbButton.Group>
              {hasAuth(['必卖品明细', '查询']) && (
                <XlbButton
                  label="查询"
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    fetchData();
                  }}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['必卖品明细', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  disabled={!dataSource?.length}
                  loading={exportLoading}
                  icon={<XlbIcon name="daochu" />}
                  onClick={(e) => exportItem(e, requestForm)}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </XlbPageContainer.ToolBtn>

      <XlbPageContainer.SearchForm>
        <XlbForm form={form} formList={FORM_LIST} isHideDate />
      </XlbPageContainer.SearchForm>

      <XlbPageContainer.Table key="fid" primaryKey="fid" />
    </XlbPageContainer>
  );
};
export default Index;
